import {
  IPublicModelDocumentModel,
  IPublicModelNode,
  IPublicModelPluginContext,
} from '@alilc/lowcode-types';
import { debounce, delay } from 'lodash';
import { hotkey, common, material, canvas, project } from '@alilc/lowcode-engine';
import { v4 as uuidv4 } from 'uuid';

enum AIActionType {
  ADD = 'add',
  REMOVE = 'remove',
  CHANGE = 'change',
  SELECT = 'select',
}

// 动作拆解
// 新增动作 => ADD
// 更新动作 => SELECT+CHANGE
// 删除动作 => REMOVE

// 每个操作 完成后 都会 return 操作的 node的 id

interface BaseAIAction {
  type: AIActionType;
  targetId?: string; // 作用的目标id（如果 不传 会依次 找 到 链式调用的上一个 节点，最终会找到 root 节点）
  content?: any;
}

interface AddAIAction extends BaseAIAction {
  type: AIActionType.ADD;
}
interface RemoveAIAction extends BaseAIAction {
  type: AIActionType.REMOVE;
}
interface ChangeAIAction extends BaseAIAction {
  type: AIActionType.CHANGE;
  content: {
    props: {
      [propName: string]: any;
    };
  };
}
interface SelectAIAction extends BaseAIAction {
  type: AIActionType.SELECT;
}

type AIAction = AddAIAction | RemoveAIAction | ChangeAIAction | SelectAIAction;

class AIActionQueue {
  private actions: AIAction[] = [];
  private isExecuting = false;
  private currentStartTime = 0;
  private minDelay = 500;
  private currentPromise: {
    type: AIActionType;
    nodeId: string;
    resolve: (nodeId: string) => void;
  } | null = null;

  // 设置动作队列
  setActions(actions: AIAction[]) {
    this.actions = actions;
  }

  // 执行队列
  execute(): Promise<void> {
    if (this.isExecuting || this.actions.length === 0)
      return Promise.reject('正在执行队列或者队列为空');

    this.isExecuting = true;
    // 使用 reduce 创建 Promise 链，每个 Promise 的结果会传递给下一个 Promise
    return this.actions
      .reduce((promise, action) => {
        return promise.then((previousNodeId?: string) => {
          return new Promise<string>((resolve) => {
            try {
              this.currentStartTime = Date.now();
              const { type, targetId, content } = action;
              // TODO 可优化
              // targetId > previousNodeId > root
              const nodeId = targetId ?? previousNodeId ?? project?.currentDocument?.root?.id ?? '';
              // id存在的情况下，节点还是 找不到，就用root兜底
              const node =
                project?.currentDocument?.getNodeById(nodeId!) ?? project?.currentDocument?.root;

              // 设置当前等待的 Promise
              this.currentPromise = {
                type: action.type,
                nodeId,
                resolve: (nodeId: string) => {
                  resolve(nodeId);
                  this.currentPromise = null;
                },
              };

              switch (type) {
                case AIActionType.ADD:
                  const { snippets = [] } = material.getComponentMeta('TEXT')?.getMetadata() as any;
                  const newNode = project.currentDocument?.createNode({
                    ...(snippets[0]?.schema ?? {}),
                    id: uuidv4(),
                  });
                  node?.children?.insert(newNode!);

                  break;
                case AIActionType.SELECT:
                  node?.select();
                  break;
                case AIActionType.CHANGE:
                  const dslInfo = node?.getPropValue('dslInfo');
                  const resultValue = {
                    ...dslInfo,
                    ...content.props,
                  };
                  node?.setPropValue('dslInfo', resultValue);
                  // TODO 这块逻辑待验证，需不需要
                  Object.keys(resultValue).forEach((key) => {
                    node?.setPropValue(`dslInfo.${key}`, resultValue[key]);
                  });
                  break;
                case AIActionType.REMOVE:
                  node?.remove();
                  break;
              }
            } catch (error) {
              console.error('Action execution error:', error);
              resolve(previousNodeId || ''); // 出错时传递上一个 nodeId
            }
          });
        });
      }, Promise.resolve<string | undefined>(undefined))
      .then(() => {
        this.isExecuting = false;
      });
  }

  private handleNext = debounce((nodeId: string) => {
    const executionTime = Date.now() - this.currentStartTime;
    const delayTime = Math.max(this.minDelay - executionTime, 0);
    delay(() => {
      this.currentPromise?.resolve(nodeId);
    }, delayTime);
  }, 200);

  // 完成当前动作
  complete(type: AIActionType, nodeId: string, parentNodeId?: string) {
    if (!this.currentPromise || this.currentPromise.type !== type) return;
    if (type !== AIActionType.ADD && this.currentPromise.nodeId !== nodeId) return;
    if (type === AIActionType.ADD && this.currentPromise.nodeId !== parentNodeId) return;
    this.handleNext(nodeId);
  }
}

const LcdpAiActionPlugin = (ctx: IPublicModelPluginContext, options: any) => {
  const actionQueue = new AIActionQueue();

  // 设置动作队列
  actionQueue.setActions([
    { type: AIActionType.ADD, content: {} },
    { type: AIActionType.SELECT, content: {} },
    {
      type: AIActionType.CHANGE,
      content: {
        props: {
          lang: {
            textContent: {
              zh_CN: '哈哈哈',
              zh_TW: '哈哈哈',
              en_US: '哈哈哈',
            },
          },
          textContent: '哈哈哈',
        },
      },
    },
    {
      type: AIActionType.REMOVE,
      content: {},
    },
    { type: AIActionType.ADD, content: {} },
  ]);

  return {
    async init() {
      console.log('LcdpAiActionPlugin init');
      setTimeout(() => {
        console.log('现在开始执行队列');
        actionQueue.execute().then(() => {
          console.log('所有动作执行完成');
        });
      }, 10000);

      const { project } = ctx;

      project.onChangeDocument((document: IPublicModelDocumentModel) => {
        document.onMountNode((payload) => {
          const { node } = payload;
          // console.log('onAddNode*******:', node);
          actionQueue.complete(AIActionType.ADD, node.id, node.parent?.id);
        });

        document.onRemoveNode((node) => {
          // console.log('onRemoveNode*******', node);
          actionQueue.complete(AIActionType.REMOVE, node.id);
        });

        document.onChangeNodeProp((info) => {
          // console.log('onChangeNodeProp*******', info);
          actionQueue.complete(AIActionType.CHANGE, info.node.id);
        });

        document.onChangeSelection((ids: string[]) => {
          // console.log('onChangeSelection*******', ids);
          actionQueue.complete(AIActionType.SELECT, ids[0]);
        });
      });
    },
  };
};

LcdpAiActionPlugin.pluginName = 'LcdpAiActionPlugin';

export default LcdpAiActionPlugin;
