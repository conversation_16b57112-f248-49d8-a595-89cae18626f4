import { event, canvas, common, config } from '@alilc/lowcode-engine';
import {
  ActionFieldTreeRefInfo,
  AthLowCodeConfigKey,
  AthLowCodeEventName,
  MessageToMainGobal,
  MessageToMainType,
} from '../plugin-ath-loader/type';
import { findNodeByKey, getDragTargetData } from '../plugin-ath-field-panel/tools';
import { IPublicEnumDragObjectType, IPublicTypeDragNodeDataObject } from '@alilc/lowcode-types';
import { transferDslToSchema } from '@/tools/business/lcdp-converter';
import { setNestedValue } from '../plugin-ath-setter/components/Button/tools';

import type { AthTreeDataNode } from '../plugin-ath-field-panel/type';
import { AthComponentType, DslData, DslSchema } from '@/tools/business/lcdp-converter/type';

const cacheDslSchemaMap: Map<string, DslSchema[]> = new Map();

const findDslSchemaByFullPath = (dslSchema: DslSchema[], fullPath: string): DslSchema | null => {
  if (!dslSchema || dslSchema.length === 0 || !fullPath) return null;
  let targetSchema: DslSchema | null = null;
  let stack: DslSchema[] = [...dslSchema];
  while (stack.length > 0) {
    const dslSchema: DslSchema | undefined = stack.shift();
    if (dslSchema) {
      const { props } = dslSchema ?? {};
      const { dslInfo } = props ?? {};
      const { schema, path } = dslInfo ?? {};
      const nodeFullPath = path ? `${path}.${schema}` : schema;
      if (nodeFullPath === fullPath) {
        targetSchema = dslSchema;
        break;
      }
      if (dslSchema.children && dslSchema.children.length > 0) {
        stack = [...stack, ...dslSchema.children];
      }
    }
  }
  return targetSchema;
};

const dealDragTargetForDataSourceName = (
  dragTarget: IPublicTypeDragNodeDataObject,
  dataSourceName: string,
) => {
  const { data } = dragTarget;
  const { componentName } = data as DslSchema;
  const needFillDataSourceNameComponentTypes: Set<AthComponentType> = new Set([
    AthComponentType.ATHENA_TABLE,
    AthComponentType.FORM_LIST,
  ]);
  if (needFillDataSourceNameComponentTypes.has(componentName)) {
    setNestedValue(data, [
      'props',
      'dslInfo',
      'queryInfo',
      'dataFilter',
      'dataSourceNames',
    ], [dataSourceName]);
  }
  return dragTarget;
};

const registerNode = (dom: HTMLDivElement, treeData: AthTreeDataNode[], dataSourceName: string) => {
  if (!canvas.dragon) return;
  canvas.dragon.from(dom, (e: Event): IPublicTypeDragNodeDataObject | null => {
    const nodeData = findNodeByKey(treeData, (e.target as HTMLDivElement)?.dataset?.fullPath);

    if (!nodeData) return null;

    if (cacheDslSchemaMap.has(dataSourceName)) {
      const dslSchema: DslSchema[] = cacheDslSchemaMap.get(dataSourceName) ?? [];
      const fullPath = nodeData.fullPath;
      const targetSchemaData = findDslSchemaByFullPath(dslSchema, fullPath);
      if (targetSchemaData) {
        console.log(
          '🚀 [Generated Log]: path = src/plugins/lcdp-datasource-panel/index.tsx, scope = registerNode, targetSchemaData = ',
          targetSchemaData,
          '; 找到了对应的后端schema',
        );
        return dealDragTargetForDataSourceName({
          type: IPublicEnumDragObjectType.NodeData,
          data: targetSchemaData,
        }, dataSourceName);
      }
    }

    const dragData = getDragTargetData(nodeData, locale);
    console.log(
      '🚀 [Generated Log]: path = src/plugins/lcdp-datasource-panel/index.tsx, scope = registerNode.dragData, dragData = ',
      dragData,
      '; 没找到对应的后端schema,走前端默认生成逻辑',
    );
    //@ts-ignore
    if (dragData.props.dslInfo) dragData.props.dslInfo.comeFrom = 'field';

    const dragTarget = {
      type: IPublicEnumDragObjectType.NodeData,
      data: dragData,
    } as IPublicTypeDragNodeDataObject;

    return dealDragTargetForDataSourceName(dragTarget, dataSourceName);
  });
  dom.dataset.registered = 'true';
};

const { getLocale } = common.utils.createIntl?.() || {};
const locale: string = (getLocale?.() || 'zh-CN').replace(/-/g, '_');
const registerAdditive = (data: ActionFieldTreeRefInfo) => {
  const { fieldTreeDom, fieldTreeIds, treeData, dataSourceName } = data;
  if (!fieldTreeDom || !canvas.dragon) return;

  if (fieldTreeIds?.length > 0) {
    fieldTreeIds.forEach((id: string) => {
      const dom: HTMLDivElement | null = fieldTreeDom.querySelector(`div[data-full-path='${id}']`);
      if (dom && dom.dataset.registered !== 'true') {
        registerNode(dom as HTMLDivElement, treeData, dataSourceName);
      }
    });
  }
};

const dealFieldTreeRefInfoUpdate = (data: ActionFieldTreeRefInfo) => {
  registerAdditive(data);
};

const sendMessage = (pluginName: string, status: string) => {
  window.microApp.clearData();
  window.microApp.dispatch({
    type: MessageToMainType.Gobal,
    data: {
      type: 'updatePluginStatus',
      data: { pluginName, status },
    },
  } as MessageToMainGobal);
};

const updateSchemaCacheMap = (extraData: any) => {
  const { backendDefaultLayout } = extraData ?? {};
  if (backendDefaultLayout) {
    cacheDslSchemaMap.clear();
    Object.keys(backendDefaultLayout).forEach((dataSourceName: string) => {
      const dslData: DslData[] = backendDefaultLayout?.[dataSourceName] ?? [];
      const dslSchema: DslSchema[] = transferDslToSchema(dslData);
      cacheDslSchemaMap.set(dataSourceName, dslSchema);
    });
  }
};

const LcdpDataSourcePanelPlugin = () => {
  return {
    async init() {
      event.on(
        `common:${AthLowCodeEventName.AthFieldTreeRefInfoUpdate}`,
        dealFieldTreeRefInfoUpdate,
      );
      event.on(`common:${AthLowCodeEventName.LowCodeExtraDataUpdate}`, updateSchemaCacheMap);
      // const athLoaderManage = config.get(AthLowCodeConfigKey.AthLoaderManage);
      // athLoaderManage?.handleUpdatePluginStatus('LcdpDataSourcePanelPlugin', 'ready');
      /**
       * 不采用原有方式的原因是，当初始化画布为空时，athLoaderManage也会为空，消息就发不出去了
       */
      sendMessage('LcdpDataSourcePanelPlugin', 'ready');
    },
    destroy() {
      event.off(
        `common:${AthLowCodeEventName.AthFieldTreeRefInfoUpdate}`,
        dealFieldTreeRefInfoUpdate,
      );
      event.off(`common:${AthLowCodeEventName.LowCodeExtraDataUpdate}`, updateSchemaCacheMap);
      // const athLoaderManage = config.get(AthLowCodeConfigKey.AthLoaderManage);
      // athLoaderManage?.handleUpdatePluginStatus('LcdpDataSourcePanelPlugin', 'destory');
      sendMessage('LcdpDataSourcePanelPlugin', 'destory');
    },
  };
};
LcdpDataSourcePanelPlugin.pluginName = 'LcdpDataSourcePanelPlugin';
LcdpDataSourcePanelPlugin.meta = {
  dependencies: ['AthLoaderPlugin'],
};
export default LcdpDataSourcePanelPlugin;
