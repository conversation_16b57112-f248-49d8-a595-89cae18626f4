import React, { useEffect, useState } from 'react';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';
import CommonSetterLayout from '../../components/common-setter-layout';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';

import type { ISubpageSelectorSetterProps } from './types';
import type { ISubPageDefineInfo } from '@core_types/components/SubpageSelector/types';

function LcdpSubpageSelectorSetter(props: ISubpageSelectorSetterProps) {

  const { options, value, onChange } = props;
  const [appInfo, setAppInfo] = useState<{appCode?: string, code?: string}>({});

  useEffect(() => {
    const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    setAppInfo({
      appCode: dynamicInfo?.applicationCode,
      code: dynamicInfo?.code,
    });
  }, []);

  const doChange = (info: ISubPageDefineInfo) => {
    onChange(info);
  }

  return (
    <CommonSetterLayout {...(options?.titleProps ?? {})}>
      <div className='lcdp-subpage-selector-setter'>
        <AthenaDesignerCoreMFComponent
          componentName="SubpageSelector"
          componentProps={{
            appCode: appInfo.appCode,
            code: appInfo.code,
            value: value,
            onChange: doChange
          }}
        />
      </div>
    </CommonSetterLayout>
  )
}

LcdpSubpageSelectorSetter.displayName = 'LcdpSubpageSelectorSetter';
export default LcdpSubpageSelectorSetter;
