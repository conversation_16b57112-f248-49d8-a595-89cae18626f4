// entry
import './public-path';
import i18nInitPromise from './i18n';
import { init, plugins } from '@alilc/lowcode-engine';
import { createFetchHandler } from '@alilc/lowcode-datasource-fetch-handler';
// import UndoRedoPlugin from '@alilc/lowcode-plugin-undo-redo';
import ZhEnPlugin from '@alilc/lowcode-plugin-zh-en';
import SchemaPlugin from '@alilc/lowcode-plugin-schema';
import InjectPlugin from '@alilc/lowcode-plugin-inject';
import ComponentPanelPlugin from './plugins/plugin-component-panel';
import SaveSamplePlugin from './plugins/plugin-save-sample';
import SetRefPropPlugin from '@alilc/lowcode-plugin-set-ref-prop';
import appHelper from './appHelper';
import './global.scss';
import i18n from 'i18next';

// ath 业务插件
// ath 初始化
import LcdpInitPlugin from './plugins/lcdp-init-plugin';
// dsl编辑面板
import AthDslEditorPlugin from './plugins/plugin-ath-dsl-editor';
// lcdp 撤销恢复
import LcdpUndoRedoPlugin from './plugins/lcdp-undo-redo-plugin';
// lcdp 操作面板区域
import LcdpHandleAreaPlugin from './plugins/lcdp-handle-area-plugin';
// ath 的 setter
import AthSetterPlugin from './plugins/plugin-ath-setter';
// ath 的 数据源 字段
// import AthFieldPanelPlugin from './plugins/plugin-ath-field-panel';
// 数据源面板插件
import LcdpDataSourcePanelPlugin from './plugins/lcdp-datasource-panel';
// ath 的hooks的插件
import AthHooksPlugin from './plugins/plugin-ath-hooks';
import AthDslCheckerPlugin from './plugins/plugin-ath-dsl-checker';
import LcdpPermissionPanelPlugin from './plugins/lcdp-permission-panel';
import { OutlinePlugin } from './plugins/plugin-outline-pane';

// ath 数据加载模块
import AthLoaderPlugin from './plugins/plugin-ath-loader';
// ath 物料加载模块
import AthMaterialLoaderPlugin from './plugins/plugin-ath-material-loader';

import LcdpAiActionPlugin from './plugins/lcdp-ai-action-plugin';

import '@/tools/business/load-module-federation-module';

/**
 * hooks
 */
// import AthHooksPlugin from './plugins/plugin-ath-hooks';

async function registerPlugins() {
  await plugins.register(InjectPlugin);

  // ath 初始化插件
  await plugins.register(LcdpInitPlugin, {
    scenarioName: 'general',
    displayName: 'ath',
  });

  // 物料加载模块
  await plugins.register(AthMaterialLoaderPlugin);

  await plugins.register(ComponentPanelPlugin);
  await plugins.register(SchemaPlugin, { isProjectSchema: true });
  // 注册回退/前进
  // await plugins.register(UndoRedoPlugin);
  // 注册中英文切换
  await plugins.register(ZhEnPlugin);
  await plugins.register(SetRefPropPlugin);
  await plugins.register(SaveSamplePlugin);

  // ath 插件
  // dsl编辑面板
  await plugins.register(AthDslEditorPlugin);
  // 撤销恢复
  await plugins.register(LcdpUndoRedoPlugin);
  // 操作面板区域
  await plugins.register(LcdpHandleAreaPlugin);
  // ath 设置器注册（设置器开发在此）
  await plugins.register(AthSetterPlugin);
  // ath 字段面板
  // await plugins.register(AthFieldPanelPlugin);
  // 注册数据源面板
  await plugins.register(LcdpDataSourcePanelPlugin);
  await plugins.register(AthHooksPlugin);
  // await plugins.register(AthDslCheckerPlugin);
  await plugins.register(OutlinePlugin);
  // await plugins.register(AthHooksPlugin);
  // 数据加载模块
  await plugins.register(AthLoaderPlugin);
  await plugins.register(LcdpPermissionPanelPlugin);

  await plugins.register(LcdpAiActionPlugin);
}

(async function main() {
  await i18nInitPromise;

  await registerPlugins();

  const locale: string = (i18n?.language || 'zh_CN').replace(/_/g, '-');

  init(document.getElementById('lce-container')!, {
    locale,
    enableCondition: true,
    enableCanvasLock: true,
    disableAutoRender: true,
    // 默认绑定变量
    supportVariableGlobally: false,
    appHelper,
    hideSettingsTabsWhenOnlyOneItem: true,
    simulatorUrl: [
      'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.4/dist/js/react-simulator-renderer.js',
      'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.4/dist/css/react-simulator-renderer.css',
    ],
  });
})();
