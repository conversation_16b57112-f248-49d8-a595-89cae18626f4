const webpack = require('webpack');
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { ModuleFederationPlugin } = webpack.container; // 模块联邦
const { getWorkerEntryConfigs } = require('./getAllWorker'); // 获取所有worker的入口文件
// 暂时不用mf2
// const { ModuleFederationPlugin } = require('@module-federation/enhanced/webpack');

const dependencies = require('../package').dependencies;
const isDev = process.env.NODE_ENV === 'development'; // 是否是开发模式

module.exports = {
  entry: {
    main: path.join(__dirname, '../src/index.tsx'),
    ...getWorkerEntryConfigs(path.join(__dirname, '../src/worker')),
  },
  output: {
    filename: 'js/[name].[chunkhash:8].js',
    path: path.join(__dirname, '../dist'),
    clean: true,
    publicPath: '/',
  },
  cache: {
    type: 'filesystem',
  },
  stats: {
    assets: true,
  },
  resolve: {
    extensions: ['.js', '.tsx', '.ts'],
    alias: {
      '@': path.join(__dirname, '../src'),
      '@components': path.join(__dirname, '../src/components'),
      '@worker': path.join(__dirname, '../src/worker'),
      '@athena-designer-editor': path.join(__dirname, '../../athena-designer-editor'),
    },
    // 如果用的是pnpm 就暂时不要配置这个,否则会找不到模块
    modules: [path.resolve(__dirname, '../node_modules')],
  },
  module: {
    rules: [
      {
        test: /.(ts|tsx)$/,
        use: ['thread-loader', 'babel-loader'],
        exclude: /\.worker\.ts$/,
        include: [path.resolve(__dirname, '../src')],
      },
      {
        test: /.worker.ts$/,
        loader: 'babel-loader',
        include: [path.resolve(__dirname, '../src')],
      },
      // 是否需要处理 node_modules 中的语法，视情况而定
      // {
      //  test: /.(js|jsx)$/,
      //  use: 'babel-loader'
      // },
      {
        test: /\.css$/,
        enforce: 'pre',
        include: [path.resolve(__dirname, '../src')],
        use: [isDev ? 'style-loader' : MiniCssExtractPlugin.loader, 'css-loader', 'postcss-loader'],
      },
      {
        test: /\.less$/,
        enforce: 'pre',
        include: [path.resolve(__dirname, '../src')],
        use: [
          isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
          'less-loader',
        ],
      },
      {
        test: /.(png|jpg|jpeg|gif|svg)$/,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024,
          },
        },
        generator: {
          filename: 'static/images/[name].[contenthash:8][ext]',
        },
      },
      {
        test: /.(woff2?|eot|ttf|otf)$/,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024,
          },
        },
        generator: {
          filename: 'static/fonts/[name].[contenthash:8][ext]',
        },
      },
      {
        test: /.(mp4|webm|ogg|mp3|wav|flac|aac)$/,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024,
          },
        },
        generator: {
          filename: 'static/media/[name].[contenthash:8][ext]',
        },
      },
    ],
  },
  // 插件
  plugins: [
    new HtmlWebpackPlugin({
      favicon: path.resolve(__dirname, '../public/images/favicon.ico'),
      template: path.resolve(__dirname, '../src/index.html'),
      inject: true,
      chunks: ['main'],
    }),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      PUBLIC_URL: JSON.stringify(path.resolve(__dirname, '../public')),
    }),
    new ModuleFederationPlugin({
      name: 'athena_designer_core',
      library: {
        type: 'window',
        name: 'athena_designer_core',
      },
      filename: 'remoteEntry.js',
      exposes: {
        './ModelGraph': './src/pages/ModelGraph/index',
        './ComponentSetting': './src/pages/ComponentSetting/index',
        './DataSource': './src/components/DataSource/index',
        './MonacoEditor': './src/components/MonacoEditor/index',
        './OpenwindowWorkDesign': './src/components/OpenwindowWorkDesign/index',
        './HooksEditor': './src/components/MonacoEditor/HooksEditor',
        './newReact': require.resolve('react'),
        './newReactDOMClient': require.resolve('react-dom/client'),
        './SelectFieldModal':
          './src/components/RuleManage/components/RuleManageContentModal/SelectFieldModal/MFShare',
        './react': require.resolve('react'),
        './reactDomClient': require.resolve('react-dom/client'),
        './AppLangInput': './src/components/AppLangInput/MFShare',
        './ActionModal': './src/components/ActionModal/index',
        './TagEditModal': './src/components/TagEditModal/index',
        './PlatformIconModal': './src/components/PlatformIconModal/MFShare',
        './ReportAndBasicDataInputModal': './src/components/ReportAndBasicDataInputModal/MFShare',
        './ImgUpload': './src/components/ImgUpload/index',
        './WorkflowSelector': './src/components/WorkflowSelector/MFShare',
        './DropDownVocabulary': './src/components/DropDownVocabulary/index',
        './ActionParams': './src/components/ActionParams/MFShare',
        './SubpageSelector': './src/components/SubpageSelector/MFShare',
        './PrintTemplateModal': './src/components/PrintTemplateModal/MFShare',
        './HooksComprehensiveEditor': './src/components/MonacoEditor/HooksComprehensiveEditor',
        './OperatePermission': './src/components/OperatePermission/MFShare',
        './PermissionTab':
          './src/components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/PermissionTab/MFShare',
        './DropDownVocabularyModal': './src/components/DropDownVocabularyModal/index',
        './DataElementModal': './src/components/DataElementModal/index',
        './RepresentClassModal': './src/components/RepresentClassModal/index',
        './SqlEditor': './src/components/MonacoEditor/SqlEditor',
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: dependencies.react,
          eager: true,
        },
        'react-dom': {
          singleton: true,
          requiredVersion: dependencies['react-dom'],
          eager: true,
        },
      },
    }),
  ],
};
