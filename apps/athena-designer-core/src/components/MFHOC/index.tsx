import React, { forwardRef, useEffect, useTransition, Suspense } from 'react';
import useGlobalStore from '@/store';
import type { ComponentType } from 'react';
import { App } from 'antd';
import '@/common/utils/i18n'; // 引入i18n配置
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import i18n from 'i18next';

interface IComponentProps extends DV.AnyOBJ {
  ref?: React.ForwardedRef<unknown>;
}

interface IHOCProps extends IComponentProps {
  systemConfig: {
    auth: Map<string, DV.AnyOBJ>;
    authId: string | null;
    authToken: {
      iamToken: string;
      token: string;
    };
    config: DV.AnyOBJ;
    currentLanguage: string;
    digiMiddlewareAuthApp: string;
    userInfo: DV.AnyOBJ;
  };
}

/**
 * React组件在angular中调用的时候，需要处理多语言和全局配置问题
 * @param Component
 * @returns
 */
const MFHOC = <T extends DV.AnyOBJ>(Component: ComponentType<IComponentProps & T>) => {
  return forwardRef((props: IHOCProps & T, ref) => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          // 禁用缓存
          gcTime: 0,
          staleTime: 0,
          retry: 0,
          refetchOnWindowFocus: false,
        },
        mutations: {
          retry: 0,
        },
      },
    });
    const { systemConfig } = props;
    const [isPending, startTransition] = useTransition();
    useEffect(() => {
      if (systemConfig) {
        startTransition(() => {
          if (systemConfig?.currentLanguage) {
            i18n.changeLanguage(systemConfig?.currentLanguage);
          }
          useGlobalStore.setState({
            ...systemConfig,
            sessionStorage,
            localStorage,
          });
          require('@/common/config/interceptors');
        });
      }
    }, [systemConfig]);

    return (
      <QueryClientProvider client={queryClient}>
        <App className="antd-app">
          <Suspense>
            {/* { !isPending && <Component ref={ref} {...props} /> } */}
            <Component ref={ref} {...props} />
          </Suspense>
        </App>
      </QueryClientProvider>
    );
  });
};

export default MFHOC;
