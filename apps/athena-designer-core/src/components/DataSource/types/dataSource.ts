import { SBoolean, IAnyObj } from '@components/DataSource/types/common';
import { IConditionData } from '@components/DataSource/types/condition';
import { IProcessConfig } from '@components/DataSource/types/processing';
import { IMetaDataFieldData, IMetaDataTreeData } from '@components/DataSource/types/metadataField';
import { FormInstance } from 'antd';
import { IDataSourceFormData } from '@components/DataSource/types/dataSourceBasic';
import { IMetaDataMidInfo } from '@components/DataSource/types/editor';
import { IPageDataInfo } from '@components/DataSource/types';
import {
  ChangeType,
  DataSourceChangePageUIElementCodeOPType,
  DataSourceType,
  OpType,
} from '@components/DataSource/enum';
import { AthTreeDataNode } from '@athena-designer-editor/src/plugins/plugin-ath-field-panel/type';

export interface IDataViewQueryInfo {
  code: string;
  isDefault?: boolean;
  type?: string;
}

export interface IEnumKeyOption {
  key: string;
  value: string;
}

export interface IEspActionFieldBase {
  data_name?: string;
  data_type?: string;
  description?: ILangDetailInfo;
  fullPath?: string;
  is_array?: boolean | SBoolean;
  is_businesskey?: boolean | SBoolean;
  is_datakey?: boolean | SBoolean;
  required?: boolean | SBoolean;
}

export interface IEspActionQuery {
  actionId: string;
  upProcessNames?: string;
  code?: string;
  dataSourceName?: string;
}

export interface IBatchFieldQueryInfo {
  actionId?: string;
  sourceType?: string;
  code?: string;
  dataSourceName?: string;
}

export interface IBatchFieldsQueryParmas {
  actionBasicList: IBatchFieldQueryInfo[];
  businessCode?: string;
}

export interface IBatchFieldQueryResponse {
  [key: string]: IEspActionFieldInModel;
}

export interface IEspActionFieldInModelDetail extends IEspActionFieldBase {
  dbFieldType: string;
  isQuoteField: boolean;
  size: string;
  unique: boolean;
}

export interface IEspActionFieldInModel extends IEspActionFieldBase {
  field: IEspActionFieldInModel[];
}

export interface IHeaderConifg {
  templateId: string;
}

export interface ILangDetailInfo {
  en_US: string;
  zh_CN: string;
  zh_TW: string;
}

export type ILangInfo = {
  [key in string]: ILangDetailInfo;
};

export interface IDataSourceDetailInfo extends IEspActionFieldBase {
  actionId?: string;
  enablePaging?: boolean;
  name: string;
  override?: string;
  sequence?: number;
  serviceName?: string;
  title: string;
  type?: string;
  dataViewQuery?: IDataViewQueryInfo;
  viewCode?: string;
  lang?: ILangInfo;
  paras?: IAnyObj;
  masterData?: string;
  notArray?: boolean;
  dataKeys?: string[];
  actionParams?: IConditionData[];
  dataProcessors?: IProcessConfig[];
  metadataFields?: IMetaDataFieldData[];
}

export interface IDataSourceInfo {
  type_alias: string;
  json: IDataSourceDetailInfo;
}

export type IDataSourceOrigin = {
  [key in string]: IDataSourceDetailInfo;
};

/**
 * 更新信息
 */
export interface IUpdateInfo {
  /**
   * 数据源actionId, 如果是查询方案就是code
   */
  actionId: string;
  /**
   * 数据源名称
   */
  name: string;
}

export interface IDataSourceUpdateDetailInfo {
  /**
   * 更新前相关数据源信息
   */
  from: IUpdateInfo;
  /**
   * 更新后相关数据源信息
   */
  to?: IUpdateInfo;
}

/**
 * 操作之后生成的数据源更新信息，后续操作以来更新信息进行
 */
export type IDataSourceUpdateInfo = {
  /**
   * 数据源操作类型
   */
  opType: OpType;
  /**
   * 画布后续变更类型，影响具体调用的回调
   */
  changeType?: ChangeType;
  /**
   * 回调方法返回的类型
   */
  callbackType?: DataSourceChangePageUIElementCodeOPType;
  /**
   * 更新数据源傅家的信息：更新详细信息
   */
  info?: IDataSourceUpdateDetailInfo;
  extraData?: { dataSourceNames?: string[] };
  /**
   * 是否禁用保留按钮: true => 禁用, condition: 删除数据源时: 只有一个数据源 || DataSourceMode === 'multiple', 其余都不禁用
   */
  //reserveDisabled?: boolean;
};

export interface IDataSourceOption {
  hideNotArray: boolean;
  hideProcessing: boolean;
  optimizeMetadata: boolean;
  isShowQueryPlan?: boolean; // 是否支持查询方案
  customMenuList?: DataSourceType[]; // 定制基础数据源类型
  customDeleteDataSource?: boolean; // 定制数据源删除逻辑
  dragMethod?: string; // 定制其他拖拽类型定义
  onlyOne?: string; // 是否只能有一个数据源
}

export interface IDataSourceStoreState {
  dataSourceLists: IDataSourceInfo[];
  dataSourceShowMap?: Map<string, boolean>;
  serviceUrl?: string;
  loading: boolean;
  enumKeyOptions: IEnumKeyOption[];
  headerConfig?: IHeaderConifg;
  favouriteCode?: string;
  applicationCodeProxy?: string;
  updateInfo?: IDataSourceUpdateInfo;
  fieldTreeMap?: Map<string, AthTreeDataNode[]>;
  dataSourceNames?: string[];
  mainDataSourceName: string | null;
}

export interface IDataSourceDataSlice extends IDataSourceStoreState {
  setDataSource: (dataSource: IDataSourceOrigin) => void;
  setQueryPlanDefault: (dataSourceName: string) => void;
  updateLoading: (loading: boolean) => void;
  addDataSource: (dataSource: IDataSourceInfo) => void;
  updateDataSource: (dataSource: IDataSourceInfo, index: number, changeType?: ChangeType) => void;
  updateDataSourceJson: (
    json: IDataSourceDetailInfo,
    dataSourceIndex: number,
    changeType?: ChangeType
  ) => void;
  deleteDataSource: (index: number, changeType?: ChangeType, dataSourceNames?: string[]) => void;
  setServiceUrl: (serviceUrl: string) => void;
  setDataSourceItemShow: (actionId: string, isShow: boolean) => void;
  updateEnumKeyOptions: (options: IEnumKeyOption[]) => void;
  updateFieldTreeMap: (fieldTreeMap: Map<string, AthTreeDataNode[]>) => void;
  updateMainDataSourceName: (dataSourceName: string) => void;
  updateDataSourceNames: (dataSourceNames?: string[]) => void;
}

export interface IDataSourceState extends IDataSourceDataSlice {
  dataSourceNameSet: Set<string>;
}

export interface IBasicFormRefStore {
  form: FormInstance<IDataSourceFormData>;
  update: (form: FormInstance<IDataSourceFormData>) => void;
}

export interface IRebuildDataSourceParams {
  formData: IDataSourceFormData;
  dataSource: IDataSourceInfo;
  conditions?: IConditionData[];
  processors?: IProcessConfig[];
  metadataTreeDatas?: IMetaDataTreeData[];
  paras?: IAnyObj;
  metadataInfo?: IMetaDataMidInfo;
}

export interface IDataSourceChangePageUIElementCodeParams {
  from: string;
  type: DataSourceChangePageUIElementCodeOPType;
  to?: string;
}

export interface IFieldTreeNodeInfo {
  dom: HTMLDivElement;
  nodeData: AthTreeDataNode;
}

export type DataSourceChangeCallback = (dataSource: IDataSourceOrigin) => void;
export type DataSourceUpdateCallback = (name: string) => void;
export type DataSourceResetPageUIElementCallback = (
  info: IDataSourceChangePageUIElementCodeParams
) => void;
export type DataSourceChangePageCallback = (pageInfo: IPageDataInfo) => void;
export type DataSourceCustomDeleteCallback = (name: string) => Promise<boolean>;
export type DataSourceChangePageUIElementCodeCallback = (
  info: IDataSourceChangePageUIElementCodeParams
) => void;
export type DataSourceUpdateMainDataSourceNameCallback = (names: string[]) => void;
export type FieldTreeMapUpdateCallback = (fieldTreeMap: Map<string, AthTreeDataNode[]>) => void;
export type FieldTreeNodeInfoListUpdateCallback = (
  fieldTreeNodeInfoList: IFieldTreeNodeInfo[]
) => void;
