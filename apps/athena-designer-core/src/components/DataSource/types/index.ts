import { SyncTabsChangeCallback } from '@components/DataSource/types/syncTabs';
import {
  DataSourceChangeCallback,
  DataSourceUpdateCallback,
  IDataSourceOrigin,
  IDataSourceOption,
  DataSourceResetPageUIElementCallback,
  DataSourceChangePageCallback,
  ILangDetailInfo,
  DataSourceCustomDeleteCallback,
  DataSourceChangePageUIElementCodeCallback,
  DataSourceUpdateMainDataSourceNameCallback,
  // FieldTreeMapUpdateCallback,
  // FieldTreeNodeInfoListUpdateCallback,
} from '@components/DataSource/types/dataSource';
import { DataSourceMode, FieldSourceMode } from '@components/DataSource/enum';
import { SBoolean } from './common';
import { IMetaDataPrecisionInfo } from './metadataField';
import { AthTreeDataNode } from '@athena-designer-editor/src/plugins/plugin-ath-field-panel/type';

export interface ICallbackCollection {
  syncTab?: SyncTabsChangeCallback;
  changeDataSource?: DataSourceChangeCallback;
  updateDataSource?: DataSourceUpdateCallback;
  changePage?: DataSourceChangePageCallback;
  resetPageUIElement?: DataSourceResetPageUIElementCallback;
  customDeleteDataSource?: DataSourceCustomDeleteCallback;
  changePageUIElementCode?: DataSourceChangePageUIElementCodeCallback;
  updateDataSourceNames?: DataSourceUpdateMainDataSourceNameCallback;
  // updateFieldTreeMap?: FieldTreeMapUpdateCallback;
  // updateFielTreeNodeInfoList?: FieldTreeNodeInfoListUpdateCallback;
  close?: () => void;
}

export interface ICallbackCollectionStore extends ICallbackCollection {
  setCallbacks: (info: ICallbackCollection) => void;
  addCallback: (
    name: keyof ICallbackCollection,
    fn: ICallbackCollection[keyof ICallbackCollection]
  ) => void;
}

// 以下formJson的类型来自个人通过代码对于formJson的类型推断，只关注代码中用到的字段 -- formJson start
export interface IFormJsonDataSource {
  actionName?: string;
}

export interface IFormJsonCustomOptions {
  dataSource?: IFormJsonDataSource;
}

export interface IFormJsonComponent {
  customOptions?: IFormJsonCustomOptions;
  components?: IFormJsonComponent[];
}

export interface IFormJson {
  components?: IFormJsonComponent[];
}

// -- formJson end

export interface IPageDataActionParam {
  type?: string;
  name?: string;
  value?: string;
}

export interface IPageDataAction {
  type?: string;
  actionParams?: IPageDataActionParam[];
}

export interface IPageDataInfo {
  submitActions?: IPageDataAction[];
  [key: string]: unknown;
}

export interface IEntryProps {
  visible: boolean;
  appCode?: string;
  pageCode?: string;
  options?: IDataSourceOption;
  applicationCodeProxy?: string;
  pageData?: IPageDataInfo;
  dataSources?: IDataSourceOrigin;
  fieldTreeMap?: Map<string, AthTreeDataNode[]>;
  dataSourceNames?: string[];
  basicDataSourceMode?: DataSourceMode;
  canvasReady?: boolean;
  dataSourcePluginReady?: boolean;
  queryPlanDataSourceMode?: DataSourceMode;
  fieldSourceMode?: FieldSourceMode;
  interceptDataSourceUpdateOperations?: boolean;
  // 该字段原本是通过事件获取更新，现在需要调用方自己监听DslFormRenderShareMobileService.formChange$事件，然后通过props传进来
  //formJson?: IFormJson;
  // 是否需要自己渲染头部,默认false
  needHeader?: boolean;
  // 以absolute固定定位展示，还是正常展示, 默认auto
  position?: 'absolute' | 'auto';
  modelPageType?: 'design' | 'browse' | 'edit' | 'notModelDriven';
}

export interface IEntryPropsStore extends IEntryProps {
  update: (props: IEntryProps) => void;
}

// rc-menu的click事件callback入参类型
export interface IMenuInfo {
  key: string;
  keyPath: string[];
  /** @deprecated This will not support in future. You should avoid to use this */
  item: React.ReactInstance;
  domEvent: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>;
}

/**
 * 字段树结构
 */
export interface IFieldSetInfo {
  data_name: string;
  data_type: string;
  description: ILangDetailInfo;
  fullPath: string;
  field?: IFieldSetInfo[];
  required?: SBoolean;
  dbFieldType?: string;
  isQuoteField?: SBoolean;
  size?: string;
  unique?: SBoolean;
  isSystem?: SBoolean;
  canSort?: SBoolean;
  canFilter?: SBoolean;
  precision?: IMetaDataPrecisionInfo;
  is_datakey?: SBoolean;
  is_array?: SBoolean;
  is_businesskey?: SBoolean;
}

export interface IFieldSetChangeCallbackData {
  data: IFieldSetInfo;
  mark: string;
}
