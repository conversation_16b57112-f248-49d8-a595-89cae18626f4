import { Category, PageCode } from '@/components/DynamicWorkDesign/config/type';

export interface INormalAjaxResData<T> {
  code?: number;
  data?: T;
}

export interface IAnyObj<T = unknown> {
  [key: string]: T;
}

export interface INormalAjaxRes<T> {
  data: INormalAjaxResData<T>;
  status: number;
  statusText: string;
}

export type SBoolean = 'true' | 'false';

export interface ITMQueryActionInfo {
  /**
   * 数据源类型
   */
  type: string;
  /**
   * 数据源actionId
   */
  actionId: string;
}

export interface IDataSourceListInfo {
  /**
   * 数据源名称
   */
  dataSourceName: string;
  /**
   * 数据源对应查询信息
   */
  tmQueryAction: ITMQueryActionInfo;
}

/**
 * 批量查询数据源对应的手段生成dsl的逻辑
 * TODO: 待定，目前结构不确定，后续定了之后再改，先按单次查询单个定义
 */
export interface IQueryGenerateBackendDefaultLayoutParams {
  /**
   * 应用code
   */
  application: string;
  /**
   * 作业code
   */
  pageDesignCode: string;
  /**
   * 作业类型
   */
  pageCode: PageCode;
  /**
   * 基础资料类型
   */
  category: Category;
  /**
   * 数据源信息集合
   */
  dataSourceList: IDataSourceListInfo[];
}

export interface IBackendDefaultElementInfo {
  layout?: any[];
}

/**
 * TODO: 结构待定
 */
export interface IQueryGenerateBackendDefaultLayoutResponse {
  dataSourceName: string;
  elements: IBackendDefaultElementInfo;
}

export interface IBackendDefaultLayoutInfo {
  [dataSourceName: string]: any[];
}
