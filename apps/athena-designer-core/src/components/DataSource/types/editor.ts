import { DataSourceType } from '@components/DataSource/enum';
import { IDataSourceInfo } from '@components/DataSource/types/dataSource';
import { IDataSourceFormData } from '@components/DataSource/types/dataSourceBasic';
import { FormInstance } from 'antd';
import { OpType } from '@components/DataSource/enum';
import { IConditionData } from '@components/DataSource/types/condition';
import { IMetaDataTreeData } from '@components/DataSource/types/metadataField';
import { IProcessConfig } from '@components/DataSource/types/processing';
import { IAnyObj } from '@components/DataSource/types/common';

export interface ISpecialChangeInfo {
  key: string;
  value: string;
}

export interface IMetaDataMidInfo {
  actionId?: string;
  serviceName?: string;
}

export interface IEditStoreState {
  visible: boolean;
  title?: string;
  opType?: OpType;
  dataSourceIndex?: number;
  dataSource?: IDataSourceInfo;
  dataSourceType?: DataSourceType;
  isEdit?: boolean;
  form?: FormInstance<IDataSourceFormData>;
  specialChangeInfo?: ISpecialChangeInfo[];
  metadataTreeDatas?: IMetaDataTreeData[];
  conditions?: IConditionData[];
  processors?: IProcessConfig[];
  paras?: IAnyObj;
  metadataInfo?: IMetaDataMidInfo;
}

export interface IEditStore extends IEditStoreState {
  clear: () => void;
  update: (info: Partial<IEditStoreState>) => void;
  updateFormInstance: (form: FormInstance<IDataSourceFormData>) => void;
  updateEditMark: (isEdit: boolean) => void;
  updateMetaData: (actionId?: string) => void;
  updateSpecialInfo: (info: ISpecialChangeInfo[]) => void;
  updateCondition: (opType: OpType, index?: number, condition?: IConditionData) => void;
  updateProcessor: (opType: OpType, index?: number, processor?: IProcessConfig) => void;
  updateTreeData: (treeDatas: IMetaDataTreeData[]) => void;
  updateParas: (paras: IAnyObj) => void;
}
