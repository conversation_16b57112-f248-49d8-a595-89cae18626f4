import { create } from 'zustand';
import { cloneDeep } from 'lodash';
import { OpType } from '@components/DataSource/enum';
import { getServiceName } from '@components/DataSource/tools';

import type { IEditStore, IEditStoreState, ISpecialChangeInfo } from '@components/DataSource/types/editor';
import type { IDataSourceFormData } from '@components/DataSource/types/dataSourceBasic';
import type { FormInstance } from 'antd';
import type { IConditionData } from '@components/DataSource/types/condition';
import type { IMetaDataTreeData } from '@components/DataSource/types/metadataField';
import type { IProcessConfig } from '@components/DataSource/types/processing';
import type { IAnyObj } from '@components/DataSource/types/common';

const useEditStore = create<IEditStore>((set) => ({
  visible: false,
  isEdit: false,
  clear: (visible: boolean = false) => {
    set({
      title: '',
      opType: null,
      dataSourceIndex: null,
      dataSource: null,
      isEdit: false,
      dataSourceType: null,
      form: null,
      specialChangeInfo: [],
      visible,
      metadataTreeDatas: [],
      conditions: [],
      processors: [],
      paras: {},
    });
  },
  update: (info: Partial<IEditStoreState>) => {
    set({
      // 默认副作用值其次
      conditions: info.dataSource?.json?.actionParams ?? [],
      processors: info.dataSource?.json?.dataProcessors ?? [],
      metadataTreeDatas: [],
      paras: info.dataSource?.json?.paras ?? {},
      // 主动传入优先级最高
      ...info,
    });
  },
  updateParas: (paras: IAnyObj) => {
    set({
      paras,
      isEdit: true,
    });
  },
  updateMetaData: (actionId?: string) => {
    set({
      metadataInfo: {
        actionId,
        serviceName: getServiceName(actionId),
      },
      isEdit: true,
    });
  },
  updateEditMark: (isEdit: boolean) => {
    set({ isEdit });
  },
  updateFormInstance: (form: FormInstance<IDataSourceFormData>) => {
    set({ form });
  },
  updateSpecialInfo: (infos: ISpecialChangeInfo[]) => {
    set({ specialChangeInfo: infos });
  },
  updateCondition: (opType: OpType, index?: number, condition?: IConditionData) => {
    set((state: IEditStoreState) => {
      const conditions = state?.conditions ?? [];
      if (opType === OpType.ADD) {
        conditions.push(condition);
      } else if (opType === OpType.EDIT) {
        conditions[index] = condition;
      } else if (opType === OpType.DELETE) {
        conditions.splice(index, 1);
      }
      return {
        conditions: [...conditions],
        isEdit: true,
      };
    });
  },
  updateProcessor: (opType: OpType, index?: number, processor?: IProcessConfig) => {
    set((state: IEditStoreState) => {
      const processors = state?.processors ?? [];
      if (opType === OpType.ADD) {
        processors.push(processor);
      } else if (opType === OpType.EDIT) {
        processors[index] = processor;
      } else if (opType === OpType.DELETE) {
        processors.splice(index, 1);
      }
      return {
        processors: [...processors],
        isEdit: true,
      };
    });
  },
  updateTreeData: (treeDatas: IMetaDataTreeData[]) => {
    set({
      metadataTreeDatas: [...treeDatas],
      isEdit: true,
    });
  }
}));

function useEdit() {
  const editStore = useEditStore();

  return {
    ...editStore,
    update: (info: Partial<IEditStoreState>) => {
      editStore.update(cloneDeep(info));
    }
  };
}

export { useEdit };
