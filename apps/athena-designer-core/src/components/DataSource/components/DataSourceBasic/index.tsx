import React, { Fragment, useState, useEffect, useMemo } from 'react';
import { Form, TreeSelect, Checkbox, Row, Col, Divider } from 'antd';
import AppInput from '@components/AppInput';
import AppLangInput from '@/components/AppLangInput';
import { ActionModal } from '@components/ActionModal';
import Icon from '@components/Icon';
import { DataViewModal } from '@components/DataViewModal';
import { DataSourceTreeNode } from '@components/DataSource/components/DataSourceBasic/DataSourceTreeNode';
import { TagNode } from '@components/DataSource/components/DataSourceBasic/TagNode';
import { TreeSelectNotFound } from '@components/DataSource/components/DataSourceBasic/TreeSelectNotFound';

import { useTranslation } from 'react-i18next';
import { useGetter } from '@components/DataSource/hooks/useGetter';
import { useSaveEntryProps } from '@components/DataSource/store/useSaveEntryProps';
import { useEdit } from '@components/DataSource/store/useEdit';

import {
  isShowOptimizeMetadataFormItem,
  formatTreeData,
} from '@components/DataSource/tools/dataSourceBasic';
import { DataSourceType, OriginDataSourceType } from '@components/DataSource/enum';
import { queryActionField } from '@components/DataSource/services';
import { getServiceName, transformTreeDatasToMap } from '@components/DataSource/tools';
import i18n from 'i18next';

import './index.less';

import type {
  IDataSourceFormData,
  ITreeNodeData,
  IActionModalState,
  IQueryPlanModalState,
} from '@components/DataSource/types/dataSourceBasic';
import type {
  IEspActionFieldInModel,
  IEspActionQuery,
  ILangDetailInfo,
} from '@components/DataSource/types/dataSource';
import type { TCallBackParams } from '@components/ActionModal/hooks/hooks';
import type { IDataViewModalCallbackParams } from '@/components/DataViewModal/types';

const FormItem = Form.Item;

function DataSourceBasic() {
  const { dataSourceType, dataSource, updateFormInstance, updateEditMark, updateSpecialInfo } =
    useEdit();

  const [form] = Form.useForm<IDataSourceFormData>();
  const { t } = useTranslation();
  const { appCode, applicationCodeProxy, options } = useSaveEntryProps();
  const [showArraySet, setShowArraySet] = useState<boolean>(false);
  const [modalState, setModalState] = useState<IActionModalState>({
    visible: false,
    actionData: {
      useApp: 'false',
    },
  });
  const [queryPlanModalState, setQueryPlanModalState] = useState<IQueryPlanModalState>({
    visible: false,
  });
  const [treeDatas, setTreeDatas] = useState<ITreeNodeData[]>([]);

  const { hideNotArray, optimizeMetadata } = options ?? {};

  const cacheTreeDatasMap = useMemo(() => {
    const cacheMap = new Map<string, string>();
    transformTreeDatasToMap(cacheMap, treeDatas);
    return cacheMap;
  }, [treeDatas]);

  useEffect(() => {
    updateFormInstance(form);
  }, [form]);

  const isQueryPlan = useGetter(
    (dataSourceType: DataSourceType) => {
      return dataSourceType === DataSourceType.QUERY_PLAN;
    },
    [dataSourceType]
  );

  const isESPOrQueryPlan = useGetter(
    (dataSourceType: DataSourceType) => {
      return [DataSourceType.QUERY_PLAN, DataSourceType.ESP].includes(dataSourceType);
    },
    [dataSourceType]
  );

  useEffect(() => {
    if (form) {
      form.setFieldsValue({
        name: dataSource?.json?.name,
        title: dataSource?.json?.title,
        actionId: null,
        masterData: dataSource?.json?.masterData,
        dataKeys: [],
        notArray: dataSource?.json?.notArray,
        type: dataSource?.json?.type,
        dataviewQueryType: dataSource?.json?.dataViewQuery?.type,
        lang: dataSource?.json?.lang ?? {
          title: { zh_CN: '', zh_TW: '', en_US: '', [i18n.language]: dataSource?.json?.title },
        },
        dataViewQuery: dataSource?.json?.dataViewQuery,
      });
    }
    setTreeDatas([]);
    if (dataSource?.json?.actionId) {
      handleSelectAction(dataSource.json.actionId, dataSource.json.dataKeys, false);
    }
  }, [dataSource]);

  const queryAction = async (actionId: string): Promise<IEspActionFieldInModel> => {
    const params: IEspActionQuery = {
      actionId,
    };
    return await queryActionField(params);
  };

  const handleSelectAction = async (
    actionId: string,
    dataKeys: string[] = [],
    patchNameValue: boolean = true
  ) => {
    const formValue: IDataSourceFormData = {
      actionId,
      serviceName: getServiceName(actionId),
    };
    try {
      const data = await queryAction(actionId);
      formValue['masterData'] = data?.data_name;
      if (!options?.hideNotArray) {
        setShowArraySet(!!data?.is_array && data?.data_type === 'object');
      }
      const treeData = data ? formatTreeData([data]) : [];
      setTreeDatas(treeData);
      if (patchNameValue) {
        const curType = form.getFieldValue('type');
        formValue['name'] =
          curType === OriginDataSourceType.QUERY_PLAN ? actionId : data?.data_name;
        formValue['title'] = data?.description?.[t('dj-LANG')] ?? '';
        formValue['lang'] = {
          title: data?.description,
        };
      }
      formValue['dataKeys'] = dataKeys;
    } finally {
      form.setFieldsValue(formValue);
    }
  };

  const doOpenAction = () => {
    setModalState({
      visible: true,
      actionData: {
        actionId: form.getFieldValue('actionId') ?? '',
        actionName: '',
        useApp: 'true',
      },
    });
  };

  const doOpenQueryPlan = () => {
    setQueryPlanModalState({
      visible: true,
      dataViewQuery: form.getFieldValue('dataViewQuery'),
    });
  };

  const doChangeTitle = (data: ILangDetailInfo) => {
    form.setFieldsValue({
      title: data?.[i18n.language],
    });
    doChangeEditStatus('title');
  };

  const doChangeEditStatus = (key?: string) => {
    if (key === 'title') {
      updateSpecialInfo([
        {
          key: 'title',
          value: form.getFieldValue('title'),
        },
      ]);
    } else if (key) {
      updateSpecialInfo([
        {
          key,
          value: form.getFieldValue(key),
        },
      ]);
    }
    updateEditMark(true);
  };

  const doChangeTreeSelect = () => {
    updateEditMark(true);
  };

  const doActionModalConfirm = (params: TCallBackParams) => {
    handleSelectAction(params.actionId);
    form.setFieldValue(
      'type',
      params.actionType === 'EspAction' ? OriginDataSourceType.ESP : OriginDataSourceType.SD
    );
    setModalState({
      visible: false,
    });
  };

  const doDataViewCancel = () => {
    setQueryPlanModalState({
      visible: false,
    });
  };

  const doDataViewConfirm = (info: IDataViewModalCallbackParams) => {
    handleSelectAction(info.code);
    form.setFieldsValue({
      dataviewQueryType: info.type,
      dataViewQuery: {
        ...dataSource.json?.dataViewQuery,
        ...info,
      },
    });
    doDataViewCancel();
  };

  const handleCancel = () => {
    setModalState({
      visible: false,
    });
  };

  return (
    <div className="data-source-basic mf-override-basic">
      <Form form={form} name="basicForm">
        <Row gutter={[26, 12]}>
          {isShowOptimizeMetadataFormItem(dataSourceType, optimizeMetadata) && (
            <Col span={12}>
              <FormItem
                name="actionId"
                colon={false}
                label={t(optimizeMetadata ? 'dj-元数据' : 'dj-API')}
                rules={
                  !optimizeMetadata
                    ? [
                        {
                          required: true,
                          message: t('dj-请选择API'),
                        },
                      ]
                    : []
                }
              >
                <AppInput
                  placeholder={t('dj-请选择')}
                  readOnly
                  suffix={
                    <Icon
                      type="iconkaichuang"
                      onClick={doOpenAction}
                      className="window-icon iconfont"
                    />
                  }
                />
              </FormItem>
            </Col>
          )}
          {isQueryPlan && (
            <Fragment>
              <Col span={12}>
                <FormItem
                  name="actionId"
                  colon={false}
                  label={t('dj-查询方案')}
                  rules={[
                    {
                      required: true,
                      message: t('dj-请选择查询方案'),
                    },
                  ]}
                >
                  <AppInput
                    placeholder={t('dj-请选择')}
                    readOnly
                    suffix={
                      <Icon
                        type="iconkaichuang"
                        onClick={doOpenQueryPlan}
                        className="window-icon iconfont"
                      />
                    }
                  />
                </FormItem>
              </Col>
              <FormItem name="dataViewQuery" hidden>
                <AppInput />
              </FormItem>
            </Fragment>
          )}
          {(isShowOptimizeMetadataFormItem(dataSourceType, optimizeMetadata) || isQueryPlan) && (
            <FormItem name="serviceName" hidden={true}>
              <AppInput />
            </FormItem>
          )}
          <Col span={12}>
            <FormItem
              name="name"
              colon={false}
              label={t('dj-名称')}
              rules={[
                {
                  required: true,
                  message: t(
                    optimizeMetadata
                      ? 'dj-数据源名称不能为空，且仅由元数据带入'
                      : 'dj-请输入数据源名称，只支持输入英文，数字，下划线'
                  ),
                },
              ]}
            >
              <AppInput
                placeholder={t('dj-请输入')}
                disabled={optimizeMetadata}
                readOnly={optimizeMetadata}
                onChange={() => doChangeEditStatus('name')}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              name={['lang', 'title']}
              colon={false}
              label={t('dj-描述')}
              rules={
                isQueryPlan
                  ? [
                      {
                        required: true,
                        message: t('dj-请输入'),
                      },
                    ]
                  : []
              }
            >
              <AppLangInput
                title={t('dj-描述')}
                placeholder={t('dj-请输入')}
                onChange={doChangeTitle}
              />
            </FormItem>
          </Col>
          <FormItem name="type" hidden>
            <AppInput />
          </FormItem>
          <FormItem name="title" hidden>
            <AppInput />
          </FormItem>
          {isESPOrQueryPlan && (
            <Fragment>
              <Col span={12}>
                <FormItem name="masterData" colon={false} label={t('dj-主数据')}>
                  <AppInput className="disabled" placeholder={t('dj-请输入')} readOnly />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem name="dataKeys" colon={false} label={t('dj-主键')}>
                  <TreeSelect<ITreeNodeData>
                    notFoundContent={<TreeSelectNotFound />}
                    popupClassName="tree-select mf-override-treeselect datakeys-treeselect"
                    showSearch={true}
                    placeholder={t('dj-请选择')}
                    multiple={true}
                    treeCheckable={true}
                    treeData={treeDatas}
                    treeDefaultExpandAll={true}
                    allowClear
                    maxTagCount={1}
                    virtual={false}
                    tagRender={({ value, onClose, isMaxTag, label, closable }) => {
                      const fullDesc = cacheTreeDatasMap.get(value);
                      return (
                        <TagNode
                          value={value}
                          isMaxTag={isMaxTag}
                          label={label}
                          closeable={closable}
                          fullDesc={fullDesc}
                          onClose={onClose}
                        />
                      );
                    }}
                    treeTitleRender={(data: ITreeNodeData) => <DataSourceTreeNode {...data} />}
                    onChange={doChangeTreeSelect}
                  />
                </FormItem>
              </Col>
            </Fragment>
          )}
          {!hideNotArray && isESPOrQueryPlan && showArraySet && (
            <Col span={24}>
              <FormItem name="notArray" colon={false} label={<span />} valuePropName="checked">
                <Checkbox onChange={() => doChangeEditStatus('notArray')}>
                  {t('dj-转换为表单数据，则永远只显示第一笔')}
                </Checkbox>
              </FormItem>
            </Col>
          )}
        </Row>
      </Form>
      {modalState.visible && (
        <ActionModal
          visible={modalState.visible}
          transferData={modalState.actionData}
          labelType="EspAction"
          appCode={appCode}
          applicationCodeProxy={applicationCodeProxy}
          callBack={doActionModalConfirm}
          closeModal={handleCancel}
        />
      )}
      <DataViewModal
        visible={queryPlanModalState.visible}
        dataViewQuery={queryPlanModalState.dataViewQuery}
        appCode={appCode}
        cancel={doDataViewCancel}
        confirm={doDataViewConfirm}
      />
      <Divider />
    </div>
  );
}

export { DataSourceBasic };
