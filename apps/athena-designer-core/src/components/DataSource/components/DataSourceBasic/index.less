.data-source-basic {
  padding: 6px 0 12px 0;
  .ant-divider {
    margin: 12px 0 0 0;
  }
  .ant-form-item {
    margin-right: 0;
    margin-bottom: 0;
    &.margin-b {
      margin-bottom: 0;
    }
    .ant-form-item-no-colon {
      font-size: 13px;
      color: #333;
    }
    .checkbox-item .ant-form-item-control-input {
      min-height: 14px;
      height: 14px;
      .check-label {
        padding-left: 8px;
      }
    }
  }
  #basicForm {
    .ant-form-item-label {
      width: 80px;
    }
    .ant-form-item-control {
      width: 304px;
    }
  }
}
.req-node {
  white-space: nowrap;
  padding: 0 4px;
  cursor: pointer;
  line-height: 24px;
  margin-left: 0;
  display: inline-block;

  .node-desc {
    padding: 0 8px;
    display: inline-block;
    color: #999;
    position: relative;
    left: 12px;
    margin-right: 4px;
  }
}
.datakeys-treeselect {
  .ant-select-tree-list-holder > div {
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }
}
