import React, { useState } from 'react';
import Icon from '@components/Icon';
import AppButton from '@components/AppButton';
import { MonacoEditor } from '@components/MonacoEditor';
import { SQLEditor } from '@/components/MonacoEditor/SqlEditor';
import { message } from 'antd';
import { analyzeJson } from '@components/DataSource/services';

import { useTranslation } from 'react-i18next';

import { SupportedEditorType } from '@components/MonacoEditor/enum';

import './importMetaJson.less';

import type { IImportMetaJsonProps } from '@components/DataSource/types/importMetaJson';

function ImportMetaJson(props: IImportMetaJsonProps) {
  const { change } = props;

  const { t } = useTranslation();

  const [codeVisible, setCodeVisible] = useState<boolean>(false);

  const handleBuildPath = (obj: any, data: any): any => {
    const getParent = (parentId, str) => {
      if (!parentId) return '';
      const parentData = data.find((item) => item.id === parentId);
      const returnPath = `${parentData['dataName']}${str ? '.' + str : ''}`;
      if (parentData.parent) {
        return getParent(parentData.parent, returnPath);
      } else {
        return returnPath;
      }
    };
    return getParent(obj.parent, '');
  };

  const handleBuildTree = (data: any): any => {
    const result = [];
    const obj = {};
    data.forEach((item) => {
      const dataType =
        item.isArray === 'Y' || data.find((d) => d.parent === item.id) ? 'object' : item.dataType;
      obj[item.id] = {
        dataType,
        name: item.dataName,
        array: item.isArray === 'Y',
        businessKey: item.isBusinessKey === 'Y',
        required: item.isRequired === 'Y',
        path: handleBuildPath(item, data),
        description: item.description || '',
        lang: {
          ...(item.lang || {}),
          description: {
            zh_CN: item?.lang?.['description']?.zh_CN || '',
            zh_TW: item?.lang?.['description']?.zh_TW || '',
            en_US: item?.lang?.['description']?.en_US || '',
          },
        },
      };
    });
    data.forEach((item) => {
      const parent = obj[item.parent];
      if (!parent) {
        result.push(obj[item.id]);
        return;
      }
      (parent[this.childType] || (parent[this.childType] = [])).push(obj[item.id]);
    });
    return result;
  };

  const doOk = async (data?: string) => {
    let jsonData: any;
    try {
      jsonData = JSON.parse(data);
      if (typeof jsonData !== 'object') {
        message.error(t('dj-JSON数据有误，请修改'));
        return;
      }
    } catch (error) {
      message.error(t('dj-JSON数据有误，请修改'));
      return;
    }
    const res = await analyzeJson(jsonData);
    if (res?.code === 0) {
      if (res?.data?.code === '000') {
        const newMetaTree = handleBuildTree(res?.data?.messageSpec ?? []);
        change(newMetaTree);
        doCancel();
      } else {
        message.error(res?.data?.description);
      }
    } else {
      message.error(res?.data);
    }
  };

  const doShow = () => {
    setCodeVisible(true);
  };

  const doCancel = () => {
    setCodeVisible(false);
  };

  return (
    <div className="import-meta-json-wrapper">
      <AppButton
        icon={<Icon type="iconscan" className="del" />}
        iconPosition="start"
        className="import-json-button"
        type="link"
        onClick={doShow}
      >
        {t('dj-导入JSON')}
      </AppButton>
      <MonacoEditor
        visible={codeVisible}
        type={SupportedEditorType.JSON}
        onOk={doOk}
        onCancel={doCancel}
      />
    </div>
  );
}

export { ImportMetaJson };
