import React, { useState } from 'react';
import { Col, Flex, Row, Tree } from 'antd';
import AppButton from '@/components/AppButton';
import { MetadataFieldModal } from '@components/DataSource/components/DataSourceVirtualField/MetadataFieldModal';
import { ImportMetaJson } from '@components/DataSource/components/DataSourceVirtualField/ImportMetaJson';
import { MetaTreeData } from '@components/DataSource/components/DataSourceVirtualField/MetaTreeNode';
import { DataSourceFormHeader } from '@components/DataSource/components/DataSourceFormHeader';

import { useTranslation } from 'react-i18next';
import { useEdit } from '@components/DataSource/store';

import { OpType } from '@components/DataSource/enum';
import {
  createNewTreeData,
  transferMetaDataToTreeData,
  operateMetaTreeData,
} from '@components/DataSource/tools';

import './index.less';

import type {
  IMetaDataFieldData,
  IMetaDataTreeData,
  IMetaDataModalState,
} from '@components/DataSource/types/metadataField';

function DataSourceVirtualField() {
  const { metadataTreeDatas, updateTreeData } = useEdit();

  const { t } = useTranslation();
  const [metaModalState, setMetaModalState] = useState<IMetaDataModalState>({
    visible: false,
    opType: OpType.ADD,
    nodeData: {
      title: '',
      key: '',
      path: '',
    },
  });

  const doImportJson = (datas: IMetaDataFieldData[]) => {
    const treeDatas: IMetaDataTreeData[] = transferMetaDataToTreeData(datas);
    triggerChange(treeDatas);
  };

  const doAddField = (parentNode?: IMetaDataTreeData) => {
    setMetaModalState({
      visible: true,
      opType: OpType.ADD,
      nodeData: createNewTreeData(parentNode),
    });
  };

  const doEditField = (node: IMetaDataTreeData) => {
    setMetaModalState({
      visible: true,
      opType: OpType.EDIT,
      nodeData: node,
    });
  };

  const doDeleteField = (node: IMetaDataTreeData) => {
    const treeDatas: IMetaDataTreeData[] = operateMetaTreeData(
      node,
      OpType.DELETE,
      metadataTreeDatas
    );
    triggerChange(treeDatas);
  };

  const doMetadataOk = (data: IMetaDataTreeData, opType: OpType) => {
    const treeDatas: IMetaDataTreeData[] = operateMetaTreeData(data, opType, metadataTreeDatas);
    triggerChange(treeDatas);
    doMetadataClose();
  };

  const triggerChange = (datas: IMetaDataTreeData[]) => {
    updateTreeData(datas);
  };

  const doMetadataClose = () => {
    setMetaModalState({
      ...metaModalState,
      visible: false,
    });
  };

  const renderNode = (node: IMetaDataTreeData) => {
    return (
      <MetaTreeData
        nodeData={node}
        add={() => doAddField(node)}
        edit={doEditField}
        del={doDeleteField}
      />
    );
  };

  return (
    <div className="data-source-virtual-field">
      <DataSourceFormHeader>{t('dj-虚拟字段')}</DataSourceFormHeader>
      <Flex align="middle" justify="start" gap={0}>
        <ImportMetaJson change={doImportJson} />
        <AppButton type="link" onClick={() => doAddField()}>
          {`+ ${t('dj-添加字段')}`}
        </AppButton>
      </Flex>
      <div className="tree-box">
        <Row>
          <Col span={24}>
            <Tree<IMetaDataTreeData>
              blockNode
              treeData={metadataTreeDatas}
              titleRender={renderNode}
            />
          </Col>
        </Row>
      </div>
      <MetadataFieldModal {...metaModalState} ok={doMetadataOk} close={doMetadataClose} />
    </div>
  );
}

export { DataSourceVirtualField };
