import React, { useState } from 'react';
import { ConditionModal } from '@components/DataSource/components/DataSourceCondition/ConditionModal';
import { Tooltip, message, But<PERSON>, Divider, Row, Col } from 'antd';
import AppButton from '@/components/AppButton';
import Icon from '@components/Icon';
import { DataSourceFormHeader } from '@components/DataSource/components/DataSourceFormHeader';

import { useEdit, useConfirmModal } from '@components/DataSource/store';

import { useTranslation } from 'react-i18next';
import { OpType } from '@components/DataSource/enum';

import './index.less';

import type { IConditionData, IConditionModalState } from '@components/DataSource/types/condition';

function DataSourceCondition() {

  const { conditions, updateCondition } = useEdit();
  const { show: showConfirmModal, hide: hideConfirmModal } = useConfirmModal<never>();

  const [modalInfo, setModalInfo] = useState<IConditionModalState>({
    visible: false,
  });

  const { t } = useTranslation();

  const doModalCancel = () => {
    setModalInfo({
      visible: false,
    });
  };

  const doModalConfirm = (data: IConditionData, type: OpType, index?: number) => {
    const findIndex = conditions.findIndex(condition => condition.name === data.name);
    if (findIndex >= 0 && findIndex !== index) {
      message.error(t('dj-条件参数名称不能重复'));
      return;
    }
    updateCondition(type, index, data);
    doModalCancel();
  };

  const doEdit = (index: number) => {
    setModalInfo({
      visible: true,
      modalInfo: conditions[index],
      type: OpType.EDIT,
      currentIndex: index,
    });
  };

  const doDelete = (index: number) => {
    showConfirmModal({
      visible: true,
      content: t('dj-确认删除条件？', { name: conditions[index].name }),
      ok: () => {
        updateCondition(OpType.DELETE, index);
        hideConfirmModal();
      },
      close: () => {
        hideConfirmModal();
      }
    });
  };

  const doAddCondition = () => {
    setModalInfo({
      visible: true,
      modalInfo: {},
      type: OpType.ADD,
      currentIndex: null,
    });
  };

  return (
    <div className="data-source-condition">
      <DataSourceFormHeader>
        {t('dj-条件')}
      </DataSourceFormHeader>
      <div className="operation">
        <AppButton
          type='link'
          onClick={doAddCondition}
        >
          {`+ ${t('dj-添加一项')}`}
        </AppButton>
      </div>
      {
        conditions?.length > 0 && (
          <div className="list">
            <Row gutter={[10, 8]}>
              {
                conditions.map((condition: IConditionData, index: number) => {
                  return (
                    <Col 
                      span={12}
                      key={`${condition.name}-${index}`}
                    >
                      <div className="item">
                        <div className="left">
                          <Tooltip
                            overlayClassName='mf-override-tooltip'
                            placement='top'
                            title={condition.name}
                          >
                            {condition.name}
                          </Tooltip>
                        </div>
                        <div className="right">
                          <Tooltip
                            overlayClassName='mf-override-tooltip'
                            placement='top'
                            title={t('dj-编辑')}
                          >
                            <Icon
                              type='iconjizhikabianji1'
                              className='iconfont'
                              onClick={() => doEdit(index)}
                            />
                          </Tooltip>
                          <Tooltip
                            overlayClassName='mf-override-tooltip'
                            placement='top'
                            title={t('dj-删除')}
                          >
                            <Icon
                              type='iconjizhikashanchu1'
                              className='iconfont'
                              onClick={() => doDelete(index)}
                            />
                          </Tooltip>
                        </div>
                      </div>
                    </Col>
                  );
                })
              }
            </Row>
          </div>
        )
      }
      <ConditionModal
        {...modalInfo}
        cancel={doModalCancel}
        confirm={doModalConfirm}
      />
      <Divider />
    </div>
  );
}

export { DataSourceCondition };
