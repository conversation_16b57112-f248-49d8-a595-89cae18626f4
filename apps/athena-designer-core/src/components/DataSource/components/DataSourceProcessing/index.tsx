import React, { useEffect, useState } from 'react';
import { Tooltip, Dropdown, Divider, Row, Col } from 'antd';
import Icon from '@components/Icon';
import AppButton from '@/components/AppButton';
import { ExtendEditorModal } from '@components/DataSource/components/ExtendEditorModal';
import { DataSourceFormHeader } from '@components/DataSource/components/DataSourceFormHeader';

import { useTranslation } from 'react-i18next';
import { useEdit, useConfirmModal } from '@components/DataSource/store';

import { processorLists } from '@components/DataSource/tools/constants';
import { OpType } from '@components/DataSource/enum';
import { cloneDeep } from 'lodash';

import './index.less';

import type { MenuProps } from 'antd';
import type { IProcessConfig, IProcessConfigData } from '@components/DataSource/types/processing';
import type { IJsonEditorState } from '@components/DataSource/types/jsonEditor';
import type { JSONValue } from '@components/JsonEditor';

function DataViewSourceProcessing() {

  const { dataSource, processors, updateProcessor } = useEdit();
  const { show: showConfirmModal, hide: hideConfirmModal } = useConfirmModal<never>();

  const [emptyActionIdMark, setEmptyActionIdMark] = useState<boolean>(false);
  const [jsonState, setJsonState] = useState<IJsonEditorState & { index?: number, originData?: IProcessConfig }>({
    visible: false,
    data: {},
    index: null,
  });

  const { t } = useTranslation();

  useEffect(() => {
    setEmptyActionIdMark(!dataSource?.json?.actionId);
  }, [dataSource?.json?.actionId]);

  const dropDownMenus: MenuProps['items'] = processorLists.map(process => {
    return {
      key: process.serviceName,
      label: (
        <span className="menu">
          {t(`dj-${process.zhName}`)}
        </span>
      ),
      disabled: emptyActionIdMark && process.serviceName === 'flatService'
    };
  });

  const getProcessZhName = (serviceName: string): string => {
    const zhName = processors.find((item) => item.serviceName === serviceName)?.zhName ?? '';
    return t(`dj-${zhName}`);
  };

  const doAdd: MenuProps['onClick'] = (e) => {
    const processor = processorLists?.find(process => process.serviceName === e.key);
    setJsonState({
      visible: true,
      data: processor?.data as unknown as JSONValue,
      opType: OpType.ADD,
      originData: processor,
    });
  };

  const doEdit = (index: number) => {
    setJsonState({
      visible: true,
      index,
      data: processors[index]?.data as unknown as JSONValue,
      opType: OpType.EDIT,
      originData: processors[index],
    });
  };

  const doDelete = (index: number) => {
    showConfirmModal({
      visible: true,
      content: t('dj-确认删除数据处理？', {
        name: t(`dj-${processors[index].zhName}`)
      }),
      ok: () => {
        doConfirmDelete(index);
        hideConfirmModal();
      },
      close: () => {
        hideConfirmModal();
      }
    });
  };

  const doJsonClose = () => {
    setJsonState({
      visible: false,
      data: {},
    });
  };

  const doJsonOk = (data: IProcessConfigData) => {
    const originData = cloneDeep(jsonState.originData);
    originData.data = data;
    updateProcessor(jsonState.opType, jsonState.index, originData);
    doJsonClose();
  };

  const doConfirmDelete = (data: number) => {
    updateProcessor(OpType.DELETE, data);
  };

  return (
    <div className="data-view-source-pricessing">
      <DataSourceFormHeader>
        {t('dj-数据处理')}
      </DataSourceFormHeader>
      <div className="operation">
        <Dropdown
          placement='bottomLeft'
          overlayClassName='mf-override-dropdown'
          menu={{ items: dropDownMenus, onClick: doAdd }}
        >
          <AppButton type='link'>
            {`+ ${t('dj-添加一项')}`}
          </AppButton>
        </Dropdown>
      </div>
      {
        processors?.length > 0 && (
          <div className="list">
            <Row gutter={[10, 8]}>
              {
                processors.map((process: IProcessConfig, index: number) => {
                  return (
                    <Col
                      span={12}
                      key={index}
                    >
                      <div className="item">
                        <div className="left">
                          <div className="name">{getProcessZhName(process.serviceName)}</div>
                        </div>
                        <div className="right">
                          <Tooltip
                            overlayClassName='mf-override-tooltip'
                            placement='top'
                            title={t('dj-编辑')}
                          >
                            <Icon
                              type='iconjizhikabianji1'
                              className='iconfont'
                              onClick={() => doEdit(index)}
                            />
                          </Tooltip>
                          <Tooltip
                            overlayClassName='mf-override-tooltip'
                            placement='top'
                            title={t('dj-删除')}
                          >
                            <Icon
                              type='iconjizhikashanchu1'
                              className='iconfont'
                              onClick={() => doDelete(index)}
                            />
                          </Tooltip>
                        </div>
                      </div>
                    </Col>
                  );
                })
              }
            </Row>
          </div>
        )
      }
      <ExtendEditorModal
        {...jsonState}
        width='640px'
        ok={doJsonOk}
        close={doJsonClose}
      />
      <Divider />
    </div>
  );
}

export { DataViewSourceProcessing };
