import React, {
  useEffect,
  Fragment,
  forwardRef,
  useImperativeHandle,
  useState,
  useRef,
} from 'react';
import Icon from '@components/Icon';
import { Dropdown, Button, message } from 'antd';
import { DataSourceList } from '@components/DataSource/components/DataSourceList';
import MFH<PERSON> from '@components/MFHOC';
import { SimpleConfirmModal } from '@components/DataSource/components/SimpleConfirmModal';
import { DataSourceEditModal } from '@components/DataSource/components/DataSourceEditModal';
import { LayoutUpdateConfirmModal } from '@components/DataSource/components/LayoutUpdateConfirmModal';

import {
  useDataSource,
  useSaveCallback,
  useSaveEntryProps,
  useEdit,
  useConfirmModal,
} from '@components/DataSource/store';
import { useGetter } from '@components/DataSource/hooks/useGetter';
import { useTranslation } from 'react-i18next';

import '@/common/utils/i18n'; // 引入i18n配置
import {
  transformToDataSourceOrigin,
  rebuildDataSourceInfo,
  preDataSourceSaveCheckDuplicateName,
  preDataSourceSaveCheckNameChange,
} from '@components/DataSource/tools';
import { getInitDataSourceByType } from '@components/DataSource/tools/dataSourceInit';
import {
  MENU_MAP,
  MENU_LIST,
  EDIT_PAGE_MENU_LIST,
  // MENU_LIST_BASIC,
  // MENU_LIST_QUERY_PLAN,
} from '@components/DataSource/tools/constants';
import {
  ChangeType,
  DataSourceChangePageUIElementCodeOPType,
  // DataSourceMode,
  DataSourceType,
  OpType,
} from '@components/DataSource/enum';
import { querySimpleDictionary } from '@components/DataSource/services';

import './index.less';
import '@/assets/mf-override-antd.less';

import type { IDataSourceInfo } from '@components/DataSource/types/dataSource';
import type { ICallbackCollection, IEntryProps } from '@components/DataSource/types';
import type { MenuProps } from 'antd';
// import { listExistQueryPlan } from './tools/dataSourceBasic';
import { ILayoutUpdateConfirmModalProps } from './types/layoutUpdateConfirmModal';

/**
 * 数据源入口
 */
const DataSource = forwardRef((props: IEntryProps & ICallbackCollection, ref) => {
  const {
    visible,
    dataSources,
    options,
    canvasReady = false,
    dataSourcePluginReady = false,
    syncTab,
    changeDataSource,
    updateDataSource,
    resetPageUIElement,
    changePage,
    fieldTreeMap,
    dataSourceNames,
    customDeleteDataSource,
    changePageUIElementCode,
    updateDataSourceNames,
    close,
    needHeader = false,
    position = 'auto',
    modelPageType,
    // updateFieldTreeMap,
    // updateFielTreeNodeInfoList,
  } = props;

  const firstUpdateRef = useRef<boolean>(false);
  const dataSourceStore = useDataSource();
  const { setCallbacks } = useSaveCallback();
  const entryProps = useSaveEntryProps();
  const { t } = useTranslation();
  const { show: showConfirmModal, hide: hideConfirmModal } = useConfirmModal<never>();
  const {
    visible: editVisible,
    dataSource: currentDataSource,
    dataSourceType: currentDataSourceType,
    opType: currentOpType,
    form: basicForm,
    update,
    conditions,
    metadataTreeDatas,
    processors,
    paras,
    metadataInfo,
    dataSourceIndex,
    clear,
  } = useEdit();
  const [layoutUpdateState, setLayoutUpdateState] =
    useState<ILayoutUpdateConfirmModalProps<IDataSourceInfo>>();

  /**
   * 处理下拉框可选数据源类型菜单
   */
  const menuLists: MenuProps['items'] = useGetter(
    (modelPageType: 'design' | 'browse' | 'edit' | 'notModelDriven') => {
      // if (!config) return;
      // const { isShowQueryPlan, customMenuList = [] } = config;
      // let list: DataSourceType[] = customMenuList.length === 0 ? MENU_LIST_BASIC : customMenuList;
      // if (isShowQueryPlan && !listExistQueryPlan(customMenuList)) {
      //   if (sourceLists?.length === 0) {
      //     list = list.concat(MENU_LIST_QUERY_PLAN);
      //   } else if (
      //     sourceLists?.length > 0 &&
      //     sourceLists[0].type_alias === DataSourceType.QUERY_PLAN
      //   ) {
      //     list = MENU_LIST_QUERY_PLAN;
      //   }
      // }
      let menuLists: DataSourceType[];
      const { customMenuList = [] } = options ?? {};
      if (customMenuList.length !== 0) {
        menuLists = customMenuList;
      } else if (['design', 'browse'].includes(modelPageType)) {
        menuLists = MENU_LIST;
      } else {
        menuLists = EDIT_PAGE_MENU_LIST;
      }
      return menuLists.map((value) => {
        return {
          key: value,
          label: t(`dj-${MENU_MAP.get(value as DataSourceType)}`),
        };
      });
    },
    [modelPageType, options?.customMenuList]
  );

  /**
   * 提供获取数据源数据方法
   */
  useImperativeHandle(ref, () => {
    return {
      getDataSourceInfo: () => {
        return transformToDataSourceOrigin(dataSourceStore.dataSourceLists);
      },
    };
  }, [dataSourceStore?.dataSourceLists]);

  // 初始化存储callback
  useEffect(() => {
    setCallbacks({
      syncTab,
      updateDataSource,
      changeDataSource,
      resetPageUIElement,
      changePage,
      customDeleteDataSource,
      changePageUIElementCode,
      updateDataSourceNames,
    });
  }, [
    syncTab,
    updateDataSource,
    changeDataSource,
    resetPageUIElement,
    changePage,
    changePageUIElementCode,
    customDeleteDataSource,
    updateDataSourceNames,
  ]);

  // 初始化存储其余入参
  useEffect(() => {
    entryProps.update({
      visible: props.visible,
      appCode: props.appCode,
      pageCode: props.pageCode,
      applicationCodeProxy: props.applicationCodeProxy,
      basicDataSourceMode: props.basicDataSourceMode,
      queryPlanDataSourceMode: props.queryPlanDataSourceMode,
      interceptDataSourceUpdateOperations: props.interceptDataSourceUpdateOperations,
      options: props.options,
      pageData: props.pageData,
      canvasReady: props.canvasReady,
      dataSourcePluginReady: props.dataSourcePluginReady,
    });
  }, [props]);

  // 根据dataSourceInfo初始化dataSourceLists
  useEffect(() => {
    dataSourceStore.setDataSource(dataSources);
  }, [dataSources]);

  /**
   * 初始化fieldTreeMap
   */
  useEffect(() => {
    dataSourceStore.updateFieldTreeMap(fieldTreeMap);
  }, [fieldTreeMap]);

  /**
   * 初始化dataSourceNames
   */
  useEffect(() => {
    dataSourceStore.updateDataSourceNames(dataSourceNames);
  }, [dataSourceNames]);

  /**
   * 初始化设置默认展示的字段树
   */
  useEffect(() => {
    if (
      !firstUpdateRef.current &&
      dataSourceNames &&
      dataSourceNames.length > 0 &&
      canvasReady &&
      dataSourcePluginReady
    ) {
      dataSourceStore.setDataSourceItemShow(dataSourceNames[0], true);
      firstUpdateRef.current = true;
    }
  }, [dataSourceNames, canvasReady, dataSourcePluginReady]);

  useEffect(() => {
    if (!canvasReady || dataSourcePluginReady) {
      firstUpdateRef.current = false;
    }
  }, [canvasReady, dataSourcePluginReady]);

  // 初始化enumKeyOptions
  useEffect(() => {
    querySimpleDictionary()
      .then((data) => {
        dataSourceStore.updateEnumKeyOptions(data);
      })
      .catch((error) => {
        console.log(
          '🚀 [Generated Log]: path = src/components/DataSource/index.tsx, scope = DataSource.forwardRef.useEffect, error = ',
          error
        );
      });
    return () => {
      console.log(
        '%c 🚀 [Neovim AutoGR Log]: path = src/components/DataSource/index.tsx, scope = DataSource.forwardRef.useEffect, return = ',
        'color: orangered; font-weight: bold;'
      );
    };
  }, []);

  // const isQueryPlan = useGetter(
  //   (isShowQueryPlan: boolean, dataSourceLists: IDataSourceInfo[]) => {
  //     return (
  //       isShowQueryPlan &&
  //       dataSourceLists.length > 0 &&
  //       dataSourceLists[0].type_alias === DataSourceType.QUERY_PLAN
  //     );
  //   },
  //   [entryProps.options?.isShowQueryPlan, dataSourceStore?.dataSourceLists]
  // );

  // const isQueryPlanMultiple = useGetter(
  //   (
  //     isQueryPlan: boolean,
  //     basicDataSourceMode: DataSourceMode,
  //     queryPlanDataSourceMode: DataSourceMode
  //   ) => {
  //     const dataSourceMode = isQueryPlan ? queryPlanDataSourceMode : basicDataSourceMode;
  //     return dataSourceMode === DataSourceMode.MULTIPLE;
  //   },
  //   [isQueryPlan, entryProps.basicDataSourceMode, entryProps.queryPlanDataSourceMode]
  // );

  // 依赖更新信息，往外抛相应的事件
  useEffect(() => {
    if (dataSourceStore?.updateInfo) {
      const { opType, changeType, info, callbackType, extraData } = dataSourceStore.updateInfo;
      const originDataSource = transformToDataSourceOrigin(dataSourceStore.dataSourceLists);
      if (opType === OpType.ADD) {
        changeDataSource?.(originDataSource);
      } else if ([OpType.DELETE, OpType.EDIT].includes(opType)) {
        switch (changeType) {
          case ChangeType.RESET: {
            const params = { from: info.from.actionId, to: info?.to?.actionId, type: callbackType };
            resetPageUIElement(params);
            break;
          }
          case ChangeType.RESERVE: {
            const params = { from: info.from.actionId, to: info?.to?.actionId, type: callbackType };
            changePageUIElementCode(params);
            break;
          }
          default:
            break;
        }
        changeDataSource?.(originDataSource);
        if (extraData?.dataSourceNames) {
          updateDataSourceNames(extraData?.dataSourceNames);
        }
        if (
          opType === OpType.EDIT &&
          (info.from.actionId !== info?.to.actionId || info.from.name !== info?.to.name)
        ) {
          const updateName = info?.to.name;
          updateDataSource(updateName);
        }
      }
    }
  }, [dataSourceStore?.updateInfo, dataSourceStore.dataSourceLists]);

  /**
   * 处理新增
   */
  const doAdd: MenuProps['onClick'] = (e) => {
    const type = e.key as DataSourceType;
    const newDataSourceInfo: IDataSourceInfo = getInitDataSourceByType(type, entryProps.options);
    update({
      visible: true,
      opType: OpType.ADD,
      dataSource: newDataSourceInfo,
      dataSourceType: type,
      title: '',
      dataSourceIndex: null,
      isEdit: false,
      specialChangeInfo: [],
      metadataTreeDatas: [],
      conditions: [],
      processors: [],
      paras: {},
    });
  };

  /**
   * 清理编辑仓库数据
   */
  const doCancelEditDataSource = () => {
    clear();
  };

  /**
   * 关闭修改数据源面板
   */
  const doClosePanel = () => {
    update({
      visible: false,
      isEdit: false,
    });
    if (close) {
      close();
    }
  };

  const updateStoreDataSource = (
    index: number,
    data?: IDataSourceInfo,
    changeType?: ChangeType
  ) => {
    if (
      dataSourceStore.dataSourceLists?.[index]?.json?.name === dataSourceStore.mainDataSourceName &&
      data.json.name !== dataSourceStore.mainDataSourceName
    ) {
      updateDataSourceNames([data.json.name]);
    }
    dataSourceStore.updateDataSource(data, index, changeType);
  };

  /**
   * 布局清理/保留弹窗回调
   */
  const doLayoutUpdateCallback = (_: OpType, data?: IDataSourceInfo, changeType?: ChangeType) => {
    if (changeType) {
      const nameValid = preDataSourceSaveCheckDuplicateName(
        data,
        dataSourceStore.dataSourceLists,
        dataSourceIndex
      );
      if (!nameValid) return;
      updateStoreDataSource(dataSourceIndex, data, changeType);
      message.success(t('dj-保存成功'));
      afterSaveSuccess(data);
      doCancelEditDataSource();
    }
    setLayoutUpdateState({
      visible: false,
    });
  };

  // 处理changePage回调, 固定值数据源特定场景触发
  const afterSaveSuccess = (dataSource: IDataSourceInfo) => {
    if (options?.optimizeMetadata && dataSource.type_alias === DataSourceType.FIXED_VALUE) {
      const pageData = props.pageData ?? {};
      const submitActions = pageData?.submitActions?.map((action) => {
        if (action.type === 'MANUALPROJECT_NEW') {
          const name = dataSource.json?.name;
          const param = {
            name,
            type: 'GET_ACTION_RESPONSE',
            value: name,
          };
          action.actionParams = action.actionParams ?? [];
          const index = action.actionParams.findIndex(
            (param) => param.type === 'GET_ACTION_RESPONSE'
          );
          if (index < 0) {
            action.actionParams.push(param);
          } else {
            action.actionParams[index] = param;
          }
        }
        return action;
      });
      const params = { ...pageData, submitActions };
      changePage(params);
    }
  };

  /**
   * 实际保存方法
   */
  const finalSave = (dataSource: IDataSourceInfo) => {
    const nameValid = preDataSourceSaveCheckDuplicateName(
      dataSource,
      dataSourceStore.dataSourceLists,
      dataSourceIndex
    );
    if (!nameValid) return;
    if (currentOpType === OpType.ADD) {
      dataSourceStore.addDataSource(dataSource);
      if (!dataSourceStore.mainDataSourceName) {
        updateDataSourceNames([dataSource.json.name]);
      }
    } else if (currentOpType === OpType.EDIT) {
      updateStoreDataSource(dataSourceIndex, dataSource);
    }
    message.success(t('dj-保存成功'));
    afterSaveSuccess(dataSource);
    doCancelEditDataSource();
  };

  /**
   * 保存回调
   */
  const doSave = async () => {
    try {
      const formData = await basicForm.validateFields();
      if (formData) {
        const newDataSourceInfo: IDataSourceInfo = rebuildDataSourceInfo(
          {
            formData,
            dataSource: currentDataSource,
            conditions,
            processors,
            metadataTreeDatas,
            paras,
            metadataInfo,
          },
          currentDataSourceType,
          entryProps.options
        );
        /**
         * 否则直走正常校验
         */
        const valid = preDataSourceSaveCheckNameChange(
          newDataSourceInfo,
          dataSourceStore.dataSourceLists,
          dataSourceIndex
        );
        if (valid) {
          finalSave(newDataSourceInfo);
          return;
        }
        /**
         * 编辑&存在拦截 打开保留/清理弹窗
         */
        if (entryProps.interceptDataSourceUpdateOperations) {
          const info: ILayoutUpdateConfirmModalProps<IDataSourceInfo> = {
            visible: true,
            opType: OpType.EDIT,
            changeType: DataSourceChangePageUIElementCodeOPType.UPDATE,
            // isQueryPlanMultiple,
            // fieldSourceMode: entryProps.fieldSourceMode,
            data: newDataSourceInfo,
          };
          setLayoutUpdateState(info);
        } else {
          showConfirmModal({
            visible: true,
            content: t('dj-更新操作确认tips'),
            ok: () => {
              hideConfirmModal();
              finalSave(newDataSourceInfo);
            },
            close: () => {
              hideConfirmModal();
            },
          });
        }
      }
    } catch (error) {
      console.error('表单校验失败：', error);
    }
  };

  return (
    <Fragment>
      {visible && (
        <div className={'data-source-wrapper mf-override-base ' + `position-${position}`}>
          <div className="data-source-info">
            {needHeader && (
              <div className="data-source-info-header">
                <span className="data-source-info-title">{t('dj-数据源')}</span>
                <span className="data-source-info-icon">
                  <Icon
                    className="closeIcon"
                    type="icondanchuxiaoxiguanbi"
                    onClick={doClosePanel}
                  />
                </span>
              </div>
            )}
            <div className="data-source-add">
              {options?.onlyOne && dataSourceStore.dataSourceLists?.length > 0 ? (
                <Button type="primary" className="disabled" disabled>
                  {t('dj-添加')}
                </Button>
              ) : (
                <Dropdown
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                  overlayClassName="mf-override-dropdown"
                  placement="bottomLeft"
                  menu={{ items: menuLists, onClick: doAdd }}
                >
                  <Button type="primary" className="add-button">
                    {t('dj-添加')}
                  </Button>
                </Dropdown>
              )}
              {/* {entryProps.options?.isShowQueryPlan && ( */}
              {/*   <Tooltip */}
              {/*     placement="top" */}
              {/*     overlayClassName="mf-override-tooltip" */}
              {/*     title={t('dj-查询方案不可与其他类型数据源同时存在')} */}
              {/*   > */}
              {/*     <Icon type="iconexplain" className="question-icon" /> */}
              {/*   </Tooltip> */}
              {/* )} */}
            </div>
            <div className="data-source-list">
              <DataSourceList />
            </div>
          </div>
        </div>
      )}
      <SimpleConfirmModal />
      {editVisible && <DataSourceEditModal ok={doSave} cancel={doCancelEditDataSource} />}
      <LayoutUpdateConfirmModal<IDataSourceInfo>
        {...layoutUpdateState}
        callback={doLayoutUpdateCallback}
      />
    </Fragment>
  );
});

export { DataSource };
export default MFHOC<IEntryProps & ICallbackCollection>(DataSource);
