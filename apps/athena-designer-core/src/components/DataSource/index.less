.data-source-wrapper.position-absolute {
  position: absolute;
  top: 0;
  left: 38px;
  z-index: 4;
}
.data-source-wrapper.position-auto > .data-source-info {
  width: 100%;
}
.data-source-wrapper {
  display: flex;
  flex-direction: row;
  height: 100%;

  .data-source-info {
    width: 232px;
    height: 100%;
    padding: 10px 10px;
    background: white;
    box-shadow: 4px 0 4px 0 rgba(233, 233, 233, 0.5);

    .data-source-info-header {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;

      .data-source-info-title {
        font-size: 16px;
        color: #333333;
      }

      .data-source-info-icon {
        .iconfont {
          font-size: 14px;
          margin-right: 6px;
        }

        .closeIcon {
          font-size: 12px;
          color: #666666;
        }
      }
    }

    .data-source-add {
      padding-bottom: 10px;
      border-bottom: 1px solid #eeeeee;
      .question-icon {
        margin-left: 8px;
      }
      .disabled {
        cursor: not-allowed;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        background: rgba(0, 0, 0, 0.04);
        box-shadow: none;
        background-color: rgb(204, 204, 204) !important;
        &:hover {
          color: rgba(0, 0, 0, 0.25);
          border-color: #d9d9d9;
          background: #f5f5f5;
          text-shadow: none;
          box-shadow: none;
          background-color: rgb(204, 204, 204) !important;
        }
      }
    }

    .data-source-list {
      padding: 10px 0;
      height: calc(100% - 39px);
      overflow-y: scroll;
    }
    .data-source-list::-webkit-scrollbar {
      display: none;
    }
  }

  .data-source-detail {
    width: 418px;
    height: 100%;
    background: white;
    box-shadow: 4px 0 4px 0 rgba(233, 233, 233, 0.5);
    border-left: 1px solid rgba(233, 233, 233, 0.5);

    .data-source-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 16px;
      margin-bottom: 10px;
      background-color: #fbfbfb;

      .title {
        font-size: 14px;
        color: #7f7f7f;
        font-weight: bold;
        width: 180px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .save-button {
        margin-right: 10px;
      }
    }

    .data-source-components {
      height: calc(100% - 58px);
      overflow-y: scroll;
    }
  }
}

::ng-deep .data-source-menu-text {
  font-size: 13px;
}

::ng-deep .switch-source-modal {
  .modal-footer {
    text-align: center;
    padding-top: 10px;

    button {
      margin: 0 11px;
    }
  }
}
