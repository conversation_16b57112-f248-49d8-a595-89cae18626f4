import React, { useState, useMemo, useEffect, useRef, ChangeEvent } from 'react';
import ReactDOMServer from 'react-dom/server';
import { Modal, Row, Col, Input, Spin, Radio, Pagination } from 'antd';
import Icon from '@components/Icon';
import { AgGridReact } from 'ag-grid-react';
import EmptyTable from '@/pages/DataCenter/pages/VocabularyDictionary/components/EmptyTable';
import { CenterModelFooter } from '@components/DataSource/components/CenterModelFooter';

import { defaultColDef } from '@components/DataViewModal/tools';
import { useTranslation } from 'react-i18next';
import { queryQueryPlanList } from '@components/DataViewModal/services';

import '@components/DataViewModal/index.less';
import '@/assets/mf-override-antd.less';

import type {
  IDataViewModalProps,
  IDataViewPaginationState,
  IQueryPlanData,
  IQueryPlanListResponse,
} from '@components/DataViewModal/types';
import type { ColDef, ICellRendererParams } from 'ag-grid-community';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

function DataViewModal(props: IDataViewModalProps) {
  const { visible, appCode, dataViewQuery, cancel, confirm } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const [dataViewCode, setDataViewCode] = useState<string>('');
  const [response, setResponse] = useState<IQueryPlanListResponse>(null);

  const gridRef = useRef<AgGridReact>(null);

  const [paginationState, setPaginationState] = useState<IDataViewPaginationState>({
    keyWord: '',
    pageIndex: 1,
    pageSize: 10,
    total: 0,
  });

  const { t } = useTranslation();

  useEffect(() => {
    if (!visible) return;
    setDataViewCode(dataViewQuery?.code);
  }, [visible, dataViewQuery]);

  useEffect(() => {
    if (!visible) return;
    doSearch();
  }, [visible, paginationState]);

  const colDefs: ColDef[] = useMemo(() => {
    return [
      {
        headerCheckboxSelection: false,
        width: 70,
        cellClass: ['ag-cell-custom', 'ag-cell-custom-center'],
        cellRenderer: (params: ICellRendererParams<IQueryPlanData>) => {
          return (
            <Radio
              checked={params.node.isSelected()}
              onChange={(event) => handleRowCheck(event, params)}
            />
          );
        },
      },
      {
        field: 'name',
        width: 160,
        headerName: t('dj-查询方案'),
        tooltipField: 'name',
      },
      {
        field: 'type',
        width: 80,
        headerName: t('dj-类型'),
        tooltipField: 'type',
      },
      {
        field: 'description',
        width: 280,
        headerName: t('dj-描述'),
        tooltipField: 'description',
      },
    ];
  }, [dataViewCode]);

  // 表格行选择
  const handleRowCheck = (event: CheckboxChangeEvent, record: ICellRendererParams) => {
    gridRef.current.api.deselectAll();
    record.node.setSelected(event.target.checked);
  };

  // 表格的选中发生变化时的处理
  const handleSelectionChanged = (event) => {
    const gridSelectedRows = event.api.getSelectedRows();
    if (gridSelectedRows?.length > 0) {
      setDataViewCode(gridSelectedRows?.[0]?.code);
    }
  };

  // 表格数据发生变化时 控制选中
  const handleRowDataUpdated = () => {
    gridRef?.current?.api.forEachNode((node) => {
      node.setSelected(node.data.code === dataViewCode);
      gridRef.current.api.refreshCells({
        rowNodes: [node],
      });
    });
  };

  const doKeyWordChange = (event: ChangeEvent<HTMLInputElement>) => {
    setPaginationState({
      ...paginationState,
      keyWord: event.target?.value,
    });
  };

  const doPagination = (page: number, pageSize: number) => {
    setPaginationState({
      ...paginationState,
      pageIndex: page,
      pageSize,
    });
  };

  const doSearch = async () => {
    setLoading(true);
    try {
      const data = await queryQueryPlanList({
        keyWord: paginationState.keyWord,
        pageIndex: paginationState.pageIndex,
        pageSize: paginationState.pageSize,
        application: appCode,
      });
      if (data) {
        setResponse(data);
      }
    } finally {
      setLoading(false);
    }
  };

  const doConfirm = () => {
    if (!dataViewCode) return;
    const dataView = response.data.find((dataView) => dataView.code === dataViewCode);
    confirm({ code: dataView.code, type: dataView.type });
  };

  return (
    <Modal
      classNames={{
        body: 'mf-override-base data-view-modal',
      }}
      open={visible}
      footer={<CenterModelFooter onSure={doConfirm} onCancel={cancel} />}
      width={610}
      closable
      title={t('dj-选择关联查询方案')}
      destroyOnClose
      onCancel={cancel}
    >
      <Row gutter={24}>
        <Col span={16} className="search">
          <Input
            placeholder={t('dj-请输入关键字')}
            value={paginationState.keyWord}
            onChange={doKeyWordChange}
            suffix={<Icon type="search" className="iconfont" onClick={doSearch} />}
          />
        </Col>
      </Row>
      <Spin spinning={loading}>
        <div>
          <AgGridReact<IQueryPlanData>
            ref={gridRef}
            defaultColDef={defaultColDef}
            rowData={response?.data}
            rowHeight={38}
            columnDefs={colDefs}
            gridOptions={{
              enableCellTextSelection: true,
              overlayNoRowsTemplate: ReactDOMServer.renderToString(<EmptyTable />),
            }}
            headerHeight={38}
            suppressCellFocus={true}
            suppressRowClickSelection={true}
            rowSelection={'multiple'}
            onSelectionChanged={handleSelectionChanged}
            onRowDataUpdated={handleRowDataUpdated}
          />
        </div>
        <div className="paginationWrapper">
          {!dataViewCode ? (
            <div className="currentSelectedWrapper">
              <span className="error">{t('dj-请选择一项数据')}</span>
            </div>
          ) : (
            <div />
          )}
          <Pagination
            size="small"
            current={paginationState.pageIndex}
            pageSize={paginationState.pageSize}
            total={response?.total ?? 0}
            onChange={doPagination}
            showSizeChanger={true}
          />
        </div>
      </Spin>
    </Modal>
  );
}

export { DataViewModal };
