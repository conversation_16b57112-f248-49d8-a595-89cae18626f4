/**
 * 子页面信息
 */
export interface ISubPageDefineInfo {
  /**
   * 子页面页面code
   */
  pageCode?: string;
  /**
   * 子页面查询code
   */
  code?: string;
  subPageCode?: string;
}

export interface ISubpageSelectorProps {
  /**
   * 作业code
   */
  code: string;
  /**
   * 应用code
   */
  appCode: string;
  value?: ISubPageDefineInfo;
  onChange: (data: ISubPageDefineInfo) => void;
}

export interface IQueryParams {
  /**
   * 应用code
   */
  application: string;
  /**
   * 作业code
   */
  parentPageCode: string;
}

export interface ISubPageListItem {
  name: string;
  code: string;
  pageCode: string;
}
