import React, { useEffect, useState } from 'react';
import { Select, Empty } from 'antd';
import { useTranslation } from 'react-i18next';
import { queryList } from './service';

import '@/assets/mf-override-antd.less';
import './index.less';

import type { ISubPageListItem, ISubpageSelectorProps } from './types';

function SubpageSelector(props: ISubpageSelectorProps) {
  const { value, appCode, code, onChange } = props;
  const [options, setOptions] = useState<ISubPageListItem[]>([]);
  const { t } = useTranslation();

  const querySubpageList = async (cAppCode: string, cCode: string) => {
    const data = await queryList({ application: cAppCode, parentPageCode: cCode });
    setOptions(
      data?.map((info) => ({ ...info, name: info.name ?? info?.lang?.name?.['zh_CN'] })) ?? []
    );
  };

  const doChange = (code: string) => {
    onChange({
      pageCode: 'sub-page',
      code,
      subPageCode: code,
    });
  };

  useEffect(() => {
    if (appCode && code) {
      querySubpageList(appCode, code);
    }
  }, [appCode, code]);

  return (
    <div className="mf-override-base subpage-selector-wrapper">
      <Select
        placeholder={t('dj-请选择')}
        size="small"
        fieldNames={{ label: 'name', value: 'code' }}
        notFoundContent={
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={t('dj-暂无数据')} />
        }
        value={value?.code}
        options={options}
        onChange={doChange}
      />
    </div>
  );
}

export { SubpageSelector };
