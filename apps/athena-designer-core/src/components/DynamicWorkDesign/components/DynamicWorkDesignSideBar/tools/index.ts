import { DataSourceType } from '@/components/DataSource/enum';
import { getType<PERSON><PERSON>s, transformToDataSourceList } from '@components/DataSource/tools';
import { v4 as uuid } from 'uuid';

import { FieldSourceModeEnum, FieldDataModeEnum } from '@components/DynamicWorkDesign/config/type';
import {
  queryActionFieldData,
  queryActionFieldModalData,
  queryGenerateDefaultLayoutByDatasource,
} from '@components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/services';
import { DataType } from '@athena-designer-editor/src/plugins/plugin-ath-field-panel/type';

import type {
  IBatchFieldsQueryParmas,
  IDataSourceDetailInfo,
  IDataSourceOrigin,
} from '@/components/DataSource/types/dataSource';
import type { IFieldSetInfo } from '@components/DataSource/types';
import type {
  Category,
  IFieldTreeNodeInfo,
  IFieldTreeNodeOldInfo,
  PageCode,
  SiderbarConfig,
} from '@/components/DynamicWorkDesign/config/type';
import type { IMetaDataFieldData } from '@/components/DataSource/types/metadataField';
import type {
  IBackendDefaultLayoutInfo,
  IQueryGenerateBackendDefaultLayoutParams,
  IQueryGenerateBackendDefaultLayoutResponse,
  SBoolean,
} from '@/components/DataSource/types/common';
import type { AthTreeDataNode } from '../../DynamicWorkDesignContent/type';
import { batchQueryFieldsInModel } from '@/components/DataSource/services';

export interface IQueryActionParams {
  masterFromDataSourceName?: boolean;
  dataSourceName?: string;
}

// 词库字段, copy自主应用源码
export const taskProcessFields = [
  {
    data_type: 'string',
    data_name: 'activity__performerName',
    description: {
      zh_CN: '任务执行人',
      zh_TW: '任務執行人',
      en_US: 'Task Executor',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__backLog__data',
    description: {
      zh_CN: '任务进展状态',
      zh_TW: '任務進展狀態',
      en_US: 'Task progress status',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__planEndTime',
    description: {
      zh_CN: '任务预计完成日',
      zh_TW: '任務預計完成日',
      en_US: 'Task expected completion date',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__approval__state',
    description: {
      zh_CN: '签核状态',
      zh_TW: '簽核狀態',
      en_US: 'Sign-off status',
    },
  },
  {
    data_type: 'string',
    data_name: 'activity__data__status',
    description: {
      zh_CN: '流转状态',
      zh_TW: '流轉狀態',
      en_US: 'Circulation status',
    },
  },
  {
    data_type: 'datetime',
    data_name: 'activity__startTime',
    description: {
      zh_CN: '任务实际开始时间',
      zh_TW: '任務實際開始時間',
      en_US: 'The actual start time of the task',
    },
  },
  {
    data_type: 'datetime',
    data_name: 'activity__endTime',
    description: {
      zh_CN: '任务实际结束时间',
      zh_TW: '任務實際結束時間',
      en_US: 'The actual end time of the task',
    },
  },
];

/**
 * 获取初始化dataSourceName, copy自主应用源码
 */
export function getInitDataSourceName(dataSources: IDataSourceOrigin): string | null {
  if (!dataSources) return null;
  const keys = Object.keys(dataSources);
  if (keys.length === 0) return null;
  const isQueryPlan = getTypeAlias(dataSources[keys[0]]) === DataSourceType.QUERY_PLAN;
  if (isQueryPlan) {
    const defaultKey = keys.find((key) => dataSources[key]?.dataViewQuery?.isDefault);
    if (defaultKey) {
      return dataSources[defaultKey].name ?? defaultKey;
    }
  }
  const dataSourceLists = transformToDataSourceList(dataSources);
  if (dataSourceLists.length === 0) return null;
  return dataSourceLists[0].json.name;
}

// 提前处理无action时的metadataFields, 处理反转字段, 将subFields全部改为field统一, copy自主应用源码
export function rebuildMetadata(data: any[]): any {
  return data.map((s) => {
    const {
      name: data_name,
      array: is_array,
      dataType: data_type,
      path,
      lang = {},
      subFields = [],
    } = s;
    const field = subFields.length > 0 ? rebuildMetadata(subFields) : [];
    return {
      ...s,
      data_name,
      is_array,
      data_type,
      fullPath: `${path ? path + '.' : ''}${data_name}`,
      description: lang.description || {},
      field,
    };
  });
}

// 工具方法：组合词库处理器默认字段, copy自主应用源码
export function combineProcessFields(
  fields: IFieldSetInfo[],
  originDataSource: IDataSourceDetailInfo,
  inquireWordLibrary: boolean,
  data?: any
): IFieldSetInfo[] {
  let newFields: IFieldSetInfo[] = fields?.length > 0 ? [...fields] : [];
  if (inquireWordLibrary) {
    const { dataProcessors = [] } = originDataSource;
    if (
      dataProcessors.some(
        (d) => d.serviceName === 'atmcDataService' || d.serviceName === 'atmcBatchDataService'
      )
    ) {
      const extraFields: IFieldSetInfo[] = taskProcessFields.map((s) => {
        return {
          ...s,
          fullPath: `${data.data_name}.${s.data_name}`,
        };
      });
      newFields = newFields.concat(extraFields);
    }
  }
  return newFields;
}

/**
 * 处理元数据
 */
export function handleMeta(
  metadataFields: IMetaDataFieldData[],
  dataSource: IDataSourceDetailInfo,
  inquireWordLibrary: boolean
): IFieldSetInfo | null {
  if (metadataFields.length > 0) {
    const { field = [] } = metadataFields[0];
    const data: any = {
      ...metadataFields[0],
      field: combineProcessFields.call(
        this,
        field,
        dataSource,
        inquireWordLibrary,
        metadataFields[0]
      ),
    };
    return data;
  } else {
    return null;
  }
}

export async function querySourceFieldWithAction(
  appCode: string,
  code: string,
  inquireWordLibrary: boolean,
  actionId: string,
  originDataSource: IDataSourceDetailInfo,
  config: Partial<SiderbarConfig>,
  dataSourceName: string,
  masterFromDataSourceName?: boolean
) {
  let param = `?actionId=${actionId}&sourceType=${originDataSource.type}`;
  const upProcessNames = (originDataSource?.dataProcessors ?? [])
    .filter((s) => s.serviceName === 'flatService' && (!s.applyTo || s.applyTo === appCode))
    .map((s) => s.paras?.flatData)
    .join(',');
  if (upProcessNames) {
    param = `${param}&upProcessNames=${upProcessNames}`;
  }

  if (config?.isQueryEspActionFieldsInModel) {
    param = `${param}&code=${code}`;
  }

  if (masterFromDataSourceName === true) {
    param = `${param}&dataSourceName=${dataSourceName}`;
  }

  const queryFN = config?.isQueryEspActionFieldsInModel
    ? queryActionFieldModalData
    : queryActionFieldData;
  const resData: any = await queryFN(param);
  return {
    ...(resData || {}),
    field: combineProcessFields(resData?.field, originDataSource, inquireWordLibrary, resData),
  };
}

// 数据源无actionId时, 讲字段中的path全部提一层
export function rebuildPath(data: any[], parent: any): any {
  return data.map((s) => {
    const { path = '', fullPath = '', field: child = [] } = s;
    const field = child.length > 0 ? rebuildPath(child, parent) : [];
    return {
      ...s,
      path: `${parent}${path ? '.' + path : ''}`,
      fullPath: `${parent}${fullPath ? '.' + fullPath : ''}`,
      field,
    };
  });
}

/**
 * 存储data_name的唯一id
 */
let reflectId = {};

/**
 * 拉平数据
 * flag: 子级数据名
 * parentId: 父级id
 */
export function flatTargetArray(origin = [], flag = 'field', parentId: any): any {
  const final = [];
  origin.forEach((d) => {
    const _parentId = parentId ? parentId : null;
    if (!reflectId[d.fullPath]) {
      reflectId[d.fullPath] = `${uuid()}_${d.fullPath}`;
    }
    final.push({
      ...d,
      _parentId,
      _frontId: reflectId[d.fullPath],
    });
    if (d[flag]?.length > 0) {
      const extra = flatTargetArray(d[flag], flag, reflectId[d.fullPath]);
      final.push(...extra);
    }
  });
  return final;
}

/**
 * 递归数据
 */
export const generateFields = (params: any): any => {
  const result = [];
  for (const param of params) {
    if (param._parentId === null || !param._parentId) {
      ['field', 'subFields'].forEach((s) => {
        if (Reflect.has(param, s)) {
          Reflect.deleteProperty(param, s);
        }
      });
      // 判断是否为顶层节点
      param['field'] = getChildren(param['_frontId'], params); // 获取子节点
      result.push(param);
    }
  }
  return result[0];
};

/**
 * 获取子类数据
 */
export const getChildren = (_frontId: any, array: any): any => {
  const child = [];
  for (const arr of array) {
    // 循环获取子节点
    if (arr._parentId === _frontId) {
      ['field', 'subFields'].forEach((s) => {
        if (Reflect.has(arr, s)) {
          Reflect.deleteProperty(arr, s);
        }
      });
      child.push(arr);
    }
  }
  for (const c of child) {
    // 获取子节点的子节点
    const currentChild = getChildren(c['_frontId'], array);
    if (currentChild.length > 0) {
      c['field'] = currentChild;
    }
  }
  return child;
};

/**
 * 拉平替换数据, covers(元数据)不再具有第一层, 全部从M以下设置, 即直接加入M的子级
 */
export function flatMetaData(origin: any, covers = []): any {
  if (covers.length === 0) {
    return origin;
  }
  if ((!origin['field'] || origin['field']?.length === 0) && covers.length > 0) {
    return { ...origin, field: covers };
  }
  reflectId = {};
  const originData = flatTargetArray([origin], 'field', null);
  const flatMatch = (data = [], parentId?: any): any => {
    data.forEach((d) => {
      const _parentId = parentId ? parentId : null;
      if (!reflectId[d.fullPath]) {
        reflectId[d.fullPath] = `${uuid()}_${d.fullPath}`;
      }
      const index = originData.findIndex((s) => s['fullPath'] === d['fullPath']);
      if (index !== -1) {
        Object.assign(originData[index], {
          ...d,
          _parentId,
          _frontId: reflectId[d.fullPath],
        });
      } else {
        originData.push({
          ...d,
          _parentId,
          _frontId: reflectId[d.fullPath],
        });
      }
      if (d['field']?.length > 0) {
        flatMatch(d['field'], reflectId[d.fullPath]);
      }
    });
  };
  flatMatch(covers, reflectId[origin.data_name]);
  return generateFields(originData);
}

export async function buildFieldTreeMap({
  dataSources,
  inquireWordLibrary,
  code,
  businessCode,
  config,
}: {
  dataSources: IDataSourceOrigin;
  inquireWordLibrary: boolean;
  code: string;
  config: Partial<SiderbarConfig>;
  businessCode?: string;
}): Promise<Map<string, AthTreeDataNode[]>> {
  return new Promise((resolve) => {
    const fieldTreeMap: Map<string, AthTreeDataNode[]> = new Map();
    const queryParams: IBatchFieldsQueryParmas = { actionBasicList: [], businessCode };
    Object.keys(dataSources).forEach((dataSourceName) => {
      const dataSourceInfo = dataSources[dataSourceName];
      if (!dataSourceInfo.actionId) {
        const { metadataFields: metadata = [] } = dataSourceInfo;
        const metadataFields = rebuildMetadata(metadata); // 转变字段key名
        const fieldSet = handleMeta(metadataFields, dataSourceInfo, inquireWordLibrary);
        const fieldTree = formatField(
          fieldSet,
          config?.fieldSourceMode,
          dataSources,
          dataSourceName
        );
        fieldTreeMap.set(dataSourceName, fieldTree);
      } else {
        queryParams.actionBasicList.push({
          actionId: dataSourceInfo?.actionId,
          sourceType: dataSourceInfo?.type,
          code,
          dataSourceName,
        });
      }
    });

    batchQueryFieldsInModel(queryParams)
      .then((res) => {
        if (res) {
          Object.keys(dataSources).forEach((dataSourceName: string) => {
            const dataSourceInfo: IDataSourceDetailInfo = dataSources[dataSourceName];
            const actionId = dataSourceInfo.actionId;
            if (actionId) {
              const { metadataFields: metadata = [] } = dataSourceInfo;
              const metadataFields = rebuildMetadata(metadata); // 转变字段key名
              const fieldTreeRes = res[actionId];
              /**
               * WARN: 这边的理由是，接口返回的数据结构key是actionId,这就带来一个问题，就是当两支数据源actionId一样时，后端返回的字段树是一个key-value对
               * 而字段树的值中第一层的data_name和fullPath=dataSourceName,就会造成其中一个数据源的字段树它的data_name和fullPath不对
               * 这边和接口确认了，这两个字段就是dataSourceName，所以前端做一下处理，后端就不改了
               * 所以这边遍历的层级也就做了优化，因为dataSources的数量会大于等于接口的返回值,而不一定是等于
               */
              fieldTreeRes.data_name = dataSourceName;
              fieldTreeRes.fullPath = dataSourceName;
              const buildMeta = rebuildPath(metadataFields, fieldTreeRes.data_name);
              const fieldSet = flatMetaData(fieldTreeRes, buildMeta);
              const fieldTree = formatField(
                fieldSet,
                config?.fieldSourceMode,
                dataSources,
                dataSourceName
              );
              fieldTreeMap.set(dataSourceName, fieldTree);
            }
          });
        }
      })
      .catch((error) => {
        console.log(
          '🚀 [Generated Log]: path = src/components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/hooks/useFieldTree.ts, scope = useFieldTree.useEffect, error = ',
          error
        );
      })
      .finally(() => {
        resolve(fieldTreeMap);
      });
  });
}

/**
 * 数据源模式
 */
export async function querySourceField(
  appCode: string,
  code: string,
  inquireWordLibrary: boolean,
  config: Partial<SiderbarConfig>,
  dataSources: IDataSourceOrigin,
  dataSourceName: string,
  masterFromDataSourceName?: boolean
): Promise<IFieldSetInfo | null> {
  const dataSource = dataSources[dataSourceName];
  if (!dataSource) return null;
  const { actionId, metadataFields: metadata = [] } = dataSource;
  const metadataFields = rebuildMetadata(metadata); // 转变字段key名
  // 原来这边有数据源类型的判断， 但是并没有发现Mix类型数据源的使用，所以直接舍弃了，如果后续有了这边需要增加类型判断
  if (!actionId) {
    return handleMeta(metadataFields, dataSource, inquireWordLibrary);
  }
  const res = await querySourceFieldWithAction(
    appCode,
    code,
    inquireWordLibrary,
    actionId,
    dataSource,
    config,
    dataSourceName,
    masterFromDataSourceName
  );
  const buildMeta = rebuildPath(metadataFields, res.data_name);
  return flatMetaData(res, buildMeta);
}

/**
 * Action模式本次接入数据源未设计，只保留一个站位
 */
export function queryActionField(): IFieldSetInfo {
  return null;
}

/**
 * 获取字段数据
 * @param {string} appCode - 应用code
 * @param {string} code - 作业code
 */
export async function getFieldSet(
  appCode: string,
  code: string,
  inquireWordLibrary: boolean,
  dataSources: IDataSourceOrigin,
  dataSourceName: string,
  config: Partial<SiderbarConfig>,
  masterFromDataSourceName?: boolean
): Promise<{ dataSourceName: string; fieldTreeRoot: IFieldSetInfo | null }> {
  const { fieldDataMode, fieldSourceMode } = config ?? {};
  if (
    fieldSourceMode === FieldSourceModeEnum.DataSource &&
    fieldDataMode === FieldDataModeEnum.ActionResponse
  ) {
    const fieldTreeRoot = await querySourceField(
      appCode,
      code,
      inquireWordLibrary,
      config,
      dataSources,
      dataSourceName,
      masterFromDataSourceName
    );
    return {
      dataSourceName,
      fieldTreeRoot,
    };
  } else if (
    fieldSourceMode === FieldSourceModeEnum.Action &&
    fieldDataMode === FieldDataModeEnum.ActionRequest
  ) {
    const fieldTreeRoot = queryActionField();
    return {
      dataSourceName,
      fieldTreeRoot,
    };
  }
  return null;
}

export function transferSBoolToBoolean(value: SBoolean): boolean {
  return [true, 'true'].includes(value);
}

/**
 * 格式化fieldData 到 fieldTree
 */
export function formatFieldDataToTree(
  datas: IFieldSetInfo[],
  parent: string,
  root: string,
  target: string,
  parentCategory: string,
  isFieldSourceMode: boolean
): AthTreeDataNode[] {
  if (!datas || datas.length === 0) return [];
  return datas.map((data: IFieldSetInfo) => {
    const currentTarget = target ? `${target}.${parent}` : parent;
    const fullPath = !target
      ? `${root}.${data.data_name}`
      : `${root}.${currentTarget}.${data.data_name}`;
    const node: AthTreeDataNode = {
      title: data.data_name,
      data_name: data.data_name,
      data_type: data.data_type as DataType,
      description: data.description,
      is_array: transferSBoolToBoolean(data.is_array),
      is_businesskey: transferSBoolToBoolean(data.is_businesskey),
      is_datakey: transferSBoolToBoolean(data.is_datakey),
      category: isFieldSourceMode ? data.data_type : data.is_array ? 'array' : data.data_type,
      parentId: parent,
      target: currentTarget,
      fullPath: data.fullPath ? data.fullPath : fullPath,
      root,
      parentCategory,
      path: currentTarget,
      dbFieldType: data.dbFieldType,
    };
    if (data.field?.length > 0) {
      node.children = formatFieldDataToTree(
        data.field,
        data.data_name,
        root,
        currentTarget,
        node.category,
        isFieldSourceMode
      );
      node.expanded = true;
    } else {
      node.isLeaf = true;
    }
    return node;
  });
}

/**
 * 将字段源数据对象（obj）转成前端使用标准的树结构（list）
 * @param data 字段源数据 (名称: 'data_name', 类型: 'data_type')
 * @param fieldMode 字段模式
 * @param dataSources 数据源对象
 * @param dataSourceName [FieldSourceModeEnum.Multiple，FieldSourceModeEnum.Single].includes(fieldMode) 时，当前的数据源名称
 * @param actionId FieldSourceModeEnum.Action === fieldMode 时，当前的API
 * @returns 树标准字段（list）
 */
export function formatField(
  data: IFieldSetInfo,
  fieldSourceMode: FieldSourceModeEnum,
  dataSources: IDataSourceOrigin,
  dataSourceName: string,
  actionId?: string
): AthTreeDataNode[] {
  if (!data) {
    return [];
  }
  const dataSource = dataSources[dataSourceName];
  const notArray = ['true', true].includes(dataSource?.notArray);
  const isFieldSourceMode = FieldSourceModeEnum.Action === fieldSourceMode;
  const category = isFieldSourceMode
    ? data.data_type
    : data.is_array && !notArray
      ? 'array'
      : data.data_type;
  const treeData: AthTreeDataNode[] = [
    {
      title: data.data_name,
      data_name: data.data_name,
      data_type: data.data_type as DataType,
      description: data.description,
      is_array: transferSBoolToBoolean(data.is_array),
      is_businesskey: transferSBoolToBoolean(data.is_businesskey),
      is_datakey: transferSBoolToBoolean(data.is_datakey),
      category,
      target: '',
      fullPath: data.fullPath ?? data.data_name,
      userTarget: isFieldSourceMode ? actionId : dataSourceName,
      expanded: true,
      parentCategory: '',
      path: '',
      children: formatFieldDataToTree(
        data.field,
        isFieldSourceMode ? '' : data.data_name,
        data.data_name,
        '',
        category,
        isFieldSourceMode
      ),
    },
  ];
  return treeData;
}

export function transferNewFiledTreeToOld(
  treeData?: IFieldTreeNodeInfo[]
): IFieldTreeNodeOldInfo[] {
  if (!treeData || treeData.length === 0) return [];

  const format = (treeData: IFieldTreeNodeInfo[]): IFieldTreeNodeOldInfo[] => {
    return treeData.map((item) => {
      const { children, ...rest } = item;
      const newChild = children && children.length > 0 ? format(children) : [];
      return {
        ...rest,
        field: newChild,
        children: newChild,
      };
    });
  };

  return format(treeData);
}

/**
 * TODO: 查询后端默认生成逻辑，待定
 */
export async function fetchBackendDefaultLayout({
  application,
  pageCode,
  category,
  pageDesignCode,
  dataSources = {},
}: {
  application: string;
  pageCode: PageCode;
  pageDesignCode: string;
  category: Category;
  dataSources?: IDataSourceOrigin;
}): Promise<IBackendDefaultLayoutInfo | null> {
  if (!dataSources) return null;
  const params: IQueryGenerateBackendDefaultLayoutParams = {
    application,
    pageCode,
    category,
    pageDesignCode,
    dataSourceList: [],
  };
  Object.keys(dataSources).forEach((dataSourceName: string) => {
    const dataSourceInfo: IDataSourceDetailInfo = dataSources[dataSourceName];
    // 其它类型数据源会没有type,这种数据源没必要查了，查了反而会报错
    if (dataSourceInfo.viewCode !== 'index' && !!dataSourceInfo.type) {
      params.dataSourceList.push({
        dataSourceName,
        tmQueryAction: {
          actionId: dataSourceInfo?.actionId,
          type: dataSourceInfo?.type,
        },
      });
    }
  });
  if (params.dataSourceList.length === 0) return null;
  const elements: IQueryGenerateBackendDefaultLayoutResponse[] =
    await queryGenerateDefaultLayoutByDatasource(params);
  if (!elements || elements.length === 0) return null;
  const backendDefaultLayoutInfo: IBackendDefaultLayoutInfo = {};
  elements.forEach(({ dataSourceName, elements }) => {
    const { layout = [] } = elements ?? {};
    backendDefaultLayoutInfo[dataSourceName] = layout;
  });
  return backendDefaultLayoutInfo;
}
