import { useEffect } from 'react';
import { useDynamicWorkDesignStore } from '@components/DynamicWorkDesign/store/modules/DynamicWorkDesign';
import { fetchBackendDefaultLayout } from '../tools';

import type { IBackendDefaultLayoutInfo } from '@/components/DataSource/types/common';

function useBackendDefaultLayout() {
  const {
    dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey,
  } = useDynamicWorkDesignStore((state) => ({
    dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey: state.setDynamicWorkDesignRenderDataByKey,
  }));

  const { applicationCode, pageCode, code, category } = dynamicWorkDesignInfo ?? {};
  const { dataSources } = dynamicWorkDesignRenderData ?? {};

  useEffect(() => {
    if (!dataSources) {
      setDynamicWorkDesignRenderDataByKey('backendDefaultLayout', null);
      return;
    }
    /**
     * TODO: 由于接口返回结构未定，这边后续再修改
     */
    fetchBackendDefaultLayout({
      application: applicationCode,
      pageCode,
      category,
      pageDesignCode: code,
      dataSources,
    })
      .then((res: IBackendDefaultLayoutInfo) => {
        if (res) {
          setDynamicWorkDesignRenderDataByKey('backendDefaultLayout', res);
        }
      })
      .catch((error) => {
        console.error(
          '🚀 [Generated Log]: path = src/components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/hooks/useBackendDefaultLayout.ts, scope = useBackendDefaultLayout.useEffect, error = ',
          error
        );
      });
  }, [dataSources, applicationCode, pageCode, category, code]);
}

export { useBackendDefaultLayout };
