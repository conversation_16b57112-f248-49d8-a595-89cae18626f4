import { useEffect, useState } from 'react';

import type { IOperatePermissionInfo } from '@/components/OperatePermission/types';
import type { IBackendDefaultLayoutInfo } from '@/components/DataSource/types/common';
import { isEqual } from 'lodash';

export interface IExtraDataStateDependenciesInfo extends IExtraDataStateInfo {}

export interface IExtraDataStateInfo {
  iamCondition: IOperatePermissionInfo[];
  backendDefaultLayout: IBackendDefaultLayoutInfo | null;
}

function useExtraData(dependencieInfo?: IExtraDataStateDependenciesInfo): IExtraDataStateInfo {
  const [extraData, setExtraData] = useState<IExtraDataStateInfo>({
    iamCondition: [],
    backendDefaultLayout: null,
  });

  useEffect(() => {
    const extraDataLatest = {
      ...extraData,
      iamCondition: dependencieInfo?.iamCondition ?? [],
      backendDefaultLayout: dependencieInfo?.backendDefaultLayout ?? {},
    };

    if (isEqual(extraData, extraDataLatest)) return;
    setExtraData(extraDataLatest);
  }, [dependencieInfo?.iamCondition, dependencieInfo?.backendDefaultLayout]);

  // const queryDataSourceGenerator = async (dataSourceName?: string) => {
  //   console.log('dataSourceName: ', dataSourceName);
  //   if (dataSourceName) {
  //     return new Promise((res) => {
  //       setTimeout(() => {
  //         const r = {
  //           test: 'test222',
  //         };
  //         extraDataRef.current = r;
  //         res(r);
  //       }, 500);
  //     });
  //   } else {
  //     return {};
  //   }
  // };

  return extraData;
}

export default useExtraData;
