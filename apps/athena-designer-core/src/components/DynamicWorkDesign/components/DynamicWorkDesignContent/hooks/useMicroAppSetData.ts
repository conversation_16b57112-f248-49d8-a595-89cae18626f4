import { useEffect, useMemo } from 'react';
import { athMicroApp, appName, url, noDataFieldTree } from '../athMicroApp';
import {
  useDynamicWorkDesignContentStore,
  useDynamicWorkDesignStore,
} from '@/components/DynamicWorkDesign/store';
import { MessageToSubType } from '../type';
import {
  ActionDataSourceInfoData,
  ActionFieldTreeData,
  ActionRuleData,
  MessageToSubAction,
  MessageToSubInitLcdp,
} from '@athena-designer-editor/src/plugins/plugin-ath-loader/type';
import { uniqueId } from 'lodash';
import useExtraData from './useExtraData';

// 在这里还是 由于 microApp的 通信机制 并不是 事件模式
// 所以如果连续两次setData，而key又相同，那么将会触发一次事件，而值是两次的merge，这个明显不能满足我们对事件逻辑的需求
// 所以 在 这里，每条消息在通信前将会生成一个唯一key
// 在lowcode解析时，通过key的规则解析成message list ，然后依次执行
export const sendMessageToEditor = (message: MessageToSubAction | MessageToSubInitLcdp) => {
  athMicroApp.clearData(appName);
  const dataKey = uniqueId('@messageData');
  const messageData = {
    [dataKey]: message,
  };
  athMicroApp.setData(appName, messageData as any);
};

function useMicroAppSetData() {
  const { dynamicWorkDesignRenderData, setIsRenderDataLoading } = useDynamicWorkDesignStore(
    (state) => ({
      dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
      setIsRenderDataLoading: state.setIsRenderDataLoading,
    })
  );

  const { lowcodeRenderInitData } = useDynamicWorkDesignContentStore((state) => ({
    lowcodeRenderInitData: state.lowcodeRenderInitData,
  }));

  const extraData = useExtraData({
    iamCondition: dynamicWorkDesignRenderData?.iamCondition ?? [],
    backendDefaultLayout: dynamicWorkDesignRenderData?.backendDefaultLayout ?? null,
  });

  // 规则
  useEffect(() => {
    sendMessageToEditor({
      type: MessageToSubType.Action,
      data: {
        key: 'rule',
        data: {
          type: 'update',
          data: dynamicWorkDesignRenderData?.ruleList ?? [],
        } as ActionRuleData,
      },
    } as MessageToSubAction);
  }, [dynamicWorkDesignRenderData?.ruleList]);

  // 数据源相关
  useEffect(() => {
    const {
      dataSourceName = '',
      dataSourceNames = [],
      dataSources = {},
    } = dynamicWorkDesignRenderData ?? {};
    sendMessageToEditor({
      type: MessageToSubType.Action,
      data: {
        key: 'dataSourceInfo',
        data: {
          type: 'update',
          data: {
            dataSourceName,
            dataSourceNames,
            dataSources,
          },
        } as ActionDataSourceInfoData,
      },
    } as MessageToSubAction);
  }, [
    dynamicWorkDesignRenderData?.dataSourceNames,
    dynamicWorkDesignRenderData?.dataSourceName,
    dynamicWorkDesignRenderData?.dataSources,
  ]);

  // 字段树相关
  useEffect(() => {
    const { fieldTreeMap } = dynamicWorkDesignRenderData ?? {};
    sendMessageToEditor({
      type: MessageToSubType.Action,
      data: {
        key: 'fieldTreeMap',
        data: {
          type: 'update',
          data: fieldTreeMap?.size > 0 ? fieldTreeMap : noDataFieldTree,
        } as ActionFieldTreeData,
      },
    } as MessageToSubAction);
  }, [dynamicWorkDesignRenderData?.fieldTreeMap]);

  // 额外信息
  useEffect(() => {
    sendMessageToEditor({
      type: MessageToSubType.Action,
      data: {
        key: 'extraData',
        data: {
          type: 'update',
          data: extraData,
        } as any,
      },
    } as MessageToSubAction);
  }, [extraData]);

  // lowcode init逻辑
  // 一般之后在DynamicWorkDesign 输入的渲染数据 发生变化时
  // 会清空之前的lowcodeRenderInitData，之后 就会根据最新数据重新 组装 lowcodeRenderInitData
  // 之后就会 在这里 触发事件，让画布 执行初始化
  useEffect(() => {
    if (lowcodeRenderInitData) {
      setIsRenderDataLoading(true);
      sendMessageToEditor({
        type: MessageToSubType.InitLcdp,
        data: {
          ...lowcodeRenderInitData,
        },
      } as MessageToSubInitLcdp);
    }
  }, [lowcodeRenderInitData]);

  return { sendMessageToEditor };
}

export default useMicroAppSetData;
