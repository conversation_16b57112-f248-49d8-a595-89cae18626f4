import { useEffect } from 'react';
import {
  getPreloadRenderDslWorkDesignData,
  getRenderDslWorkDesignBaseData,
  getRenderDslWorkDesignData,
} from '../../../utils';
import { useDynamicWorkDesignStore, useDynamicWorkDesignContentStore } from '../../../store';
import { IPublicTypeComponentMetadata } from './useIsvPackageData/type';
import useExtraData from './useExtraData';
import { DynamicWorkDesignStatus } from '@/components/DynamicWorkDesign/config/type';

function useLowcodeRenderInitData(
  systemConfig: any,
  isvComponentList: IPublicTypeComponentMetadata[],
  dynamicWorkDesignStatus: DynamicWorkDesignStatus
) {
  // 用于lowcode 初始化渲染的数据
  // lowcodeRenderInitData 会在发生变化时 触发 lowcode 画布 init事件，重新渲染画布
  // 单向数据流，当 DynamicWorkDesign input 的 数据发生变化时，都会 先滞空 lowcodeRenderInitData
  // 之后 DynamicWorkDesign 就会 根据 最新的 input 数据 重新 组装 lowcodeRenderInitData 数据
  // 之后 就会 触发 触发 lowcode 画布 init事件，重新渲染画布
  // 之后 所有 画布渲染数据的变更 都是 lowcode 画布自行处理的（比如 五要素数据）
  // 当画布数据发生变化后 ，lowcode 画布 会 通过事件，将 最新的 画布数据 传递到 DynamicWorkDesign
  // DynamicWorkDesign 不会做 其他处理，直接 将其组装 到 全量数据中 丢给 DynamicWorkDesign 的 调用方
  // DynamicWorkDesign 的 调用方 自行更新数据 即可
  // 由于DynamicWorkDesign并不是一个单纯的react组件模块，其还包含了画布子应用，所以天然分成了两层结构
  // 单向数据流，单一应用维护数据，在DynamicWorkDesign中 ，DynamicWorkDesign自身会维护一部分 数据，其中的 画布子应用 会维护 画布数据
  // 绝不允许 一份数据 在 多个 应用中 维护 和 同步，那会造成，数据流转 相当混乱，不好维护
  const { lowcodeRenderInitData, setLowcodeRenderInitData } = useDynamicWorkDesignContentStore(
    (state) => ({
      lowcodeRenderInitData: state.lowcodeRenderInitData,
      setLowcodeRenderInitData: state.setLowcodeRenderInitData,
    })
  );

  const { isPreload, dynamicWorkDesignInfo, dynamicWorkDesignRenderData } =
    useDynamicWorkDesignStore((state) => ({
      isPreload: state.isPreload,
      dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
      dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
    }));

  const extraData = useExtraData({
    iamCondition: dynamicWorkDesignRenderData?.iamCondition ?? [],
    backendDefaultLayout: dynamicWorkDesignRenderData?.backendDefaultLayout ?? null,
  });

  useEffect(() => {
    // 组装lowcodeRenderInitData（lowcode初始化必要数据）的逻辑
    // 当lowcode editor 微前端环境（lowcode画布业务逻辑此时没有初始化）加载完毕
    // 而lowcodeRenderInitData为空，而其所需的必要数据（getRenderDslWorkDesignBaseData）准备完成时
    // 就会组装lowcodeRenderData
    // 这时在useMicroAppSetData就会因为lowcodeRenderData的变更，触发MessageToSubType.Init
    // lowcode的画布就会 开始初始化
    if (lowcodeRenderInitData) return;
    if (dynamicWorkDesignStatus === DynamicWorkDesignStatus.Loading) return;
    if (isPreload) {
      setLowcodeRenderInitData(getPreloadRenderDslWorkDesignData());
      return;
    }

    const renderDslWorkDesignBaseData = getRenderDslWorkDesignBaseData(
      dynamicWorkDesignInfo,
      dynamicWorkDesignRenderData
    );
    if (!renderDslWorkDesignBaseData) return;
    setLowcodeRenderInitData(
      getRenderDslWorkDesignData(
        dynamicWorkDesignInfo,
        dynamicWorkDesignRenderData,
        systemConfig,
        isvComponentList,
        renderDslWorkDesignBaseData,
        extraData
      )
    );
  }, [
    isPreload,
    dynamicWorkDesignStatus,
    lowcodeRenderInitData,
    dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData,
  ]);

  return {};
}

export default useLowcodeRenderInitData;
