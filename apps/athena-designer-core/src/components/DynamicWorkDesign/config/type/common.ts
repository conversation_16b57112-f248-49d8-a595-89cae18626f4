import { ILangDetailInfo } from '@components/DataSource/types/dataSource';
import { DynamicWorkDesignConfig, DynamicWorkDesignConfigOld } from './dynamicWorkDesignConfig';
import { IsvPackageDataAllInfo } from '@main/projects/form-editor-components/src/lib/service/isv-custom/isv-custom.type';
import { AthTreeDataNode } from '@athena-designer-editor/src/plugins/plugin-ath-field-panel/type';
import { IBackendDefaultLayoutInfo } from '@/components/DataSource/types/common';

/**
 * 菜单code 改用 平台的 pageCode
 */
export enum PageCode {
  /**
   * 单档多栏的 界面设计
   */
  BASIC_DATA = 'basic-data',
  /**
   * 浏览界面
   */
  BROWSE_PAGE = 'browse-page',
  /**
   * 编辑界面
   */
  EDIT_PAGE = 'edit-page',
  /**
   * 子页面
   */
  SUB_PAGE = 'sub-page',
  /**
   * TODO: 自定义任务卡？？？确定之后修改
   */
  TASK_CARD = 'task-card',
}

/**
 * 业务类型定义
 */
export enum Pattern {
  /**
   * 基础资料
   */
  DATA_ENTRY = 'DATA_ENTRY',
  /**
   * 报表
   */
  STATEMENT = 'STATEMENT',
  /**
   * TODO: 任务相关，只是定义，不保熟
   */
  BUSINESS = 'BUSINESS',
  CUSTOM = 'CUSTOM',
}

/**
 * 基础资料类型定义
 */
export enum Category {
  /**
   * 模型驱动单档多栏
   */
  'SIGN-DOCUMENT' = 'SIGN-DOCUMENT',
  /**
   * 模型驱动单档多栏树
   */
  'TREEDATA-SINGLE-DOCUMENT' = 'TREEDATA-SINGLE-DOCUMENT',
  /**
   * 模型驱动单档
   */
  'DOUBLE-DOCUMENT-FORM' = 'DOUBLE-DOCUMENT-FORM',
  /**
   * 模型驱动单档树
   */
  'TREEDATA-DOUBLE-DOCUMENT-FORM' = 'TREEDATA-DOUBLE-DOCUMENT-FORM',
  /**
   * 模型驱动双档
   */
  'DOUBLE-DOCUMENT' = 'DOUBLE-DOCUMENT',
  /**
   * 模型驱动双档树
   */
  'TREEDATA-DOUBLE-DOCUMENT' = 'TREEDATA-DOUBLE-DOCUMENT',
  /**
   * 模型驱动多档
   */
  'DOUBLE-DOCUMENT-MULTI' = 'DOUBLE-DOCUMENT-MULTI',
  /**
   * 模型驱动多档树
   */
  'TREEDATA-DOUBLE-DOCUMENT-MULTI' = 'TREEDATA-DOUBLE-DOCUMENT-MULTI',
}

// 当前作业的原始数据
export interface DynamicWorkDesignInfo {
  code: string; //作业code
  parentPageCode?: string; //父页面的code(pageCode为子页面类型时才有值)
  name: string; // 作业名称
  actionId: string; // actionId, 原来dsl-work-design的一个Input
  category: Category; //作业category
  applicationCode: string; //应用code
  appType: number; //应用类型
  pageCode: PageCode; //作业的 页面code
  isvPackageDataList: IsvPackageDataAllInfo[];
  dynamicWorkDesignConfig: DynamicWorkDesignConfig; // 新版本的配置项
  config: Partial<DynamicWorkDesignConfigOld>; // 旧版本的配置项，最终会废弃
  businessCode?: string; // 业务对象code, 1.0作业没有
}

// 设计器的状态
export enum DynamicWorkDesignStatus {
  Loading = 'Loading', // 加载中(可能是基础数据，画布初始化等)
  Rendering = 'Rendering', // 渲染中（可能是组装需要渲染的数据，数据正在渲染中等）
  Error = 'Error', // 出错(保留)
  Ready = 'Ready', // 准备就绪
}

// 后台用来产生PageUIElement的原始信息
export interface PageUIElementGenerateOrigin extends PageUIElementSearchInfo {
  actionId?: string;
  type: string;
}

export interface PageUIElementSearchInfo {
  code: string;
  activityId: string;
  pageCode: string;
}

// pageUIElement
export interface PageUIElement extends PageUIElementSearchInfo {
  elements: PageUIElementContent;
}

// pageUIElement
export interface PageUIElementContent {
  layout: any[];
  operations: any[];
  submitActions: any[];
  hooks: any[];
  gridSettings: any[];
}

// 当前作业的渲染数据（既input）
export interface DynamicWorkDesignRenderData {
  dataSources: any; // （DynamicWorkDesign维护）
  dataSourceNames: string[]; // （DynamicWorkDesign维护）
  extendedFields: any; // （DynamicWorkDesign维护）
  pageUIElementContent: PageUIElementContent; // （lowcode画布维护）用来渲染的PageUIElement五要素数据，虽然现在五要素结构和多tab的逻辑已经被废弃或者半废弃，但运行时还会保留PageUIElement结构，所以这里依然保留
  iamCondition?: any[];
  backendDefaultLayout?: IBackendDefaultLayoutInfo;
  groupSchemaList?: string[]; // 后端需要的，用于组装数据源请求参数给 运行时使用（当然这个逻辑本身我觉得有点问题，我觉得运行时应该有能力根据dsl自行组装）
}

// 当前作业的完整数据（既实际渲染之后的界面设计器数据）
export interface DynamicWorkDesignRenderWholeData extends DynamicWorkDesignRenderData {
  dataSourceName?: string; // （DynamicWorkDesign维护）
  ruleList: any[]; // （DynamicWorkDesign维护）
  fieldTreeMap: Map<string, AthTreeDataNode[]>; // （DynamicWorkDesign维护）数据源相关字段树数据集合
}

export interface IFieldTreeNodeInfo {
  title: string;
  data_name: string;
  data_type: string;
  is_datakey?: boolean;
  is_array?: boolean;
  is_businesskey?: boolean;
  description?: ILangDetailInfo;
  category: string;
  target: string;
  fullPath: string;
  parentCategory: string;
  path: string;
  userTarget?: string;
  expanded?: boolean;
  root?: string;
  isLeaf?: boolean;
  parentId?: string;
  dbFieldType?: string;
  children?: IFieldTreeNodeInfo[];
}

export interface IFieldTreeNodeOldInfo extends IFieldTreeNodeInfo {
  field?: IFieldTreeNodeOldInfo[];
  children?: IFieldTreeNodeOldInfo[];
}
