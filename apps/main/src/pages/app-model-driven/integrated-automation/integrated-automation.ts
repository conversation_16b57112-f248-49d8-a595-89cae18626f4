export const integratedRoutes = [
  // {
  //   id: 'dj-驱动执行DTD&TTT',
  //   path: 'dtd-designer-view',
  //   type: 'program',
  //   icon: 'iconzhenceliucheng',
  // },
  {
    id: 'dj-数据侦测',
    path: 'data-execution/detect-rule',
    type: 'program',
    icon: 'iconzhenceliucheng',
  },
  {
    id: 'dj-驱动执行',
    path: 'data-execution-new',
    type: 'program',
    icon: 'iconqudongzhihang',
  },
  {
    id: 'dj-签核自定义',
    path: 'data-execution-manual',
    type: 'program',
    icon: 'iconqianhe-xian1',
  },
  /* 驱动执行 */
  {
    id: 'dj-服务编排',
    path: 'so',
    type: 'program',
    icon: 'iconfuwu-xin',
  },
  {
    id: 'dj-行动',
    path: 'action',
    type: 'program',
    icon: 'iconhangdong',
  },
  {
    id: 'dj-参数设定',
    path: 'param-set',
    type: 'program',
    icon: 'iconcanshupeizhi1',
  },
  {
    id: 'app/other/extend-info',
    path: 'extend-info',
    type: 'program',
    icon: 'iconkuozhanxinxi',
  },
  // {
  //   id: 'dj-参数设定',
  //   path: 'param',
  //   type: 'program',
  //   children: [
  //     {
  //       id: 'dj-解决方案参数',
  //       path: 'param/appParam',
  //       type: 'program',
  //     },
  //     {
  //       id: 'dj-来自API参数',
  //       path: 'param/apiParam',
  //       type: 'program',
  //     },
  //     {
  //       id: 'dj-解决方案清单参数',
  //       path: 'param/appParamList',
  //       type: 'program',
  //     },
  //     {
  //       id: 'dj-开窗信息配置',
  //       path: 'param/openWindow',
  //       type: 'program',
  //     },
  //     {
  //       id: 'dj-参数控制任务显隐配置',
  //       path: 'param/activityVisibleConfig',
  //       type: 'program',
  //     },
  //   ],
  // },

  {
    id: 'dj-消息管理',
    path: 'messages',
    type: 'program',
    icon: 'iconqudongzhihang',
    children: [
      {
        id: 'dj-消息事件',
        icon: 'iconxiaoxishijian',
        path: 'messages/event',
        type: 'program',
      },
      {
        id: 'dj-消息推送',
        icon: 'iconxiaoxituisong',
        path: 'messages/notify',
        type: 'program',
      },
    ],
  },
];
