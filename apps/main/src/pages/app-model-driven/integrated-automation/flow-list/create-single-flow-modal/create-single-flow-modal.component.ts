import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CreateDetectInfo, ProcessMonitorType } from '../../view-designer-detect/type';
import {
  BindFormType,
  EType,
  NodeType,
  PannelTabsType,
  SDNodeType,
  TriggerType,
} from '../../view-designer/config/typings';
import { CommonValidators } from 'common/utils/common.validators';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { isEmpty, trim } from 'lodash';
import { LocaleService } from 'common/service/locale.service';
import { AppService } from 'pages/apps/app.service';
import { DetectService } from 'pages/app/dtd/detect/detect.service';
import { MdCreateFlowModalService } from '../md-create-flow-modal/md-create-flow-modal.service';
import { ViewStoreService } from '../../view-designer/service/store.service';
import { initFlowGraph } from '../../view-designer/config/init-graph';
import { CreateSingleFlowModalService } from './create-single-flow-modal.service';
import CodeEnum from 'common/config/code';

@Component({
  selector: 'app-create-single-flow-modal',
  templateUrl: './create-single-flow-modal.component.html',
  styleUrls: ['./create-single-flow-modal.component.less'],
  providers: [MdCreateFlowModalService, ViewStoreService, DetectService, CreateSingleFlowModalService],
})
export class CreateSingleFlowModalComponent implements OnInit {
  @Input() sourceData: { [key: string]: any } = {};
  @Input() type: any;
  @Input() flowCopyItem: any = null;
  @Input() triggerType: TriggerType;

  // 通知父组件关闭本弹窗
  @Output() closeModal = new EventEmitter<CreateDetectInfo>();
  @Output() finished = new EventEmitter();

  public TriggerType = TriggerType;
  public dataForm: FormGroup;
  public projectNameLang = {
    zh_CN: '',
    zh_TW: '',
    en_US: '',
  };
  public suffix = {
    zh_CN: '副本',
    zh_TW: '複本',
    en_US: 'Copy',
  };
  public errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translateService.instant('dj-请输入'),
    },
  };
  public projectName: string = '';
  public spining: boolean = false;
  public eventList: any[] = [];
  public businessList: any[] = [];
  public submitLoading: boolean;
  // 作业列表
  public activityList: any[] = [];

  // 当前语言
  get currentLanguage() {
    return this.languageService.currentLanguage || 'zh_CN';
  }

  get modalTitle() {
    return !isEmpty(this.flowCopyItem) ? 'dj-项目复制' : 'dj-创建项目';
  }

  get dtdReference() {
    return !(this.appService?.selectedApp?.tag?.sourceComponent === 'BC');
  }

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private message: NzMessageService,
    private languageService: LocaleService,
    private mdCreateFlowModalService: MdCreateFlowModalService,
    public appService: AppService,
    private detectService: DetectService,
    private viewStoreServie: ViewStoreService,
    private createSingleFlowModalService: CreateSingleFlowModalService,
  ) {}

  // 修改名称
  changeName(e): void {
    this.projectNameLang = e.lang;
    this.dataForm.patchValue({
      projectName: trim(e?.value),
    });
  }

  // 弹窗取消
  handleCancel(createDetectInfo?: CreateDetectInfo): void {
    this.dataForm.reset();

    this.projectNameLang = {
      zh_CN: '',
      zh_TW: '',
      en_US: '',
    };

    this.closeModal.emit(createDetectInfo ?? null);
  }

  /**
   * 复制的新增
   * @param dataFormValue
   */
  async handleCopeFlow(dataFormValue): Promise<void> {
    const { triggerType, processId, processConfig, triggerConfig } = this.flowCopyItem;

    // 冗余 processName lang.processName 两个字段给后端
    const params: any = {
      processId: processId,
      processName: dataFormValue.projectName,
      projectName: dataFormValue.projectName,
      projectCode: dataFormValue.projectCode,
      lang: {
        processName: this.projectNameLang,
        projectName: this.projectNameLang,
      },
      triggerType: dataFormValue.triggerType,
    };

    if (dataFormValue?.triggerType === TriggerType.EVENT) {
      params.triggerConfig = {
        businessCode: dataFormValue?.businessCode,
        eventId: dataFormValue?.eventId,
      };
    }

    // const StartNode
    const oldFormCode = processConfig?.nodes?.find((node) => node.type === SDNodeType.START_NODE)?.bindForm?.formCode;
    // 复制前是手工发起改变，复制后也是手工发起，但是手工发起的表单信息改了
    const isProjectChange =
      triggerType === dataFormValue.triggerType &&
      triggerType === TriggerType.PROJECT &&
      oldFormCode !== dataFormValue.formCode;
    // 复制前是事件，复制后也是事件，但是事件内的表单信息改了
    const isEventChange =
      triggerType === dataFormValue.triggerType &&
      triggerType === TriggerType.EVENT &&
      (triggerConfig.businessCode !== dataFormValue.businessCode || triggerConfig.eventId !== dataFormValue.eventId);

    if (triggerType !== dataFormValue.triggerType || isProjectChange || isEventChange) {
      const { flowGraphNode, processConfigNode } = this.initFlowGraphAndConfigByCopy(dataFormValue.triggerType);
      params.flowGraphNode = flowGraphNode;
      params.processConfigNode = processConfigNode;
    }

    this.submitLoading = true;
    // const projectCode = await this.appService.getCodeByType(CodeEnum.PC);
    // params.projectCode = projectCode;
    this.createSingleFlowModalService.copySingleProject(params).subscribe(
      (res) => {
        this.submitLoading = false;

        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-保存成功'));

          this.handleCancel();

          this.finished.emit({
            ...res.data,
            activeTab: 'all',
            menuOperateType: 'add',
          });
        }
      },
      () => {
        this.submitLoading = false;
      },
    );
  }

  async handleOk(): Promise<void> {
    for (const i of Object.keys(this.dataForm.controls)) {
      this.dataForm.controls[i].markAsDirty();
      this.dataForm.controls[i].updateValueAndValidity();
    }

    if (this.dataForm.valid) {
      const dataFormValue = this.dataForm.getRawValue();

      if (!isEmpty(this.flowCopyItem)) {
        this.handleCopeFlow(dataFormValue);
        return;
      }

      // 冗余 processName lang.processName 两个字段给后端
      let params: any = {
        businessCode: this.sourceData?.businessCode || undefined,
        addSourceType: this.sourceData?.addSourceType || undefined,
        processName: dataFormValue.projectName,
        projectName: dataFormValue.projectName,
        projectCode: dataFormValue.projectCode,
        projectFrom: 'single',
        triggerType: dataFormValue.triggerType,
        triggerConfig: {
          businessCode: dataFormValue?.businessCode,
          eventId: dataFormValue?.eventId,
        },
      };
      // 新增
      const { flowGraph, processConfig } = this.initFlowGraphAndConfig(dataFormValue.triggerType);

      params = {
        ...params,
        flowGraph: flowGraph,
        processConfig: {
          ...processConfig,
          projectName: this.dataForm.get('projectName')?.value,
        },
        bindForm: { type: EType.NONE },
        lang: {
          sourceName: {
            zh_CN: '未命名',
            zh_TW: '未命名',
            en_US: 'Untitled',
          },
          subjectRule: {
            zh_CN: '未命名标题',
            zh_TW: '未命名標題',
            en_US: 'Untitled title',
          },
          processName: this.projectNameLang,
          projectName: this.projectNameLang,
        },
      };
      this.submitLoading = true;

      this.createSingleFlowModalService.postUpsertProcess(params).subscribe(
        (res) => {
          this.submitLoading = false;

          if (res.code === 0) {
            this.message.success(this.translateService.instant('dj-保存成功'));
            this.handleCancel();

            this.finished.emit({
              ...params,
              ...res.data,
              menuOperateType: 'add',
              activeTab: params.triggerType || 'all',
            });
          }
        },
        () => {
          this.submitLoading = false;
        },
      );

      // this.mdCreateFlowModalService.postUpsertProcess(params).subscribe(
      //   (res) => {
      //     this.submitLoading = false;

      //     if (res.code === 0) {
      //       this.message.success(this.translateService.instant('dj-保存成功'));
      //       this.handleCancel();

      //       this.finished.emit({
      //         ...res.data,
      //         menuOperateType: 'add',
      //         activeTab: params.triggerType || 'all',
      //       });
      //     }
      //   },
      //   () => {
      //     this.submitLoading = false;
      //   },
      // );
    }
  }

  // 复制流
  handleCopyFlow() {
    const { name, lang } = this.flowCopyItem;
    const language = this.languageService?.currentLanguage || 'zh_CN';

    this.dataForm.patchValue({
      projectName: `${name}_${this.suffix[language]}`,
      triggerType: this.flowCopyItem.triggerType,
    });
    this.projectName = `${name}_${this.suffix[language]}`;
    this.projectNameLang = {
      zh_CN: `${lang.name?.zh_CN}_${this.suffix[language]}`,
      zh_TW: `${lang.name?.zh_TW}_${this.suffix[language]}`,
      en_US: `${lang.name?.en_US}_${this.suffix[language]}`,
    };

    if (this.flowCopyItem.triggerType === TriggerType.EVENT) {
      // 事件触发
      this.dataForm.patchValue({
        businessCode: this.flowCopyItem.triggerConfig?.businessCode,
        eventId: this.flowCopyItem.triggerConfig?.eventId,
      });

      this.getEventList(this.flowCopyItem.triggerConfig?.businessCode);
    }

    if (this.flowCopyItem.triggerType === TriggerType.PROJECT) {
      // 手工发起
      this.dataForm.patchValue({
        formCode: this.flowCopyItem.processConfig?.nodes?.find((node) => node.type === SDNodeType.START_NODE)?.bindForm
          ?.formCode,
      });
    }
  }

  // 修改业务对象
  changeBusinessObject(e): void {
    if (e) {
      this.dataForm.patchValue({ eventId: null });
      this.getEventList(e);
    } else {
      this.dataForm.patchValue({ eventId: null });
      this.eventList = [];
    }
  }

  // 修改触发类型
  changeTriggerType(e): void {
    this.dataForm.patchValue({ businessCode: null, eventId: null, formCode: null });

    if (e === TriggerType.EVENT) {
      this.dataForm.controls['businessCode'].setValidators([Validators.required]);
      this.dataForm.controls['eventId'].setValidators([Validators.required]);
    } else {
      this.dataForm.controls['businessCode'].setValidators([]);
      this.dataForm.controls['eventId'].setValidators([]);
    }
  }

  // 获取解决方案下的作业
  async getActivityList(): Promise<void> {
    const result = await this.mdCreateFlowModalService.getActivityListByPatternAndApplication().toPromise();

    this.activityList = result.data;
  }

  // 获取事件信息
  async getEventList(code): Promise<void> {
    if (!code) return;

    const params = {
      businessCode: code,
    };

    try {
      this.spining = true;

      const res = await this.mdCreateFlowModalService.postEventListQuery(params).toPromise();

      if (res?.code === 0) {
        this.eventList = res?.data || [];
      }
    } finally {
      this.spining = false;
    }
  }

  // 获取业务对象信息
  getBusinessObject() {
    this.mdCreateFlowModalService.postQueryBusinessObject().subscribe((res) => {
      if (res?.code === 0) {
        this.businessList = res?.data || [];
      }
    });
  }

  // 新增流程初始化配置项
  initFlowGraphAndConfig(type) {
    const { nodes, links }: any = initFlowGraph(type, this.translateService);
    const configNodes = nodes.map((node) => {
      const { nodeId, nodeType } = node?.data || {};
      const defaultConfig: any = this.viewStoreServie.initPropertiesByType(nodeType, this.translateService);

      // 事件触发开始节点配置
      if (nodeType === NodeType.START_EVENT) {
        const dataFormValue = this.dataForm.getRawValue();
        defaultConfig.triggerConfig = {
          businessCode: dataFormValue?.businessCode,
          eventId: dataFormValue?.eventId,
        };
      }

      // 结束节点配置
      if (nodeType === NodeType.END_FLOW) {
        defaultConfig._events = [{ type: EType.NONE }];
      }

      if (nodeType === NodeType.START_PROJECT) {
        const dataFormValue = this.dataForm.getRawValue();
        const item = this.activityList.find((e) => e.code === dataFormValue.formCode);

        if (item) {
          defaultConfig.bindForm = {
            modelCode: item.simpleModelCode,
            serviceCode: item.navigateModelServiceCode,
            formCode: item.code,
            type: BindFormType.PageDesign,
          };
          defaultConfig.taskName = item.lang.name[this.currentLanguage] || item.name;
          node.attrs.body.stroke = '#E9E9E9';
          node.data.isVerificationPassed = true;
          defaultConfig._isValidPassed = { [PannelTabsType.BASE]: true };
        } else {
          defaultConfig.bindForm = {};
        }
      }

      return {
        ...defaultConfig,
        id: `${defaultConfig.type}_${nodeId}`,
        _nodeId: nodeId,
        _nodeType: nodeType,
      };
    });

    const configLinks =
      links?.map((m) => {
        return {
          id: m.id,
          fromId: configNodes?.find((item) => item._nodeId === m.source.cell).id,
          toId: configNodes?.find((item) => item._nodeId === m.target.cell).id,
        };
      }) || [];
    const flowGraph = {
      nodes: nodes,
      links: links,
    };
    const processConfig = {
      sourceName: this.translateService.instant('dj-未命名'),
      subjectRule: this.translateService.instant('dj-未命名标题'),
      projectName: this.translateService.instant('dj-项目名称'),
      nodes: configNodes,
      links: configLinks,
    };

    return { flowGraph, processConfig };
  }

  /**
   * 复制的场景下，初始化流程图和配置项
   * @param type
   * @returns
   */
  initFlowGraphAndConfigByCopy(type) {
    const { nodes }: any = initFlowGraph(type, this.translateService);
    const configNodes = nodes.map((node) => {
      const { nodeId, nodeType } = node?.data || {};
      const defaultConfig: any = this.viewStoreServie.initPropertiesByType(nodeType, this.translateService);

      // 事件触发开始节点配置
      if (nodeType === NodeType.START_EVENT) {
        const dataFormValue = this.dataForm.getRawValue();
        defaultConfig.triggerConfig = {
          businessCode: dataFormValue?.businessCode,
          eventId: dataFormValue?.eventId,
        };
      }

      // 结束节点配置
      if (nodeType === NodeType.END_FLOW) {
        defaultConfig._events = [{ type: EType.NONE }];
      }

      if (nodeType === NodeType.START_PROJECT) {
        const dataFormValue = this.dataForm.getRawValue();
        const item = this.activityList.find((e) => e.code === dataFormValue.formCode);

        if (item) {
          defaultConfig.bindForm = {
            modelCode: item.simpleModelCode,
            serviceCode: item.navigateModelServiceCode,
            formCode: item.code,
            type: BindFormType.PageDesign,
          };
          defaultConfig.taskName = item.lang.name[this.currentLanguage] || item.name;
          node.attrs.body.stroke = '#E9E9E9';
          node.data.isVerificationPassed = true;
          defaultConfig._isValidPassed = { [PannelTabsType.BASE]: true };
        } else {
          defaultConfig.bindForm = {};
        }
      }

      return {
        ...defaultConfig,
        id: `${defaultConfig.type}_${nodeId}`,
        _nodeId: nodeId,
        _nodeType: nodeType,
      };
    });

    const flowGraphNode = nodes?.[0] || null;
    const processConfigNode = configNodes?.[0] || null;

    return { flowGraphNode, processConfigNode };
  }

  ngOnInit() {
    const triggerType = this.triggerType !== 'all' ? this.triggerType : TriggerType.PAGE;

    this.dataForm = this.fb.group({
      // 名称
      projectName: ['', [CommonValidators.trim, Validators.required]],
      // code
      projectCode: [{ value: '', disabled: true }, [CommonValidators.trim, Validators.required], true],
      // 触发类型
      triggerType: [triggerType, [Validators.required]],

      businessCode: [null, [Validators.required]],
      eventId: [null, [Validators.required]],
      // 数据侦测相关：
      monitorType: [ProcessMonitorType.API, [Validators.required]], // 数据侦测类型
      // 作业
      formCode: [null],
    });

    this.getBusinessObject();
    this.getActivityList();

    this.appService.getCodeByType(CodeEnum.PC).then((projectCode) => {
      this.dataForm.patchValue({ projectCode });
    });

    this.dataForm.patchValue({ triggerType: triggerType });
    this.changeTriggerType(triggerType);

    if (!isEmpty(this.flowCopyItem)) this.handleCopyFlow();
  }
}
