import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AppService } from 'pages/apps/app.service';
import {
  NodeType,
  TriggerType,
  AddType,
  EType,
  BindFormType,
  PannelTabsType,
  SDNodeType,
} from '../../view-designer/config/typings';
import { ProcessMonitorType, CreateDetectInfo } from '../../view-designer-detect/type';

import { initFlowGraph } from '../../view-designer/config/init-graph';
import { ViewStoreService } from '../../view-designer/service/store.service';
import { MdCreateFlowModalService } from './md-create-flow-modal.service';
import { Interface } from 'readline';
import { DetectService } from 'pages/app/dtd/detect/detect.service';
import { isEmpty, trim } from 'lodash';
import { CommonValidators } from 'common/utils/common.validators';

@Component({
  selector: 'app-md-create-flow-modal',
  templateUrl: './md-create-flow-modal.component.html',
  styleUrls: ['./md-create-flow-modal.component.less'],
  providers: [MdCreateFlowModalService, ViewStoreService, DetectService],
})
export class MdCreateFlowModalComponent implements OnInit {
  currentLanguage;
  TriggerType = TriggerType;
  AddType = AddType;
  ProcessMonitorType = ProcessMonitorType;
  originFlowData: any = {};
  processNameLang: any = {
    zh_CN: '',
    zh_TW: '',
    en_US: '',
  };
  suffix = {
    zh_CN: '副本',
    zh_TW: '複本',
    en_US: 'Copy',
  };
  processName: string = '';
  businessList: any[] = [];
  eventList: any[] = [];
  supportDesign: boolean = false;
  initLoading: boolean = false;
  submitLoading: boolean = false;

  // 作业列表
  activityList: any[] = [];

  dataForm: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translateService.instant('dj-请输入'),
    },
  };
  modalTitle: string = '';

  spining: boolean = false;

  // 弹窗初始化的数据
  @Input() sourceData: { [key: string]: any } = {};
  @Input() type: any;
  @Input() flowCopyItem: any = null;
  // 通知父组件关闭本弹窗
  @Output() closeModal = new EventEmitter<CreateDetectInfo>();
  @Output() finished = new EventEmitter();

  constructor(
    private fb: FormBuilder,
    public appService: AppService,
    private translateService: TranslateService,
    private message: NzMessageService,
    private languageService: LocaleService,
    private viewStoreServie: ViewStoreService,
    private mdCreateFlowModalService: MdCreateFlowModalService,
    private detectService: DetectService,
  ) {
    this.currentLanguage = this.languageService.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {
    this.modalTitle = this.type === AddType.MONITOR ? 'dj-新建数据侦测' : 'dj-新建业务流';
    this.dataForm = this.fb.group({
      addType: [this.type || AddType.FLOW, [Validators.required]], // 新增的类型 数据侦测/工作流
      // 工作流相关：
      processName: ['', [CommonValidators.trim, Validators.required]],
      triggerType: [TriggerType.PAGE, [Validators.required]],
      businessCode: [null, [Validators.required]],
      eventId: [null, [Validators.required]],
      // 数据侦测相关：
      monitorType: [ProcessMonitorType.API, [Validators.required]], // 数据侦测类型
      // 作业
      formCode: [null],
    });

    this.getBusinessObject();
    this.getActivityList();
    this.dataForm.patchValue({ triggerType: TriggerType.PAGE });
    this.changeTriggerType(TriggerType.PAGE);
    this.handleCopyFlow();
  }

  handleCopyFlow() {
    if (isEmpty(this.flowCopyItem)) return;
    this.modalTitle = 'dj-工作流程复制';
    const { processName, lang } = this.flowCopyItem;
    const language = this.languageService?.currentLanguage || 'zh_CN';
    this.dataForm.patchValue({
      processName: `${processName}_${this.suffix[language]}`,
      triggerType: this.flowCopyItem.triggerType,
    });
    this.processName = `${processName}_${this.suffix[language]}`;
    this.processNameLang = {
      zh_CN: `${lang.processName?.zh_CN}_${this.suffix[language]}`,
      zh_TW: `${lang.processName?.zh_TW}_${this.suffix[language]}`,
      en_US: `${lang.processName?.en_US}_${this.suffix[language]}`,
    };
    if (this.flowCopyItem.triggerType === TriggerType.EVENT) {
      // 事件触发
      this.dataForm.patchValue({
        businessCode: this.flowCopyItem.triggerConfig?.businessCode,
        eventId: this.flowCopyItem.triggerConfig?.eventId,
      });
      this.getEventList(this.flowCopyItem.triggerConfig?.businessCode);
    }
    if (this.flowCopyItem.triggerType === TriggerType.PROJECT) {
      // 手工发起
      this.dataForm.patchValue({
        formCode: this.flowCopyItem.processConfig?.nodes?.find((node) => node.type === SDNodeType.START_NODE)?.bindForm
          ?.formCode,
      });
    }
  }

  // 获取业务对象信息
  getBusinessObject() {
    this.mdCreateFlowModalService.postQueryBusinessObject().subscribe((res) => {
      if (res?.code === 0) {
        this.businessList = res?.data || [];
      }
    });
  }

  // 获取事件信息
  async getEventList(code): Promise<void> {
    if (!code) return;
    const params = {
      businessCode: code,
    };
    try {
      this.spining = true;
      const res = await this.mdCreateFlowModalService.postEventListQuery(params).toPromise();
      if (res?.code === 0) {
        this.eventList = res?.data || [];
      }
    } finally {
      this.spining = false;
    }
  }

  // 获取流程信息
  getFlowData(id) {
    const params = {
      processId: id,
    };
    this.initLoading = true;
    this.mdCreateFlowModalService.getFindProcessById(params).subscribe(
      (res) => {
        this.initLoading = false;
        if (res.code === 0) {
          this.originFlowData = res?.data || {};
          this.processNameLang = res?.data?.lang?.processName;
          const formData = {
            processName: res?.data?.processName,
            triggerType: res?.data?.triggerType,
            businessCode: res?.data?.triggerConfig?.businessCode,
            eventId: res?.data?.triggerConfig?.eventId,
          };
          this.dataForm.patchValue(formData);
          res?.data?.businessCode && this.getEventList(res?.data?.businessCode);
          this.changeTriggerType(res?.data?.triggerType);
        }
      },
      () => {
        this.initLoading = false;
      },
    );
  }

  // 修改名称
  changeName(e): void {
    this.processNameLang = e.lang;
    this.dataForm.patchValue({
      processName: trim(e?.value),
    });
  }

  // 修改新增的类型
  changeAddType(e): void {
    this.dataForm.patchValue({
      businessCode: null,
      eventId: null,
      formCode: null,
      triggerType: TriggerType.PAGE,
      monitorType: ProcessMonitorType.API,
    });

    this.dataForm.controls['triggerType'].setValidators(e === AddType.FLOW ? [Validators.required] : []);
    this.dataForm.controls['businessCode'].setValidators([]);
    this.dataForm.controls['eventId'].setValidators([]);
    this.dataForm.controls['monitorType'].setValidators(e === AddType.FLOW ? [] : [Validators.required]);
  }

  // 获取解决方案下的作业
  async getActivityList(): Promise<void> {
    const result = await this.mdCreateFlowModalService.getActivityListByPatternAndApplication().toPromise();
    this.activityList = result.data;
  }

  // 修改触发类型
  changeTriggerType(e): void {
    this.dataForm.patchValue({ businessCode: null, eventId: null, formCode: null });
    if (e === TriggerType.EVENT) {
      this.dataForm.controls['businessCode'].setValidators([Validators.required]);
      this.dataForm.controls['eventId'].setValidators([Validators.required]);
    } else {
      this.dataForm.controls['businessCode'].setValidators([]);
      this.dataForm.controls['eventId'].setValidators([]);
    }
  }

  // 修改业务对象
  changeBusinessObject(e): void {
    if (e) {
      this.dataForm.patchValue({ eventId: null });
      this.getEventList(e);
    } else {
      this.dataForm.patchValue({ eventId: null });
      this.eventList = [];
    }
  }

  // 弹窗取消
  handleCancel(createDetectInfo?: CreateDetectInfo): void {
    this.dataForm.reset();
    this.processNameLang = {
      zh_CN: '',
      zh_TW: '',
      en_US: '',
    };
    this.closeModal.emit(createDetectInfo ?? null);
  }

  // 弹窗确定
  async handleOk(): Promise<void> {
    for (const i of Object.keys(this.dataForm.controls)) {
      this.dataForm.controls[i].markAsDirty();
      this.dataForm.controls[i].updateValueAndValidity();
    }

    if (this.dataForm.valid) {
      const dataFormValue = this.dataForm.getRawValue();

      if (dataFormValue.addType === AddType.MONITOR) {
        // 进入新增数据侦测逻辑
        const params = {
          application: this.appService?.selectedApp?.code,
          type: 'process',
          jsonObject: {
            name: this.processNameLang,
            monitorType: dataFormValue.monitorType,
          },
        };
        const param = !isEmpty(this.sourceData) ? Object.assign({}, params, this.sourceData) : params;
        try {
          await this.detectService.updateIntegrationAutomationInfo(param).toPromise();
          this.handleCancel({
            name: dataFormValue.processName,
            monitorType: dataFormValue.monitorType,
          } as CreateDetectInfo);
        } catch (error) {}
        return;
      }

      if (!isEmpty(this.flowCopyItem)) {
        this.handleCopeFlow(dataFormValue);
        return;
      }

      let params: any = {
        businessCode: this.sourceData?.businessCode || undefined,
        addSourceType: this.sourceData?.addSourceType || undefined,
        processName: dataFormValue.processName,
        triggerType: dataFormValue.triggerType,
        triggerConfig: {
          businessCode: dataFormValue?.businessCode,
          eventId: dataFormValue?.eventId,
        },
      };
      // 新增
      const { flowGraph, processConfig } = this.initFlowGraphAndConfig(dataFormValue.triggerType);
      params = {
        ...params,
        flowGraph: flowGraph,
        processConfig: processConfig,
        bindForm: { type: EType.NONE },
        lang: {
          sourceName: {
            zh_CN: '未命名',
            zh_TW: '未命名',
            en_US: 'Untitled',
          },
          subjectRule: {
            zh_CN: '未命名标题',
            zh_TW: '未命名標題',
            en_US: 'Untitled title',
          },
          processName: this.processNameLang,
          projectName: {
            zh_CN: '项目名称',
            en_US: 'Project Name',
            zh_TW: '專案名稱',
          },
        },
      };
      this.submitLoading = true;
      this.mdCreateFlowModalService.postUpsertProcess(params).subscribe(
        (res) => {
          this.submitLoading = false;
          if (res.code === 0) {
            this.message.success(this.translateService.instant('dj-保存成功'));
            this.handleCancel();
            this.finished.emit({
              ...res.data,
              menuOperateType: 'add',
              activeTab: params.triggerType || 'all',
            });
          }
        },
        () => {
          this.submitLoading = false;
        },
      );
    }
  }

  /**
   * 复制的新增
   * @param dataFormValue
   */
  handleCopeFlow(dataFormValue) {
    const { triggerType, processId, processConfig, triggerConfig } = this.flowCopyItem;
    const parma: any = {
      processId: processId,
      processName: dataFormValue.processName,
      lang: {
        processName: this.processNameLang,
      },
      triggerType: dataFormValue.triggerType,
    };
    if (dataFormValue?.triggerType === TriggerType.EVENT) {
      parma.triggerConfig = {
        businessCode: dataFormValue?.businessCode,
        eventId: dataFormValue?.eventId,
      };
    }
    // const StartNode
    const oldFormCode = processConfig?.nodes?.find((node) => node.type === SDNodeType.START_NODE)?.bindForm?.formCode;
    // 复制前是手工发起改变，复制后也是手工发起，但是手工发起的表单信息改了
    const isProjectChange =
      triggerType === dataFormValue.triggerType &&
      triggerType === TriggerType.PROJECT &&
      oldFormCode !== dataFormValue.formCode;
    // 复制前是事件，复制后也是事件，但是事件内的表单信息改了
    const isEventChange =
      triggerType === dataFormValue.triggerType &&
      triggerType === TriggerType.EVENT &&
      (triggerConfig.businessCode !== dataFormValue.businessCode || triggerConfig.eventId !== dataFormValue.eventId);

    if (triggerType !== dataFormValue.triggerType || isProjectChange || isEventChange) {
      const { flowGraphNode, processConfigNode } = this.initFlowGraphAndConfigByCopy(dataFormValue.triggerType);
      parma.flowGraphNode = flowGraphNode;
      parma.processConfigNode = processConfigNode;
    }
    this.submitLoading = true;
    this.mdCreateFlowModalService.processCopy(parma).subscribe(
      (res) => {
        this.submitLoading = false;
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-保存成功'));
          this.handleCancel();
          this.finished.emit({
            ...res.data,
            activeTab: 'all',
            menuOperateType: 'add',
          });
        }
      },
      () => {
        this.submitLoading = false;
      },
    );
  }

  // 新增流程初始化配置项
  initFlowGraphAndConfig(type) {
    const { nodes, links }: any = initFlowGraph(type, this.translateService);
    const configNodes = nodes.map((node) => {
      const { nodeId, nodeType } = node?.data || {};
      const defaultConfig: any = this.viewStoreServie.initPropertiesByType(nodeType, this.translateService);
      // 事件触发开始节点配置
      if (nodeType === NodeType.START_EVENT) {
        const dataFormValue = this.dataForm.getRawValue();
        defaultConfig.triggerConfig = {
          businessCode: dataFormValue?.businessCode,
          eventId: dataFormValue?.eventId,
        };
      }
      // 结束节点配置
      if (nodeType === NodeType.END_FLOW) {
        defaultConfig._events = [{ type: EType.NONE }];
      }
      if (nodeType === NodeType.START_PROJECT) {
        const dataFormValue = this.dataForm.getRawValue();
        const item = this.activityList.find((e) => e.code === dataFormValue.formCode);
        if (item) {
          defaultConfig.bindForm = {
            modelCode: item.simpleModelCode,
            serviceCode: item.navigateModelServiceCode,
            formCode: item.code,
            type: BindFormType.PageDesign,
          };
          defaultConfig.taskName = item.lang.name[this.currentLanguage] || item.name;
          node.attrs.body.stroke = '#E9E9E9';
          node.data.isVerificationPassed = true;
          defaultConfig._isValidPassed = { [PannelTabsType.BASE]: true };
        } else {
          defaultConfig.bindForm = {};
        }
      }
      return {
        ...defaultConfig,
        id: `${defaultConfig.type}_${nodeId}`,
        _nodeId: nodeId,
        _nodeType: nodeType,
      };
    });
    const configLinks =
      links?.map((m) => {
        return {
          id: m.id,
          fromId: configNodes?.find((item) => item._nodeId === m.source.cell).id,
          toId: configNodes?.find((item) => item._nodeId === m.target.cell).id,
        };
      }) || [];

    const flowGraph = {
      nodes: nodes,
      links: links,
    };
    const processConfig = {
      sourceName: this.translateService.instant('dj-未命名'),
      subjectRule: this.translateService.instant('dj-未命名标题'),
      projectName: this.translateService.instant('dj-项目名称'),
      nodes: configNodes,
      links: configLinks,
    };

    return { flowGraph, processConfig };
  }

  /**
   * 复制的场景下，初始化流程图和配置项
   * @param type
   * @returns
   */
  initFlowGraphAndConfigByCopy(type) {
    const { nodes }: any = initFlowGraph(type, this.translateService);
    const configNodes = nodes.map((node) => {
      const { nodeId, nodeType } = node?.data || {};
      const defaultConfig: any = this.viewStoreServie.initPropertiesByType(nodeType, this.translateService);
      // 事件触发开始节点配置
      if (nodeType === NodeType.START_EVENT) {
        const dataFormValue = this.dataForm.getRawValue();
        defaultConfig.triggerConfig = {
          businessCode: dataFormValue?.businessCode,
          eventId: dataFormValue?.eventId,
        };
      }
      // 结束节点配置
      if (nodeType === NodeType.END_FLOW) {
        defaultConfig._events = [{ type: EType.NONE }];
      }
      if (nodeType === NodeType.START_PROJECT) {
        const dataFormValue = this.dataForm.getRawValue();
        const item = this.activityList.find((e) => e.code === dataFormValue.formCode);
        if (item) {
          defaultConfig.bindForm = {
            modelCode: item.simpleModelCode,
            serviceCode: item.navigateModelServiceCode,
            formCode: item.code,
            type: BindFormType.PageDesign,
          };
          defaultConfig.taskName = item.lang.name[this.currentLanguage] || item.name;
          node.attrs.body.stroke = '#E9E9E9';
          node.data.isVerificationPassed = true;
          defaultConfig._isValidPassed = { [PannelTabsType.BASE]: true };
        } else {
          defaultConfig.bindForm = {};
        }
      }
      return {
        ...defaultConfig,
        id: `${defaultConfig.type}_${nodeId}`,
        _nodeId: nodeId,
        _nodeType: nodeType,
      };
    });

    const flowGraphNode = nodes?.[0] || null;
    const processConfigNode = configNodes?.[0] || null;

    return { flowGraphNode, processConfigNode };
  }
}
