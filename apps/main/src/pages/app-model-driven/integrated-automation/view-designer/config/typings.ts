import { Node } from '@antv/x6';

export enum SDNodeType {
  START_NODE = 'StartNode', // 开始节点
  END_NODE = 'EndNode', // 结束节点
  USER_TASK = 'UserTask', // 人工签核节点
  MANUAL_TASK = 'ManualTask', // 人工关卡节点
  NOTIFY_TASK = 'NotifyTask', // 人工知会节点
  EXCLUSIVE_GATEWAY = 'ExclusiveGateway', // 条件分支节点
  SERVICE_TASK = 'ServiceTask', // 自动HTTP/ESP/SCRIPT节点
  WAIT = 'Wait', // 等待
  EVOKE_WAIT = 'WakeWaiting', // 唤起等待
  SUB_PROJECT = 'SubProject', // 子项目
  SEND_TASK = 'SendTask', // 自动消息通知节点
  EVENT_TASK = 'EventTask', // 自动事件节点
  DATA_TASK = 'DataTask', //数据节点
  PARALLEL_NODE = 'ParallelGateway', // 并行分支节点
  DATADEAL = 'DataDeal', // 数据节点
  DATAGROUP = 'DataGroup', //
}

export enum NodeType {
  START_FLOW = 'StartFlow', // 开始节点（前端使用）
  START_EVENT = 'StartEvent', // 开始事件触发节点
  START_TIMER = 'StartTimer', // 开始定时触发节点
  START_MANUAL = 'StartManual', // 开始人工触发节点
  START_PROJECT = 'StartProject', // 手动发起
  END_FLOW = 'EndFlow', // 结束节点
  MANUAL_APPROVE = 'ManualApprove', // 人工签核节点
  MANUAL_EXECUTION = 'ManualExecution', // 人工关卡节点
  MANUAL_NOTIFICATION = 'ManualNotification', // 人工知会节点
  AUTO_HTTP = 'AutoHttp', // 自动HTTP节点
  AUTO_ESP = 'AutoEsp', // 自动ESP节点
  AUTO_CALL_EVENT = 'AutoCallEvent', // 自动事件节点
  AUTO_NOTIFY = 'AutoNotify', // 自动消息通知节点
  MSG_SEND = 'MsgSend', // 自动消息通知节点 新版
  AUTO_SCRIPT = 'AutoScript', // 自动脚本节点
  AUTO_SUB_PROCESS = 'AutoSubProcess', // 自动子流程节点
  AUTO_SUB_PROJECT = 'SubProject', // 自动子项目节点
  AUTO_WAIT = 'Wait', // 自动等待
  AUTO_EVOKE_WAIT = 'WakeWaiting', // 自动唤起等待
  CONDITION_BRANCH = 'ConditionBranch', // 条件分支节点
  CONDITION_BRANCH_START = 'ConditionBranchStart', // 条件分支开始节点
  CONDITION_BRANCH_END = 'ConditionBranchEnd', // 条件分支结束节点
  PARALLEL_BRANCH = 'ParallelGatewayConditionBranch', // 并行分支节点
  PARALLEL_BRANCH_START = 'ParallelGatewayBranchStart', // 并行分支开始节点
  PARALLEL_BRANCH_END = 'ParallelGatewayBranchEnd', // 并行分支结束节点
  GLOBAL_SETTING = 'GlobalSetting', // 全局设置
  DATA_ADD = 'add', //数据节点-新增
  DATA_UPDATE = 'update', // 数据节点更新
  DATA_GET = 'querySingle', // 获取数据
  DATA_GET_MULTI = 'queryMult', // 获取多条数据
  DATA_DELETE = 'delete', // 删除数据
  DATA_GROUPING = 'grouping', // 数据分组
}

export const NodeTypeName = {
  [NodeType.START_FLOW]: 'node-开始',
  [NodeType.START_EVENT]: 'node-事件触发',
  [NodeType.START_TIMER]: 'node-定时触发',
  [NodeType.START_PROJECT]: 'node-手动发起',
  [NodeType.START_MANUAL]: 'node-通用触发',
  [NodeType.END_FLOW]: 'node-结束',
  [NodeType.MANUAL_APPROVE]: 'node-人工签核',
  [NodeType.MANUAL_EXECUTION]: 'node-人工关卡',
  [NodeType.MANUAL_NOTIFICATION]: 'node-知会',
  [NodeType.AUTO_HTTP]: 'node-HTTP',
  [NodeType.AUTO_ESP]: 'node-ESP',
  [NodeType.AUTO_CALL_EVENT]: 'node-事件',
  [NodeType.AUTO_NOTIFY]: 'node-消息通知',
  [NodeType.MSG_SEND]: 'node-消息通知',
  [NodeType.AUTO_SCRIPT]: 'node-脚本',
  [NodeType.AUTO_SUB_PROCESS]: 'node-子流程',
  [NodeType.AUTO_SUB_PROJECT]: 'node-子项目',
  [NodeType.AUTO_WAIT]: 'node-等待',
  [NodeType.AUTO_EVOKE_WAIT]: 'node-唤起等待',
  [NodeType.CONDITION_BRANCH]: 'node-条件分支',
  [NodeType.PARALLEL_BRANCH]: 'node-并行分支',
  [NodeType.CONDITION_BRANCH_START]: 'node-条件分支开始',
  [NodeType.CONDITION_BRANCH_END]: 'node-条件分支结束',
  [NodeType.DATA_ADD]: 'node-新增数据',
  [NodeType.DATA_UPDATE]: 'node-更新数据',
  [NodeType.DATA_GET]: 'node-获取数据',
  [NodeType.DATA_GET_MULTI]: 'node-获取多条数据',
  [NodeType.DATA_DELETE]: 'node-删除数据',
  [NodeType.DATA_GROUPING]: 'node-数据分组',
  [NodeType.PARALLEL_BRANCH_START]: 'node-并行分支开始',
  [NodeType.PARALLEL_BRANCH_END]: 'node-并行分支结束',
};

// 新增集成与自动化的类型
export enum AddType {
  FLOW = 'flow', // 工作流
  MONITOR = 'monitor', // 数据侦测
}

export enum TriggerType {
  ALL = 'all', // 所有触发 = PAGE + MONITOR_RULE + EVENT + PROJECT
  PROCESS_MONITOR = 'processMonitor', // 数据侦测
  PAGE = 'page', // 人工触发
  MONITOR_RULE = 'monitorRule', // 定时触发
  EVENT = 'event', // 事件触发
  PROJECT = 'project', // 手动发起
}

export const TriggerNodeType = {
  [TriggerType.PAGE]: NodeType.START_MANUAL, // 人工触发
  [TriggerType.MONITOR_RULE]: NodeType.START_TIMER, // 定时触发
  [TriggerType.EVENT]: NodeType.START_EVENT, // 事件触发
  [TriggerType.PROJECT]: NodeType.START_PROJECT, // 手动发起
};

export const TriggerName = {
  [TriggerType.PAGE]: 'dj-通用触发', // 人工触发
  [TriggerType.MONITOR_RULE]: 'dj-定时触发', // 定时触发
  [TriggerType.EVENT]: 'dj-事件触发', // 事件触发
  [TriggerType.PROJECT]: 'dj-手动发起', // 手动发起
};

export interface IProperties {
  nodeType: string;
  nodeId: string;
  conditionType?: string; // 条件类型 common|other
  conditionNodesLevel?: any[]; // 条件优先级列表
  preManualNodesMode?: any[]; // 前序人工节点选择的模型列表
  preManualNodes?: any[]; // 前序人工节点信息
  [key: string]: any;
}

export interface ICreateNodeParams {
  nodeName: string;
  nodeType: string;
  [key: string]: any;
}

export interface IAddEdgeBetweenTwoNodes {
  sourceNode: Node;
  targetNode: Node;
  sourceDirection: string;
  targetDirection: string;
}

export interface ITransmitData {
  type: 'node' | 'edge' | 'copy';
  edgeId?: string;
  nodeId?: string;
  sourceDirection: 'top' | 'right' | 'bottom' | 'left';
  targetDirection: 'top' | 'right' | 'bottom' | 'left';
}

export enum BindFormType {
  // 自定义
  PageView = 'pageView',
  PageDesign = 'pageDesign',
}

// String,Integer,Decimal,Boolean,DateTime,BusinessObject
export enum VariableType {
  STRING = 'String',
  INTEGER = 'Integer',
  DECIMAL = 'Decimal',
  BOOLEAN = 'Boolean',
  DATETIME = 'DateTime',
  BUSINESS_OBJECT = 'BusinessObject', // 业务对象
  PROCESS = 'Process', // 系统变量
  NODE = 'Node', // 节点变量
  MODEL = 'Model', // 模型变量
  Mechanism = 'Mechanism', // 机制变量
  DTD = 'DTDVar',
  OBJECT = 'Object', // 对象
}

export type VariableTypeKey = `${VariableType}`;

// 属性面板Tabs类型
export enum PannelTabsType {
  BASE = 'base', // 基本属性面板
  DATA_MAPPING = 'dataMapping', // 数据映射面板
  ACTION = 'action', // 前后置动作
  DELAYCONFIG = 'delayConfig', // 延时配置
}

export type ManualNodeMode = {
  serviceCode?: string;
  modelCode?: string;
  pageViewCode?: string;
  code?: string;
  name?: string;
  nodeId?: string;
};

/**
 * 模型节点类型
 */
export const ModelNodes = [
  NodeType.GLOBAL_SETTING, // 全局设置-小齿轮
  NodeType.MANUAL_APPROVE,
  NodeType.MANUAL_EXECUTION,
  NodeType.MANUAL_NOTIFICATION,
  NodeType.DATA_ADD,
  NodeType.DATA_UPDATE,
  NodeType.DATA_GET,
  NodeType.DATA_GET_MULTI,
  NodeType.DATA_DELETE,
];

export enum EType {
  // 无
  NONE = 0,
  // 模型
  MODEL = 1,
  // ESP
  ESP = 2,
}
