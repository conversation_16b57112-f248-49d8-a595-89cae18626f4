import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { AppService } from '../../../../../../apps/app.service';
import { AdUserService } from 'pages/login/service/user.service';

@Injectable()
export class ManualApprovePropertyService {
  private adesignerUrl: string;
  bindFormChange$: Subject<any> = new Subject();
  isTenantActive = false; // 是否租户激活
  constructor(
    private systemConfigService: SystemConfigService,
    public appService: AppService,
    private http: HttpClient,
    public adUserService: AdUserService,
  ) {
    this.systemConfigService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }
  /**
   * 查询当前解决方案下的基础资料
   * @returns
   */
  loadBasicByAppCode(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getActivityListByPatternAndApplication`;
    return this.http.get(url, {
      params: { pattern: 'DATA_ENTRY', application: this.appService?.selectedApp?.code },
      headers: this.isTenantActive ? {level: 'develop'} : {}
    });
  }

  /**
   * 根据作业code去查询actionId
   * @returns
   */
  getActionIdByCode(code): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/getActionIdByCode`;
    return this.http.get(url, { params: { code: code } });
  }
}
