.global-content {
  height: 100%;
  overflow-y: auto;
  font-family: <PERSON>Fang SC;
  font-weight: normal;

  ::ng-deep .ant-tabs-content {
    min-height: 260px;
  }
  .nz-form-item-content {
    margin-bottom: 6px;
  }
  ::ng-deep {
    .ant-input.disabled {
      background-color: #f7f7f7;
      color: #ccc;
    }
  }
  .form-item {
    margin-bottom: 0;

    &.switch-form-item {
      display: flex;
      align-items: center;
      position: relative;
    }

    .item-title {
      color: #333;
      font-size: 13px;
      margin-bottom: 8px;
      display: inline-block;
      font-weight: normal;

      .item-required {
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
      }
      &.switch-item-title {
        margin-bottom: 0;
      }
    }

    .hasError {
      ::ng-deep .ant-input-affix-wrapper {
        border: 1px solid #ea3d46;
      }
    }

    .hasErrorBorder {
      border: 1px solid #ea3d46;
    }

    .error {
      color: #ff4d4f;
      font-size: 12px;
    }

    ::ng-deep .ath-content {
      font-weight: normal;
    }
    .edit-icon {
      color: #6a4cff;
      cursor: pointer;
      position: absolute;
      right: 0;
    }

    .custom-project-card-switch {
      margin-bottom: 4px;
    }
    .extend-setting-icon {
      color: #6a4cff;
      &.disabled {
        color: #ccc;
      }
    }
  }
  .form-item-time {
    background: #f7f8fe;
    border-radius: 4px;
    padding: 16px;
    ::ng-deep .ant-form-item-explain.ant-form-item-explain-error {
      position: absolute;
      bottom: -21px;
    }
    .field-paramType.disabled {
      background-color: #f9f9f9;
      color: #ccc;
    }
  }
  .time-type-select {
    min-width: 70px;
    ::ng-deep .ant-select-selector {
      height: 32px;
    }
  }
  .multi-form-item {
    ::ng-deep .ant-input-number {
      height: 32px;
      border-radius: 4px 0 0 4px;
    }
  }
  .flex-form-item {
    align-items: center;
    display: flex;
    span {
      font-size: 13px;
      font-weight: 400;
    }

    .page-select {
      display: flex;
      flex: 1;
      position: relative;
      .select-loading {
        display: inline-flex;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background-color: rgba(255, 255, 255, 0.7);
        align-items: center;
        padding-top: 5px;
        padding-left: 20px;
      }
    }
  }
  .question-icon {
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    line-height: 17px;
    vertical-align: middle;
  }
  .custom-card-checkbox {
    font-weight: normal;
  }
}
