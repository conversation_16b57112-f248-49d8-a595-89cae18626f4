<div class="grouping-data-property-root submit-form">
  <div class="header">
    <span>{{ 'dj-数据分组' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: nameLang?.name,
                  needLang: true,
                  readOnly: disabledAll
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>

      <!-- 数据设置面板 -->
      <nz-collapse-panel [nzHeader]="'dj-数据设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control
            [nzAutoTips]="errorTips"
            [ngClass]="{
              'ant-form-item-has-error': !dataFormGroup.get('dataSourceCode').value
            }"
          >
            <div class="form-item" [class.hasError]="!dataFormGroup.get('dataSourceCode').value">
              <div class="item-title">
                {{ 'dj-分组数据来源' | translate }}
                <span class="item-required">*</span>
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  aria-hidden="true"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-分组数据来源描述' | translate"
                >
                </i>
              </div>

              <app-variable-select-input
                formControlName="dataSourceSuffix"
                [modelRoot]="true"
                [inputable]="true"
                [sufixKey]="'dataSourceSuffix'"
                [rootMatchKey]="'variableCode'"
                [variableRange]="variableRange"
                [nodeId]="nodeId"
                [item]="item"
                (onChanged)="handleValueChanged($event)"
              >
              </app-variable-select-input>

              <!-- <nz-input-group [prefix]="prefixTpl" [nzSuffix]="modalTpl">
                <input
                  formControlName="dataSourceSuffix"
                  (ngModelChange)="handleChangeDataSourceSuffix()"
                  nz-input
                  type="text"
                  [placeholder]="getGroupDataSourcePlaceholder()"
                  [readonly]="!dataFormGroup.get('dataSourceCode').value"
                  nz-tooltip
                  nzTooltipTrigger="hover"
                  nzTooltipPlacement="topLeft"
                  [nzTooltipTitle]="getGroupDataSourceTooltip()"
                />
              </nz-input-group> -->
              <span *ngIf="!dataFormGroup.get('dataSourceCode').value" style="color: red">{{
                'dj-必填' | translate
              }}</span>
              <!-- <ng-template #prefixTpl>
                <ng-container *ngIf="dataFormGroup.get('dataSourceCode').value">
                  <span
                    nz-tooltip
                    nzTooltipTrigger="hover"
                    nzTooltipPlacement="topLeft"
                    [nzTooltipTitle]="getGroupDataSourceTooltip()"
                    [style]="{
                      maxWidth: '165px',
                      'white-space': 'nowrap',
                      overflow: 'hidden',
                      'text-overflow': 'ellipsis'
                    }"
                  >
                    {{ dataFormGroup.get('dataSourceCode').value }}
                  </span>
                </ng-container>
              </ng-template>
              <ng-template #modalTpl>
                <i class="icon" adIcon [iconfont]="'icon-kaichuang'" (click)="showDataSourceModal = true"></i>
              </ng-template> -->
            </div>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-分组类型' | translate }}
                <span class="item-required">*</span>
              </div>
              <ad-select
                formControlName="groupType"
                (ngModelChange)="handleChangGroupType()"
                [nzPlaceHolder]="'dj-请选择' | translate"
                style="width: 100%"
                [nzAllowClear]="false"
              >
                <ad-option
                  [nzLabel]="groupTypeItem.label"
                  [nzValue]="groupTypeItem.value"
                  *ngFor="let groupTypeItem of groupTypeList"
                ></ad-option>
              </ad-select>
            </div>
          </nz-form-control>
        </nz-form-item>
        <ng-container *ngIf="'byCondition' === dataFormGroup.get('groupType').value">
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-条件筛选器' | translate }}
                  <span class="item-required">*</span>
                </div>
              </div>
              <nz-radio-group formControlName="filterType" (ngModelChange)="handleChangeFilterType()">
                <label nz-radio nzValue="filter" [nzDisabled]="filterDisabled">{{ 'dj-UI模式' | translate }}</label>
                <label nz-radio nzValue="script">{{ 'dj-表达式模式' | translate }}</label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
          <div *ngIf="'filter' === dataFormGroup.get('filterType').value" style="margin-bottom: 8px">
            <app-data-view-filter
              [hideTip]="true"
              [allFields]="allFields"
              [currentLang]="currentLang"
              [groupConditionList]="groupingConditionList"
              [mulitableMode]="true"
              [isRequired]="true"
              [showTitle]="false"
              [editable]="disabledAll"
              [dataViewFilterError]="dataViewFilterError"
              (dataViewFilterChange)="handleDataViewFilterChange($event)"
            ></app-data-view-filter>
          </div>
          <ng-container *ngIf="'script' === dataFormGroup.get('filterType').value">
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <span>
                  {{ 'dj-表达式设置' | translate }}
                  <i
                    adIcon
                    iconfont="iconshuomingwenzi"
                    class="question-icon"
                    nzTooltipTrigger="hover"
                    nz-tooltip
                    [nzTooltipTitle]="'dj-表达式模式，多个条件分组，请以分号分隔' | translate"
                  >
                  </i>
                </span>
                <nz-input-group [nzSuffix]="suffixIcon">
                  <input
                    readonly
                    nz-input
                    (dblclick)="handleOpenScript()"
                    formControlName="groupingQueryConditionScript"
                  />
                </nz-input-group>
                <ng-template #suffixIcon>
                  <i adIcon iconfont="icongaodaima" (click)="handleOpenScript()"></i>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </ng-container>
        </ng-container>
        <ngContainer *ngIf="'byKey' === dataFormGroup.get('groupType').value">
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-字段设置' | translate }}
                  <span class="item-required">*</span>
                </div>
                <nz-input-group>
                  <input
                    formControlName="groupData"
                    nz-input
                    type="text"
                    nz-tooltip
                    nzTooltipTrigger="hover"
                    nzTooltipPlacement="topLeft"
                    [nzTooltipTitle]="getGroupDataTooltip()"
                  />
                </nz-input-group>
              </div>
            </nz-form-control>
          </nz-form-item>
        </ngContainer>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>

<app-select-data-source-modal
  *ngIf="showDataSourceModal"
  showModal="showDataSourceModal"
  [nodeId]="nodeId"
  [initDataSource]="data.groupDataSource"
  (close)="showDataSourceModal = false"
  (changeVariable)="handleChangeVariable($event)"
>
</app-select-data-source-modal>

<!--script脚本编辑器弹窗-->
<app-script-editor
  *ngIf="scriptModalVisible"
  [scriptModal]="scriptModalVisible"
  [script]="groupingQueryConditionScript"
  (confirm)="handleCloseScript('confirm', $event)"
  (close)="handleCloseScript('close', $event)"
></app-script-editor>
