import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';
import { ViewStoreService } from 'pages/app-model-driven/integrated-automation/view-designer/service/store.service';
import { ViewGraphService } from 'pages/app-model-driven/integrated-automation/view-designer/service/graph.service';
import { cloneDeep } from 'lodash';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';
import { VariableType } from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { isNotNone } from 'common/utils/core.utils';

export enum EVariableRange {
  CUSTOM = 1 << 0,
  NODE = 1 << 1,
  MODEL = 1 << 2,
  SYSTEM = 1 << 3,
  MECHANISM = 1 << 4,
  BUSINESS = 1 << 5,
  DTD = 1 << 6,
}

@Component({
  selector: 'variable-modal',
  templateUrl: './variable-modal.component.html',
  styleUrls: ['./variable-modal.component.less'],
})
export class VariableModalComponent implements OnInit {
  @Input() variableRange: EVariableRange =
    EVariableRange.CUSTOM |
    EVariableRange.NODE |
    EVariableRange.MODEL |
    EVariableRange.SYSTEM |
    EVariableRange.MECHANISM |
    EVariableRange.BUSINESS |
    EVariableRange.DTD;
  @Input() showModal: boolean;
  @Input() nodeId: string;
  @Input() initVariable: any;
  @Input() modelRoot: boolean = false; // 是不是只要展示模型的根
  @Input() includeGlobalModel: boolean = true; // 是否包含流程的回掉模型

  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() changeVariable: EventEmitter<any> = new EventEmitter();

  originFieldValueList: any[];
  fieldValueList: any[];
  searchValue: string;
  checkedValue: string = '';
  variable: {
    dataType: string;
    originDataType: string;
    variableCode: string;
    variableType: string;
    variablePath: string;
    modelCode?: string;
    serviceCode?: string;
    variableAlias?: string;
    extendsInfo?: any;
  };
  variableDesc: string = '';
  loading: boolean = true;

  mapOfExpandedData: any = {};

  constructor(
    private viewStoreService: ViewStoreService,
    private viewGraphService: ViewGraphService,
    private viewApiService: ViewApiService,
    private translate: TranslateService,
    private language: LocaleService,
  ) {}

  async ngOnInit(): Promise<void> {
    const {
      propertiesObj,
      originalFlowData: { code },
    } = this.viewStoreService.state;
    const process = propertiesObj?.[code];
    const {
      customVariables,
      systemVariable,
      businessObjectVariables,
      mechanismVariables,
      modelVariables,
      dtdVariable,
    } = process;
    this.loading = true;
    this.originFieldValueList = [];

    // 自定义变量+对象变量
    let allCustomVariablesData = {
      title: this.translate.instant('dj-自定义变量'),
      expand: true,
      key: 'a',
      children: [],
    };
    // 自定义变量
    if ((customVariables ?? [])?.length > 0 && (this.variableRange & EVariableRange.CUSTOM) === EVariableRange.CUSTOM) {
      const customVariablesData = this.getCustomVariablesData(customVariables);
      allCustomVariablesData.children = [...allCustomVariablesData.children, ...(customVariablesData?.children ?? [])];
    }
    // 对象变量
    if (
      (businessObjectVariables ?? [])?.length > 0 &&
      (this.variableRange & EVariableRange.BUSINESS) === EVariableRange.BUSINESS
    ) {
      const fieldsGroupList = await this.handleExtendDataChange(businessObjectVariables, 'modeCode', 'varName');
      const businessObjectVariablesData = await this.getBusinessObjectVariablesData(fieldsGroupList);
      allCustomVariablesData.children = [
        ...allCustomVariablesData.children,
        ...(businessObjectVariablesData?.children ?? []),
      ];
    }
    // 机制变量
    if (
      (mechanismVariables ?? []).length > 0 &&
      (this.variableRange & EVariableRange.MECHANISM) === EVariableRange.MECHANISM
    ) {
      const mechanismVariablesData = this.getMechanismVariablesData(mechanismVariables);
      allCustomVariablesData.children = [
        ...allCustomVariablesData.children,
        ...(mechanismVariablesData?.children ?? []),
      ];
    }
    // 合并
    if (allCustomVariablesData?.children?.length > 0) {
      this.originFieldValueList.push(allCustomVariablesData);
    }

    // DTD变量
    if ((dtdVariable ?? [])?.length > 0 && (this.variableRange & EVariableRange.DTD) === EVariableRange.DTD) {
      const dtdVariableData = this.getDTDVariableData(dtdVariable);
      this.originFieldValueList.push(dtdVariableData);
    }
    // 系统变量
    if ((systemVariable ?? [])?.length > 0 && (this.variableRange & EVariableRange.SYSTEM) === EVariableRange.SYSTEM) {
      const systemVariableData = this.getSystemVariableData(systemVariable);
      this.originFieldValueList.push(systemVariableData);
    }
    // 前置节点变量
    const _nodeId = this.nodeId?.includes('_') ? this.nodeId.split('_')[1] : this.nodeId;
    const node: any = this.viewGraphService.graph.getCellById(_nodeId);
    const preNodes = isNotNone(node) ? this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node }) ?? [] : [];
    if ((preNodes ?? [])?.length > 0 && (this.variableRange & EVariableRange.NODE) === EVariableRange.NODE) {
      const nodeVariablesData = this.getNodeVariablesData(preNodes);
      this.originFieldValueList.push(nodeVariablesData);
    }
    // 模型变量
    if ((this.variableRange & EVariableRange.MODEL) === EVariableRange.MODEL) {
      const preNodesModels = this.getPreNodesModel(preNodes, propertiesObj, process, modelVariables);
      if (preNodesModels.length) {
        const nodeMap = preNodes.reduce((pre, curr) => {
          if (propertiesObj[curr._nodeId]?.bindForm?.modelCode) {
            const bindForm = propertiesObj[curr._nodeId]?.bindForm;
            const key = `${bindForm.modelCode}&${bindForm.serviceCode}`;
            pre[key] = propertiesObj[curr._nodeId];
          }
          return pre;
        }, {});
        const nodeInfo = preNodesModels.reduce((pre, curr) => {
          if (nodeMap[curr.uniqueKey]) {
            pre[curr.uniqueKey] = {
              nodeId: nodeMap[curr.uniqueKey].id,
              noteSubType: nodeMap[curr.uniqueKey]._nodeType,
              noteType: nodeMap[curr.uniqueKey].type,
            };
          }
          return pre;
        }, {});
        const modelFieldsGroupList = await this.handleExtendDataChange(preNodesModels, 'modelCode', 'uniqueKey');
        const modelVariablesData = await this.getModelVariablesData(modelFieldsGroupList, nodeInfo);
        this.originFieldValueList.push(modelVariablesData);
      }
    }
    this.handleOnSearch();
    this.loading = false;
    this.initCheckedValue();
  }

  private handleData = () => {
    const convertTreeToList = (root) => {
      const stack = [];
      const array = [];
      const hashMap = {};
      stack.push({ ...root, level: 0 });

      while (stack.length !== 0) {
        const node = stack.pop()!;
        if (!hashMap[node.key]) {
          hashMap[node.key] = true;
          array.push(node);
        }
        if (node.children) {
          for (let i = node.children.length - 1; i >= 0; i--) {
            stack.push({
              ...node.children[i],
              level: node.level! + 1,
              parent: node,
            });
          }
        }
      }
      return array;
    };
    this.fieldValueList.forEach((item) => {
      this.mapOfExpandedData[item.key] = convertTreeToList(item);
    });
  };

  collapse(array: any[], data: any, $event: boolean): void {
    data.expand = $event;
    if (!data.expand) {
      if (data.children?.length) {
        data.children.forEach((d) => {
          const target = array.find((a) => a.key === d.key);
          if (target) {
            target.expand = false;
            this.collapse(array, target, false);
          }
        });
      } else {
        return;
      }
    }
  }

  //#region 变量处理

  /**
   * 获取前置节点绑定的模型
   * @param preNodes
   * @param propertiesObj
   * @param process
   * @param modelVariables
   * @returns
   */
  private getPreNodesModel(preNodes: any[], propertiesObj: any, process: any, modelVariables: any[]): any[] {
    const preNodesModels = preNodes
      .map((preNode) => {
        const bindForm = propertiesObj[preNode._nodeId].bindForm;
        if (bindForm) return `${bindForm.modelCode}&${bindForm.serviceCode}`;
        return null;
      })
      .filter((e) => !!e);
    if (process.bindForm && this.includeGlobalModel) {
      preNodesModels.push(`${process.bindForm.modelCode}&${process.bindForm.serviceCode}`);
    }
    return modelVariables.filter((e) => preNodesModels.includes(e.uniqueKey));
  }

  initCheckedValue() {
    const { variableCode, variableType, variablePath } = this.initVariable;
    this.variable = this.initVariable;
    if (variableCode || variablePath) {
      if (['businessObjectVariable', 'modelVariable'].includes(variableType)) {
        this.checkedValue = variablePath || variableCode;
      } else {
        this.checkedValue = variableCode;
      }
    }
  }

  /**
   * 自定义变量
   * @param customVariables
   * @returns
   */
  getCustomVariablesData(customVariables: any[]): any {
    const customVariablesData = {
      title: this.translate.instant('dj-自定义变量'),
      expand: true,
      children: (customVariables ?? []).map((c) => {
        return {
          key: c.varName,
          fieldName: c.description || '-',
          title: c.description || '-',
          fieldType: 'customVariable',
          dataType: c.dataType,
          originDataType: c.dataType,
          actionId: c?.actionId,
          isLeaf: true,
          expand: true,
          children: [],
        };
      }),
    };
    return customVariablesData;
  }

  getDTDVariableData(dtdVariable: any[]): any {
    const customVariablesData = {
      title: this.translate.instant('dj-DTD变量'),
      expand: true,
      children: (dtdVariable ?? []).map((c) => {
        return {
          key: c.varName,
          fieldName: c.description || '-',
          title: c.description || '-',
          fieldType: 'dtdVariable',
          dataType: VariableType.DTD,
          originDataType: c.dataType,
          isLeaf: true,
          expand: true,
          children: [],
        };
      }),
    };
    return customVariablesData;
  }

  /**
   * 机制变量
   * @param mechanismVariables
   * @returns
   */
  getMechanismVariablesData(mechanismVariables: any[]): any {
    const mechanismVariablesData = {
      title: this.translate.instant('dj-自定义变量'),
      expand: true,
      key: 'c',
      children: (mechanismVariables ?? []).map((c) => {
        return {
          key: c.varName,
          title: c.varName,
          actionId: c.actionId,
          fieldName: c.description,
          fieldType: 'mechanismVariable',
          dataType: VariableType.Mechanism,
          originDataType: c.dataType,
          isLeaf: true,
          expand: true,
          children: [],
        };
      }),
    };
    return mechanismVariablesData;
  }

  /**
   * 系统变量
   * @param systemVariable
   * @returns
   */
  getSystemVariableData(systemVariable: any): any {
    const systemVariableData = {
      title: this.translate.instant('dj-系统变量'),
      expand: true,
      key: 'f',
      children: (systemVariable ?? []).map((s) => {
        return {
          key: s.varName,
          title: s.description,
          fieldName: s.description,
          fieldType: 'systemVariable',
          dataType: VariableType.PROCESS,
          originDataType: s.dataType,
          isLeaf: true,
          expand: true,
          children: [],
        };
      }),
    };
    return systemVariableData;
  }

  /**
   * 节点变量
   * @returns
   */
  getNodeVariablesData(preNodes: any[]): any {
    let nodeVariablesData = {
      title: this.translate.instant('dj-节点变量'),
      expand: true,
      key: 'd',
      children: [],
    };
    const { propertiesObj } = this.viewStoreService.state;
    nodeVariablesData['children'] = preNodes.map((n) => {
      return {
        key: n.nodeId,
        fieldName: n.name,
        title: n.name,
        modelCode: propertiesObj[n._nodeId]?.bindForm?.modelCode,
        serviceCode: propertiesObj[n._nodeId]?.bindForm?.serviceCode,
        fieldType: 'nodeVariable',
        dataType: VariableType.NODE,
        originDataType: n.dataType,
        isLeaf: true,
        expand: true,
        children: [],
      };
    });
    return nodeVariablesData;
  }

  /**
   * 对象变量
   * @param businessObjectVariables
   */
  async getBusinessObjectVariablesData(fieldsGroupList: any[]): Promise<any> {
    const language = this.language?.currentLanguage || 'zh_CN';
    const businessObjectVariablesData = {
      title: this.translate.instant('dj-对象变量'),
      expand: true,
      key: 'b',
      children: [],
    };
    businessObjectVariablesData['children'] = fieldsGroupList.map((f) => {
      const { serviceCode } = f;
      const { variableCode, lang, modelFileInfos = [], modelCode } = f.modelFieldInfoVo;
      if (this.modelRoot) {
        return {
          varDescription: f.varDescription,
          varName: variableCode,
          key: modelCode,
          modelCode,
          serviceCode,
          isMainTable: true,
          dataType: VariableType.BUSINESS_OBJECT,
          originDataType: f.dataType,
          fieldName: `${lang?.comment?.[language]}`,
          title: `${lang?.comment?.[language]}`,
          fieldType: 'businessObjectVariable',
          isLeaf: true,
          expand: true,
          variableCode,
          variableAlias: variableCode,
          children: [],
        };
      }
      return {
        varDescription: f.varDescription,
        varName: variableCode,
        title: `${lang?.comment?.[language]}`,
        expand: true,
        key: modelCode,
        modelCode,
        serviceCode,
        isMainTable: true,
        dataType: VariableType.BUSINESS_OBJECT,
        originDataType: f.dataType,
        fieldName: `${lang?.comment?.[language]}`,
        fieldType: 'businessObjectVariable',
        variableAlias: variableCode,
        variableCode,
        children: this.ergodicList(
          f.dataType,
          modelFileInfos,
          modelCode,
          serviceCode,
          VariableType.BUSINESS_OBJECT,
          'businessObjectVariable',
          variableCode,
          [],
        ),
      };
    });
    return businessObjectVariablesData;
  }

  /**
   * 模型变量
   * @param businessObjectVariables
   */
  async getModelVariablesData(fieldsGroupList: any[], nodeInfo: any): Promise<any> {
    const language = this.language?.currentLanguage || 'zh_CN';
    const modelVariablesData = {
      title: this.translate.instant('dj-模型变量'),
      expand: true,
      key: 'e',
      children: [],
    };
    modelVariablesData['children'] = fieldsGroupList.map((f) => {
      const { serviceCode } = f;
      const { variableCode, lang, modelFileInfos = [], modelCode } = f.modelFieldInfoVo;
      if (this.modelRoot) {
        return {
          dataType: VariableType.MODEL,
          originDataType: f.dataType,
          key: modelCode,
          isMainTable: true,
          modelCode,
          serviceCode,
          fieldName: `${lang?.comment?.[language]}`,
          title: `${lang?.comment?.[language]}`,
          fieldType: 'modelVariable',
          isLeaf: true,
          expand: true,
          variableCode,
          variableAlias: variableCode,
          children: [],
          _nodeInfo: nodeInfo[variableCode],
        };
      }
      return {
        title: `${lang?.comment?.[language]}`,
        expand: true,
        dataType: VariableType.MODEL,
        originDataType: f.dataType,
        key: modelCode,
        isMainTable: true,
        modelCode,
        serviceCode,
        fieldName: `${lang?.comment?.[language]}`,
        fieldType: 'modelVariable',
        variableAlias: variableCode,
        variableCode,
        _nodeInfo: nodeInfo[variableCode],
        children: this.ergodicList(
          f.dataType,
          modelFileInfos,
          modelCode,
          serviceCode,
          VariableType.MODEL,
          'modelVariable',
          variableCode,
          [modelCode],
          nodeInfo[variableCode],
        ),
      };
    });
    return modelVariablesData;
  }

  private ergodicList = (
    originDataType,
    list: any[],
    modelCode: string,
    serviceCode: string,
    dataType: VariableType,
    fieldType: string,
    variableCode: string,
    parentKey: string[],
    nodeInfo?: any,
    index: number = 1,
  ) => {
    const prefix = parentKey.slice(1).join('.');
    const returnList = list.map((item) => {
      const key = (prefix ? prefix + '.' : prefix) + item.key;
      return {
        ...item,
        key: key,
        title: item.fieldName,
        modelCode,
        serviceCode,
        dataType,
        originDataType,
        fieldType,
        variableCode,
        variableAlias: variableCode,
        expand: !!item.children?.length,
        _nodeInfo: nodeInfo,
        children: item.children?.length
          ? this.ergodicList(
              item.dataType,
              item.children,
              modelCode,
              serviceCode,
              dataType,
              fieldType,
              variableCode,
              key.split('.').slice(index),
              nodeInfo,
              index + 1,
            )
          : undefined,
      };
    });
    return returnList;
  };

  //#endregion

  /**
   * 处理变量的扩展信息
   * @param variable
   * @returns
   */
  async handleExtendDataChange(variables: any[], key1: string, key2: string) {
    if (!variables.length) return [];
    const params = variables.map((item) => {
      const base: any = {
        serviceCode: item.serviceCode,
        modelCode: item[key1],
        variableCode: item[key2],
        isHasChildren: true,
      };
      return base;
    });
    const res = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (res.code === 0)
      return (res.data ?? []).map((e, i) => ({
        ...e,
        varDescription: variables[i].description,
      }));
    return [];
  }

  /**
   * 搜索
   */
  handleOnSearch() {
    if (!this.searchValue) {
      this.fieldValueList = cloneDeep(this.originFieldValueList);
    } else {
      this.fieldValueList = this.filterTreeData(cloneDeep(this.originFieldValueList), this.searchValue.trim());
    }
    this.handleData();
  }

  /**
   * 过滤器
   * @param data
   * @param searchValue
   * @returns
   */
  filterTreeData(data: any[], searchValue: string): any[] {
    return data
      .map((node) => {
        const filteredNode = { ...node };
        if (node.children && node.children.length > 0) {
          filteredNode.children = this.filterTreeData(node.children, searchValue);
          filteredNode.expand = true;
        }
        if (
          filteredNode.fieldName?.includes(searchValue) ||
          filteredNode.key?.includes(searchValue) ||
          (filteredNode.children && filteredNode.children.length > 0)
        ) {
          return filteredNode;
        }
        return null;
      })
      .filter(Boolean);
  }

  /**
   *  清空搜索
   */
  handleClearSearch() {
    this.searchValue = '';
    this.handleOnSearch();
  }

  changeCheckboxValue(node, $event): void {
    if ($event) {
      this.changeRadioValue(node);
    } else {
      //@ts-ignore
      this.variable = {};
      this.variableDesc = '';
    }
  }

  /**
   * 单选
   * @param node
   */
  changeRadioValue(node) {
    this.checkedValue = node.key;
    const {
      key,
      actionId,
      fieldType: variableType,
      variableAlias,
      dataType,
      originDataType,
      variableCode,
      fieldName,
      modelCode,
      serviceCode,
    } = node;
    if (['businessObjectVariable', 'modelVariable'].includes(variableType)) {
      this.variable = {
        dataType,
        originDataType,
        variableCode,
        variableType,
        variablePath: key,
        modelCode,
        serviceCode,
        variableAlias,
        extendsInfo: node,
      };
    } else {
      this.variable = {
        dataType,
        originDataType,
        variableCode: key,
        variableType,
        variablePath: 'mechanismVariable' === variableType ? actionId : '',
        modelCode,
        serviceCode,
        variableAlias,
        extendsInfo: node,
      };
    }
    this.variableDesc = fieldName;
  }

  /**
   * 关闭
   */
  handleClose(): void {
    this.close.emit();
  }

  /**
   * 确认
   */
  handleSubmit(): void {
    if (this.checkedValue !== this.initVariable.variableCode) {
      this.changeVariable.emit({ variable: this.variable, variableDesc: this.variableDesc });
    } else {
      this.close.emit();
    }
  }

  checkLeafIsShow(item: any): boolean {
    if (item.level > 0 && !item.children?.length) {
      if (this.checkedValue === item?.parent?.key && this.variable?.variableType === item?.parent?.fieldType) {
        return false;
      }
      return true;
    }
    return false;
  }
}
