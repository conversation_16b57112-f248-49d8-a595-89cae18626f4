.global-content {
  height: 100%;
  overflow-y: auto;
  font-family: <PERSON>Fang SC;
  font-weight: normal;

  ::ng-deep .ant-tabs-content {
    min-height: 260px;
  }
  .nz-form-item-content {
    margin-bottom: 6px;
  }
  .form-item {
    margin-bottom: 0;

    .item-title {
      color: #333;
      font-size: 13px;
      margin-bottom: 8px;
      display: inline-block;
      font-weight: normal;

      .item-required {
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
      }
    }

    ::ng-deep nz-input-group.disabled {
      background-color: rgb(249, 249, 249);
      & > .ant-input {
        background-color: transparent;
        color: #ccc;
      }
    }

    .hasError {
      ::ng-deep .ant-input-affix-wrapper {
        border: 1px solid #ea3d46;
      }
    }

    .hasErrorBorder {
      border: 1px solid #ea3d46;
    }

    .error {
      color: #ff4d4f;
      font-size: 12px;
    }

    ::ng-deep .ath-content {
      font-weight: normal;
    }
  }
  .question-icon {
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    line-height: 17px;
    vertical-align: middle;
  }
  .form-item-tip {
    height: 48px;
    background: rgba(240, 98, 24, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: self-start;
    font-size: 12px;
    color: #fd5609;
    padding: 8px;
    margin-top: 6px;
  }
}
