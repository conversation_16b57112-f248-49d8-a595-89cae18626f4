<div class="auto-evoke-wait-root submit-form">
  <div class="header">
    <span>{{ 'node-唤起等待' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true,
                  notAllowClear: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: this.nameLang?.name,
                  needLang: true,
                  readOnly: disabledAll
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
      <nz-collapse-panel [nzHeader]="'dj-节点设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-选择等待节点' | translate }}
                <span class="item-required">*</span>
              </div>
              <nz-input-group [nzSuffix]="suffixIcon">
                <input readonly nz-input formControlName="waitnode" [placeholder]="'dj-请选择' | translate" />
              </nz-input-group>
            </div>
            <ng-template #suffixIcon>
              <i
                adIcon
                iconfont="iconkaichuang"
                aria-hidden="true"
                class="window-icon iconfont"
                (click)="handleOpenAction()"
              >
              </i>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>

<!--action开窗组件-->
<app-auto-evoke-wait-action-modal
  *ngIf="actionModal"
  [transferModal]="actionModal"
  [transferData]="actionData"
  labelType="EspAction"
  [nodeId]="nodeId"
  (callBack)="handleSelectAction($event)"
  (closeModal)="actionModal = false"
>
