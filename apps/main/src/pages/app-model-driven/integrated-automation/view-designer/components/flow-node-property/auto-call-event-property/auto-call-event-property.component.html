<div class="auto-call-event-property-root submit-form">
  <div class="header">
    <span>{{ 'dj-事件' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <!--节点id-->
        <div class="form-item">
          <div class="item-title">
            {{ 'dj-节点id' | translate }}
            <span class="item-required">*</span>
          </div>
          <app-component-input
            [attr]="{
              name: '节点id',
              required: true,
              needLang: false,
              readOnly: true
            }"
            style="width: 100%"
            formControlName="id"
            [value]="dataFormGroup.get('id')?.value"
            ngDefaultControl
          >
          </app-component-input>
        </div>

        <!--节点名称-->
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzErrorTip]="translate.instant('dj-请输入')">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: nameLang?.name,
                  needLang: true,
                  readOnly: disabledAll
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <!--节点名称-->
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzErrorTip]="translate.instant('dj-请输入')">
            <!--请求脚本-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-事件入参设置' | translate }}
                <span class="item-required">*</span>
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  aria-hidden="true"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="
                    'dj-指定事件入参，默认为当前流程对应的业务对象。如需自定义可以通过修改脚本字段重新指定' | translate
                  "
                >
                </i>
              </div>
              <textarea nz-input formControlName="_eventBody" (dblclick)="showScriptModal('_eventBody')"></textarea>
            </div>
          </nz-form-control>
        </nz-form-item>

        <!--事件-->
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzErrorTip]="translate.instant('dj-请输入')">
            <!--选择事件-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-选择事件' | translate }}
                <span class="item-required">*</span>
              </div>
              <nz-input-group [nzSuffix]="suffixIcon" class="service-name">
                <input readonly nz-input formControlName="_eventName" [placeholder]="'dj-请选择' | translate" />
              </nz-input-group>
              <ng-template #suffixIcon>
                <i
                  adIcon
                  iconfont="iconkaichuang"
                  aria-hidden="true"
                  class="window-icon iconfont"
                  (click)="showEventList()"
                >
                </i>
              </ng-template>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>

<!--事件选择组件-->
<app-select-event-modal
  *ngIf="eventListVisible"
  [transferModal]="eventListVisible"
  [eventId]="dataFormGroup.get('_eventId')?.value || ''"
  (closeModal)="eventListVisible = false"
  (callBack)="handleChooseEvent($event)"
></app-select-event-modal>
<app-script-editor
  *ngIf="scriptModal"
  [scriptModal]="scriptModal"
  [script]="scriptData"
  (confirm)="handleCloseSript('confirm', $event)"
  (close)="handleCloseSript('close', $event)"
></app-script-editor>
