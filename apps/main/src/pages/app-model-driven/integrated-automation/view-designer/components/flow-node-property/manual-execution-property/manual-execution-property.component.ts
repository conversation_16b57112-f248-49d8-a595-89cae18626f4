import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ViewStoreService } from '../../../service/store.service';
import { ManualExecutionPropertyService } from './manual-execution-property.service';
import { ExecutionCardSettingComponent } from './components/card-setting/card-setting.component';
import { CheckpointSettingComponent } from './components/checkpoint-setting/checkpoint-setting.component';

@Component({
  selector: 'app-manual-execution-property',
  templateUrl: './manual-execution-property.component.html',
  styleUrls: ['./manual-execution-property.component.less'],
  providers: [ManualExecutionPropertyService],
})
export class ManualExecutionPropertyComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  @ViewChild('cardSetting') cardSetting: ExecutionCardSettingComponent;
  @ViewChild('checkpointSetting') checkpointSetting: CheckpointSettingComponent;

  @Input() uiKey: string;
  @Output() clearUiKey: EventEmitter<any> = new EventEmitter();

  tabIndex: number = 0;

  constructor(public translate: TranslateService, private viewStoreService: ViewStoreService) {}

  ngOnInit(): void {}

  handleChangeTab(data) {
    this.tabIndex = data?.index;
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  handleClearUiKey(): void {
    this.clearUiKey.emit();
  }

  handleChangeData(e) {
    const { data, valid, componentType } = e;
    let mergeDataValidate: any = {};
    if (componentType === 'cardSetting') {
      mergeDataValidate = this.checkpointSetting?.getFormValidate();
    } else {
      mergeDataValidate = this.cardSetting?.getFormValidate();
    }
    const isVerificationPassed = this.viewStoreService.transformVerificationPassed(valid && mergeDataValidate);
    const returnData = {
      data,
      isVerificationPassed,
    };
    this.changeData.emit(returnData);
  }

  handleCopyData(info: any) {
    this.cardSetting?.handleCopyData(info?.data, info?.oldKey);
    this.checkpointSetting?.handleCopyData(info?.data);
  }
}
