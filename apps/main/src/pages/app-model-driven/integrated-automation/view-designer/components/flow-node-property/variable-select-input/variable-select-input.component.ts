import {
  Component,
  EventEmitter,
  forwardRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { isNone, isNotNone } from 'common/utils/core.utils';
import { isEmpty, isEqual, omit } from 'lodash';
import { VariableService } from '../../../service/variable.service';
import { ViewStoreService } from '../../../service/store.service';
import { ViewGraphService } from '../../../service/graph.service';
import { ViewApiService } from '../../../service/api.service';
import { VariableType } from '../../../config/typings';
import { EVariableRange } from './components/variable-modal/variable-modal.component';
import { Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ergodicList, matchModelPath } from 'pages/app-model-driven/utils/utils';

@Component({
  selector: 'app-variable-select-input',
  templateUrl: './variable-select-input.component.html',
  styleUrls: ['./variable-select-input.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => VariableSelectInputComponent),
      multi: true,
    },
  ],
})
export class VariableSelectInputComponent implements OnInit, OnDestroy, OnChanges, ControlValueAccessor {
  @Input() variableRange: EVariableRange;
  @Input() valueType: string;
  @Input() item: any;
  @Input() inputable: boolean = false; // 是否可输入后缀
  @Input() modelRoot: boolean = false; // 是不是只要展示模型的根
  @Input() rootMatchKey: string = 'modelCode';
  @Input() sufixKey: string = 'valueSuffix';
  @Input() includeGlobalModel: boolean = true; // 是否包含流程的回掉模型
  @Input() maxWidth: number | undefined = undefined;
  @Input() readonly: boolean = false;

  @Output() onChanged = new EventEmitter<any>();

  disabled: boolean;
  subscribe: Subscription;
  onChange = (value) => {};
  onTouched = () => {};

  constructor(
    private variableService: VariableService,
    private viewStoreService: ViewStoreService,
    private viewGraphService: ViewGraphService,
    public viewApiService: ViewApiService,
  ) {
    this.subscribe = this.variableService.variableRefresh$.pipe(debounceTime(120)).subscribe((res) => {
      this.checkAndChangeVariable();
    });
  }
  writeValue(obj: any): void {}
  registerOnChange(fn: any): void {
    this.onChange = (value) => {
      fn(value);
    };
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  _nodeId: string;

  @Input() nodeId: string;

  showVariableModal: boolean = false;

  variableSuffix: string;
  variableDesc: string;
  variable: any;

  get variableName(): string {
    const { variableCode, variableType, variablePath } = this.variable;
    if (variableCode || variablePath) {
      if (['businessObjectVariable', 'modelVariable'].includes(variableType)) {
        return variablePath || variableCode;
      } else {
        return variableCode;
      }
    }
  }

  ngOnInit(): void {
    this.initDefaultVariable(this.item);
  }

  ngOnDestroy(): void {
    this.subscribe.unsubscribe();
    this.subscribe = undefined;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      !isEqual(
        omit(changes.item?.currentValue || {}, this.sufixKey),
        omit(changes.item?.previousValue || {}, this.sufixKey),
      )
    ) {
      this.initDefaultVariable(changes.item.currentValue);
    }
    if ('readonly' in changes) {
      this.setDisabledState(changes.readonly.currentValue);
    }
  }

  private initDefaultVariable(item?: any): void {
    if (item) {
      const isModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(item.valueType);
      this.variable = {
        dataType: item.valueType,
        variableCode: isModel ? item.valuePath : item.valueCode,
        variablePath: !isModel ? item.valuePath : item.valueCode,
        variableType: '',
      };
      this.variableDesc = '';
      this.variableSuffix = item[this.sufixKey];
    } else {
      this.variable = {};
      this.variableDesc = '';
      this.variableSuffix = '';
    }
    this.checkAndChangeVariable();
  }

  getVariableTooltip(): string {
    if (isNotNone(this.variableName)) {
      const variableSuffix = this.variableSuffix;
      return !isEmpty(variableSuffix)
        ? `${this.variableDesc}（${this.variableName}${variableSuffix}）`
        : `${this.variableDesc}（${this.variableName}）`;
    }
    return '';
  }

  /**
   * 返回前缀宽度
   * @returns
   */
  getPrefixWidth(): string {
    if (this.maxWidth) return this.maxWidth - 40 + 'px';
    if (!this.inputable) return '228px';
    return '175px';
  }

  /**
   * 选择变量
   */
  handleChangeVariable(e: { variable: any; variableDesc: string }) {
    this.showVariableModal = false;
    this.variable = e.variable;
    this.variableSuffix = null;
    this.variableDesc = e.variableDesc;
    this.handleSelChange();
  }

  handleSelChange(): void {
    if (this.inputable) this.onChange(this.variableSuffix);
    else if (['businessObjectVariable', 'modelVariable'].includes(this.variable?.variableType)) {
      this.onChange(this.variable?.variablePath);
    } else {
      this.onChange(this.variable?.variableCode);
    }
    this.onChanged.emit({
      actionId: this.variable?.actionId,
      valueCode: this.variable?.variableCode,
      valueType: this.variable?.dataType,
      dataType: this.variable?.dataType,
      variableCode: this.variable?.variablePath,
      variableAlias: this.variable?.variableAlias,
      modelCode: this.variable?.modelCode,
      serviceCode: this.variable?.serviceCode,
      variableSuffix: this.variableSuffix,
      variableDesc: this.variableDesc,
      extendsInfo: this.variable?.extendsInfo,
      originDataType: this.variable.originDataType,
      variableType: this.variable.variableType,
    });
  }

  handleShowVariable() {
    if (this.disabled) return;
    this.showVariableModal = true;
  }

  /**
   * 检测变量
   * @param variable
   * @returns
   */
  async checkAndChangeVariable() {
    const { variableCode, variableType, dataType, variablePath } = this.variable ?? {};
    if (isEmpty(this.variable)) return;
    const {
      propertiesObj,
      originalFlowData: { code },
    } = this.viewStoreService.state;
    const process = propertiesObj?.[code];
    const {
      systemVariable = [],
      customVariables = [],
      businessObjectVariables = [],
      modelVariables = [],
      mechanismVariables = [],
      dtdVariable = [],
    } = process;
    switch (dataType) {
      case VariableType.DTD:
        const cV0 = dtdVariable.find((c) => c.varName === variableCode);
        if (isNone(cV0)) {
          this.variable = {};
          this.variableSuffix = null;
        } else {
          this.variable.variableType = 'dtdVariable';
          this.variableDesc = cV0?.description ?? '';
        }
        break;
      case VariableType.BOOLEAN:
      case VariableType.DECIMAL:
      case VariableType.DATETIME:
      case VariableType.INTEGER:
      case VariableType.STRING:
      case VariableType.OBJECT:
        const cV = customVariables.find((c) => c.varName === variableCode);
        if (isNone(cV)) {
          this.variable = {};
          this.variableSuffix = null;
        } else {
          this.variable.variableType = 'customVariable';
          this.variableDesc = cV?.description ?? '';
        }
        break;
      case VariableType.PROCESS:
        const sV = systemVariable.find((s) => s.varName === variableCode);
        if (isNone(sV)) {
          this.variable = {};
          this.variableSuffix = null;
        } else {
          this.variable.variableType = 'systemVariable';
          this.variableDesc = sV?.description ?? '';
        }
        break;
      case VariableType.NODE:
        const node: any = this.viewGraphService.graph.getCellById(
          this.nodeId.includes('_') ? this.nodeId.split('_')[1] : this.nodeId,
        );
        const availableNodeVariables = this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node });
        const nV = availableNodeVariables.find((n) => n.nodeId === variableCode);
        if (isNone(nV)) {
          this.variable = {};
          this.variableSuffix = null;
        } else {
          this.variable.variableType = 'nodeVariable';
          this.variableDesc = nV?.name ?? '';
        }
        break;
      case VariableType.BUSINESS_OBJECT:
        const businessVariableInfo = await this.getBusinessObjectVariableInfo(businessObjectVariables);
        if (this.modelRoot) {
          const variableInfo = businessVariableInfo.find((e) => e[this.rootMatchKey] === variableCode);
          if (isNone(variableInfo)) {
            this.variable = {};
            this.variableSuffix = null;
          } else {
            this.variable.variableType = 'businessObjectVariable';
            this.variableDesc = variableInfo.modelFieldInfoVo?.comment ?? '';
          }
        } else {
          const variableInfo = businessVariableInfo.find((e) => e.variableCode === variableCode);
          const { modelFileInfos = [], modelCode, comment } = variableInfo?.modelFieldInfoVo ?? {};
          const ret = matchModelPath(variablePath, ergodicList(modelFileInfos, [modelCode]));
          if (modelCode !== variablePath && !ret) {
            this.variable = {};
            this.variableSuffix = null;
          } else {
            this.variable.variableType = 'businessObjectVariable';
            this.variableDesc = ret?.fieldName || comment || '';
          }
        }
        break;
      case VariableType.MODEL:
        // 前置节点变量
        const _nodeId = this.nodeId?.includes('_') ? this.nodeId.split('_')[1] : this.nodeId;
        const currentNode: any = this.viewGraphService.graph.getCellById(_nodeId);
        const preNodes = isNotNone(currentNode)
          ? this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node: currentNode }) ?? []
          : [];
        const preNodeModels = this.getPreNodesModel(preNodes, modelVariables);
        const modelVariableInfo = await this.getModelVariableInfo(preNodeModels);
        if (this.modelRoot) {
          const variableInfo2 = modelVariableInfo.find((e) => e[this.rootMatchKey] === variableCode);
          if (isNone(variableInfo2)) {
            this.variable = {};
            this.variableSuffix = null;
          } else {
            this.variable.variableType = 'modelVariable';
            this.variableDesc = variableInfo2.modelFieldInfoVo?.comment ?? '';
          }
        } else {
          const variableInfo2 = modelVariableInfo.find((e) => e.variableCode === variableCode);
          const {
            modelFileInfos: modelFileInfos2 = [],
            modelCode: modelCode2,
            comment: comment2,
          } = variableInfo2?.modelFieldInfoVo ?? {};
          const ret2 = matchModelPath(variablePath, ergodicList(modelFileInfos2, []));
          if (modelCode2 !== variablePath && !ret2) {
            this.variable = {};
            this.variableSuffix = null;
          } else {
            this.variable.variableType = 'modelVariable';
            this.variableDesc = ret2?.fieldName || comment2 || '';
          }
        }
        break;
      case VariableType.Mechanism:
        const sVM = mechanismVariables.find((s) => s.varName === variableCode);
        if (isNone(sVM)) {
          this.variable = {};
          this.variableSuffix = null;
        } else {
          this.variable.variableType = 'mechanismVariable';
          this.variableDesc = sVM?.description ?? '';
        }
        break;
      default:
        this.variable = {};
        this.variableSuffix = null;
        break;
    }
    if (isNone(this.variable?.variableCode)) {
      this.handleSelChange();
    }
  }

  /**
   * 获取前置节点绑定的模型
   * @param preNodes
   * @param propertiesObj
   * @param process
   * @param modelVariables
   * @returns
   */
  private getPreNodesModel(preNodes: any[], modelVariables: any[]): any[] {
    const {
      propertiesObj,
      originalFlowData: { code },
    } = this.viewStoreService.state;
    const process = propertiesObj?.[code];
    const preNodesModels = preNodes
      .map((preNode) => {
        const bindForm = propertiesObj[preNode._nodeId].bindForm;
        if (bindForm) return `${bindForm.modelCode}&${bindForm.serviceCode}`;
        return null;
      })
      .filter((e) => !!e);
    if (process.bindForm && this.includeGlobalModel) {
      preNodesModels.push(`${process.bindForm.modelCode}&${process.bindForm.serviceCode}`);
    }
    return modelVariables.filter((e) => preNodesModels.includes(e.uniqueKey));
  }

  /**
   * 获取对象节点的信息
   * @param businessObjectVariables
   * @returns
   */
  private getBusinessObjectVariableInfo = async (businessObjectVariables: any[]) => {
    if (!businessObjectVariables.length) return [];
    const params = businessObjectVariables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modeCode,
      variableCode: item.varName,
      isHasChildren: true,
    }));
    const result = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (result.code === 0) return result.data;
    return [];
  };

  /**
   * 获取模型节点的信息
   * @param businessObjectVariables
   * @returns
   */
  private getModelVariableInfo = async (variables: any[]) => {
    if (!variables.length) return [];
    const params = variables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modelCode,
      variableCode: item.uniqueKey,
      isHasChildren: true,
    }));
    const result = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (result.code === 0) return result.data;
    return [];
  };
}
