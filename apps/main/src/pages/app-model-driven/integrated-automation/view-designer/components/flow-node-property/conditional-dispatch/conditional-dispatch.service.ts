import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { AppService } from '../../../../../../apps/app.service';

@Injectable()
export class ConditionalDispatchService {
  private adesignerUrl: string;
  constructor(
    private systemConfigService: SystemConfigService,
    public appService: AppService,
    private http: HttpClient,
  ) {
    this.systemConfigService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }
  // 查询当前解决方案下的开窗数据
  loadOpenWindowParams(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/openWindow/${param}`;
    return this.http.get(url);
  }
  // 翻译
  translate(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/translate/translate`;
    return this.http.post(url, param);
  }
}
