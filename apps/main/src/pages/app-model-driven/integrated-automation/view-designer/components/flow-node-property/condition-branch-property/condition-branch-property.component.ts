import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { SourceField } from '../../../../../../../components/page-design/entries/data-view/config/data-view.type';
import { QueryData } from '../../../../../../../components/page-design/entries/data-view/components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter-content/advanced-query.type';
import { map, find, forEach, sortBy, cloneDeep, size, findIndex, isEmpty, findLast, debounce } from 'lodash';
import { ConditionBranchPropertyService } from './condition-branch-property.service';
import { DataViewFilterComponent } from 'components/page-design/entries/data-view/components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter.component';
import { ViewStoreService } from '../../../service/store.service';
import { PannelTabsType, VariableType } from '../../../config/typings';
import { PeopleSettingPropertyService } from '../people-setting-property/people-setting-property.service';
import { EVariableRange } from '../variable-select-input/components/variable-modal/variable-modal.component';
@Component({
  selector: 'app-condition-branch-property',
  templateUrl: './condition-branch-property.component.html',
  styleUrls: ['./condition-branch-property.component.less'],
  providers: [ConditionBranchPropertyService, PeopleSettingPropertyService],
})
export class ConditionBranchPropertyComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  currScriptControlName: string;
  scriptModal: boolean;
  conditionType = 'rule';
  currentLang;
  // 设置条件过滤的所有模型字段
  allFields: SourceField[] = [];
  // 这个是自己界面使用的
  conditionList: QueryData[] = [];
  // 这个是平台使用的
  queryConditions = [];
  // 条件排序
  conditionSortList = [];
  // 当前条件的顺序
  sort;
  // 多语言单独处理，name title
  lang: any;
  // 筛选器前选的模型code
  // 筛选器前选的模型对象
  modelSelObj: any = {
    code: '',
  };
  // 筛选模式
  conditionMode: 'UI' | 'SCRIPT';
  // 脚本
  queryConditionScript: string;
  // 从节点中获取前序的模型，用于下面筛选器的选择
  preManualNodesMode = [];
  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;
  dataViewFilterError: boolean = false;
  scriptError: boolean = false;
  @ViewChild('dataViewFilter') dataViewFilter: DataViewFilterComponent;

  UIDisabled: boolean = false;

  defaultScriptString: string = `/*
  此处编写内容为条件表达式，如 $variables['balance'] > 10  该表达式含义为当自定义变量'balance‘大于10时，通过该条件判断
*/`;

  get variableRange() {
    const mode =
      EVariableRange.CUSTOM |
      EVariableRange.NODE |
      EVariableRange.MODEL |
      EVariableRange.SYSTEM |
      EVariableRange.MECHANISM |
      EVariableRange.BUSINESS;
    if (this.viewStoreSrevice.isFusionMode()) {
      return mode | EVariableRange.DTD;
    }
    return mode;
  }

  get item() {
    if (!this.modelSelObj) return undefined;
    return {
      valueType: this.modelSelObj.dataType,
      valuePath: this.modelSelObj.code, // 模型/业务对象
      valueCode: this.modelSelObj.variableCode, // 变量
    };
  }

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    private conditionBranchPropertyService: ConditionBranchPropertyService,
    private viewStoreSrevice: ViewStoreService,
  ) {
    this.currentLang = this.translate.currentLang;
  }

  ngOnInit(): void {
    // 初始化fromgroup
    // 直接校验
    // 对父组件发出change事件主要里面混入isVerificationPassed
    this.modelSelObj = cloneDeep(this.data.modelSelObj || { code: '' });
    this.processModelSelObj();
    const isBusinessOrModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(this.modelSelObj?.dataType);
    this.conditionMode = this.data.conditionMode || 'UI';
    this.queryConditionScript = this.data.queryConditionScript;
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      modelCodeSel: [
        (isBusinessOrModel ? this.modelSelObj.code : this.modelSelObj.variableCode) || null,
        [Validators.required],
      ],
    });
    this.UIDisabled = !isBusinessOrModel && this.modelSelObj?.dataType !== 'Object';
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
    this.handleInit();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      // id不相同认为是不同的节点需要重新构建formgroup，后期可以使用patchValue,而非重新初始化formgroup
      if (changes.nodeId.currentValue !== changes.nodeId.previousValue) {
        // 为了patchValue不触发valueChanges
        this.formGroupValidityFlag = true;
        this.modelSelObj = cloneDeep(this.data.modelSelObj || { code: '' });
        this.processModelSelObj();
        const isBusinessOrModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(
          this.modelSelObj?.dataType,
        );
        this.UIDisabled = !isBusinessOrModel && this.modelSelObj?.dataType !== 'Object';
        this.conditionMode = this.data.conditionMode || 'UI';
        this.queryConditionScript = this.data.queryConditionScript;
        this.dataFormGroup?.patchValue({
          id: this.data?.id,
          name: this.data?.name,
          modelCodeSel: (isBusinessOrModel ? this.modelSelObj.code : this.modelSelObj.variableCode) || null,
        });
        this.formGroupValidityFlag = false;
        this.handleInit();
      }
    }
  }
  handleClosePanel(): void {
    this.close.emit();
  }
  showScriptModal(controlName) {
    this.currScriptControlName = controlName;
    this.scriptModal = true;
  }

  handleSortChange(e) {
    const newSortList = (e ?? []).map((item, index) => {
      item.sort = index + 1;
      return item;
    });
    const findSortItem = find(newSortList, (item) => item.code === this.data._nodeId);
    if (findSortItem) {
      this.sort = findSortItem.sort;
    }
    // 再更新conditionNodesLevel 结构
    forEach(this.conditionSortList, (item) => {
      const code = item.code;
      const findNodeItem = find(newSortList, (newSortItem) => newSortItem.code === code);
      if (findNodeItem) {
        item.sort = findNodeItem.sort;
      }
    });
    this.conditionSortList = sortBy(this.conditionSortList, (item) => item.sort);
    this.getCurrentData();
  }
  handleCloseSript(flag, value) {
    if (flag === 'confirm') {
      // this.form.patchValue({
      //   [this.currScriptControlName]: value,
      // });
    }
    this.scriptModal = false;
  }

  // 根据输入的组装fromgroup
  async handleInit() {
    this.modelSelObj = cloneDeep(this.data.modelSelObj || { code: '' });
    this.processModelSelObj();
    this.lang = cloneDeep(this.data.lang);
    await this.loadDataViewFields(this.modelSelObj);
    this.conditionList = cloneDeep(this.data?.conditionList || []);
    this.queryConditions = cloneDeep(this.data?.queryConditions || []);
    // 把刘康从图中给数据结构转为泽宇组件封装的数据结构
    this.conditionSortList = map(this.data.conditionNodesLevel, (item) => ({
      code: item.nodeId,
      name: item.nodeName,
      lang: {
        name: {
          zh_CN: item.nodeName,
          zh_TW: item.nodeName,
          en_US: item.nodeName,
        },
      },
      sort: item.nodeSort,
      nodeType: item.nodeType,
    }));
    const nodeId = this.data._nodeId;
    const findConditionSortItemIndex = findIndex(this.conditionSortList, (item) => item.code === nodeId);
    // 获取最新的排序顺序
    this.sort = findConditionSortItemIndex + 1;
    this.getNodesModeList(this.data.preManualNodesMode);
    this.getCurrentData();
  }

  getNodesModeList(modeArr) {
    const groupArr = modeArr.reduce(
      (prev, next) => {
        if (next.modelCode && next.serviceCode) {
          prev.common.push(next);
        } else {
          prev.manual.push(next);
        }
        return prev;
      },
      { common: [], manual: [] },
    );
    const param = map(groupArr.common, (item) => ({
      code: item.modelCode,
      serviceCode: item.serviceCode,
    }));
    this.conditionBranchPropertyService.findModelByCode(param).subscribe((res) => {
      this.preManualNodesMode = res.data;
      if (groupArr.manual.length > 0) {
        this.preManualNodesMode = this.preManualNodesMode.concat(groupArr.manual);
      }
    });
  }
  // 多语言栏位改变的事件处理
  handlePatchLang(key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    if ('name' === key) {
      // 名称修改要同步到条件排序的数组中 conditionSortList
      const nodeId = this.data._nodeId;
      const findConditionSortItem = find(this.conditionSortList, (item) => item.code === nodeId);
      if (findConditionSortItem) {
        findConditionSortItem.name = data?.value;
        findConditionSortItem.lang.name = data.lang;
      }
    }
    this.dataFormGroup.patchValue({ [key]: data?.value });
  }

  // 获取当前最新的数据
  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.value;
      currentData.sort = this.sort;

      // 混入isVerificationPassed 是否校验通过
      let isVerificationPassed = this.viewStoreSrevice.transformVerificationPassed(this.dataFormGroup.valid);
      currentData.modelSelObj = this.modelSelObj;
      currentData.conditionMode = this.conditionMode;
      currentData.queryConditionScript = this.queryConditionScript;
      currentData.conditionList = this.conditionList;
      currentData.queryConditions = this.queryConditions;
      // 再转回刘康的数据结构
      const conditionSortList = map(this.conditionSortList, (item) => ({
        nodeId: item.code,
        nodeName: item.name,
        sort: item.sort,
        nodeType: item.nodeType,
      }));
      if (this.dataFormGroup.valid) {
        if (this.conditionMode === 'UI') {
          if (size(currentData.conditionList) == 0) {
            isVerificationPassed[PannelTabsType.BASE] = false;
            this.dataViewFilterError = true;
          } else {
            this.dataViewFilterError = false;
          }
          this.scriptError = false;
        } else {
          this.dataViewFilterError = false;
          if (!this.queryConditionScript) {
            isVerificationPassed[PannelTabsType.BASE] = false;
            this.scriptError = true;
          } else {
            this.scriptError = false;
          }
        }
      }
      const returnData = {
        data: Object.assign(this.data, currentData, { conditionSortList, lang: this.lang, allFields: this.allFields }),
        isVerificationPassed,
      };
      this.changeData.emit(returnData);
      return returnData;
    },
    150,
    { leading: false, trailing: true },
  );

  handleDataViewFilterChange(e) {
    // this.dataViewService.setConditionList(e.conditionList);
    // this.dataViewService.setQueryConditions(e.queryConditions);
    this.conditionList = e.conditionList;
    this.queryConditions = e.queryConditions;
    this.getCurrentData();
  }

  async loadDataViewFields(param) {
    const actionId = param.dataType === VariableType.NODE ? this.getESPNodeActionId(param?.nodeId) : null;
    if (param && (param.code || param.actionId || actionId)) {
      const finalParam = actionId ? { actionId } : param;
      const res = await this.conditionBranchPropertyService.queryDataViewFields(finalParam).toPromise();
      this.allFields = res.data?.mainField || [];
    }
  }

  getESPNodeActionId(nodeCode: string) {
    const nodeId = nodeCode?.split('_')?.[1];
    const nodeData = this.viewStoreSrevice?.state?.propertiesObj?.[nodeId];
    if (nodeData?.name === 'ESP') {
      return nodeData?.serviceConfig?.actionId;
    }
    return null;
  }

  /**
   * 处理变量
   * @param variable
   */
  handleValueChanged(variable: any): void {
    const { modelCode, serviceCode, variableDesc, dataType, valueCode, variableAlias, extendsInfo } = variable;
    if (this.formGroupValidityFlag) return;
    if (VariableType.MODEL === dataType) {
      const findRes = {
        code: modelCode,
        serviceCode,
        dataType,
        nodeId: undefined,
      };
      this.modelSelObj = findRes;
      this.loadDataViewFields(findRes);
      this.UIDisabled = false;
      // 之前配置的筛选器清空
      this.handleModeTypeChange('UI');
    } else if (VariableType.BUSINESS_OBJECT === dataType) {
      const findModel = {
        code: modelCode,
        name: variableDesc,
        serviceCode,
      };
      this.modelSelObj = {
        ...findModel,
        dataType,
        nodeId: variableAlias,
      };
      this.loadDataViewFields(findModel);
      this.UIDisabled = false;
      // 之前配置的筛选器清空
      this.handleModeTypeChange('UI');
    } else {
      const needQueryFields = [VariableType.OBJECT, VariableType.NODE].includes(dataType);
      if (needQueryFields) {
        let actionId = null;
        if (VariableType.NODE === dataType) {
          actionId = this.getESPNodeActionId(valueCode);
        } else if (VariableType.OBJECT === dataType) {
          actionId = extendsInfo?.actionId;
        }
        if (actionId) {
          this.loadDataViewFields({ actionId });
        }
      }
      const baseInfo: any = {
        code: modelCode,
        serviceCode,
        dataType: dataType,
        variableCode: valueCode,
        nodeId: valueCode,
      };
      // Object存一下actionId
      const isObject = VariableType.OBJECT === dataType;
      if (isObject) {
        baseInfo.actionId = extendsInfo?.actionId;
      } else {
        this.allFields = [];
      }
      this.modelSelObj = baseInfo;
      this.UIDisabled = isObject ? false : true;
      // 之前配置的筛选器清空
      this.handleModeTypeChange(isObject ? 'UI' : 'SCRIPT');
    }
  }

  handleModeTypeChange(type: 'UI' | 'SCRIPT'): void {
    this.conditionMode = type;
    this.queryConditionScript = undefined;
    this.queryConditions = [];
    this.conditionList = [];
    this.getCurrentData();
  }

  handleScriptChange(script: string): void {
    this.queryConditionScript = script;
    this.getCurrentData();
  }

  private processModelSelObj() {
    if (!this.modelSelObj.dataType) this.modelSelObj.dataType = VariableType.MODEL;
  }
}
