/**
 * 数据类型图标映射
 */
export const DataTypeIconMap = {
  String: 'iconwenben',
  Integer: 'iconzhengshu',
  Decimal: 'iconxiaoshu',
  Boolean: 'iconbuerzhi',
  DateTime: 'icona-ziyuan22',
  BusinessObject: 'iconyewuduixiang1',
  Node: 'iconjiedianshuchubianliang',
  Model: 'iconmoxingbianliang',
  Mechanism: 'iconyewuduixiang2',
  DTDVar: 'iconDTDbianliang',
  Process: 'iconjiedianshuchubianliang',
  Object: 'iconduixiang-xianxing',
};

/**
 *
 */
export const VariableNameMap = {
  String: 'dj-文本',
  Integer: 'dj-整数',
  Decimal: 'dj-小数',
  Boolean: 'dj-布尔值',
  DateTime: '日期时间',
  BusinessObject: 'dj-业务对象',
  Node: 'dj-节点对象',
  Mechanism: 'dj-机制参数',
  Object: 'dj-对象',
};
