<div class="auto-http-property-root submit-form">
  <div class="header">
    <span>{{ 'HTTP' }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: this.nameLang?.name,
                  needLang: true,
                  readOnly: disabledAll
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <!--活动排程-->
        <div class="form-item" formGroupName="scheduleRule">
          <div class="item-title">
            {{ 'dj-活动排程' | translate }}
          </div>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <ad-select
              style="width: 100%"
              formControlName="schedule_type"
              [nzPlaceHolder]="'dj-请输入' | translate"
              (ngModelChange)="handleScheduleTypeChange($event)"
            >
              <ad-option nzValue="0" [nzLabel]="'dj-延迟执行' | translate"></ad-option>
            </ad-select>
          </nz-form-control>
        </div>

        <!--延迟时间-->
        <div
          class="form-item"
          formGroupName="scheduleRule"
          *ngIf="dataFormGroup.get(['scheduleRule', 'schedule_type']).value === '0'"
        >
          <div class="item-title">
            {{ 'dj-延迟时间' | translate }}
            <span class="item-required">*</span>
          </div>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="delay_seconds" ngDefaultControl />
          </nz-form-control>
        </div>
      </nz-collapse-panel>
      <!--HTTP设置-->
      <nz-collapse-panel [nzHeader]="'dj-HTTP设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <!--请求头-->
        <div class="form-item">
          <div class="item-title">
            {{ 'dj-请求头' | translate }}
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-请求头中使用变量参考以下格式' | translate"
            >
            </i>
          </div>
          <input nz-input formControlName="header" [placeholder]="'dj-请输入' | translate" />
        </div>

        <!--Domain-->
        <div class="form-item">
          <div class="item-title">
            {{ 'Domain' }}
          </div>
          <ad-select style="width: 100%" formControlName="domain" [nzPlaceHolder]="'dj-请选择' | translate">
            <ng-container *ngFor="let data of domainList">
              <ad-option [nzValue]="data" [nzLabel]="data"></ad-option>
            </ng-container>
          </ad-select>
        </div>

        <!--URL-->
        <div class="form-item">
          <div class="item-title">
            {{ 'URL' }}
          </div>
          <input nz-input formControlName="url" [placeholder]="'dj-请输入' | translate" />
        </div>

        <!--请求方式-->
        <div class="form-item">
          <div class="item-title">
            {{ 'dj-请求方式' | translate }}
          </div>
          <ad-select style="width: 100%" formControlName="method" [nzPlaceHolder]="'dj-请选择' | translate">
            <ad-option nzValue="POST" nzLabel="POST"></ad-option>
            <ad-option nzValue="GET" nzLabel="GET"></ad-option>
          </ad-select>
        </div>

        <!--请求脚本-->
        <div class="form-item">
          <div class="item-title">
            {{ 'dj-请求脚本' | translate }}
          </div>
          <textarea nz-input formControlName="requestScript" (dblclick)="showScriptModal('requestScript')"></textarea>
        </div>

        <!--返回脚本-->
        <div class="form-item">
          <div class="item-title">
            {{ 'dj-返回脚本' | translate }}
          </div>
          <textarea nz-input formControlName="responseScript" (dblclick)="showScriptModal('responseScript')"></textarea>
        </div>

        <!--推送消息设置-->
        <!-- <div class="form-item">
          <div class="item-title">
            {{ 'dj-推送消息设置' | translate }}
          </div>
          <div nz-col nzSpan="24" style="margin-bottom: 8px">
            <label nz-checkbox formControlName="milestone">{{ 'dj-是否推送任务消息' | translate }}</label>
          </div>
        </div> -->

        <!--异步设置-->
        <!--        <div class="form-item">-->
        <!--          <div class="item-title">-->
        <!--            {{ 'dj-异步设置' | translate }}-->
        <!--          </div>-->
        <!--          <div nz-col nzSpan="24" style="margin-bottom: 8px">-->
        <!--            <label nz-checkbox>{{ 'dj-是否异步' | translate }}</label>-->
        <!--          </div>-->
        <!--        </div>-->
      </nz-collapse-panel>
    </nz-collapse>
  </form>

  <app-script-editor
    *ngIf="scriptModal"
    [scriptModal]="scriptModal"
    [script]="scriptData"
    (confirm)="handleCloseSript('confirm', $event)"
    (close)="handleCloseSript('close', $event)"
  ></app-script-editor>
</div>
