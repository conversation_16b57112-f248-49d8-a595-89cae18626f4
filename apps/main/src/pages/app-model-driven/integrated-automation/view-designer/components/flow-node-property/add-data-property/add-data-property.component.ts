import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { AddDataService } from './add-data-property.service';
import { validatorForm } from 'common/utils/core.utils';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FieldSetService } from './components/field-set/field-set.service';
import { isEmpty } from 'lodash';
import { ViewStoreService } from '../../../service/store.service';
import { ViewGraphService } from '../../../service/graph.service';

@Component({
  selector: 'app-add-data-property',
  templateUrl: './add-data-property.component.html',
  styleUrls: ['./add-data-property.component.less'],
  providers: [AddDataService, FieldSetService],
})
export class AddDataPropertyComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  dataFormGroup;
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  businessConstructorList: any = []; // 业务对象下拉

  fieldInfosData = [];
  fieldInfosPassed: boolean = true;
  preModelNodes: any;

  get disabledAll() {
    return !!this.viewGraphService?.fromManual;
  }

  constructor(
    private translate: TranslateService,
    private addDataService: AddDataService,
    private message: NzMessageService,
    private fieldSetService: FieldSetService,
    private fb: FormBuilder,
    private viewStoreSrevice: ViewStoreService,
    private viewGraphService: ViewGraphService,
  ) {}

  ngOnInit(): void {
    this.handleInit();
    this.getCurrentData();
    this.getBCList();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  getBCList() {
    this.addDataService.getBusinessDirList().subscribe((data) => {
      const trees = data.data || [];
      const _modelList: any[] = [];
      trees.forEach((item) => {
        const models = item.businessDirTree.find((e) => e.type === 'modelDesign')?.businessDirTree || [];
        models.forEach((model) => {
          _modelList.push({
            label: item.isOther
              ? model.lang?.modelName?.[this.translate.instant('dj-LANG')] || model.businessSubName
              : item.lang?.name?.[this.translate.instant('dj-LANG')] || item.businessName,
            modelCode: model.businessSubCode,
            serviceCode: model.serviceCode,
            value: `${item.businessCode}&${model.businessSubCode}&${model.serviceCode}`,
            groupLabel: item.isOther ? this.translate.instant('dj-其他') : undefined,
          });
        });
      });
      this.businessConstructorList = _modelList;
    });
  }

  /**
   * 切换相同节点类型的不同节点
   */
  handleChangeValue() {
    this.formGroupValidityFlag = true;
    const formCode = this.data?.bindForm?.formCode
      ? `${this.data?.bindForm?.formCode}&${this.data?.bindForm?.modelCode}&${this.data?.bindForm?.serviceCode}`
      : undefined;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      bindForm: {
        formCode: formCode,
        modelCode: this.data?.bindForm?.modelCode,
        serviceCode: this.data?.bindForm?.serviceCode,
      },
      addType: this.data?.addType,
    });
    this.handleDefaultInit();
    this.formGroupValidityFlag = false;
    this.getCurrentData();
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  /**
   * 初始化dataFormGroup
   */
  handleInit(): void {
    const formCode = this.data?.bindForm?.formCode
      ? `${this.data?.bindForm?.formCode}&${this.data?.bindForm?.modelCode}&${this.data?.bindForm?.serviceCode}`
      : undefined;
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      bindForm: this.fb.group({
        formCode: [formCode, [Validators.required]],
        modelCode: [this.data?.bindForm?.modelCode, [Validators.required]],
        serviceCode: [this.data?.bindForm?.serviceCode, [Validators.required]],
      }),
      addType: [this.data?.addType, [Validators.required]],
    });
    this.handleDefaultInit();
    this.dataFormGroup
      .get('bindForm')
      .get('formCode')
      .valueChanges.subscribe((value) => {
        if (!this.formGroupValidityFlag) {
          this.handleChangeFormCode(value);
        }
      });
  }

  /**
   * 设置其他公共的值
   */
  handleDefaultInit() {
    this.nameLang = this.data.lang;
    if (this.data?.bindForm.formCode) {
      this.fieldSetService.getFieldModelList(this.data?.bindForm, false);
    }
    this.preModelNodes = [];
    this.fieldInfosData = this.data?.fieldInfos || [];
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleChangeFormCode(code) {
    const modelItem = this.businessConstructorList.find((data) => data.value === code);
    if (!isEmpty(modelItem)) {
      const modalData = {
        modelCode: modelItem.modelCode,
        serviceCode: modelItem.serviceCode,
      };
      this.dataFormGroup.get('bindForm').patchValue(modalData);
      this.fieldSetService.getFieldModelList(modalData, true);
      return;
    }
    this.dataFormGroup.get('bindForm').patchValue({
      modelCode: '',
      serviceCode: '',
    });
    this.fieldSetService.fieldModelList = [];
    this.fieldSetService.setChangeFormCode(true);
  }

  checkBindForm() {
    if (!this.dataFormGroup.getRawValue()?.bindForm?.formCode) {
      this.message.info(this.translate.instant('dj-请选择业务对象'));
      return;
    }
    this.fieldSetService.AddFields$.next();
  }

  /**
   * field-set 子组建back
   * @param param0
   */
  fieldInfosDataBack({ currentData, isVerificationPassed }) {
    this.fieldInfosData = currentData;
    this.fieldInfosPassed = isVerificationPassed;
    this.getCurrentData();
  }

  // 获取当前最新的数据
  getCurrentData() {
    this.formGroupValidityFlag = true;
    validatorForm(this.dataFormGroup);
    this.formGroupValidityFlag = false;
    const currentData = this.dataFormGroup.getRawValue();
    // 混入isVerificationPassed 是否校验通过
    let isVerificationPassed = this.viewStoreSrevice.transformVerificationPassed(
      this.dataFormGroup.valid && this.fieldInfosPassed,
    );
    if (currentData?.bindForm?.formCode) {
      const [businessCode, modelCode, serviceCode] = currentData.bindForm.formCode.split('&');
      currentData.bindForm.formCode = businessCode;
      currentData.bindForm.modelCode = modelCode;
      currentData.bindForm.serviceCode = serviceCode;
    }
    let data = Object.assign(this.data, currentData, { lang: this.nameLang });
    data.fieldInfos = this.fieldInfosData;
    const returnData = {
      data: data,
      isVerificationPassed,
    };
    this.changeData.emit(returnData);
    return returnData;
  }
}
