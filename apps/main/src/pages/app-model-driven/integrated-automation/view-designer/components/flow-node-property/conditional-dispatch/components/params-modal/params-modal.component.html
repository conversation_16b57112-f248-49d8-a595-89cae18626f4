<ad-modal
  [(nzVisible)]="showModal"
  [nzWidth]="'680px'"
  [nzTitle]="'dj-开窗参数' | translate"
  [nzFooter]="null"
  (nzOnCancel)="handleClose()"
>
  <ng-container *adModalContent>
    <div class="add-field-value-body">
      <div class="search-content">
        <nz-input-group [nzSuffix]="suffixIcon">
          <input
            type="text"
            nz-input
            [(ngModel)]="searchValue"
            (ngModelChange)="handleOnSearch()"
            [placeholder]="'dj-请输入名称或代号' | translate"
          />
        </nz-input-group>
        <ng-template #suffixIcon>
          <span class="input-clear" *ngIf="!!searchValue">
            <i adIcon type="close-circle" theme="fill" (click)="handleClearSearch()"></i>
          </span>
          <i adIcon iconfont="iconinputserach" class="searchIcon iconfont" aria-hidden="true"> </i>
        </ng-template>
      </div>

      <div class="tree-box tree-box-top">
        <nz-table
          #expandTable
          class="content-table"
          [nzData]="paramsList"
          *ngIf="paramsList?.length"
          nzSize="middle"
          [nzScroll]="{ y: '380px', x: null }"
          [nzShowPagination]="false"
          [nzLoading]="loading"
          [nzBordered]="true"
        >
          <thead>
            <tr>
              <th nzWidth="50px"></th>
              <th nzWidth="200px">{{ 'dj-参数名称' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let item of expandTable.data">
              <tr>
                <td>
                  <div class="node-line-desc">
                    <label
                      nz-radio
                      style="margin-right: 0"
                      (click)="changeRadioValue(item)"
                      [ngModel]="checkedValue === item?.paramCode && variable?.paramCode === item?.paramCode"
                    >
                    </label>
                  </div>
                </td>
                <td>
                  <div
                    class="node-line-desc-v"
                    [nz-tooltip]="true"
                    [nzTooltipTitle]="item?.paramName"
                  >
                    {{ item?.paramName }}
                  </div>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </nz-table>
      </div>
      <div *ngIf="!loading && paramsList?.length === 0" class="tree-box-empty">
        <ad-empty [nzNotFoundContent]="contentTpl1">
          <ng-template #contentTpl1>
            <span>{{ 'dj-暂无数据' | translate }}</span>
          </ng-template>
        </ad-empty>
      </div>
    </div>
    <div class="add-field-value-footer">
      <button ad-button adType="default" class="mr20" (click)="handleClose()">
        {{ 'dj-取消' | translate }}
      </button>
      <button ad-button adType="primary" (click)="handleSubmit()">
        {{ 'dj-确定' | translate }}
      </button>
    </div>
  </ng-container>
</ad-modal>
