import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { fromEvent, Subject, Subscription } from 'rxjs';
import { ViewApiService } from '../../../service/api.service';
import { isJSON, validatorForm } from 'common/utils/core.utils';
import { cloneDeep, debounce, throttle } from 'lodash';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ViewStoreService } from '../../../service/store.service';
import { PannelTabsType } from '../../../config/typings';
import { ViewGraphService } from '../../../service/graph.service';
import { ViewToolsService } from '../../../service/tools.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { scripts } from 'pages/app/dtd/activity-flow/service/flow.util';
import { responseScript } from 'pages/app/dtd/drive-execution-new/components/api-design/service/script-template';

enum EActionType {
  ESP = 'ESP',
  HTTP = 'HTTP',
  Script = 'SCRIPT',
  PASS = 'NODE_PASS',
  Modify = 'MODEL_UPDATE',
}

type TAction = 'pre' | 'after';

@Component({
  selector: 'app-action-setting',
  templateUrl: './action-setting.component.html',
  styleUrls: ['./action-setting.component.less'],
})
export class ActionSettingComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() nodeId: string;

  @Output() close = new EventEmitter();

  EActionType = EActionType;
  loading: boolean = false;
  form: FormGroup;

  @ViewChild('scrollContent') scrollContent: ElementRef<HTMLDivElement>;

  get pre() {
    return this.form?.get('pre') as FormArray;
  }

  get after() {
    return this.form?.get('after') as FormArray;
  }

  get disabledAll() {
    return !!this.viewGraphService?.fromManual;
  }

  private subjectMap = new Map<string, Subscription[]>();
  private destory$ = new Subject();
  private formGroupValidityFlag: boolean = false;
  private visibleMap = new Map<string, boolean>();

  private scrollSubject: Subscription;
  actions: {
    pre: any[];
    after: any[];
    exceptionPolicy: any;
  };
  private get state() {
    return this.viewStoreService.state;
  }

  //#region 脚本编辑器
  // 脚本编辑器
  scriptModal: boolean = false;
  scriptData: string | null = null;

  editingForm:
    | {
        type: TAction;
        index: number;
        from?: string;
      }
    | undefined = undefined;
  //#endregion

  // 下拉选项
  actionOptions: { label: string; value: string }[] = [];

  //#region esp
  // action开窗是否显示
  espActionVisible: boolean = false;

  produces: {
    [k: string]: string[];
  } = {};
  //#endregion

  //#region http
  readonly domainList: string[] = [
    'default',
    'eoc',
    'iam',
    'smartdata',
    'thememap',
    'aim',
    'atmc',
    'flowengine',
    'taskengine',
    'emc',
    'lcdp',
    'abi',
    'aam',
    'mdc',
    'itsys',
    'cac',
    'airh',
  ];
  //#endregion

  //#region 更新模型
  modelList: {
    label: string;
    value: string;
  }[] = [];

  modelStatus: {
    [k: string]: any[];
  } = {};
  //#endregion

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private modal: AdModalService,
    private apiService: ViewApiService,
    private viewStoreService: ViewStoreService,
    private viewGraphService: ViewGraphService,
    private viewToolsService: ViewToolsService,
  ) {
    this.initForm();
  }

  async ngOnInit(): Promise<void> {
    const { actions } = this.state.propertiesObj[this.state.currentSelectedNodeId];
    this.actions = actions;
    this.initSelectOptions();
    try {
      this.loading = true;
      await this.getModelVaribale();
      this.handleInit();
    } finally {
      this.loading = false;
    }
  }

  ngAfterViewInit(): void {
    this.scrollSubject = fromEvent(this.scrollContent.nativeElement, 'scroll').subscribe(
      throttle(
        () => {
          this.visibleMap.forEach((v, k) => {
            if (v) {
              this.visibleMap.set(k, false);
            }
          });
          this.visibleMap.clear();
        },
        100,
        { leading: true, trailing: false },
      ),
    );
  }

  ngOnDestroy(): void {
    this.scrollSubject?.unsubscribe();
    this.unSubject('pre');
    this.unSubject('after');
    this.destory$.next();
    this.destory$.complete();
  }

  // 初始化form
  initForm(): void {
    this.form = this.fb.group({
      // 前置
      pre: this.fb.array([]),
      // 后置
      after: this.fb.array([]),
      // 异常
      exceptionPolicy: ['actionSkip', [Validators.required]],
    });
    this.form.valueChanges.pipe(takeUntil(this.destory$)).subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  /**
   * 新增前置、后置动作
   * @param type
   */
  addAction(type: TAction): void {
    this[type]?.push(
      this.fb.group({
        expression: [{ value: '', disabled: this.disabledAll }],
        expand: [true],
        exceptionPolicy: [null],
        actionCfg: this.fb.group({
          domain: [''],
          url: [{ value: '', disabled: this.disabledAll }],
          prod: [{ value: '', disabled: this.disabledAll }],
          method: [{ value: '', disabled: this.disabledAll }],
          isAsync: [{ value: false, disabled: this.disabledAll }],
          serviceName: [{ value: '', disabled: this.disabledAll }],
          header: [{ value: this.objectToString({}), disabled: this.disabledAll }],
          requestScript: [{ value: '', disabled: this.disabledAll }],
          responseScript: [{ value: '', disabled: this.disabledAll }],

          // 更新模型状态
          bindForm: this.fb.group({
            modelCode: [null],
            serviceCode: [null],
          }),
          fieldInfos: this.fb.array([
            this.fb.group({
              fieldId: ['manage_status'],
              fieldValue: [null],
              fieldType: ['VARCHAR'],
              fullPath: [''],
              paramType: ['constant'],
            }),
          ]),
        }),
        adpType: [null, [Validators.required]],
      }),
    );
    this.subscriptActionChange(type);
  }

  drop(type: TAction, event: CdkDragDrop<string[]>): void {
    moveItemInArray(this[type]?.controls, event.previousIndex, event.currentIndex);
    moveItemInArray(this[type]?.value, event.previousIndex, event.currentIndex);
    this.getCurrentData();
  }

  /**
   * 编辑条件
   * @param type
   * @param index
   */
  handleEditExpression(type: TAction, index: number): void {
    const defaultExpression = (type === 'pre' ? this.pre : this.after).at(index).get('expression').value;
    this.scriptData =
      defaultExpression ||
      `/*
  此处编写内容为条件表达式，如 $variables['balance'] > 10  该表达式含义为当自定义变量'balance‘大于10时，通过该条件判断
*/`;
    this.scriptModal = true;
    this.editingForm = {
      type,
      index,
      from: 'expression',
    };
  }

  /**
   * 删除动作
   * @param type
   * @param index
   */
  removeAction(type: TAction, index: number): void {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzOnOk: () => {
        const activeForm = this.getActiveForm(type);
        activeForm.removeAt(index);
        this.subscriptActionChange(type);
      },
      nzOnCancel: () => {},
    });
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  /**
   * 展开/收起面板
   * @param e
   * @param type
   * @param index
   */
  toggleExpand(e: MouseEvent, type: TAction, index: number): void {
    e.stopPropagation();
    const activeForm = this.getActiveForm(type);
    activeForm.at(index).patchValue({ expand: !activeForm.at(index).value.expand });
  }

  //#region 模型修改
  async handleModelChange(type: TAction, index: number, value: string): Promise<void> {
    const config = this.getActiveForm(type).at(index).get('actionCfg');
    const serviceCode = config.get('bindForm').get('serviceCode').value;
    const obj: { [k: string]: any } = {};
    if (serviceCode) {
      const [modelCode] = serviceCode.split('&');
      obj['fullPath'] = `${modelCode}.manage_status`;
    } else {
      obj['fullPath'] = null;
    }
    const fieldInfos = config.get('fieldInfos') as FormArray;
    fieldInfos.at(0).patchValue({
      ...obj,
      fieldValue: null,
    });
    if (serviceCode) {
      await this.getModelStatus(serviceCode);
    }
  }

  getDictionaries(path?: string): any[] {
    return this.modelStatus[path] || [];
  }

  //#endregion

  //#region esp methods

  /**
   * 显示ESP动作选择面板
   * @param type
   * @param index
   */
  showEspAction(type: TAction, index: number): void {
    this.editingForm = {
      type,
      index,
    };
    this.espActionVisible = true;
  }

  /**
   * 选择ESP动作面的回掉
   * @param data
   * @param type
   */
  async handleSelectAction(data: any, close: boolean): Promise<void> {
    if (!close && data.actionId) {
      const actionId = data.actionId.startsWith('esp_') ? data.actionId.replace('esp_', '') : data.actionId;
      const { type, index } = this.editingForm;
      this.getActiveForm(type).at(index).get('actionCfg').get('serviceName').setValue(actionId);
      this.espActionVisible = false;
      await this.queryProducts(actionId);
      this.handleSelect(type, index, this.produces[actionId][0]);
    } else {
      this.espActionVisible = false;
    }
    this.editingForm = undefined;
  }

  handleSelect(type: TAction, index: number, item: string): void {
    this.getActiveForm(type).at(index).get('actionCfg').get('prod').setValue(item);
  }

  showScriptModal(type: TAction, index: number, from: 'requestScript' | 'responseScript'): void {
    const defaultScript = this.getActiveForm(type).at(index).get('actionCfg').get(from).value;
    this.editingForm = { type, index, from: 'actionCfg.' + from };
    if (defaultScript) {
      this.scriptData = defaultScript;
    } else {
      const adpType = this.getActiveForm(type).at(index).get('adpType').value;
      this.scriptData = this.getDefaultScript(adpType, from === 'requestScript');
    }
    this.scriptModal = true;
  }
  //#endregion

  //#region 脚本编辑回显
  handleCloseSript(action: 'confirm' | 'close', data: string): void {
    if (action === 'confirm') {
      const { type, index, from } = this.editingForm;
      const activeForm = this.getActiveForm(type);
      if (from) {
        const path = from.split('.');
        let control = activeForm.at(index);
        while (path.length) {
          const first = path.shift();
          control = control.get(first);
        }
        control.setValue(data);
      }
    }
    this.scriptModal = false;
    this.scriptData = undefined;
    this.editingForm = undefined;
  }

  handleDropMenuVisible(visible: boolean, type: TAction, index: number): void {
    this.visibleMap.set(`${type}-${index}`, visible);
  }

  //#endregion

  //#region private methods

  /**
   * 获取产品名
   * @param actionId
   */
  private async queryProducts(actionId: string): Promise<void> {
    try {
      if (!this.produces[actionId]) {
        const res = (await this.apiService.getApiProvider(actionId).toPromise()) as any;
        this.produces[actionId] = res.data || [];
      }
    } catch {}
  }

  private cleanForm(): void {
    this.pre?.clear();
    this.after?.clear();
    this.form?.patchValue({ exceptionPolicy: null });
  }

  private handleInit(): void {
    this.cleanForm();
    const list: TAction[] = ['pre', 'after'];
    list.forEach((type) => {
      this.actions?.[type]?.forEach((item) => {
        const hasValue = this.modelList.find(
          (e) => e.value === `${item.actionCfg?.bindForm?.modelCode}&${item.actionCfg?.bindForm?.serviceCode}`,
        );
        this[type]?.push(
          this.fb.group({
            expression: [{ value: item.expression || '', disabled: this.disabledAll }],
            expand: true,
            exceptionPolicy: item.exceptionPolicy || null,
            adpType: [item.adpType || null, [Validators.required]],
            actionCfg: this.fb.group({
              domain: item.actionCfg?.domain || '',
              url: [{ value: item.actionCfg?.url || '', disabled: this.disabledAll }],
              prod: [{ value: item.actionCfg?.prod || '', disabled: this.disabledAll }],
              method: [{ value: item.actionCfg?.method || '', disabled: this.disabledAll }],
              isAsync: [{ value: item.actionCfg?.isAsync || false, disabled: this.disabledAll }],
              serviceName: [{ value: item.actionCfg?.serviceName || '', disabled: this.disabledAll }],
              header: [{ value: this.objectToString(item.actionCfg?.header || {}), disabled: this.disabledAll }],
              requestScript: [{ value: item.actionCfg?.requestScript || '', disabled: this.disabledAll }],
              responseScript: [{ value: item.actionCfg?.responseScript || '', disabled: this.disabledAll }],

              bindForm: this.fb.group({
                modelCode: hasValue ? item.actionCfg?.bindForm?.modelCode : null,
                serviceCode: hasValue ? hasValue.value : null,
              }),
              fieldInfos: this.fb.array(
                item.actionCfg?.fieldInfos?.map((info) =>
                  this.fb.group({
                    fieldId: info?.fieldId || null,
                    fieldValue: info?.fieldValue || null,
                    fieldType: info?.fieldType || null,
                    fullPath: info?.fullPath || null,
                    paramType: info?.paramType || null,
                  }),
                ) || [],
              ),
            }),
          }),
        );
        if (item.adpType === EActionType.ESP && item.actionCfg?.serviceName) {
          this.queryProducts(item.actionCfg?.serviceName);
        }
      });
      this.subscriptActionChange(type);
    });
    this.form?.get('exceptionPolicy').setValue(this.actions?.exceptionPolicy || 'actionSkip');
    this.getCurrentData();
  }

  /**
   * 获取模型变量
   */
  private async getModelVaribale(): Promise<void> {
    try {
      const res = await this.apiService.getBusinessDirList().toPromise();
      const _modelList: any[] = [];
      res.data.forEach((item) => {
        const models = item.businessDirTree.find((e) => e.type === 'modelDesign')?.businessDirTree || [];
        models.forEach((model) => {
          _modelList.push({
            modelCode: model.businessSubCode,
            serviceCode: model.serviceCode,
            groupLabel: item.isOther ? this.translate.instant('dj-其他') : undefined,
          });
        });
      });
      await this.getModelFields(_modelList);
    } catch {
      // empty
    }
  }

  /**
   * 筛选有manage_status的模型
   * @param params
   */
  private async getModelFields(params: any[]): Promise<void> {
    const map = params.reduce((pre, curr) => {
      pre[`${curr.modelCode}&${curr.serviceCode}`] = curr;
      return pre;
    }, {});
    const data = await this.queryModelByCodes(params);
    const models = data
      .filter((e) => e.model.fields.some((f) => f.fieldId === 'manage_status'))
      .map((e) => {
        const value = `${e.code}&${e.serviceCode}`;
        this.handleOneModel(e.model, value);
        return {
          label: e.lang.name[this.translate.instant('dj-LANG')] || e.name,
          groupLabel: map[value].groupLabel,
          modelCode: e.code,
          serviceCode: e.serviceCode,
          value: value,
        };
      });
    this.modelList = models;
  }

  /**
   * 获取当前编辑的form
   * @param type
   * @returns
   */
  private getActiveForm(type: TAction): FormArray {
    return type === 'pre' ? this.pre : this.after;
  }

  /**
   * 设置监听
   * @param type
   */
  private subscriptActionChange(type: TAction): void {
    const activeForm = this.getActiveForm(type);
    this.unSubject(type);
    const subscribes = activeForm.controls.map((control, i) => {
      const actionCfg = this.getActiveForm(type).at(i).get('actionCfg');
      const setValidators = (value) => {
        const actionCfg = this.getActiveForm(type).at(i).get('actionCfg');
        if (value === EActionType.Modify) {
          actionCfg.get('bindForm').get('serviceCode').setValidators(Validators.required);
        } else {
          actionCfg.get('bindForm').get('serviceCode').setValidators(null);
        }
        const fieldInfoForm = actionCfg.get('fieldInfos') as FormArray;
        fieldInfoForm.controls.forEach((control) => {
          control.get('fieldValue').setValidators(value === EActionType.Modify ? Validators.required : null);
        });
      };
      setValidators(control.get('adpType').value);
      return control.get('adpType').valueChanges.subscribe((value) => {
        setValidators(value);
        actionCfg.patchValue({
          type: null,
          domain: null,
          url: '',
          prod: '',
          method: '',
          isAsync: false,
          serviceName: '',
          header: this.objectToString({}),
          requestScript: '',
          responseScript: '',
          bindForm: {
            serviceCode: null,
          },
          fieldInfos: [
            {
              fieldId: 'manage_status',
              fieldValue: null,
              fieldType: 'VARCHAR',
              fullPath: '',
              paramType: 'constant',
            },
          ],
        });
      });
    });
    this.subjectMap.set(type, subscribes);
  }

  /**
   * 取消监听
   * @param type
   */
  private unSubject(type: TAction): void {
    this.subjectMap.get(type)?.forEach((subject) => subject.unsubscribe());
    this.subjectMap.delete(type);
  }

  /**
   * 获取当前数据
   */
  private getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      validatorForm(this.form);
      this.formGroupValidityFlag = false;
      this.setNodeValid();
      this.nodeErrorHighlight();
      const values = this.form.getRawValue();
      this.handleFormData(cloneDeep(values));
    },
    100,
    { leading: false, trailing: true },
  );

  private handleFormData(values: any): void {
    const list: TAction[] = ['pre', 'after'];
    list.forEach((type) => {
      values[type]?.forEach((item) => {
        item.actionType = item.adpType;
        item.actionCfg.type = item.adpType;
        if (item.actionCfg.header) {
          try {
            item.actionCfg.header = JSON.parse(item.actionCfg.header);
          } catch {}
        }
        if (item.actionCfg?.bindForm?.serviceCode) {
          const [modelCode, serviceCode] = item.actionCfg?.bindForm?.serviceCode.split('&');
          item.actionCfg.bindForm = {
            modelCode,
            serviceCode,
          };
        }
      });
    });
    this.viewStoreService.setState((state) => {
      state.propertiesObj[this.state.currentSelectedNodeId].actions = values;
    });
  }

  private getNodeData(): any {
    const currentSelectedNodeId = this.state.currentSelectedNodeId;
    return this.state.propertiesObj[currentSelectedNodeId];
  }

  private setNodeValid(): void {
    const { _isValidPassed } = this.getNodeData();
    let newValidPassed;
    if (isJSON(_isValidPassed)) {
      newValidPassed = { ..._isValidPassed, [PannelTabsType.ACTION]: this.form.valid };
    } else {
      newValidPassed = { [PannelTabsType.BASE]: _isValidPassed, [PannelTabsType.ACTION]: this.form.valid };
    }
    this.viewStoreService.setState((state) => {
      state.propertiesObj[this.state.currentSelectedNodeId]._isValidPassed = newValidPassed;
    });
  }

  private nodeErrorHighlight() {
    const { currentSelectedNodeId } = this.state;
    const node = this.viewGraphService.graph.getCellById(currentSelectedNodeId);
    const { _isValidPassed } = this.getNodeData();

    node.setData({
      isVerificationPassed: _isValidPassed,
    });
    this.viewToolsService.toolsNodeStyle(node, 'highlight');
  }

  /**
   * 初始化下拉选项
   */
  private initSelectOptions() {
    this.actionOptions = [
      {
        label: 'ESP',
        value: EActionType.ESP,
      },
      {
        label: 'HTTP',
        value: EActionType.HTTP,
      },
      {
        label: this.translate.instant('dj-脚本'),
        value: EActionType.Script,
      },
      // 新需求：临时关闭 跳过 动作
      // {
      //   label: this.translate.instant('dj-跳过'),
      //   value: EActionType.PASS,
      // },
      {
        label: this.translate.instant('dj-修改模型审核状态'),
        value: EActionType.Modify,
      },
    ];
  }

  /**
   * 批量查询模型
   * @param models
   */
  private async queryModelByCodes(models: { modelCode: string; serviceCode: string }[]): Promise<any> {
    const res = await this.apiService.queryModelByCodes(models).toPromise();
    return res.data || [];
  }

  /**
   * 获取模型的ManageStatus的下拉词汇
   * @param fullCode
   */
  private async getModelStatus(fullCode: string): Promise<void> {
    if (!this.modelStatus[fullCode]) {
      const [modelCode, serviceCode] = fullCode.split('&');
      const res = await this.apiService.fetchModelDrivenData({ code: modelCode, serviceCode: serviceCode }).toPromise();
      if (res.code === 0) {
        this.handleOneModel(res.data.model, fullCode);
      }
    }
  }

  private handleOneModel(model: any, fullCode: string): void {
    const dictionaryContent = model.fields.find((e) => e.fieldId === 'manage_status')?.dictionaryContent;
    if (dictionaryContent) {
      const dictionary = JSON.parse(dictionaryContent);
      this.modelStatus[fullCode] = dictionary;
    }
  }

  /**
   * 获取默认的脚本
   * @param type
   * @param isReq
   * @returns
   */
  private getDefaultScript(type: EActionType, isReq: boolean = false): string {
    if (type === EActionType.ESP) {
      if (isReq) return scripts.script_5;
      return responseScript(JSON.stringify({}, null, '\t'));
    }
    if (type === EActionType.HTTP) {
      if (isReq) {
        return `/*
脚本处理请求参数后return内容为接口入参
  var request = {
      'std_data': {
          'parameter': {
          }
      }
  };
  return request;
 */`;
      }
      return `/*
处理返回结果并存储
var response = $(response);
  return {
    "success" : true,
    "processVariable" : {
      "key1" : "value1"
    },
    "errorMessage" : ""
  };
 */`;
    }
    if (type === EActionType.Script) {
      return `/*
进行数据处理，转换，更新
例 拿到当前节点输出中的name更新流程变量name
return{
  "name":$variables['ServiceTask_ec333f586c982d2b019cc8a42a061506']['name'] //当前节点id
}
 */`;
    }
    return undefined;
  }

  private objectToString(obj: any = {}): string {
    if (typeof obj === 'string') return obj;
    return JSON.stringify(obj);
  }
  //#endregion
}
