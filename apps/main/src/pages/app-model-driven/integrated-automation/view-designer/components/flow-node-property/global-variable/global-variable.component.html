<div class="header">
  <span>{{ 'dj-全局变量' | translate }}</span>
  <i adIcon iconfont="icondanchuxiaoxiguanbi" (click)="handleClosePanel()" class="iconfont"></i>
</div>

<section class="content gobal-variable">
  <div class="search-content">
    <nz-input-group [nzSuffix]="suffixIcon">
      <input
        type="text"
        nz-input
        [(ngModel)]="searchValue"
        (change)="handleOnSearch()"
        [placeholder]="'dj-请输入变量名' | translate"
      />
    </nz-input-group>
    <ng-template #suffixIcon>
      <span class="input-clear" *ngIf="!!searchValue">
        <i adIcon type="close-circle" theme="fill" (click)="handleClearSearch()"></i>
      </span>
      <i adIcon iconfont="iconinputserach" class="searchIcon iconfont" aria-hidden="true"> </i>
    </ng-template>
  </div>

  <nz-collapse [nzBordered]="false">
    <!-- 自定义变量 -->
    <nz-collapse-panel [nzHeader]="'dj-自定义变量' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <app-variable-item
        [variableData]="customVariables"
        [showCopy]="true"
        [showOperation]="disabledAll ? false : true"
        [showAdd]="false"
        [showMore]="false"
        variableType="custom"
        (edit)="handleEdit($event)"
      ></app-variable-item>
      <app-variable-item
        [variableData]="businessObjectVariables"
        [variableExtendData]="variableExtendData"
        [showCopy]="true"
        [showAdd]="false"
        [showOperation]="disabledAll ? false : true"
        [showMore]="true"
        variableType="businessObject"
        (edit)="handleEdit($event)"
        (extendDataChangeEmit)="handleExtendDataChange($event)"
      >
      </app-variable-item>
      <app-variable-item
        [variableData]="mechanismVariables"
        [showCopy]="true"
        [showAdd]="disabledAll ? false : true"
        [showOperation]="disabledAll ? false : true"
        [showMore]="false"
        variableType="mechanism"
        (edit)="handleEdit($event)"
      >
      </app-variable-item>
    </nz-collapse-panel>

    <!-- 自定义变量 -->
    <nz-collapse-panel [nzHeader]="'dj-系统变量' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <app-variable-item
        [variableData]="systemVariable"
        [showCopy]="true"
        [showOperation]="false"
        [showAdd]="false"
        [showMore]="false"
        variableType="system"
        (edit)="handleEdit($event)"
      ></app-variable-item>
    </nz-collapse-panel>

    <!-- 自定义变量 -->
    <nz-collapse-panel [nzHeader]="'dj-节点变量' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <app-variable-item
        [variableData]="nodeVariables"
        [showCopy]="true"
        [showOperation]="false"
        [showAdd]="false"
        [showMore]="false"
        variableType="system"
        (edit)="handleEdit($event)"
      ></app-variable-item>
    </nz-collapse-panel>

    <!-- 对象变量 -->
    <!-- <nz-collapse-panel [nzHeader]="'dj-对象变量' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">

    </nz-collapse-panel> -->

    <!-- 自定义变量 -->
    <nz-collapse-panel [nzHeader]="'dj-模型变量' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <app-variable-item
        [variableData]="originModelVariables"
        [variableExtendData]="variableExtendData"
        [showCopy]="true"
        [showAdd]="false"
        [showOperation]="false"
        [showMore]="true"
        (extendDataChangeEmit)="handleExtendDataChange($event)"
      >
      </app-variable-item>
    </nz-collapse-panel>
    <nz-collapse-panel
      [nzHeader]="'dj-DTD变量' | translate"
      [nzActive]="true"
      [nzExpandedIcon]="'caret-right'"
      *ngIf="isFusionMode"
    >
      <app-variable-item
        [variableData]="dtdVariable"
        [showCopy]="true"
        [showOperation]="disabledAll ? false : true"
        [showAdd]="disabledAll ? false : true"
        [showMore]="false"
        variableType="DTDVar"
        (edit)="handleEdit($event)"
      ></app-variable-item>
    </nz-collapse-panel>
  </nz-collapse>
</section>
