import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { ConditionalDispatchService } from "../../conditional-dispatch.service"
import { isNotNone } from 'common/utils/core.utils';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'params-modal',
  templateUrl: './params-modal.component.html',
  styleUrls: ['./params-modal.component.less'],
})
export class ParamsModalComponent implements OnInit {
  @Input() showModal: boolean;
  @Input() initVariable: any;

  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() changeVariable: EventEmitter<any> = new EventEmitter();

  originParamsList: any[];
  paramsList: any[];
  searchValue: string;
  checkedValue: string = '';
  variable: {
    paramCode: string;
    paramName: string,
    sceneCatg: string;
    extendsInfo?: any;
  };
  variableDesc: string = '';
  loading: boolean = true;

  constructor(
    private appService: AppService,
    private conditionalDispatchService: ConditionalDispatchService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.loading = true;
    this.originParamsList = [];
    const res = await this.conditionalDispatchService.loadOpenWindowParams(this.appService?.selectedApp?.code).toPromise()
    const { code, data} = res
    if (code === 0) {
      this.originParamsList = data
    }

    this.handleOnSearch();
    this.loading = false;
    this.initCheckedValue();
  }

  //#region 变量处理
  initCheckedValue() {
    const { paramCode } = this.initVariable;
    this.variable = this.initVariable;
    if (paramCode) {
        this.checkedValue = paramCode;
    }
  }

  //#endregion

  /**
   * 搜索
   */
  handleOnSearch() {
    if (!this.searchValue) {
      this.paramsList = cloneDeep(this.originParamsList);
    } else {
      this.paramsList = this.filterData(cloneDeep(this.originParamsList), this.searchValue.trim());
    }
  }

  /**
   * 过滤器
   * @param data
   * @param searchValue
   * @returns
   */
  filterData(data: any[], searchValue: string): any[] {
    return data
      .map((node) => {
        const filteredNode = { ...node };
        if (filteredNode.paramName?.includes(searchValue)) {
          return filteredNode;
        }
        return null;
      })
      .filter(Boolean);
  }

  /**
   *  清空搜索
   */
  handleClearSearch() {
    this.searchValue = '';
    this.handleOnSearch();
  }

  changeCheckboxValue(node, $event): void {
    if ($event) {
      this.changeRadioValue(node);
    } else {
      //@ts-ignore
      this.variable = {};
      this.variableDesc = '';
    }
  }

  /**
   * 单选
   * @param node
   */
  changeRadioValue(node) {
    this.checkedValue = node.paramCode;
    const {
      paramCode,
      paramName,
      sceneCatg
    } = node;
    this.variable = {
      paramCode,
      paramName,
      sceneCatg,
      extendsInfo: node,
    };
    this.variableDesc = paramName;
  }

  /**
   * 关闭
   */
  handleClose(): void {
    this.close.emit();
  }

  /**
   * 确认
   */
  handleSubmit(): void {
    if (this.checkedValue !== this.initVariable.paramCode) {
      this.changeVariable.emit({ variable: this.variable, variableDesc: this.variableDesc });
    } else {
      this.close.emit();
    }
  }
}
