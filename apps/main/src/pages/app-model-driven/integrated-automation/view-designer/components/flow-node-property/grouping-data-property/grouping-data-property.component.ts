import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DeleteDataService } from '../delete-data-property/delete-data-property.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ViewStoreService } from '../../../service/store.service';
import { QueryData } from 'pages/ai/ai-assistance/assistance-config/custom-components/data-view-filter-content/advanced-query.type';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { cloneDeep, isEmpty, size, debounce, isEqual } from 'lodash';
import { isNone, validatorForm } from 'common/utils/core.utils';
import { GroupingDataPropertyService } from './grouping-data-property.service';
import { FieldSetService } from '../add-data-property/components/field-set/field-set.service';
import { ViewGraphService } from '../../../service/graph.service';
import { VariableService } from '../../../service/variable.service';
import { ViewApiService } from '../../../service/api.service';
import { LocaleService } from 'common/service/locale.service';
import { EVariableRange } from '../variable-select-input/components/variable-modal/variable-modal.component';
import { VariableType } from '../../../config/typings';
import { dataTypeToVariableType, variableTypeToDataType } from 'pages/app-model-driven/utils/utils';
import { debounceTime } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-grouping-data-property',
  templateUrl: './grouping-data-property.component.html',
  styleUrls: ['./grouping-data-property.component.less'],
  providers: [GroupingDataPropertyService, FieldSetService],
})
export class GroupingDataPropertyComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };

  // UI筛选模式是否禁用
  filterDisabled: boolean = false;
  defaultScript: string = `/**
  * 表达式模式为直接执行的表达式，请写正确的表达式，例如：
  * workflowCondition.stock_quantity >500;
  * workflowCondition.part_name.indexOf('弹簧') != -1
  * 批量分组使用分号分隔,其中workflowCondition含义为数据来源
  */`;

  dataFormGroup: FormGroup;
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;

  allFields: any = [];
  currentLang;
  // 这个是自己界面使用的
  groupingConditionList: QueryData[][] = [];
  // 这个是平台使用的
  groupingQueryConditions = [];
  dataViewFilterError: boolean = false;

  showDataSourceModal: boolean = false;
  groupDataSource: any = {};
  dataSourceDesc: string = '';

  private dataSourceCodeSub: Subscription | undefined = undefined;
  private lastFormValue: any | undefined = undefined;

  groupTypeList = [
    { label: this.translate.instant('dj-字段分组'), value: 'byKey' },
    { label: this.translate.instant('dj-条件分组'), value: 'byCondition' },
  ];

  // 筛选过滤器类型转换
  dataTypeTransform: Map<string, string> = new Map([
    ['Boolean', 'boolean'],
    ['Integer', 'number'],
    ['Decimal', 'number'],
    ['String', 'string'],
    ['DateTime', 'datetime'],
  ]);

  scriptModalVisible: boolean = false;
  groupingQueryConditionScript: string = '';

  get variableRange() {
    const mode =
      EVariableRange.BUSINESS |
      EVariableRange.CUSTOM |
      EVariableRange.MODEL |
      EVariableRange.NODE |
      EVariableRange.SYSTEM;
    if (this.viewStoreService.isFusionMode()) return mode | EVariableRange.DTD;
    return mode;
  }

  get item() {
    if (isEmpty(this.groupDataSource)) return undefined;
    const type = dataTypeToVariableType(this.groupDataSource.dataSourceType);
    const isModel = [VariableType.MODEL, VariableType.BUSINESS_OBJECT].includes(type);
    return {
      dataSourceSuffix: this.groupDataSource?.dataSourceSuffix,
      valueType: type,
      valuePath: isModel ? this.groupDataSource.dataSourceCode : this.groupDataSource.dataSourcePath, // 模型/业务对象
      valueCode: isModel ? this.groupDataSource.dataSourcePath : this.groupDataSource.dataSourceCode, // 变量
    };
  }

  get disabledAll() {
    return !!this.viewGraphService?.fromManual;
  }

  constructor(
    public translate: TranslateService,
    private message: NzMessageService,
    private fieldSetService: FieldSetService,
    private fb: FormBuilder,
    private groupService: GroupingDataPropertyService,
    private viewStoreService: ViewStoreService,
    private variableService: VariableService,
    private viewGraphService: ViewGraphService,
    public viewApiService: ViewApiService,
  ) {
    this.currentLang = this.translate.currentLang;
    this.variableService.variableRefresh$.pipe(debounceTime(120)).subscribe((res) => {
      this.checkAndChangeVariable([
        'customVariable',
        'systemVariable',
        'nodeVariable',
        'businessObjectVariable',
        'modelVariable',
      ]);
    });
  }

  async ngOnInit(): Promise<void> {
    await this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe((res) => {
      if (isEqual(this.lastFormValue, res)) return;
      this.lastFormValue = cloneDeep(res);
      this.getCurrentData();
    });
  }

  async ngOnChanges(changes: SimpleChanges): Promise<void> {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.lastFormValue = undefined;
        await this.handleChangeValue();
      }
    }
  }

  /**
   * 切换相同节点类型的不同节点
   */
  async handleChangeValue(): Promise<void> {
    this.formGroupValidityFlag = true;

    const { dataSourceCode, dataSourceType, dataSourcePath, dataSourceSuffix } = this.data.groupDataSource ?? {};
    const groupType = this.data?.groupType || 'byKey';
    const filterType = this.data?.filterType || 'filter';
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      groupType,
      filterType,
    });
    this.dataSourceCodeSub?.unsubscribe();
    if ('byKey' === groupType) {
      this.dataFormGroup.patchValue({
        groupData: this.data?.groupData,
      });
      this.dataFormGroup.removeControl('groupingQueryConditionScript');
    } else {
      if ('script' === filterType) {
        this.dataFormGroup.removeControl('groupingQueryConditionScript');
        this.dataFormGroup.addControl(
          'groupingQueryConditionScript',
          this.fb.control(
            { value: this.data?.groupingQueryConditionScript || '', disabled: this.disabledAll },
            Validators.required,
          ),
        );
      }
      this.dataFormGroup.removeControl('groupData');
    }
    this.dataFormGroup.patchValue({
      dataSourceCode: ['businessObjectVariable', 'modelVariable'].includes(dataSourceType)
        ? dataSourcePath
        : dataSourceCode,
      dataSourceSuffix,
    });
    this.groupDataSource = this.data?.groupDataSource ?? {};
    await this.checkAndChangeVariable([
      'customVariable',
      'systemVariable',
      'nodeVariable',
      'businessObjectVariable',
      'modelVariable',
    ]);
    await this.handleDefaultInit();
    this.addDataSourceSubject();
    this.formGroupValidityFlag = false;
    this.getCurrentData();
  }

  private addDataSourceSubject(): void {
    if (this.dataSourceCodeSub) {
      this.dataSourceCodeSub?.unsubscribe();
    }
    this.dataSourceCodeSub = this.dataFormGroup
      .get('dataSourceCode')
      .valueChanges.pipe(debounceTime(50))
      .subscribe((value) => {
        this.handleChangeDataSourceCode();
      });
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  /**
   * 初始化dataFormGroup
   */
  async handleInit(): Promise<void> {
    const { dataSourceCode, dataSourceType, dataSourcePath, dataSourceSuffix } = this.data.groupDataSource ?? {};
    const groupType = this.data?.groupType || 'byKey';
    const filterType = this.data?.filterType || 'filter';
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      dataSourceCode: [
        (['businessObjectVariable', 'modelVariable'].includes(dataSourceType) ? dataSourcePath : dataSourceCode) ||
          null,
        [Validators.required],
      ],
      dataSourceSuffix: [{ value: dataSourceSuffix || null, disabled: this.disabledAll }],
      groupType: [{ value: groupType, disabled: this.disabledAll }, [Validators.required]],
      filterType: [{ value: filterType, disabled: this.disabledAll }],
    });
    if ('byKey' === groupType) {
      this.dataFormGroup.addControl(
        'groupData',
        this.fb.control({ value: this.data?.groupData || '', disabled: this.disabledAll }, Validators.required),
      );
    } else {
      if ('script' === filterType) {
        this.dataFormGroup.addControl(
          'groupingQueryConditionScript',
          this.fb.control(
            { value: this.data?.groupingQueryConditionScript || '', disabled: this.disabledAll },
            Validators.required,
          ),
        );
      }
    }

    this.groupDataSource = this.data?.groupDataSource ?? {};
    await this.checkAndChangeVariable([
      'customVariable',
      'systemVariable',
      'nodeVariable',
      'businessObjectVariable',
      'modelVariable',
    ]);
    await this.handleDefaultInit();
    this.handleUpdateFilterMode(filterType, true);
    this.addDataSourceSubject();
  }

  async handleDefaultInit() {
    this.nameLang = this.data.lang;
    await this.loadDataViewFields(false);
    this.groupingConditionList = cloneDeep(this.data?.groupingConditionList || []);
    this.groupingQueryConditions = cloneDeep(this.data?.groupingQueryConditions || []);
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleChangGroupType() {
    const groupType = this.dataFormGroup.get('groupType').value;
    if ('byKey' === groupType) {
      this.dataFormGroup.addControl(
        'groupData',
        this.fb.control({ value: '', disabled: this.disabledAll }, Validators.required),
      );
      // 当选择了字段分组的时候，UI/脚本模式要取消验证
      this.dataFormGroup.removeControl('groupingQueryConditionScript');
      this.dataViewFilterError = false;
      this.handleDataViewFilterChange({
        groupingConditionList: [],
        groupingQueryConditions: [],
      });
    } else if ('byCondition' === groupType) {
      this.dataFormGroup.removeControl('groupData');

      const { dataSourceType } = this.groupDataSource ?? {};
      if (['businessObjectVariable', 'modelVariable'].includes(dataSourceType)) {
        this.handleUpdateFilterMode('filter', true);
      } else {
        this.handleUpdateFilterMode('script', true);
      }
      this.handleChangeFilterType();
    }
  }

  handleChangeFilterType() {
    const filterType = this.dataFormGroup.get('filterType').value;
    if ('filter' === filterType) {
      this.dataFormGroup.removeControl('groupingQueryConditionScript');
    } else if ('script' === filterType) {
      this.dataFormGroup.addControl(
        'groupingQueryConditionScript',
        this.fb.control({ value: '', disabled: this.disabledAll }, Validators.required),
      );
      this.handleDataViewFilterChange({
        groupingConditionList: [],
        groupingQueryConditions: [],
      });
    }
  }

  /**
   * 点击打开脚本编辑器
   */
  handleOpenScript(): void {
    this.scriptModalVisible = true;
    this.groupingQueryConditionScript =
      this.dataFormGroup.get('groupingQueryConditionScript').value || this.defaultScript;
  }

  /**
   * 关闭或确认打开脚本编辑器
   * @param type
   * @param data
   */
  handleCloseScript(type, data): void {
    this.scriptModalVisible = false;
    if (type === 'confirm') {
      this.dataFormGroup.get('groupingQueryConditionScript').setValue(data);
    }
  }

  handleChangeDataSourceCode() {
    this.loadDataViewFields();
    // 之前配置的筛选器清空
    this.groupingConditionList = [];
    this.groupingQueryConditions = [];
  }

  /**
   * 查询模型数据
   * @param param
   */
  async loadDataViewFields(patchValue: boolean = true) {
    const { dataSourceCode, dataSourceType, dataSourcePath, dataSourceSuffix } = this.groupDataSource ?? {};
    this.allFields = [];
    if (!isEmpty(dataSourceSuffix)) {
      if (['businessObjectVariable', 'modelVariable'].includes(dataSourceType)) {
        this.allFields = [
          {
            data_name: `${dataSourcePath}${dataSourceSuffix}`,
            data_type: 'string',
            description: '',
          },
        ];
        this.handleUpdateFilterMode('filter', patchValue);
      } else {
        this.allFields = [];
        this.handleUpdateFilterMode('script', patchValue);
      }
    } else {
      const {
        propertiesObj,
        originalFlowData: { code },
      } = this.viewStoreService.state;
      switch (dataSourceType) {
        case 'businessObjectVariable':
          const { businessObjectVariables = [] } = propertiesObj?.[code];
          const businessVariableInfo = await this.getBusinessObjectVariableInfo(businessObjectVariables);
          const variableInfo = businessVariableInfo.find((e) => e.variableCode === dataSourceCode);
          const { modelFileInfos = [] } = variableInfo?.modelFieldInfoVo ?? {};
          this.allFields = modelFileInfos
            .filter((e) => !!e.data_name)
            .map((i) => {
              const { data_name, data_type, description, lang, fullPath, search_table_path } = i;
              return {
                data_name,
                data_type,
                description,
                lang: {
                  description: lang.description,
                },
                fullPath,
                search_table_path,
              };
            });
          this.handleUpdateFilterMode('filter', patchValue);
          break;
        case 'modelVariable':
          const { modelVariables = [] } = propertiesObj?.[code];
          const modelVariableInfo = await this.getModelVariableInfo(modelVariables);
          const variableInfo2 = modelVariableInfo.find((e) => e.variableCode === dataSourceCode);
          const { modelFileInfos: modelFileInfos2 = [] } = variableInfo2?.modelFieldInfoVo ?? {};
          this.allFields = modelFileInfos2
            .filter((e) => !!e.data_name)
            .map((i) => {
              const { data_name, data_type, description, lang, fullPath, search_table_path } = i;
              return {
                data_name,
                data_type,
                description,
                lang: {
                  description: lang.description,
                },
                fullPath,
                search_table_path,
              };
            });
          this.handleUpdateFilterMode('filter', patchValue);
          break;
        default:
          this.handleUpdateFilterMode('script', patchValue);
          break;
      }
    }
  }

  /**
   * 设置模式
   * @param mode
   */
  private handleUpdateFilterMode(mode: 'filter' | 'script', patchValue: boolean = true) {
    if (patchValue && this.dataFormGroup.get('filterType').value !== mode) {
      this.dataFormGroup.patchValue({
        filterType: mode,
      });
    }
    this.filterDisabled = mode === 'script';
  }

  /**
   * 筛选器back
   * @param e
   */
  handleDataViewFilterChange(e) {
    this.groupingConditionList = e.conditionList;
    this.groupingQueryConditions = e.queryConditions;
    this.getCurrentData();
  }

  /**
   * 条件的校验规则
   */
  getConditionPassed(data) {
    let childPassed = true;
    if (size(data.groupingConditionList) == 0) {
      childPassed = false;
      this.dataViewFilterError = true;
    } else {
      this.dataViewFilterError = false;
      childPassed = true;
    }
    return childPassed;
  }

  // 获取当前最新的数据
  getCurrentData = debounce(
    () => {
      if (this.formGroupValidityFlag) {
        return;
      }
      this.formGroupValidityFlag = true;
      validatorForm(this.dataFormGroup);
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();

      let data = Object.assign(this.data, currentData, { lang: this.nameLang });
      data.groupDataSource = { ...this.groupDataSource, dataSourceSuffix: data?.dataSourceSuffix };
      Reflect.deleteProperty(data, 'dataSourceCode');
      Reflect.deleteProperty(data, 'dataSourceSuffix');

      data.groupingConditionList = this.groupingConditionList;
      data.groupingQueryConditions = this.groupingQueryConditions;

      const childPassed =
        'byCondition' === this.dataFormGroup.get('groupType').value &&
        'filter' === this.dataFormGroup.get('filterType').value
          ? this.getConditionPassed(data)
          : true;
      // 混入isVerificationPassed 是否校验通过
      let isVerificationPassed = this.viewStoreService.transformVerificationPassed(
        this.dataFormGroup.valid && childPassed,
      );
      const returnData = {
        data: data,
        isVerificationPassed,
      };
      this.changeData.emit(returnData);
      return returnData;
    },
    120,
    { leading: false, trailing: true },
  );

  /**
   * 选择变量
   */
  handleChangeVariable(
    e: {
      dataSource: {
        dataSourceCode: string;
        dataSourceType: string;
        dataSourcePath: string;
      };
      dataSourceDesc: string;
    },
    dataSourceSuffix: string,
  ) {
    this.showDataSourceModal = false;
    this.groupDataSource = { ...e.dataSource, dataSourceSuffix: dataSourceSuffix };
    this.dataSourceDesc = e.dataSourceDesc;
    const { dataSourceCode, dataSourceType, dataSourcePath } = e.dataSource;
    if (['businessObjectVariable', 'modelVariable'].includes(dataSourceType)) {
      this.dataFormGroup.patchValue({
        dataSourceCode: dataSourcePath,
        dataSourceSuffix: dataSourceSuffix,
      });
    } else {
      this.dataFormGroup.patchValue({
        dataSourceCode: dataSourceCode,
        dataSourceSuffix: dataSourceSuffix,
      });
    }
  }

  handleChangeDataSourceSuffix() {
    this.handleChangeDataSourceCode();
    this.getCurrentData();
  }

  /**
   * 检测变量
   * @param groupDataSource
   * @returns
   */
  async checkAndChangeVariable(
    checkTypeList: (
      | 'customVariable'
      | 'systemVariable'
      | 'nodeVariable'
      | 'businessObjectVariable'
      | 'modelVariable'
    )[],
  ) {
    const { dataSourceCode, dataSourceType, dataSourcePath } = this.groupDataSource ?? {};
    if (!checkTypeList.includes(dataSourceType)) {
      return;
    }
    const {
      propertiesObj,
      originalFlowData: { code },
    } = this.viewStoreService.state;
    const process = propertiesObj?.[code];
    const { systemVariable = [], customVariables = [], businessObjectVariables = [], modelVariables = [] } = process;

    switch (dataSourceType) {
      case 'customVariable':
        const cV = customVariables.find((c) => c.varName === dataSourceCode);
        if (isNone(cV)) {
          this.groupDataSource = {};
          this.handleChangeDataSourceCode();
        } else {
          this.dataSourceDesc = cV?.description ?? '';
        }
        break;
      case 'systemVariable':
        const sV = systemVariable.find((s) => s.varName === dataSourceCode);
        if (isNone(sV)) {
          this.groupDataSource = {};
          this.handleChangeDataSourceCode();
        } else {
          this.dataSourceDesc = sV?.description ?? '';
        }
        break;
      case 'nodeVariable':
        const node: any = this.viewGraphService.graph.getCellById(this.nodeId.split('_')[1]);
        const availableNodeVariables = this.viewGraphService.getNodesVariable({ type: 'pre', node });
        const nV = availableNodeVariables.find((n) => n.nodeId === dataSourceCode);
        if (isNone(nV)) {
          this.groupDataSource = {};
          this.handleChangeDataSourceCode();
        } else {
          this.dataSourceDesc = nV?.name ?? '';
        }
        break;
      case 'businessObjectVariable':
        const businessVariableInfo = await this.getBusinessObjectVariableInfo(businessObjectVariables);
        const variableInfo = businessVariableInfo.find((e) => e.variableCode === dataSourceCode);
        const { modelCode, lang } = variableInfo?.modelFieldInfoVo ?? {};
        const oV = modelCode === dataSourcePath;
        if (!oV) {
          this.groupDataSource = {};
          this.handleChangeDataSourceCode();
        } else {
          this.dataSourceDesc = `${lang?.comment?.[this.currentLang]}`;
        }
        break;
      case 'modelVariable':
        const modelVariableInfo = await this.getModelVariableInfo(modelVariables);
        const variableInfo2 = modelVariableInfo.find((e) => e.variableCode === dataSourceCode);
        const { modelCode: modelCode2, lang: lang2 } = variableInfo2?.modelFieldInfoVo ?? {};
        const oV2 = modelCode2 === dataSourcePath;
        if (!oV2) {
          this.groupDataSource = {};
          this.handleChangeDataSourceCode();
        } else {
          this.dataSourceDesc = `${lang2?.comment?.[this.currentLang]}`;
        }
        break;
      default:
        this.groupDataSource = {};
        break;
    }
    if (isNone(this.groupDataSource?.dataSourceCode)) {
      this.dataFormGroup.patchValue({
        dataSourceCode: null,
        dataSourceSuffix: null,
      });
    }
  }

  /**
   * 获取对象节点的信息
   * @param businessObjectVariables
   * @returns
   */
  private getBusinessObjectVariableInfo = async (businessObjectVariables: any[]) => {
    if (!businessObjectVariables.length) return [];
    const params = businessObjectVariables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modeCode,
      variableCode: item.varName,
    }));
    const result = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (result.code === 0) return result.data;
    return [];
  };

  /**
   * 获取模型节点的信息
   * @param businessObjectVariables
   * @returns
   */
  private getModelVariableInfo = async (variables: any[]) => {
    if (!variables.length) return [];
    const params = variables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modelCode,
      variableCode: item.uniqueKey,
    }));
    const result = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (result.code === 0) return result.data;
    return [];
  };

  /**
   * 分组数据来源的Tooltip
   * @returns
   */
  getGroupDataSourceTooltip(): string {
    const value = this.dataFormGroup.get('dataSourceCode').value;
    const suffix = this.dataFormGroup.get('dataSourceSuffix').value;
    if (isEmpty(value)) {
      return '';
    }
    return isEmpty(suffix) ? `${this.dataSourceDesc}（${value}）` : `${this.dataSourceDesc}（${value}${suffix}）`;
  }

  /**
   * 分组数据来源的placeholder
   * @returns
   */
  getGroupDataSourcePlaceholder(): string {
    const value = this.dataFormGroup.get('dataSourceCode').value;
    if (isEmpty(value)) {
      return this.translate.instant('dj-请选择');
    }
    // return this.translate.instant('dj-请输入');
    return '';
  }

  /**
   * 字段设置的Tooltip
   * @returns
   */
  getGroupDataTooltip(): string {
    const value = this.dataFormGroup.get('groupData')?.value;
    if (isEmpty(value)) {
      return this.translate.instant('dj-请输入字段，以“，”分隔');
    }
    return value ?? '';
  }

  handleValueChanged(event: any): void {
    const { valueType, variableDesc, variableSuffix, variableCode, valueCode } = event;
    const type = variableTypeToDataType(valueType);
    const isModel = ['businessObjectVariable', 'modelVariable'].includes(type);
    const e = {
      dataSource: {
        dataSourceType: type,
        dataSourceCode: valueCode,
        dataSourcePath: isModel ? variableCode : '',
      },
      dataSourceDesc: variableDesc,
    };
    this.handleChangeVariable(e, variableSuffix);
  }
}
