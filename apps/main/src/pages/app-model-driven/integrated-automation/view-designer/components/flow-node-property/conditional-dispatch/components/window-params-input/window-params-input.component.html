<nz-input-group [nzPrefix]="prefixTpl" [nzSuffix]="modalTpl" class="input-group-wrap">
  <input
    [(ngModel)]="paramName"
    (ngModelChange)="handleSelChange()"
    [ngModelOptions]="{ standalone: true }"
    nz-input
    type="text"
    [placeholder]="!formPrefix ? ('dj-请选择' | translate) : ''"
    nz-tooltip
    nzTooltipTrigger="hover"
    nzTooltipPlacement="topLeft"
    [nzTooltipTitle]="getVariableTooltip()"
    [readonly]="!inputable || !formPrefix"
  />
</nz-input-group>
<ng-template #prefixTpl>
  <ng-container *ngIf="formPrefix">
    <span
      nz-tooltip
      nzTooltipTrigger="hover"
      nzTooltipPlacement="topLeft"
      [nzTooltipTitle]="getVariableTooltip()"
      [style]="{
        maxWidth: getPrefixWidth(),
        'white-space': 'nowrap',
        overflow: 'hidden',
        'text-overflow': 'ellipsis'
      }"
    >
      {{ formPrefix }}
    </span>
  </ng-container>
</ng-template>
<ng-template #modalTpl>
  <i class="icon" adIcon [iconfont]="'iconkaichuang1'" (click)="showVariableModal = true"></i>
</ng-template>

<params-modal
  *ngIf="showVariableModal"
  [showModal]="showVariableModal"
  [initVariable]="variable"
  (close)="showVariableModal = false"
  (changeVariable)="handleChangeVariable($event)"
></params-modal>
