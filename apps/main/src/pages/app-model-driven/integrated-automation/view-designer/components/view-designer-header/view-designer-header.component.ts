import { Component, OnInit, EventEmitter, Input, Output, TemplateRef, ViewChild, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ViewStoreService } from '../../service/store.service';
import { ViewGraphService } from '../../service/graph.service';
import { ViewApiService } from '../../service/api.service';
import { AppService } from 'pages/apps/app.service';
import { ViewToolsService } from '../../service/tools.service';
import { NodeType, PannelTabsType, TriggerType, VariableType } from '../../config/typings';
import { isObjectAndHasProperty } from '../../config/utils';
import { ActivatedRoute } from '@angular/router';
import { VariableService } from '../../service/variable.service';
import { isJSON } from 'common/utils/core.utils';
import { checkModelHasValuePath, ergodicList } from 'pages/app-model-driven/utils/utils';
import { isEmpty } from 'lodash';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-view-designer-header',
  templateUrl: './view-designer-header.component.html',
  styleUrls: ['./view-designer-header.component.less'],
})
export class ViewDesignerHeaderComponent implements OnInit, OnDestroy {
  @Input() headerCustomTemplate: TemplateRef<any> | null = null;
  @Input() refreshMenuAndCrumbsName: boolean = false;
  @Input() isHideSaveAndPublish: boolean = false; // 是否隐藏保存和发布按钮
  @Output() operateOpenNodeMenu: EventEmitter<any> = new EventEmitter(); // 新增任务
  @Output() operateOriginalSize: EventEmitter<any> = new EventEmitter(); // 操作 还原画布原始尺寸
  @Output() operateEnlarge: EventEmitter<any> = new EventEmitter(); // 操作 放大
  @Output() operateReduce: EventEmitter<any> = new EventEmitter(); // 操作 缩小
  @Output() operateUndo: EventEmitter<any> = new EventEmitter(); // 操作 撤销
  @Output() operateRedo: EventEmitter<any> = new EventEmitter(); // 操作 重做
  @Output() setViewDesign: EventEmitter<any> = new EventEmitter(); // 全局设置流程flow
  @Output() updateBreadcrumb: EventEmitter<any> = new EventEmitter(); // 更新面包屑
  @Output() reloadPage: EventEmitter<any> = new EventEmitter(); // 发布
  popoverVisible: boolean;
  variableModalVisible: boolean; // 流程变量开窗
  isTenantActive = false; // 租户级 开发平台 是否激活

  get objectId() {
    const { objectId } = this.activatedRoute.snapshot.params;
    return objectId;
  }

  // 是否是租户级流程的编辑（当前是通过路由中的objectId判断）
  get isTenant() {
    return !!this.objectId;
  }

  // 租户级的流程 只能选择一个 租户，且 发布后 不可变更
  get tenantUserId() {
    const { originalFlowData } = this.viewStoreService.state;
    return originalFlowData?.adpTenantList?.[0]?.tenantId ?? null;
  }

  // 租户级流程可能存在的运营单元信息
  get eocInfo() {
    const { originalFlowData } = this.viewStoreService.state;
    return originalFlowData?.eocInfo ?? null;
  }

  get taskCode() {
    if (this.viewStoreService.isFusionMode()) return this.viewStoreService.state?.originalFlowData?.taskCode;
    return undefined;
  }

  get graph() {
    return this.viewGraphService.graph;
  }

  get processId() {
    const { originalFlowData } = this.viewStoreService.state;
    return originalFlowData?.processId;
  }

  get processName() {
    const { originalFlowData, propertiesObj } = this.viewStoreService.state;
    return propertiesObj?.[originalFlowData?.processId]?.processName;
  }

  get processSettingValid() {
    const { originalFlowData, propertiesObj } = this.viewStoreService.state;
    const _valid = propertiesObj?.[originalFlowData?.processId]?._isValidPassed;
    return isJSON(_valid) ? _valid?.base : _valid;
  }

  get triggerType() {
    const { originalFlowData } = this.viewStoreService.state;
    return originalFlowData.triggerType;
  }

  get effectData() {
    return {
      processId: this.processId,
      adpVersion: this.viewGraphService.currentSelectedVersion?.adpVersion,
    };
  }

  // 是否是引用的dtd
  get isFromDtdReference() {
    return this.viewStoreService.state?.isFromDtdReference;
  }

  /**
   * 是否来自签核自定义
   */
  get isFromManual() {
    return this.viewGraphService.fromManual;
  }

  get projectCatetory() {
    return this.viewStoreService.state.projectCategory;
  }

  TriggerType = TriggerType;
  listActiveTab: string = '';
  listActivePageSize: number;
  listActivePageNumber: number;
  // 来源
  pageSource?: string;

  versionList: any[] = [];
  manageVersionVisible: boolean = false;
  versionRemarkVisible: boolean = false;
  addVersionLoading: boolean = false;

  loadingVersion: boolean = false;

  destroy$ = new Subject();

  // 发布按钮的句柄
  @ViewChild('publishButton', { static: false }) publishButtonRef: any;

  constructor(
    private location: Location,
    private router: Router,
    private appService: AppService,
    private translateService: TranslateService,
    private athMessageService: NzMessageService,
    public viewStoreService: ViewStoreService,
    public viewGraphService: ViewGraphService,
    private viewToolsService: ViewToolsService,
    public viewApiService: ViewApiService,
    private activatedRoute: ActivatedRoute,
    private variableService: VariableService,
    public adUserService: AdUserService,
  ) {
    this.activatedRoute.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (params['activeTab']) this.listActiveTab = params['activeTab'];
      if (params['pageSize']) this.listActivePageSize = params['pageSize'];
      if (params['pageNum']) this.listActivePageNumber = params['pageNum'];
      if (params['pageSource']) this.pageSource = params['pageSource'];
    });
  }

  ngOnInit(): void {
    this.activatedRoute.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (params?.id) {
        this.getVersions(params?.id, this.activatedRoute.snapshot.queryParams?.adpVersion);
      }
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 查询流程的所有版本
   * @param processId
   */
  getVersions(processId, adpVersion?: string) {
    this.loadingVersion = true;
    this.viewApiService
      .loadProcessVersionList({
        processId: processId,
        adpStatus: '',
        timeType: '',
        adpRemark: '',
      })
      .subscribe(
        (res) => {
          this.loadingVersion = false;
          let versionData: any = {};
          this.versionList = res?.data || [];
          // 路由带过来的版本
          if (adpVersion) {
            versionData = res?.data?.find((item) => item.adpVersion === adpVersion);
          }
          //路由带过来的版本号被删除
          if (isEmpty(versionData)) {
            versionData = res?.data?.find((item) => item.adpStatus === 'effect');
          }
          this.viewGraphService.currentSelectedVersion = versionData;
          if (!isEmpty(versionData)) {
            this.viewApiService.headers = {
              adpVersion: versionData.adpVersion,
              adpStatus: versionData.adpStatus,
            };
          }
        },
        () => {
          this.versionList = [];
          this.loadingVersion = false;
        },
      );
  }

  handleRefreshVersionList() {
    this.getVersions(this.processId, this.viewGraphService.currentSelectedVersion?.adpVersion);
  }

  // 返回
  goback() {
    if (this.pageSource === 'workflow-task') {
      // 从新T的设计器来的
      this.location.back();
    } else {
      const url = this.isFromManual
        ? 'app/integrated-automation/data-execution-manual'
        : 'app/integrated-automation/data-execution-new';
      this.router.navigate([url], {
        queryParams: {
          appCode: this.appService?.selectedApp?.code,
          // TODO:暂不需要记录列表的状态
          // active: this.listActiveTab || this.triggerType,
          // pageNum: this.listActivePageNumber,
          // pageSize: this.listActivePageSize,
        },
      });
    }
  }

  /**
   * 版本管理
   */
  handleVersionManagement() {
    this.manageVersionVisible = true;
  }

  handleUpdateRemark() {
    this.versionRemarkVisible = true;
  }

  /**
   * 修改版本备注
   */
  handleUpdateRemarkOK() {
    this.getVersions(this.processId, this.viewGraphService.currentSelectedVersion?.adpVersion);
  }

  /**
   * 关闭版本管理弹窗
   */
  handleManageVisible() {
    this.manageVersionVisible = false;
    this.handleUpdateRemarkOK();
  }

  handleAddVersion() {
    this.handleOperateSave(false, true);
  }

  /**
   * 新增版本
   */
  handleCreateNewVersion() {
    if (this.versionList?.length > 9) {
      this.athMessageService.error(
        this.translateService.instant('dj-当前设计版本数量已达上限，请选择已有设计版本修改'),
      );
      return;
    }
    this.addVersionLoading = true;
    this.viewApiService
      .createProcessVersion({
        processId: this.processId,
        adpVersion: this.viewGraphService.currentSelectedVersion?.adpVersion,
      })
      .subscribe(
        (res) => {
          this.addVersionLoading = false;
          this.athMessageService.success(this.translateService.instant('dj-创建成功，将跳转到创建生成的版本'));
          this.viewGraphService.adpVersion$.next({
            adpVersion: res?.data?.adpVersion,
            adpStatus: res?.data?.adpStatus,
          });
          this.getVersions(this.processId, res?.data?.adpVersion);
        },
        () => {
          this.addVersionLoading = false;
        },
      );
  }

  handleSelectVersion(item) {
    const { adpVersion } = this.viewGraphService.currentSelectedVersion;
    if (item.adpVersion === adpVersion) {
      this.athMessageService.error(this.translateService.instant('dj-该版本是当前设计器查看的版本'));
      return;
    }
    this.viewGraphService.adpVersion$.next({
      adpVersion: item?.adpVersion,
      adpStatus: item?.adpStatus,
    });
    this.viewGraphService.currentSelectedVersion = item;
  }

  updateMenuAndCrumbsName(flowData) {
    const data: any = {
      businessCode: flowData?.businessCode,
      menuType: 'businessProcess',
      operatorType: 'edit',
      navigateUrl: '',
    };
    this.updateBreadcrumb.emit(data);
  }

  // check所有条件节点&并行节点的关联模型里挂载的nodeId是否是最近的节点的nodeId，如果不是则修改成最近的nodeId
  preCheckAndModifyModelRelateNodeId(params) {
    const needCheckNodeTypeLists = [NodeType.CONDITION_BRANCH, NodeType.PARALLEL_BRANCH];
    if (params?.processConfig?.nodes?.length > 0) {
      params.processConfig.nodes = params.processConfig.nodes.map((node) => {
        if (needCheckNodeTypeLists.includes(node._nodeType) && node?.modelSelObj?.code) {
          const cell = this.viewGraphService.graph.getCellById(node._nodeId);
          const preNodesMode = this.viewGraphService.getAllPredecessorsNodesMode(cell);
          const findNode = preNodesMode?.find(
            (item) => item.code === node.modelSelObj.code || item.modelCode === node.modelSelObj.code,
          );
          if (findNode && node.modelSelObj.nodeId !== findNode.nodeId) {
            return {
              ...node,
              modelSelObj: {
                ...node.modelSelObj,
                nodeId: findNode.nodeId,
              },
            };
          }
        }
        return node;
      });
    }
    return params;
  }

  // 保存
  async handleOperateSave(isAfterSaveNeedPublish = false, isAddNewVersion = false) {
    this.viewGraphService.isSaveLoading = true;
    // 检查条件分支节点的模型与前序人工节点是否匹配
    this.checkAndSetPreManualNodes();
    this.checkAndSetPreDataNodes();
    this.checkAndSetPreModelNodes();
    await this.checkNodesIsValid();
    // 检查流程配置是否已完善
    if (!this.processSettingValid) {
      this.athMessageService.warning(this.translateService.instant('dj-请点击【设置】按钮完善业务流设置'));
      this.viewGraphService.isSaveLoading = false;
      return;
    }

    // 检查所有节点是否已完善
    const checkNodeIsVerificationPassed = this.checkNodeIsVerificationPassed();
    if (!checkNodeIsVerificationPassed) {
      this.athMessageService.warning(this.translateService.instant('dj-业务流配置不完善，请点击标红节点完善配置'));
      this.viewGraphService.isSaveLoading = false;
      return;
    }

    // 检查流程开始到结束连线是否完善
    const checkFlowIsComplete = this.checkFlowIsComplete();
    if (!checkFlowIsComplete) {
      this.athMessageService.warning(this.translateService.instant('dj-业务流配置不完善，请连接节点完成流程配置'));
      this.viewGraphService.isSaveLoading = false;
      return;
    }

    // 关闭浮层节点菜单
    this.viewGraphService.handleCloseNodeMenu();
    // 通知关闭当前节点的属性面板
    this.viewGraphService.isShowPropertiesPanel$.next({
      isShow: false,
      propertiesPanel: null,
    });
    // 清除所有节点的高亮和工具
    this.viewToolsService.toolsClearAllNodesTools(this.graph);
    // 清除所有线的高亮和工具
    this.viewToolsService.toolsClearAllEdgesTools(this.graph);

    // 流程参数
    let params = this.viewGraphService.handleFlowDataParams();

    // 前序check完成，这时check一下模型相关nodeId是否是最近的nodeId，如果不是则更新成最近的nodeId
    params = this.preCheckAndModifyModelRelateNodeId(params);

    params.flowGraph?.links?.forEach((link) => {
      if (link?.router?.name) link.router.name = 'manhattan';
      // 去除连接线的锚点
      link.vertices = undefined;
    });
    // 当前的逻辑 ，有objectId 就是 租户级
    const { objectId } = this.activatedRoute.snapshot.params;
    const { addSourceType } = this.activatedRoute.snapshot.queryParams;
    let requestApi = 'postUpsertProcess';
    if (objectId) requestApi = 'postUpsertProcessTenant';
    if (addSourceType) params['addSourceType'] = addSourceType;
    params.adpStatus = this.viewApiService.headers?.adpStatus || params.adpStatus;

    this.viewApiService[requestApi]?.(params).subscribe(
      (res) => {
        if (res.code === 0) {
          if (isAfterSaveNeedPublish) {
            this.publishButtonRef.startPublish();
            // this.handleEffectiveFlow();
          } else if (isAddNewVersion) {
            this.handleCreateNewVersion();
          } else {
            this.athMessageService.success(this.translateService.instant('dj-保存成功'));
          }

          // 存储原流程
          this.viewStoreService.setState((state) => {
            state.originalFlowData = params;
          });
          this.refreshMenuAndCrumbsName && this.updateMenuAndCrumbsName(res?.data);
        }
        // 存储原流程
        this.viewStoreService.setState((state) => {
          state.originalFlowData = params;
        });
        this.refreshMenuAndCrumbsName && this.updateMenuAndCrumbsName(res?.data);

        this.viewGraphService.isSaveLoading = false;
      },
      () => {
        this.viewGraphService.isSaveLoading = false;
      },
    );
  }

  /**
   * 发布时调用生效接口
   */
  handleEffectiveFlow() {
    this.getVersions(this.processId, this.viewGraphService.currentSelectedVersion?.adpVersion);
    this.refreshMenuAndCrumbsName && this.updateMenuAndCrumbsName(this.viewStoreService.state.originalFlowData);
  }

  // 发布前
  handleOperateBeforePublish(): void {
    this.handleOperateSave(true, false);
  }

  // 发布中
  handleOperatePublish(data) {
    this.viewGraphService.isPublishLoading = data;
  }

  handleUpDateState() {
    this.reloadPage.emit();
    // this.viewStoreService.setState((state) => {
    //   state.originalFlowData.publishFlag = true;
    // });
  }

  // 打开节点菜单
  handleOpenNodeMenu(event: Event) {
    // 清空
    this.viewGraphService.currentTransmitData = null;

    const { clientX, clientY }: any = event;
    this.operateOpenNodeMenu.emit({
      clientX: clientX || 30,
      clientY: clientY || 125,
    });
    event.preventDefault();
    event.stopPropagation();
  }

  // 100% 视图
  handleOperateOriginalSize() {
    this.operateOriginalSize.emit();
  }

  // 放大
  handleOperateEnlarge() {
    this.operateEnlarge.emit();
  }

  // 缩小
  handleOperateReduce() {
    this.operateReduce.emit();
  }

  // 撤销
  handleOperateUndo() {
    this.operateUndo.emit();
  }

  // 重做
  handleOperateRedo() {
    this.operateRedo.emit();
  }

  /**
   * 设置全局流程flow
   */
  handleSetViewDesign() {
    this.setViewDesign.emit();
  }

  // 检查节点配置是否完善
  checkNodeIsVerificationPassed() {
    let isPass = true;

    const { propertiesObj } = this.viewStoreService.state;
    const propertiesArr = Object.entries(propertiesObj);

    propertiesArr?.forEach((arr) => {
      const data: any = arr?.[1] || {};
      if (isObjectAndHasProperty(data, '_isValidPassed')) {
        // 属性面板存在多个tab，_isValidPassed使用JSON格式存储，这里用于解析JSON格式
        if (isJSON(data._isValidPassed) && Object.values(data._isValidPassed).includes(false)) {
          isPass = false;
        } else if (!data._isValidPassed) {
          // 兼容旧数据(旧数据的_isValidPassed是布尔值)
          isPass = false;
        }
      }
    });

    return isPass;
  }

  // 检查流程开始到结束连线是否完善
  checkFlowIsComplete() {
    let isComplete = false;

    const allNodes = this.graph.getNodes();
    const endNode = allNodes?.find((node) => node?.data?.nodeType === NodeType.END_FLOW);
    if (endNode) {
      const predecessors = this.graph.getPredecessors(endNode);
      predecessors?.forEach((node) => {
        if (
          [
            NodeType.START_FLOW,
            NodeType.START_MANUAL,
            NodeType.START_PROJECT,
            NodeType.START_EVENT,
            NodeType.START_TIMER,
          ].includes(node?.data?.nodeType)
        ) {
          isComplete = true;
        }
      });
    }

    return isComplete;
  }

  // 检查所有条件分支节点的前序节点任务界面是否已配置
  checkAndSetPreManualNodes() {
    const allNodes = this.graph.getNodes();
    const allConditionBranchNodes =
      allNodes?.filter((node) => {
        const { nodeType, conditionType } = node?.data || {};
        return nodeType === NodeType.CONDITION_BRANCH && conditionType === 'common';
      }) || [];
    const {
      propertiesObj,
      originalFlowData: { code },
    } = this.viewStoreService.state;
    const process = propertiesObj?.[code];

    const setNodeError = (node, nodeId) => {
      // 节点属性
      this.viewStoreService.setState((state) => {
        state.propertiesObj[nodeId]._isValidPassed = false;
        state.propertiesObj[nodeId].modelCodeSel = '';
        state.propertiesObj[nodeId].modelSelObj = {
          code: '',
        };
        state.propertiesObj[nodeId].conditionList = [];
        state.propertiesObj[nodeId].allFields = [];
        state.propertiesObj[nodeId].queryConditions = [];
      });
      // 节点配置
      node.setData({
        ...(node?.data || {}),
        condition: JSON.stringify([]),
        queryFields: JSON.stringify([]),
        isVerificationPassed: false,
      });
      // 节点样式
      node.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置条件`));
      node.setAttrByPath('condition/fill', '#999999');
      this.viewToolsService.toolsNodeStyle(node, 'highlight');
    };

    allConditionBranchNodes.forEach((node) => {
      const { nodeId } = node?.data || {};
      const {
        code,
        dataType,
        variableCode,
        serviceCode,
        nodeId: findNodeId,
      } = propertiesObj[nodeId]?.modelSelObj || {};
      if ([VariableType.DTD].includes(dataType)) {
        // DTD变量
        const { dtdVariable = [] } = process;
        if (!dtdVariable.some((e) => e.varName === variableCode)) {
          setNodeError(node, nodeId);
        }
      } else if ([VariableType.MODEL].includes(dataType)) {
        const { modelVariables = [] } = process;
        if (!modelVariables.some((e) => e.modelCode === code && e.serviceCode === serviceCode)) {
          setNodeError(node, nodeId);
        }
      } else if ([VariableType.BUSINESS_OBJECT].includes(dataType)) {
        // 业务变量
        const { businessObjectVariables = [] } = process;
        if (!businessObjectVariables.some((e) => e.modeCode === code && e.serviceCode === serviceCode)) {
          setNodeError(node, nodeId);
        }
      } else if (
        [
          VariableType.BOOLEAN,
          VariableType.DATETIME,
          VariableType.DECIMAL,
          VariableType.INTEGER,
          VariableType.STRING,
        ].includes(dataType)
      ) {
        // 自定义变量
        const { customVariables = [] } = process;
        if (!customVariables.some((e) => e.varName === variableCode)) {
          setNodeError(node, nodeId);
        }
      } else if ([VariableType.Mechanism].includes(dataType)) {
        // 机智参数
        const { mechanismVariables = [] } = process;
        if (!mechanismVariables.some((e) => e.varName === variableCode)) {
          setNodeError(node, nodeId);
        }
      } else if ([VariableType.NODE].includes(dataType)) {
        // 引用了节点
        const preNodes = this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node });
        if (!preNodes.some((e) => e.nodeId === variableCode)) {
          setNodeError(node, nodeId);
        }
      }
    });
  }

  /**
   * 获取数据，获取多条数据/删除数据 节点 需要检查前序节点
   */
  checkAndSetPreDataNodes() {
    const allNodes = this.graph.getNodes();
    const allNeedCheckedNodes =
      allNodes?.filter((node) => {
        const { nodeType } = node?.data || {};
        return (
          nodeType === NodeType.DATA_GET ||
          nodeType === NodeType.DATA_GET_MULTI ||
          nodeType === NodeType.DATA_DELETE ||
          nodeType === NodeType.DATA_GROUPING
        );
      }) || [];
    allNeedCheckedNodes.forEach((node) => {
      const { nodeId } = node?.data || {};
      const predecessorsNodesMode = this.viewGraphService.getPreGetDataNodesMode(node);
      const { nodeList = [], dataSource } = this.viewStoreService.state.propertiesObj[nodeId];
      // todo 要判获取数据来源是业务对象还是数据节点
      if (
        dataSource === 'node' &&
        nodeList?.length > 0 &&
        predecessorsNodesMode.findIndex((item) => item.id === nodeList?.[0]) === -1
      ) {
        // 节点属性
        this.viewStoreService.setState((state) => {
          state.propertiesObj[nodeId]._isValidPassed = {
            [PannelTabsType.BASE]: false,
          };
          state.propertiesObj[nodeId].nodeList = [];
        });
        // 节点配置
        node.setData({
          ...(node?.data || {}),
          isVerificationPassed: {
            [PannelTabsType.BASE]: false,
          },
        });
        // 节点样式
        node.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置节点`));
        node.setAttrByPath('condition/fill', '#999999');
        this.viewToolsService.toolsNodeStyle(node, 'highlight');
      }
    });
  }

  /**
   * 新增/更新节点，需要检查前序节点的模型是否匹配
   * DATA_ADD,DATA_UPDATE
   */
  checkAndSetPreModelNodes() {
    const allNodes = this.graph.getNodes();
    const allNeedCheckedNodes =
      allNodes?.filter((node) => {
        const { nodeType } = node?.data || {};
        return nodeType === NodeType.DATA_ADD || nodeType === NodeType.DATA_UPDATE;
      }) || [];
    allNeedCheckedNodes.forEach((node) => {
      const { nodeId } = node?.data || {};
      const {
        propertiesObj,
        originalFlowData: { code },
      } = this.viewStoreService.state;
      const process = propertiesObj?.[code];
      const { fieldInfos = [], _isValidPassed } = propertiesObj?.[nodeId];

      let isValidPassed = _isValidPassed.base;
      const _fieldInfos = fieldInfos.map((item) => {
        if (item.paramType !== 'variable') return item;
        if (item.valueType === 'modelVariable') {
          const { modelVariables = [] } = process;
          // 为了兼容以前的老数据
          if (
            !modelVariables.some((e) => e.uniqueKey === item.variableCode) &&
            !modelVariables.some((e) => e.modelCode === item.modelCode && e.serviceCode === item.serviceCode)
          ) {
            isValidPassed = false;
            return {
              ...item,
              fieldValue: undefined,
              modelCode: undefined,
              serviceCode: undefined,
            };
          }
          const nextItem = { ...item };
          if (item.variableCode === undefined) {
            nextItem.variableCode = `${item.modelCode}&${item.serviceCode}`;
          }
          if (item.modelCode === undefined || item.serviceCode === undefined) {
            const [modelCode, serviceCode] = item.variableCode.split('&');
            nextItem.modelCode = modelCode;
            nextItem.serviceCode = serviceCode;
          }
          return nextItem;
        } else if (item.valueType === 'customVariable') {
          const { customVariables = [] } = process;
          if (!customVariables.some((e) => e.varName === item.variableCode)) {
            isValidPassed = false;
            return {
              ...item,
              fieldValue: undefined,
              modelCode: undefined,
              serviceCode: undefined,
            };
          }
        } else if (item.valueType === 'mechanismVariable') {
          const { mechanismVariables = [] } = process;
          if (!mechanismVariables.some((e) => e.actionId === item.fieldValue)) {
            isValidPassed = false;
            return {
              ...item,
              fieldValue: undefined,
              modelCode: undefined,
              serviceCode: undefined,
            };
          }
        } else if (item.valueType === 'nodeVariable') {
          // 引用了节点
          const preNodes = this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node });
          if (!preNodes.some((e) => e.nodeId === item.fieldValue)) {
            isValidPassed = false;
            return {
              ...item,
              fieldValue: undefined,
              modelCode: undefined,
              serviceCode: undefined,
            };
          }
        } else if (item.valueType === 'businessObjectVariable') {
          // 业务变量
          const { businessObjectVariables = [] } = process;
          if (!businessObjectVariables.some((e) => e.varName === item.variableCode)) {
            isValidPassed = false;
            return {
              ...item,
              fieldValue: undefined,
              modelCode: undefined,
              serviceCode: undefined,
            };
          }
        }
        return item;
      });
      this.viewStoreService.setState((state) => {
        state.propertiesObj[nodeId]._isValidPassed = {
          [PannelTabsType.BASE]: isValidPassed,
        };
        state.propertiesObj[nodeId].fieldInfos = _fieldInfos;
      });
      // 节点属性
      this.viewStoreService.setState((state) => {
        state.propertiesObj[nodeId]._isValidPassed = {
          [PannelTabsType.BASE]: isValidPassed,
        };
        state.propertiesObj[nodeId].fieldInfos = _fieldInfos;
      });
      // 节点配置
      node.setData({
        ...(node?.data || {}),
        isVerificationPassed: {
          [PannelTabsType.BASE]: isValidPassed,
        },
      });
      if (!isValidPassed) {
        // 节点样式
        node.setAttrByPath('condition/text', this.translateService.instant(`dj-请设置节点`));
        node.setAttrByPath('condition/fill', '#999999');
        this.viewToolsService.toolsNodeStyle(node, 'highlight');
      }
    });
  }

  /**
   * 点击新增流程变量
   */
  handleAddVariable() {
    this.variableModalVisible = true;
  }

  handleVisibleChange(e: boolean) {
    this.variableModalVisible = e;
  }

  handleUpdateFormData(data) {
    if (!this.variableService.handleCheckAddVariable(data)) {
      this.athMessageService.error(this.translateService.instant(`dj-变量名称重复，请重新输入`));
      return;
    }
    this.variableService.handleChangeVariableData({
      item: data,
      type: 'add',
    });
    this.variableModalVisible = false;
  }

  // 保存时校验参数
  /**
   * 获取对象节点的信息
   * @param businessObjectVariables
   * @returns
   */
  private getBusinessObjectVariableInfo = async (businessObjectVariables: any[]) => {
    if (!businessObjectVariables.length) return [];
    const params = businessObjectVariables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modeCode,
      variableCode: item.varName,
      isHasChildren: true,
    }));
    const result = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (result.code === 0) return result.data;
    return [];
  };

  //#region 获取模型
  private async getModelVaribale(): Promise<any> {
    try {
      const res = await this.viewApiService.getBusinessDirList().toPromise();
      const _modelList: any[] = [];
      res.data.forEach((item) => {
        const models = item.businessDirTree.find((e) => e.type === 'modelDesign')?.businessDirTree || [];
        models.forEach((model) => {
          _modelList.push({
            modelCode: model.businessSubCode,
            serviceCode: model.serviceCode,
          });
        });
      });
      return await this.getModelFields(_modelList);
    } catch {
      return [];
    }
  }

  private async getModelFields(params: any[]): Promise<any> {
    const res = await this.viewApiService.queryModelByCodes(params).toPromise();
    const data = res.data || [];
    const models = data
      .filter((e) => e.model.fields.some((f) => f.fieldId === 'manage_status'))
      .map((e) => ({
        value: `${e.code}&${e.serviceCode}`,
        dictionaries: JSON.parse(e.model.fields.find((x) => x.fieldId === 'manage_status')?.dictionaryContent || '[]'),
      }));
    return models;
  }
  //#endregion

  /**
   * 查询model
   * @param modelVariables
   * @returns
   */
  private getModelVariableInfo = async (modelVariables: any[]) => {
    if (!modelVariables.length) return [];
    const params = modelVariables.map((item) => ({
      serviceCode: item.serviceCode,
      modelCode: item.modelCode,
      variableCode: item.uniqueKey,
      isHasChildren: true,
    }));
    const result = await this.viewApiService.queryFieldsGroupList(params).toPromise();
    if (result.code === 0) return result.data;
    return [];
  };

  /**
   * 检测节点是否有效【只包含 人工签核节点、人工关卡节点、人工知会节点】
   */
  async checkNodesIsValid() {
    try {
      const nodes = this.graph
        .getNodes()
        .filter((e) =>
          [NodeType.MANUAL_APPROVE, NodeType.MANUAL_EXECUTION, NodeType.MANUAL_NOTIFICATION].includes(e.data.nodeType),
        );
      const {
        propertiesObj,
        originalFlowData: { code },
      } = this.viewStoreService.state;
      const process = propertiesObj?.[code];
      const {
        systemVariable = [],
        customVariables = [],
        businessObjectVariables = [],
        modelVariables = [],
        mechanismVariables = [],
        dtdVariable = [],
      } = process;
      const businessVariableInfo = await this.getBusinessObjectVariableInfo(businessObjectVariables);
      const modelVariablesInfo = await this.getModelVariableInfo(modelVariables);
      const { data: formList = [] } = await this.viewApiService.loadBasicByAppCode().toPromise();
      const modelFields = await this.getModelVaribale();
      // 检测参数映射
      const mappingIsValid = (nodeConfig: any, node: any) => {
        // 检测入参和出参
        const checkMapping = (mappings: any[], node: any): boolean => {
          for (let i = 0; i < mappings.length; i++) {
            const mapping = mappings[i];
            if (mapping.valueType === VariableType.DTD) {
              // DTD变量
              if (!dtdVariable.some((e) => e.varName === mapping.valueCode)) return false;
            } else if (mapping.valueType === VariableType.NODE) {
              // 节点变量
              const availableNodeVariables = this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node: node });
              if (!availableNodeVariables.some((e) => e.nodeId === mapping.valueCode)) return false;
            } else if (mapping.valueType === VariableType.BUSINESS_OBJECT) {
              // 业务对象
              if (!businessObjectVariables.some((e) => e.varName === mapping.valuePath)) return false;
              // 检测是否包含模型的属性
              const variableInfo = businessVariableInfo.find((e) => e.variableCode === mapping.valuePath);
              const modelFieldInfoVo = variableInfo?.modelFieldInfoVo;
              const newList = ergodicList(modelFieldInfoVo?.modelFileInfos, [modelFieldInfoVo?.modelCode]);
              if (
                mapping.valueCode !== modelFieldInfoVo?.modelCode &&
                !checkModelHasValuePath(mapping.valueCode, newList)
              )
                return false;
            } else if (mapping.valueType === VariableType.PROCESS) {
              // 系统变量
              if (!systemVariable.some((e) => e.varName === mapping.valueCode)) return false;
            } else if (mapping.valueType === VariableType.MODEL) {
              // 模型变量
              if (!mapping.valuePath) return;
              const [modelCode, serviceCode] = mapping.valuePath.split('&');
              if (!modelVariables.some((e) => e.modelCode === modelCode && e.serviceCode === serviceCode)) return false;
              const _modelVariablesInfo = modelVariablesInfo.find((e) => e.variableCode === mapping.valuePath);
              const modelFieldInfoVo = _modelVariablesInfo?.modelFieldInfoVo;

              const newList = ergodicList(modelFieldInfoVo?.modelFileInfos, [modelFieldInfoVo?.modelCode]);
              if (modelCode !== modelFieldInfoVo?.modelCode && !checkModelHasValuePath(mapping.valueCode, newList))
                return false;
            } else if (mapping.valueType === VariableType.Mechanism) {
              // 机制变量
              if (!mechanismVariables.some((e) => e.varName === mapping.valueCode)) return false;
            } else {
              // 自定义变量
              if (!customVariables.some((e) => e.varName === mapping.valueCode)) return false;
            }
          }
          return true;
        };

        let inputValid = true;
        if (nodeConfig.variablesMapping?.input?.type === 'mapping') {
          inputValid = checkMapping(nodeConfig.variablesMapping.input.mapping, node);
        }
        let outputValid = true;
        if (nodeConfig.variablesMapping?.output?.type === 'mapping') {
          outputValid = checkMapping(nodeConfig.variablesMapping.output.mapping, node);
        }
        return inputValid && outputValid;
      };
      // 基本配置检测
      const baseIsValid = (nodeConfig: any, node: any) => {
        let isValid = true;
        const { source, variable } = nodeConfig.executor ?? {};
        if (source === 'variable') {
          const { variableCode, variableType, variablePath } = variable?.[0] ?? {};
          isValid = this.checkVariableIsValid(
            node,
            variableCode,
            variableType,
            variablePath,
            customVariables,
            systemVariable,
            dtdVariable,
            businessVariableInfo,
            modelVariablesInfo,
          );
        }
        const { sourceType, variableCode, variablePath, variableType } = nodeConfig.planEndTime ?? {};
        if (sourceType === 'variable') {
          isValid = this.checkVariableIsValid(
            node,
            variableCode,
            variableType,
            variablePath,
            customVariables,
            systemVariable,
            dtdVariable,
            businessVariableInfo,
            modelVariablesInfo,
          );
        }
        return isValid;
      };

      // 前后置动作检测
      const actionIsValid = (nodeConfig: any) => {
        let isValid = true;
        const map = modelFields.reduce((pre, curr) => {
          pre[curr.value] = curr.dictionaries;
          return pre;
        }, {});
        const checkList = ['pre', 'after'];
        outer: for (let i = 0; i < checkList.length; i++) {
          const type = checkList[i];
          const list = nodeConfig.actions?.[type] || [];
          for (let j = 0; j < list.length; j++) {
            const item = list[j];
            if (item.adpType !== 'MODEL_UPDATE') continue;
            if (!item.actionCfg?.bindForm?.modelCode || !item.actionCfg?.bindForm?.serviceCode) {
              isValid = false;
              break outer;
            }
            const status = map[`${item.actionCfg?.bindForm?.modelCode}&${item.actionCfg?.bindForm?.serviceCode}`];
            if (!status || !status.some((e) => e.code === item.actionCfg?.fieldInfos?.[0]?.fieldValue)) {
              isValid = false;
              break outer;
            }
          }
        }
        return isValid;
      };

      // 检测参数映射和基本配置
      nodes.forEach((node) => {
        const { nodeId } = node.data;
        const nodeConfig = propertiesObj[nodeId];
        if (nodeConfig) {
          if (
            nodeConfig.bindForm?.type !== 'pageView' &&
            (!nodeConfig.bindForm?.modelCode || !nodeConfig.bindForm?.serviceCode)
          ) {
            const form = formList.find((e) => e.code === nodeConfig.bindForm?.formCode);
            if (form) {
              this.viewStoreService.setState((state) => {
                state.propertiesObj[nodeId].bindForm.modelCode = form.simpleModelCode;
                state.propertiesObj[nodeId].bindForm.serviceCode = form.navigateModelServiceCode;
              });
            }
          }
          // 检测入参映射
          const mappingValid = mappingIsValid(nodeConfig, node);
          // 基本的是不是通过
          const baseValid = baseIsValid(nodeConfig, node);
          const actionValid = actionIsValid(nodeConfig);
          if (!mappingValid || !baseValid || !actionValid) {
            const _isValidPassed = {
              [PannelTabsType.DATA_MAPPING]: mappingValid,
              [PannelTabsType.BASE]: baseValid,
              [PannelTabsType.ACTION]: actionValid,
            };
            this.viewStoreService.setState((state) => {
              state.propertiesObj[nodeId]._isValidPassed = _isValidPassed;
              node.setData({
                isVerificationPassed: _isValidPassed,
              });
              this.viewToolsService.toolsNodeStyle(node, 'none');
            });
          }
        }
      });

      // 基本配置检测
      const dataGroupingBaseIsValid = (nodeConfig: any, node: any) => {
        const { groupDataSource } = nodeConfig ?? {};
        const { dataSourceCode, dataSourceType, dataSourcePath } = groupDataSource ?? {};
        return this.checkVariableIsValid(
          node,
          dataSourceCode,
          dataSourceType,
          dataSourcePath,
          customVariables,
          systemVariable,
          dtdVariable,
          businessVariableInfo,
          modelVariablesInfo,
          'ParentNode',
        );
      };

      const dataGroupingNodes = this.graph.getNodes().filter((e) => [NodeType.DATA_GROUPING].includes(e.data.nodeType));
      dataGroupingNodes.forEach((node) => {
        const { nodeId } = node.data;
        const nodeConfig = propertiesObj[nodeId];
        // 基本的是不是通过
        const dataGroupingBaseValid = dataGroupingBaseIsValid(nodeConfig, node);

        if (!dataGroupingBaseValid) {
          const _isValidPassed = {
            [PannelTabsType.BASE]: dataGroupingBaseValid,
          };
          this.viewStoreService.setState((state) => {
            state.propertiesObj[nodeId]._isValidPassed = _isValidPassed;
            node.setData({
              isVerificationPassed: _isValidPassed,
            });
            this.viewToolsService.toolsNodeStyle(node, 'none');
          });
        }
      });
    } catch (ex) {
      //
    }
  }

  /**
   * 工具方法
   * @param node
   * @param variableCode
   * @param variableType
   * @param variablePath
   * @param customVariables
   * @param systemVariable
   * @param businessVariableInfo
   * @param modelVariables
   * @returns
   */
  checkVariableIsValid(
    node,
    variableCode,
    variableType,
    variablePath,
    customVariables,
    systemVariable,
    dtdVariable,
    businessVariableInfo,
    modelVariablesInfo,
    businessObjectType: 'AllNode' | 'ParentNode' | 'ChildrenNode' = 'ChildrenNode',
  ): boolean {
    switch (variableType) {
      case 'dtdVariable':
        return dtdVariable.some((c) => c.varName === variableCode);
      case 'customVariable':
        return customVariables.some((c) => c.varName === variableCode);
      case 'systemVariable':
        return systemVariable.some((s) => s.varName === variableCode);
      case 'nodeVariable':
        const availableNodeVariables = this.viewGraphService.getNodesVariable({ type: 'preAndSelf', node: node });
        return availableNodeVariables.some((n) => n.nodeId === variableCode);
      case 'businessObjectVariable':
        const variableInfo = businessVariableInfo.find((e) => e.variableCode === variableCode);
        const { modelCode, modelFileInfos = [] } = variableInfo?.modelFieldInfoVo ?? {};
        const newList = ergodicList(modelFileInfos, [modelCode]);
        const hasPath = modelCode === variablePath || checkModelHasValuePath(variablePath, newList);
        if (businessObjectType === 'AllNode') {
          return modelCode === variablePath || hasPath;
        } else if (businessObjectType === 'ParentNode') {
          return modelCode === variablePath;
        } else {
          return hasPath;
        }
      case 'modelVariable':
        const variableInfo2 = modelVariablesInfo.find((e) => e.variableCode === variableCode);
        const { modelCode: modelCode2, modelFileInfos: modelFileInfos2 = [] } = variableInfo2?.modelFieldInfoVo ?? {};
        const newList2 = ergodicList(modelFileInfos2, [modelCode2]);
        const hasPath2 = modelCode2 === variablePath || checkModelHasValuePath(variablePath, newList2);
        if (businessObjectType === 'AllNode') {
          return modelCode2 === variablePath || hasPath2;
        } else if (businessObjectType === 'ParentNode') {
          return modelCode2 === variablePath;
        } else {
          return hasPath2;
        }
      default:
        return false;
    }
  }
}
