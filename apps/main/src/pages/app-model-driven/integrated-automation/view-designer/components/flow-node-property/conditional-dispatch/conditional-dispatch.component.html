<form nz-form class="conditional-dispatch-content" [formGroup]="dataFormGroup">
  <div style="margin-top: 10px">
    <div class="condition-dispatch">
      <div>
        <span style="margin-right: 8px">{{ 'dj-条件分派' | translate}}</span>
        <nz-switch
          [(ngModel)]="supportConditionAssign"
          [ngModelOptions]="{ standalone: true }"
          (ngModelChange)="handelDispatchChange($event)"
        ></nz-switch>
      </div>
      <div *ngIf="supportConditionAssign">
        <button ad-button adType="default" nzSize="mini" class="add-button" (click)="handleAddDispatch()">
          {{ 'dj-新增' | translate }}
        </button>
      </div>
    </div>
    <ul class="mapping-list" formArrayName="optionsList">
      <li class="mapping-item form-item" *ngFor="let item of optionsList.controls; let i = index" [formGroupName]="i">
        <nz-form-item>
          <div class="item-title" nzNoColon>
            {{ 'dj-选项名称' | translate }}
            <span class="item-required">*</span>
          </div>
          <nz-form-control [nzErrorTip]="'dj-必填' | translate">
            <app-variable-select-input
              [variableRange]="inputRange"
              *ngIf="!inputName"
              [inputable]="true"
              formControlName="valueCode"
              [valueType]="item.get('valueType').value"
              [item]="item.value"
              [sufixKey]="'columnSuffix'"
              [nodeId]="state.currentSelectedNodeId"
              (onChanged)="handleMappingTargetChange($event, i)"
            >
            </app-variable-select-input>
            <nz-input-group class="input-group-wrap">
              <input
                nz-input
                *ngIf="inputName"
                style="height: 30px"
                formControlName="valueCode"
                (ngModelChange)="inputValueCodeChange($event, i)"
              />
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <div class="item-title" nzNoColon>
            {{ 'dj-选项内容' | translate }}
            <span class="item-required">*</span>
          </div>
          <nz-form-control [nzErrorTip]="'dj-必填' | translate">
            <app-window-params-input
              [inputable]="true"
              formControlName="openWindowName"
              [item]="{paramCode: item.get('openWindowKey').value, paramName: item.get('openWindowName').value}"
              (onChanged)="handleWindowParamsChange($event, i)"
            >
            </app-window-params-input>
          </nz-form-control>
        </nz-form-item>

        <i adIcon iconfont="iconjianhao" class="iconjian" (click)="removeMapping(i)"></i>
      </li>
    </ul>
  </div>
</form>