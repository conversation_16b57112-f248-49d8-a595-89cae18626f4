<div class="header">
  <span>{{ 'dj-动作设置' | translate }}</span>
  <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
</div>
<nz-spin [nzSpinning]="loading" style="height: calc(100% - 55px)">
  <section class="content action-setting" #scrollContent>
    <form nz-form [formGroup]="form" [nzLayout]="'vertical'">
      <nz-collapse [nzBordered]="false">
        <!-- 前置动作 -->
        <nz-collapse-panel [nzHeader]="'dj-前置动作' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <ng-container>
            <ul
              class="actions-list"
              formArrayName="pre"
              cdkDropList
              cdkDropListOrientation="vertical"
              (cdkDropListDropped)="drop('pre', $event)"
            >
              <li
                class="actions-item"
                cdkDrag
                *ngFor="let beforeItem of pre.controls; let i = index"
                [formGroupName]="i"
                cdkDragPreviewContainer="parent"
              >
                <div class="actions-item-placeholder" *cdkDragPlaceholder></div>
                <nz-form-item>
                  <nz-form-label nzNoColon>
                    <span class="label-text">{{ 'dj-触发条件' | translate }}</span>
                    <span class="item-required" *ngIf="beforeItem.get('adpType').value === EActionType.PASS">*</span>
                    <i
                      adIcon
                      iconfont="iconshuomingwenzi"
                      class="question-icon"
                      style="margin-left: 3px"
                      nz-tooltip
                      [nzTooltipTitle]="
                        'dj-未配置条件则默认执行，若配置了触发条件，则将依据配置的条件执行动作' | translate
                      "
                    >
                    </i>
                  </nz-form-label>
                  <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
                    <nz-input-group [nzSuffix]="suffixIconk">
                      <input
                        readonly
                        nz-input
                        formControlName="expression"
                        [required]="beforeItem.get('adpType').value === EActionType.PASS"
                        (dblclick)="handleEditExpression('pre', i)"
                      />
                    </nz-input-group>
                    <ng-template #suffixIconk>
                      <i adIcon iconfont="icongaodaima" (click)="handleEditExpression('pre', i)"></i>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label nzNoColon>
                    <span class="label-text"> {{ 'dj-动作执行失败处理' | translate }}</span>
                  </nz-form-label>
                  <nz-form-control>
                    <nz-radio-group formControlName="exceptionPolicy">
                      <label nz-radio nzValue="actionSkip" [nzDisabled]="disabledAll">{{ 'dj-跳过' | translate }}</label>
                      <label nz-radio nzValue="actionAbort" [nzDisabled]="disabledAll">{{ 'dj-中止流程' | translate }}</label>
                    </nz-radio-group>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label nzNoColon>
                    <span class="label-text">{{ 'dj-动作类型' | translate }}</span>
                    <span class="item-required">*</span>
                    <i
                      adIcon
                      iconfont="iconshuomingwenzi"
                      class="question-icon"
                      style="margin-left: 3px"
                      nz-tooltip
                      [nzTooltipTitle]="'dj-支持五种动作类型，存在多个动作时，配置循序不影响执行顺序' | translate"
                    >
                    </i>
                  </nz-form-label>
                  <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                    <ad-select
                      class="action-select"
                      [nzPlaceHolder]="'dj-请选择动作类型' | translate"
                      [nzAllowClear]="false"
                      [nzShowSearch]="false"
                      formControlName="adpType"
                      [nzDisabled]="disabledAll"
                    >
                      <ad-option
                        *ngFor="let option of actionOptions"
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                      ></ad-option>
                    </ad-select>
                  </nz-form-control>
                </nz-form-item>
                <div
                  class="divid-line"
                  [ngClass]="{
                    'hide-line': !beforeItem.get('expand').value
                  }"
                  *ngIf="beforeItem.get('adpType').value && beforeItem.get('adpType').value !== EActionType.PASS"
                >
                  <span (click)="toggleExpand($event, 'pre', i)"
                    >{{ (beforeItem.get('expand').value ? 'dj-收起' : 'dj-展开') | translate
                    }}<i
                      adIcon
                      iconfont="iconbottomMechanism"
                      class="icon-arrow"
                      [ngStyle]="{
                        transform: beforeItem.get('expand').value
                          ? 'translateY(0px) rotate(180deg)'
                          : 'translateY(2px) rotate(0deg)'
                      }"
                    ></i
                  ></span>
                </div>
                <ng-container *ngIf="beforeItem.get('expand').value">
                  <!-- ESP的配置 -->
                  <div
                    class="esp"
                    formGroupName="actionCfg"
                    *ngIf="beforeItem.get('adpType').value === EActionType.ESP"
                  >
                    <nz-form-item>
                      <div class="form-item">
                        <div class="item-title">
                          <span class="label-text">{{ 'dj-服务名称' | translate }}</span>
                        </div>
                        <nz-input-group [nzSuffix]="suffixIconl" class="service-name">
                          <input
                            readonly
                            nz-input
                            [placeholder]="'dj-请选择' | translate"
                            formControlName="serviceName"
                            (dblclick)="showEspAction('pre', i)"
                          />
                        </nz-input-group>
                        <ng-template #suffixIconl>
                          <i
                            adIcon
                            iconfont="iconkaichuang"
                            aria-hidden="true"
                            class="window-icon iconfont"
                            (click)="showEspAction('pre', i)"
                          >
                          </i>
                        </ng-template>
                      </div>
                    </nz-form-item>
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-产品名' | translate }}</span>
                      </div>
                      <nz-form-control>
                        <input
                          formControlName="prod"
                          nz-dropdown
                          [nzDropdownMenu]="menu"
                          [nzVisible]="this.visibleMap.get('before-' + i)"
                          (nzVisibleChange)="handleDropMenuVisible($event, 'before', i)"
                          nz-input
                          type="text"
                          [nzTrigger]="'click'"
                          [placeholder]="'dj-请输入' | translate"
                        />
                        <nz-dropdown-menu #menu="nzDropdownMenu">
                          <ad-empty
                            class="adempty"
                            size="small"
                            *ngIf="!(produces[beforeItem.get('actionCfg').get('serviceName').value] || []).length"
                          >
                            <ng-template #contentTpl>
                              <span>{{ 'dj-暂无数据' | translate }}</span>
                            </ng-template>
                          </ad-empty>
                          <ul
                            nz-menu
                            *ngIf="(produces[beforeItem.get('actionCfg').get('serviceName').value] || []).length"
                          >
                            <li
                              nz-menu-item
                              *ngFor="let item of produces[beforeItem.get('actionCfg').get('serviceName').value] || []"
                              (click)="handleSelect('pre', i, item)"
                              style="padding: 4px 8px; font-size: 13px"
                            >
                              {{ item }}
                            </li>
                          </ul>
                        </nz-dropdown-menu>
                      </nz-form-control>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求头' | translate }}</span>
                        <i
                          adIcon
                          iconfont="iconshuomingwenzi"
                          aria-hidden="true"
                          class="question-icon"
                          nzTooltipTrigger="hover"
                          nz-tooltip
                          [nzTooltipTitle]="'dj-请求头中使用变量参考以下格式' | translate"
                        >
                        </i>
                      </div>
                      <nz-form-control>
                        <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="header" />
                      </nz-form-control>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIconx">
                        <input
                          readonly
                          nz-input
                          formControlName="requestScript"
                          (dblclick)="showScriptModal('pre', i, 'requestScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconx>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('pre', i, 'requestScript')"></i>
                      </ng-template>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-返回脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIcony">
                        <input
                          readonly
                          nz-input
                          formControlName="responseScript"
                          (dblclick)="showScriptModal('pre', i, 'responseScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIcony>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('pre', i, 'responseScript')"></i>
                      </ng-template>
                    </div>

                    <div class="form-item">
                      <div class="item-title" style="margin-bottom: 0">
                        <span class="label-text">{{ 'dj-异步设置' | translate }}</span>
                      </div>
                      <div nz-col nzSpan="24" style="margin-bottom: 8px">
                        <nz-form-control>
                          <label nz-checkbox formControlName="isAsync">{{ 'dj-是否异步' | translate }}</label>
                        </nz-form-control>
                      </div>
                    </div>
                  </div>
                  <!-- HTTP的配置 -->
                  <div
                    class="http"
                    *ngIf="beforeItem.get('adpType').value === EActionType.HTTP"
                    formGroupName="actionCfg"
                  >
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求头' | translate }}</span>
                        <i
                          adIcon
                          iconfont="iconshuomingwenzi"
                          aria-hidden="true"
                          class="question-icon"
                          nzTooltipTrigger="hover"
                          nz-tooltip
                          [nzTooltipTitle]="'dj-请求头中使用变量参考以下格式' | translate"
                        >
                        </i>
                      </div>
                      <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="header" />
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'Domain' }}</span>
                      </div>
                      <ad-select style="width: 100%" [nzPlaceHolder]="'dj-请选择' | translate" formControlName="domain" [nzDisabled]="disabledAll">
                        <ng-container *ngFor="let data of domainList">
                          <ad-option [nzValue]="data" [nzLabel]="data"></ad-option>
                        </ng-container>
                      </ad-select>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'URL' }}</span>
                      </div>
                      <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="url" />
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求方式' | translate }}</span>
                      </div>
                      <ad-select style="width: 100%" [nzPlaceHolder]="'dj-请选择' | translate" formControlName="method">
                        <ad-option nzValue="POST" nzLabel="POST"></ad-option>
                        <ad-option nzValue="GET" nzLabel="GET"></ad-option>
                      </ad-select>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求脚本' | translate }}</span>
                      </div>

                      <nz-input-group [nzSuffix]="suffixIcona">
                        <input
                          readonly
                          nz-input
                          formControlName="requestScript"
                          (dblclick)="showScriptModal('pre', i, 'requestScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIcona>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('pre', i, 'requestScript')"></i>
                      </ng-template>
                    </div>
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-返回脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIconb">
                        <input
                          readonly
                          nz-input
                          formControlName="responseScript"
                          (dblclick)="showScriptModal('pre', i, 'responseScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconb>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('pre', i, 'responseScript')"></i>
                      </ng-template>
                    </div>
                  </div>
                  <!-- 脚本的配置 -->
                  <div
                    class="script"
                    *ngIf="beforeItem.get('adpType').value === EActionType.Script"
                    formGroupName="actionCfg"
                  >
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-返回脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIconc">
                        <input
                          readonly
                          nz-input
                          formControlName="responseScript"
                          (dblclick)="showScriptModal('pre', i, 'responseScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconc>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('pre', i, 'responseScript')"></i>
                      </ng-template>
                    </div>
                  </div>
                  <!-- 修改模型状态 -->
                  <div
                    class="status"
                    *ngIf="beforeItem.get('adpType').value === EActionType.Modify"
                    formGroupName="actionCfg"
                  >
                    <div class="form-item" formGroupName="bindForm">
                      <nz-form-item>
                        <div class="item-title">
                          <span class="label-text">{{ 'dj-选择模型' | translate }}</span>
                          <span class="item-required">*</span>
                        </div>
                        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                          <ad-select
                            style="width: 100%"
                            [nzPlaceHolder]="'dj-请选择' | translate"
                            formControlName="serviceCode"
                            (ngModelChange)="handleModelChange('pre', i, $event)"
                            [nzOptions]="modelList"
                            [nzDisabled]="disabledAll"
                          >
                          </ad-select>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                    <div formArrayName="fieldInfos">
                      <div
                        class="form-item"
                        *ngFor="let fd of beforeItem.get('actionCfg').get('fieldInfos').controls; let j = index"
                        [formGroupName]="j"
                      >
                        <nz-form-item>
                          <div class="item-title">
                            <span class="label-text">{{ 'dj-审核状态更新为' | translate }}</span>
                            <span class="item-required">*</span>
                          </div>
                          <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                            <ad-select
                              style="width: 100%"
                              [nzPlaceHolder]="'dj-请选择' | translate"
                              formControlName="fieldValue"
                              [nzDisabled]="disabledAll"
                            >
                              <ng-container
                                *ngFor="
                                  let data of getDictionaries(
                                    beforeItem.get('actionCfg')?.get('bindForm')?.get('serviceCode')?.value
                                  )
                                "
                              >
                                <ad-option
                                  [nzValue]="data.code"
                                  [nzLabel]="data.code + '_' + (data.lang.value['dj-LANG' | translate] || data.value)"
                                ></ad-option>
                              </ng-container>
                            </ad-select>
                          </nz-form-control>
                        </nz-form-item>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <i *ngIf="!disabledAll" adIcon iconfont="iconjianhao" class="iconjian" (click)="removeAction('pre', i)"></i>
                <i *ngIf="!disabledAll" adIcon cdkDragHandle iconfont="icontuozhuaiziduan" class="iconmove"></i>
              </li>
            </ul>

            <button *ngIf="!disabledAll" ad-button adType="default" nzSize="large" class="add-button" (click)="addAction('pre')">
              {{ 'dj-新增一组' | translate }}
            </button>
          </ng-container>
        </nz-collapse-panel>

        <!-- 后置动作 -->
        <nz-collapse-panel [nzHeader]="'dj-后置动作' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <ng-container>
            <ul
              class="actions-list"
              formArrayName="after"
              cdkDropList
              cdkDropListOrientation="vertical"
              (cdkDropListDropped)="drop('after', $event)"
            >
              <li
                class="actions-item"
                cdkDrag
                *ngFor="let afterItem of after.controls; let i = index"
                [formGroupName]="i"
                cdkDragPreviewContainer="parent"
              >
                <div class="actions-item-placeholder" *cdkDragPlaceholder></div>
                <nz-form-item>
                  <nz-form-label nzNoColon>
                    <span class="label-text">{{ 'dj-触发条件' | translate }}</span>
                    <span class="item-required" *ngIf="afterItem.get('adpType').value === EActionType.PASS">*</span>
                    <i
                      adIcon
                      iconfont="iconshuomingwenzi"
                      class="question-icon"
                      style="margin-left: 3px"
                      nz-tooltip
                      [nzTooltipTitle]="
                        'dj-未配置条件则默认执行，若配置了触发条件，则将依据配置的条件执行动作' | translate
                      "
                    >
                    </i>
                  </nz-form-label>
                  <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
                    <nz-input-group [nzSuffix]="suffixIcond">
                      <input
                        readonly
                        nz-input
                        formControlName="expression"
                        [required]="afterItem.get('adpType').value === EActionType.PASS"
                        (dblclick)="handleEditExpression('after', i)"
                      />
                    </nz-input-group>
                    <ng-template #suffixIcond>
                      <i adIcon iconfont="icongaodaima" (click)="handleEditExpression('after', i)"></i>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label nzNoColon>
                    <span class="label-text">{{ 'dj-动作执行失败处理' | translate }}</span>
                  </nz-form-label>
                  <nz-form-control>
                    <nz-radio-group formControlName="exceptionPolicy">
                      <label nz-radio nzValue="actionSkip" [nzDisabled]="disabledAll">{{ 'dj-跳过' | translate }}</label>
                      <label nz-radio nzValue="actionAbort" [nzDisabled]="disabledAll">{{ 'dj-中止流程' | translate }}</label>
                    </nz-radio-group>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label nzNoColon>
                    <span class="label-text">{{ 'dj-动作类型' | translate }}</span>
                    <span class="item-required">*</span>
                    <i
                      adIcon
                      iconfont="iconshuomingwenzi"
                      class="question-icon"
                      style="margin-left: 3px"
                      nz-tooltip
                      [nzTooltipTitle]="'dj-支持五种动作类型，存在多个动作时，配置循序不影响执行顺序' | translate"
                    >
                    </i>
                  </nz-form-label>
                  <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                    <ad-select
                      class="action-select"
                      [nzPlaceHolder]="'dj-请选择动作类型' | translate"
                      [nzAllowClear]="false"
                      [nzShowSearch]="false"
                      formControlName="adpType"
                      [nzDisabled]="disabledAll"
                    >
                      <ad-option
                        *ngFor="let option of actionOptions"
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                      ></ad-option>
                    </ad-select>
                  </nz-form-control>
                </nz-form-item>
                <div
                  class="divid-line"
                  [ngClass]="{
                    'hide-line': !afterItem.get('expand').value
                  }"
                  *ngIf="afterItem.get('adpType').value && afterItem.get('adpType').value !== EActionType.PASS"
                >
                  <span (click)="toggleExpand($event, 'after', i)"
                    >{{ (afterItem.get('expand').value ? 'dj-收起' : 'dj-展开') | translate
                    }}<i
                      adIcon
                      iconfont="iconbottomMechanism"
                      class="icon-arrow"
                      [ngStyle]="{
                        transform: afterItem.get('expand').value
                          ? 'translateY(0px) rotate(180deg)'
                          : 'rotate(0deg) translateY(2px)'
                      }"
                    ></i
                  ></span>
                </div>
                <ng-container *ngIf="afterItem.get('expand').value">
                  <!-- ESP的配置 -->
                  <div class="esp" formGroupName="actionCfg" *ngIf="afterItem.get('adpType').value === EActionType.ESP">
                    <nz-form-item>
                      <div class="form-item">
                        <div class="item-title">
                          <span class="label-text">{{ 'dj-服务名称' | translate }}</span>
                        </div>
                        <nz-input-group [nzSuffix]="suffixIcone" class="service-name">
                          <input
                            readonly
                            nz-input
                            [placeholder]="'dj-请选择' | translate"
                            formControlName="serviceName"
                            (dblclick)="showEspAction('after', i)"
                          />
                        </nz-input-group>
                        <ng-template #suffixIcone>
                          <i
                            adIcon
                            iconfont="iconkaichuang"
                            aria-hidden="true"
                            class="window-icon iconfont"
                            (click)="showEspAction('after', i)"
                          >
                          </i>
                        </ng-template>
                      </div>
                    </nz-form-item>
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-产品名' | translate }}</span>
                      </div>
                      <nz-form-control>
                        <input
                          formControlName="prod"
                          nz-dropdown
                          [nzDropdownMenu]="menu1"
                          [nzVisible]="this.visibleMap.get('after-' + i)"
                          (nzVisibleChange)="handleDropMenuVisible($event, 'after', i)"
                          nz-input
                          type="text"
                          [nzTrigger]="'click'"
                          [placeholder]="'dj-请输入' | translate"
                        />
                        <nz-dropdown-menu #menu1="nzDropdownMenu">
                          <ad-empty
                            size="small"
                            class="adempty"
                            *ngIf="!(produces[afterItem.get('actionCfg').get('serviceName').value] || []).length"
                          >
                            <ng-template #contentTpl>
                              <span>{{ 'dj-暂无数据' | translate }}</span>
                            </ng-template>
                          </ad-empty>
                          <ul
                            nz-menu
                            *ngIf="(produces[afterItem.get('actionCfg').get('serviceName').value] || []).length"
                          >
                            <li
                              nz-menu-item
                              *ngFor="let item of produces[afterItem.get('actionCfg').get('serviceName').value] || []"
                              (click)="handleSelect('after', i, item)"
                              style="padding: 4px 8px; font-size: 13px"
                            >
                              {{ item }}
                            </li>
                          </ul>
                        </nz-dropdown-menu>
                      </nz-form-control>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求头' | translate }}</span>
                        <i
                          adIcon
                          iconfont="iconshuomingwenzi"
                          aria-hidden="true"
                          class="question-icon"
                          nzTooltipTrigger="hover"
                          nz-tooltip
                          [nzTooltipTitle]="'dj-请求头中使用变量参考以下格式' | translate"
                        >
                        </i>
                      </div>
                      <nz-form-control>
                        <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="header" />
                      </nz-form-control>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIconf">
                        <input
                          readonly
                          nz-input
                          formControlName="requestScript"
                          (dblclick)="showScriptModal('after', i, 'requestScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconf>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('after', i, 'requestScript')"></i>
                      </ng-template>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-返回脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIcong">
                        <input
                          readonly
                          nz-input
                          formControlName="responseScript"
                          (dblclick)="showScriptModal('after', i, 'responseScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIcong>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('after', i, 'responseScript')"></i>
                      </ng-template>
                    </div>

                    <div class="form-item">
                      <div class="item-title" style="margin-bottom: 0">
                        <span class="label-text">{{ 'dj-异步设置' | translate }}</span>
                      </div>
                      <div nz-col nzSpan="24" style="margin-bottom: 8px">
                        <nz-form-control>
                          <label nz-checkbox formControlName="isAsync">{{ 'dj-是否异步' | translate }}</label>
                        </nz-form-control>
                      </div>
                    </div>
                  </div>
                  <!-- HTTP的配置 -->
                  <div
                    class="http"
                    *ngIf="afterItem.get('adpType').value === EActionType.HTTP"
                    formGroupName="actionCfg"
                  >
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求头' | translate }}</span>
                        <i
                          adIcon
                          iconfont="iconshuomingwenzi"
                          aria-hidden="true"
                          class="question-icon"
                          nzTooltipTrigger="hover"
                          nz-tooltip
                          [nzTooltipTitle]="'dj-请求头中使用变量参考以下格式' | translate"
                        >
                        </i>
                      </div>
                      <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="header" />
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'Domain' }}</span>
                      </div>
                      <ad-select style="width: 100%" [nzPlaceHolder]="'dj-请选择' | translate" formControlName="domain" [nzDisabled]="disabledAll">
                        <ng-container *ngFor="let data of domainList">
                          <ad-option [nzValue]="data" [nzLabel]="data"></ad-option>
                        </ng-container>
                      </ad-select>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'URL' }}</span>
                      </div>
                      <input nz-input [placeholder]="'dj-请输入' | translate" formControlName="url" />
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求方式' | translate }}</span>
                      </div>
                      <ad-select style="width: 100%" [nzPlaceHolder]="'dj-请选择' | translate" formControlName="method">
                        <ad-option nzValue="POST" nzLabel="POST"></ad-option>
                        <ad-option nzValue="GET" nzLabel="GET"></ad-option>
                      </ad-select>
                    </div>

                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-请求脚本' | translate }}</span>
                      </div>

                      <nz-input-group [nzSuffix]="suffixIconh">
                        <input
                          readonly
                          nz-input
                          formControlName="requestScript"
                          (dblclick)="showScriptModal('after', i, 'requestScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconh>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('after', i, 'requestScript')"></i>
                      </ng-template>
                    </div>
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-返回脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIconi">
                        <input
                          readonly
                          nz-input
                          formControlName="responseScript"
                          (dblclick)="showScriptModal('after', i, 'responseScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconi>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('after', i, 'responseScript')"></i>
                      </ng-template>
                    </div>
                  </div>
                  <!-- 脚本的配置 -->
                  <div
                    class="script"
                    *ngIf="afterItem.get('adpType').value === EActionType.Script"
                    formGroupName="actionCfg"
                  >
                    <div class="form-item">
                      <div class="item-title">
                        <span class="label-text">{{ 'dj-返回脚本' | translate }}</span>
                      </div>
                      <nz-input-group [nzSuffix]="suffixIconj">
                        <input
                          readonly
                          nz-input
                          formControlName="responseScript"
                          (dblclick)="showScriptModal('after', i, 'responseScript')"
                        />
                      </nz-input-group>
                      <ng-template #suffixIconj>
                        <i adIcon iconfont="icongaodaima" (click)="showScriptModal('after', i, 'responseScript')"></i>
                      </ng-template>
                    </div>
                  </div>
                  <!-- 修改模型状态 -->
                  <div
                    class="status"
                    *ngIf="afterItem.get('adpType').value === EActionType.Modify"
                    formGroupName="actionCfg"
                  >
                    <div class="form-item" formGroupName="bindForm">
                      <nz-form-item>
                        <div class="item-title">
                          <span class="label-text">{{ 'dj-选择模型' | translate }}</span>
                          <span class="item-required">*</span>
                        </div>
                        <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                          <ad-select
                            style="width: 100%"
                            [nzPlaceHolder]="'dj-请选择' | translate"
                            formControlName="serviceCode"
                            (ngModelChange)="handleModelChange('after', i, $event)"
                            [nzOptions]="modelList"
                            [nzDisabled]="disabledAll"
                          >
                          </ad-select>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                    <div formArrayName="fieldInfos">
                      <div
                        class="form-item"
                        *ngFor="let fd of afterItem.get('actionCfg').get('fieldInfos').controls; let j = index"
                        [formGroupName]="j"
                      >
                        <nz-form-item>
                          <div class="item-title">
                            <span class="label-text">{{ 'dj-审核状态更新为' | translate }}</span>
                            <span class="item-required">*</span>
                          </div>
                          <nz-form-control [nzErrorTip]="'dj-请选择' | translate">
                            <ad-select
                              style="width: 100%"
                              [nzPlaceHolder]="'dj-请选择' | translate"
                              formControlName="fieldValue"
                              [nzDisabled]="disabledAll"
                            >
                              <ng-container
                                *ngFor="
                                  let data of getDictionaries(
                                    afterItem.get('actionCfg')?.get('bindForm')?.get('serviceCode')?.value
                                  )
                                "
                              >
                                <ad-option
                                  [nzValue]="data.code"
                                  [nzLabel]="data.code + '_' + (data.lang.value['dj-LANG' | translate] || data.value)"
                                ></ad-option>
                              </ng-container>
                            </ad-select>
                          </nz-form-control>
                        </nz-form-item>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <i *ngIf="!disabledAll" adIcon iconfont="iconjianhao" class="iconjian" (click)="removeAction('after', i)"></i>
                <i *ngIf="!disabledAll" adIcon cdkDragHandle iconfont="icontuozhuaiziduan" class="iconmove"></i>
              </li>
            </ul>

            <button *ngIf="!disabledAll" ad-button adType="default" nzSize="large" class="add-button" (click)="addAction('after')">
              {{ 'dj-新增一组' | translate }}
            </button>
          </ng-container>
        </nz-collapse-panel>

        <!-- 异常处理 -->
        <nz-collapse-panel [nzHeader]="'dj-异常处理' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <nz-form-item>
            <nz-form-label nzNoColon>
              {{ 'dj-动作执行失败处理' | translate }}
              <span class="item-required">*</span>
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                class="question-icon"
                style="margin-left: 3px"
                nz-tooltip
                [nzTooltipTitle]="'dj-动作执行异常时，当前节点处理方法' | translate"
              >
              </i>
            </nz-form-label>
            <nz-form-control [nzErrorTip]="'dj-必选！' | translate">
              <nz-radio-group formControlName="exceptionPolicy">
                <label nz-radio nzValue="actionSkip" [nzDisabled]="disabledAll">{{ 'dj-跳过' | translate }}</label>
                <label nz-radio nzValue="actionAbort" [nzDisabled]="disabledAll">{{ 'dj-中止流程' | translate }}</label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
        </nz-collapse-panel>
      </nz-collapse>
    </form>
  </section>
</nz-spin>
<app-script-editor
  *ngIf="scriptModal"
  [scriptModal]="scriptModal"
  [script]="scriptData"
  (confirm)="handleCloseSript('confirm', $event)"
  (close)="handleCloseSript('close', $event)"
></app-script-editor>

<app-action-modal
  *ngIf="espActionVisible"
  [transferModal]="espActionVisible"
  [transferData]="{}"
  labelType="EspAction"
  (callBack)="handleSelectAction($event, false)"
  (closeModal)="handleSelectAction(null, true)"
>
</app-action-modal>
