<form nz-form class="checkpoint-setting-content" [formGroup]="dataFormGroup">
  <nz-collapse [nzBordered]="false">
    <!--人工设置的面板-->
    <nz-collapse-panel [nzHeader]="'dj-人工设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
      <ad-tabs class="node-setting-tab" navStyle="button" [nzSelectedIndex]="0">
        <ad-tab [nzTitle]="'dj-人员设置' | translate">
          <ng-container *ngTemplateOutlet="personalSetting"></ng-container>
          <ng-container *ngTemplateOutlet="conditionalDispatch"></ng-container>
        </ad-tab>
        <ad-tab *ngIf="!isCustomizeCard" [nzTitle]="'dj-签核按钮' | translate">
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-签核按钮设置' | translate }}
              <span class="item-required">*</span>
            </div>
            <div class="approve-btn-table">
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-操作按钮' | translate }}</div>
                <div nz-col nzSpan="12">{{ 'dj-启用' | translate }}</div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-保存' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="saveButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-提交' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="submitButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
              <div nz-row>
                <div nz-col nzSpan="12">{{ 'dj-退回' | translate }}</div>
                <div nz-col nzSpan="12">
                  <nz-switch
                    [(ngModel)]="backButton"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  ></nz-switch>
                </div>
              </div>
            </div>

            <!--退回按钮未打开，隐藏不给选-->
            <div class="approve-btn-backsetting" [ngClass]="{ 'approve-btn-backsetting-hide': !backButton }">
              <div class="form-item">
                <span class="item-title">
                  {{ 'dj-退回设置' | translate }}
                  <span class="item-required">*</span>
                </span>
                <div style="padding-left: 16px">
                  <span style="display: inline-block; margin-bottom: 8px">{{
                    'dj-退回至发起人重新提交后' | translate
                  }}</span>
                  <nz-radio-group
                    [(ngModel)]="returnExecutionStrategy"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="handelSwitchChange()"
                  >
                    <label style="margin-bottom: 8px" nz-radio nzValue="Sequential">
                      {{ 'dj-重新依次审批' | translate }}
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        aria-hidden="true"
                        class="question-icon"
                        nzTooltipTrigger="hover"
                        nz-tooltip
                        [nzTooltipTitle]="'dj-退回后，需回滚A-C间两个节点数据，并重新执行相关节点' | translate"
                      >
                      </i>
                    </label>
                    <!-- <label nz-radio nzValue="NonSequential" [nzDisabled]="true">
                      {{ 'dj-从当前节点审批' | translate }}
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        aria-hidden="true"
                        class="question-icon"
                        nzTooltipTrigger="hover"
                        nz-tooltip
                        [nzTooltipTitle]="'dj-退回后，需回滚' | translate"
                      >
                      </i>
                    </label> -->
                  </nz-radio-group>
                </div>
                <app-return-strategy
                  [nodeId]="data.id"
                  [returnStrategyData]="returnStrategy"
                  [preManualNodes]="preManualNodes"
                  (returnStrategyDataChange)="handleReturnStrategyDataChange($event)"
                ></app-return-strategy>
              </div>
            </div>
          </div>
        </ad-tab>
        <ad-tab [nzTitle]="'dj-字段权限' | translate">
          <app-field-setting-property
            [data]="fieldConfig"
            [bindForm]="bindForm"
            [nodeId]="data.id"
            [processId]="data.processId"
            [extendHeader]="service.combineHeaders"
            (changeData)="handleFieldSettingChange($event)"
          ></app-field-setting-property>
        </ad-tab>
      </ad-tabs>
      <div *ngIf="false">
        <ng-container *ngTemplateOutlet="personalSetting"></ng-container>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</form>

<ng-template #personalSetting>
  <app-people-setting-property
    #peopleSetting
    personType="execution"
    [bindForm]="bindForm"
    [nodeId]="nodeId"
    [nodeType]="nodeType"
    [executor]="executor"
    [decisionConfig]="decisionConfig"
    [preManualNodes]="preManualNodes"
    (changeData)="handlePeopleSettingChange($event)"
  ></app-people-setting-property>
</ng-template>

<ng-template #conditionalDispatch>
  <app-conditional-dispatch
    [data]="dispatchData"
    (changeData)="handleConditionalDispatchChange($event)"
  ></app-conditional-dispatch>
</ng-template>