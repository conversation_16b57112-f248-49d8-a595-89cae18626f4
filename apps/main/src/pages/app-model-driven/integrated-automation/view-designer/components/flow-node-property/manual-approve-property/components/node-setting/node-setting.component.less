.app-node-content {
  font-weight: normal;
  height: 100%;
  overflow-y: auto;
  font-family: PingFang SC;
  font-weight: normal;
  padding-bottom: 16px;
  .nz-form-item-content {
    margin-bottom: 6px;
  }
  .form-item {
    margin-bottom: 8px;

    .item-title {
      color: #333;
      font-size: 13px;
      margin-bottom: 8px;
      display: inline-block;

      .item-required {
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
      }
    }

    .hasError {
      ::ng-deep .ant-input-affix-wrapper {
        border: 1px solid #ea3d46;
      }
    }

    .hasErrorBorder {
      border: 1px solid #ea3d46;
    }

    .error {
      color: #ff4d4f;
      font-size: 12px;
    }
  }
  .question-icon {
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    line-height: 17px;
    vertical-align: middle;
  }

  .approve-btn-table {
    text-align: center;
    font-size: 13px;

    & > div[nz-row] {
      padding: 4px;
      border-top: 1px solid #e5e5e5;
      border-left: 1px solid #e5e5e5;
      border-right: 1px solid #e5e5e5;
    }

    & > div[nz-row]:last-child {
      border-bottom: 1px solid #e5e5e5;
    }

    & > div:first-child {
      font-size: 14px;
      background-color: #f2f2f2;
    }
  }

  .approve-btn-backsetting {
    background-color: #f8fafd;
    padding: 8px 16px;
    margin-top: 16px;
  }
  .approve-btn-backsetting-hide {
    display: none;
  }
  .form-item-tip {
    background: rgba(240, 98, 24, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: self-start;
    font-size: 12px;
    color: #fd5609;
    padding: 8px;
    margin-top: 16px;
  }

  ::ng-deep .node-setting-tab {
    .ant-tabs-nav-wrap {
      border-bottom: none !important;
      margin-bottom: 16px !important;
      margin-top: 8px;
    }

    .ant-tabs-tab {
      border: 1px solid #bec3e1;
      color: #6868ae;
      width: 82px !important;
    }

    .ant-tabs-tab-active {
      background: #dbdaf0;
      font-weight: normal;
    }
    ::ng-deep .ant-tabs-ink-bar {
      display: none !important;
    }
    .auto-comp-flag {
      margin-top: 12px;
    }
  }
}
