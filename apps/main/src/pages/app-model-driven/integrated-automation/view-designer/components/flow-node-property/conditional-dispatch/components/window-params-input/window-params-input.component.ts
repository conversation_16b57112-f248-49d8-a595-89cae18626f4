import {
  Component,
  EventEmitter,
  forwardRef,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { isNone, isNotNone } from 'common/utils/core.utils';
import { isEmpty, isEqual, omit } from 'lodash';

@Component({
  selector: 'app-window-params-input',
  templateUrl: './window-params-input.component.html',
  styleUrls: ['./window-params-input.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => WindowParamsInputComponent),
      multi: true,
    },
  ],
})
export class WindowParamsInputComponent implements OnInit, OnChanges, ControlValueAccessor {
  @Input() item: any;
  @Input() inputable: boolean = false; // 是否可输入后缀
  @Input() modelRoot: boolean = false; // 是不是只要展示模型的根
  @Input() rootMatchKey: string = 'modelCode';
  @Input() includeGlobalModel: boolean = true; // 是否包含流程的回掉模型
  @Input() maxWidth: number | undefined = undefined;
  /** 输入框前缀 */
  @Input() formPrefix = '';

  @Output() onChanged = new EventEmitter<any>();
  onChange = (value) => {};

  disabled: boolean;
  onTouched = () => {};

  constructor(
    // public viewApiService: ViewApiService,
  ) {
  }
  writeValue(obj: any): void {}
  registerOnChange(fn: any): void {
    this.onChange = (value) => {
      fn(value);
    };
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  showVariableModal: boolean = false;

  paramName: string;
  variable: any;

  ngOnInit(): void {
    this.initDefaultVariable(this.item);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      !isEqual(
        omit(changes.item?.currentValue || {}),
        omit(changes.item?.previousValue || {}),
      )
    ) {
      this.initDefaultVariable(changes.item.currentValue);
    }
  }

  private initDefaultVariable(item?: any): void {
    if (item) {
      this.variable = {
        paramName: item.paramName,
        paramCode: item.paramCode,
      };
      this.paramName = item.paramName;
    } else {
      this.variable = {};
      this.paramName = '';
    }
    this.checkAndChangeVariable()
  }

  getVariableTooltip(): string {
    if (isNotNone(this.formPrefix)) {
      const paramName = this.paramName;
      return !isEmpty(paramName)
        ? `${this.formPrefix}${paramName}`
        : `${this.formPrefix}`;
    }
    return '';
  }

  /**
   * 返回前缀宽度
   * @returns
   */
  getPrefixWidth(): string {
    if (this.maxWidth) return this.maxWidth - 40 + 'px';
    if (!this.inputable) return '228px';
    return '175px';
  }

  /**
   * 选择变量
   */
  handleChangeVariable(e: { variable: any}) {
    this.showVariableModal = false;
    this.variable = e.variable;
    this.paramName = null;
    this.handleSelChange();
  }

  handleSelChange(): void {
    if (this.inputable) this.onChange(this.paramName);
    this.onChange(this.variable?.paramName);
    this.onChanged.emit({
      paramCode: this.variable?.paramCode,
      paramName: this.variable?.paramName,
      sceneCatg: this.variable?.sceneCatg,
      extendsInfo: this.variable?.extendsInfo,
    });
  }
    /**
     * 检测变量
     * @param variable
     * @returns
     */
    async checkAndChangeVariable() {
      if (isEmpty(this.variable)) return;
      if (isNone(this.variable?.paramCode)) {
        this.handleSelChange();
      }
    }
}