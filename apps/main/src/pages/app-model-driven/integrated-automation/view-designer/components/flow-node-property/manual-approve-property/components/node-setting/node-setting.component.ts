import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, Validators } from '@angular/forms';
import {
  BindFormType,
  PannelTabsType,
} from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { cloneDeep, find, isEmpty, size } from 'lodash';
import { ManualApprovePropertyService } from '../../manual-approve-property.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';
import { PeopleSettingPropertyComponent } from '../../../people-setting-property/people-setting-property.component';

@Component({
  selector: 'app-approve-node-setting',
  templateUrl: './node-setting.component.html',
  styleUrls: ['./node-setting.component.less'],
})
export class ApproveNodeSettingComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  @ViewChild('peopleSetting') peopleSetting: PeopleSettingPropertyComponent;
  dataFormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  bindForm = {
    formCode: '', // 作业code
    modelCode: '', // 模型code
    serviceCode: '', // 服务code
    type: '', // 类型
  };

  // 当前选择的是否是自定义任务卡
  get isCustomizeCard() {
    return this.bindForm?.formCode && this.bindForm?.type === BindFormType.PageView;
  }

  get nodeType() {
    return this.data?._nodeType;
  }

  // 字段的设置
  fieldConfig: [];

  // 执行人不走formgroup ,单独处理
  executor: any = {};
  // 核决层级配置
  decisionConfig: any = {};
  // 前置的人工节点
  preManualNodes = [];
  // 自动跳过策略不走formgroup ,单独处理
  executeByPass = [];
  // 退回开启后的设置
  returnExecutionStrategy;

  // 退回开启表单配置
  returnStrategy = [];

  approveButton = true;
  reassignmentButton = true;
  backButton = true;
  refuseButton = true;
  addApproveButton = true;

  destroy$ = new Subject();

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    public service: ViewApiService,
    private manualApprovePropertyService: ManualApprovePropertyService,
  ) {
    this.manualApprovePropertyService.bindFormChange$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      if (isEmpty(data)) return;
      this.bindForm = data;
      this.fieldConfig = [];
    });
  }

  ngOnInit() {
    this.dataFormGroup = this.fb.group({
      strategyConfig: this.fb.group({
        // 审批策略
        multiplayerStrategy: [this.data?.strategyConfig.multiplayerStrategy, [Validators.required]],
        // 自动完成
        autoCompleteFlag: [this.data?.strategyConfig.autoCompleteFlag || false],
      }),
    });
    this.handleInit();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      // id不相同认为是不同的节点需要重新构建formgroup，后期可以使用patchValue,而非重新初始化formgroup
      if (!changes.nodeId.firstChange) {
        // 为了patchValue不触发valueChanges
        this.formGroupValidityFlag = true;
        this.dataFormGroup.patchValue({
          strategyConfig: {
            multiplayerStrategy: this.data?.strategyConfig.multiplayerStrategy,
            autoCompleteFlag: this.data?.strategyConfig.autoCompleteFlag || false,
          },
        });
        this.formGroupValidityFlag = false;
        this.handleInit();
      }
    }
  }

  handleInit(data?: any) {
    const cData = data ?? this.data;
    this.executor = cloneDeep(cData?.executor); //
    this.preManualNodes = cloneDeep(cData?.preManualNodes || []); //
    this.decisionConfig = cloneDeep(cData?.decisionConfig); //
    // bindForm的处理
    this.bindForm = cloneDeep(cData?.bindForm);
    this.fieldConfig = cloneDeep(cData?.fieldConfig); //
    this.executeByPass = cloneDeep(
      cData?.strategyConfig.executeByPass.map((item) => ({
        key: item.key,
        enable: item.enable === 'Y' ? true : false,
      })),
    ); //
    this.returnExecutionStrategy = cloneDeep(cData?.strategyConfig.returnExecutionStrategy || 'Sequential'); //
    this.returnStrategy = cloneDeep(cData?.strategyConfig.returnStrategy || []); //
    // _buttons的处理
    this.approveButton = find(cData?._buttons, (button) => button.id === 'workflow-act-agree');
    this.refuseButton = find(cData?._buttons, (button) => button.id === 'workflow-act-disagree');
    this.addApproveButton = find(cData?._buttons, (button) => button.id === 'workflow-act-add');
    this.backButton = find(cData?._buttons, (button) => button.id === 'workflow-act-return');
    this.reassignmentButton = find(cData?._buttons, (button) => button.id === 'workflow-act-reassignment');
  }

  handleCopyData(data: any) {
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      strategyConfig: this.fb.group({
        // 审批策略
        multiplayerStrategy: [data?.strategyConfig.multiplayerStrategy, [Validators.required]],
        autoCompleteFlag: this.data?.strategyConfig.autoCompleteFlag || false,
      }),
    });
    this.formGroupValidityFlag = false;
    // QC说的是prd这块没写不复制，所以就应该复制
    // this.peopleSetting?.resetPersonnelSel();
    this.peopleSetting?.refresh(data);
    this.handleInit(data);
    // this.peopleSetting?.updataRequireClassStatue();
    setTimeout(() => {
      this.getCurrentData();
    }, 200);
  }

  ngAfterViewInit() {
    this.getCurrentData();
  }

  // 子组件人员设定发生了改变
  handlePeopleSettingChange(data) {
    const { executor, decisionConfig } = data;
    this.executor = executor;
    this.decisionConfig = decisionConfig;
    this.getCurrentData();
  }

  handleFieldSettingChange(data) {
    const { fieldConfig } = data;
    this.fieldConfig = fieldConfig;
    this.getCurrentData();
  }

  // executeByPass的改变要手动通知外层
  handleExecuteByPassChange() {
    // 异步是为了等checkbox值改变完，this.executeByPass已是最新值再去调用
    setTimeout(() => {
      this.getCurrentData();
    });
  }

  // 签核按钮的改变
  handelSwitchChange() {
    this.getCurrentData();
  }

  handleReturnStrategyDataChange(data) {
    this.returnStrategy = data || [];
    this.getCurrentData();
  }

  getReturnStrategyValidate() {
    let validate = true;
    for (let item of this.returnStrategy) {
      if (!item.nodeId || !item.actionType) {
        validate = false;
        break;
      }
    }
    return validate;
  }

  getFormValidate() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    this.formGroupValidityFlag = false;
    let isVerificationPassed = this.dataFormGroup.valid;
    if (this.dataFormGroup.valid) {
      // 签核人设置是否符合
      if (this.executor?.source === 'personnel') {
        if (size(this.executor?.personnel) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'variable') {
        if (size(this.executor?.variable) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'activity') {
        if (size(this.executor?.activity) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'formFiled') {
        if (size(this.executor?.formFiled) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'department') {
        if (size(this.executor?.department) === 0) {
          isVerificationPassed = false;
        }
      }
      // 针对核决权限的层级来源、审批终点来校验
      if (this.executor?.type === 'decision') {
        if (!this.decisionConfig.levelType || !this.decisionConfig.level) {
          isVerificationPassed = false;
        }
      }
      if (this.backButton && this.returnStrategy?.length > 0 && !this.getReturnStrategyValidate()) {
        isVerificationPassed = false;
      }
    }
    return isVerificationPassed;
  }

  // 获取当前最新的数据
  getCurrentData() {
    // 混入isVerificationPassed 是否校验通过
    let isVerificationPassed = this.getFormValidate();

    let currentData = this.dataFormGroup.getRawValue();

    // 回填 strategyConfig.executeByPass
    currentData.strategyConfig.executeByPass = this.executeByPass.map((item) => ({
      key: item.key,
      enable: item.enable ? 'Y' : 'N',
    }));
    // 回填 executor
    currentData.executor = this.executor;
    console.log('🚀 ~ ApproveNodeSettingComponent ~ getCurrentData ~ this.executor:', this.executor);
    // 回填 decisionConfig 核决层级
    currentData.decisionConfig = this.decisionConfig;
    // 回填 fieldConfig
    currentData.fieldConfig = this.fieldConfig;
    // 回填 _buttons的处理
    currentData._buttons = [];
    if (currentData.strategyConfig.multiplayerStrategy === 'andSign') {
      this.addApproveButton = false;
    }

    if (this.approveButton) {
      currentData._buttons.push({ id: 'workflow-act-agree' });
    }
    if (this.refuseButton) {
      currentData._buttons.push({ id: 'workflow-act-disagree' });
    }
    if (this.addApproveButton) {
      currentData._buttons.push({ id: 'workflow-act-add' });
    }
    // 增加自定义任务卡场景：当选择的是自定义任务卡&转派勾选框选中
    if (this.reassignmentButton || this.data.transferButton) {
      currentData._buttons.push({ id: 'workflow-act-reassignment' });
    }
    if (this.backButton) {
      currentData._buttons.push({ id: 'workflow-act-return' });
      // 退回按钮开启 回填 strategyConfig.returnExecutionStrategy
      currentData.strategyConfig.returnExecutionStrategy = this.returnExecutionStrategy;
      currentData.strategyConfig.returnStrategy = this.returnStrategy;
    } else {
      // 没开启给默认值Sequential
      currentData.strategyConfig.returnExecutionStrategy = 'Sequential';
      currentData.strategyConfig.returnStrategy = [];
    }

    // 多语言回填
    const returnData = {
      data: Object.assign(this.data, currentData, { taskCode: currentData.id }),
      valid: isVerificationPassed,
      componentType: 'approveNodeSetting',
    };
    this.changeData.emit(returnData);
    return returnData;
  }
}
