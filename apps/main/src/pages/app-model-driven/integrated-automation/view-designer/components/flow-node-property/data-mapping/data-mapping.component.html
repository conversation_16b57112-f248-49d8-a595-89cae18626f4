<div class="header">
  <span>{{ 'dj-数据映射' | translate }}</span>
  <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
</div>

<nz-spin [nzSpinning]="loading" class="spining">
  <section class="content data-mapping">
    <form nz-form [formGroup]="form" [nzLayout]="'vertical'">
      <nz-collapse [nzBordered]="false">
        <!-- 入参设置 -->
        <nz-collapse-panel
          *ngIf="!isAutoNode"
          [nzHeader]="collapseInHeader"
          [nzActive]="true"
          [nzExpandedIcon]="'caret-right'"
        >
          <ng-container formGroupName="input">
            <nz-form-item>
              <nz-form-control>
                <nz-radio-group formControlName="type">
                  <label nz-radio nzValue="mapping" [nzDisabled]="!!isWaitNode || disabledAll">{{ 'dj-UI模式' | translate }}</label>
                  <label nz-radio nzValue="script" [nzDisabled]="disabledAll">{{ 'dj-脚本模式' | translate }}</label>
                </nz-radio-group>
              </nz-form-control>
            </nz-form-item>

            <ng-container *ngIf="form.get('input.type').value === 'mapping'; else inscriptTemplate">
              <ul class="mapping-list" formArrayName="mapping">
                <li class="mapping-item" *ngFor="let item of inputMapping.controls; let i = index" [formGroupName]="i">
                  <nz-form-item>
                    <nz-form-label nzNoColon>
                      {{ 'dj-当前节点' | translate }}
                      <span class="item-required">*</span>
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        class="question-icon"
                        style="margin-left: 3px"
                        nz-tooltip
                        [nzTooltipTitle]="
                          'dj-选择当前节点的映射信息，当需要整笔映射时，第一行需配置root节点' | translate
                        "
                      >
                      </i>
                    </nz-form-label>
                    <nz-form-control [nzErrorTip]="'dj-必填' | translate">
                      <app-mutil-tree-select
                        [options]="currentNodeSelectData"
                        formControlName="keyCode"
                      ></app-mutil-tree-select>
                    </nz-form-control>
                  </nz-form-item>
                  <nz-form-item>
                    <nz-form-label nzNoColon>
                      {{ 'dj-输入源' | translate }}
                      <span class="item-required">*</span>
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        class="question-icon"
                        style="margin-left: 3px"
                        nz-tooltip
                        [nzTooltipTitle]="'dj-选择输入源，支持选择前序节点输出的流程变量' | translate"
                      >
                      </i>
                    </nz-form-label>
                    <nz-form-control [nzErrorTip]="'dj-必填' | translate">
                      <app-variable-select-input
                        [variableRange]="inputRange"
                        [inputable]="true"
                        formControlName="valueCode"
                        [valueType]="item.get('valueType').value"
                        [item]="item.value"
                        [readonly]="disabledAll"
                        [nodeId]="state.currentSelectedNodeId"
                        (onChanged)="handleMappingTargetChange($event, 'input', i)"
                      >
                      </app-variable-select-input>
                    </nz-form-control>
                  </nz-form-item>

                  <i *ngIf="!disabledAll" adIcon iconfont="iconjianhao" class="iconjian" (click)="removeMapping('input', i)"></i>
                </li>
              </ul>

              <button *ngIf="!disabledAll" ad-button adType="default" nzSize="large" class="add-button" (click)="handleAddMapping('input')">
                {{ 'dj-新增一组' | translate }}
              </button>
            </ng-container>
            <!-- 脚本模式 -->
            <ng-template #inscriptTemplate>
              <span>
                {{ 'dj-输出脚本设置' | translate }}
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-脚本中必须有return' | translate"
                >
                </i>
              </span>
              <nz-input-group [nzSuffix]="suffixIcon">
                <input readonly nz-input formControlName="script" (dblclick)="handleOpenSript('input')" />
              </nz-input-group>
              <ng-template #suffixIcon>
                <i adIcon iconfont="icongaodaima" (click)="handleOpenSript('input')"></i>
              </ng-template>
            </ng-template>
          </ng-container>
          <ng-template #collapseInHeader>
            <span class="collapse-header">
              {{ 'dj-入参设置' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                class="question-icon"
                nz-tooltip
                [nzTooltipTitle]="
                  'dj-默认入参为上一节点的输出，可通过该模块将前序使用的变量赋值给当前节点，系统将根据配置的顺序进行数据处理，如需实现整包数据的单笔替换，需配置两笔'
                    | translate
                "
              >
              </i>
            </span>
          </ng-template>
        </nz-collapse-panel>

        <!-- 出参设置 -->
        <nz-collapse-panel [nzHeader]="collapseOutHeader" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <ng-container formGroupName="output">
            <nz-form-item>
              <nz-form-control>
                <nz-radio-group formControlName="type">
                  <label nz-radio nzValue="mapping" [nzDisabled]="!!isAutoNode || !!isWaitNode || disabledAll">{{ 'dj-UI模式' | translate }}</label>
                  <label nz-radio nzValue="script" [nzDisabled]="disabledAll">{{ 'dj-脚本模式' | translate }}</label>
                </nz-radio-group>
              </nz-form-control>
            </nz-form-item>

            <ng-container *ngIf="form.get('output.type').value === 'mapping'; else outscriptTemplate">
              <ul class="mapping-list" formArrayName="mapping">
                <li class="mapping-item" *ngFor="let item of outputMapping.controls; let i = index" [formGroupName]="i">
                  <nz-form-item>
                    <nz-form-label nzNoColon>
                      {{ 'dj-当前节点' | translate }}
                      <span class="item-required">*</span>
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        class="question-icon"
                        style="margin-left: 3px"
                        nz-tooltip
                        [nzTooltipTitle]="
                          'dj-选择当前节点的映射信息，选择当前节点的映射信息，当需要整笔映射时，第一行需配置root节点'
                            | translate
                        "
                      >
                      </i>
                    </nz-form-label>
                    <nz-form-control [nzErrorTip]="'dj-必填' | translate">
                      <app-mutil-tree-select
                        [options]="currentNodeSelectData"
                        formControlName="keyCode"
                      ></app-mutil-tree-select>
                    </nz-form-control>
                  </nz-form-item>
                  <nz-form-item>
                    <nz-form-label nzNoColon>
                      {{ 'dj-输出目标' | translate }}
                      <span class="item-required">*</span>
                      <i
                        adIcon
                        iconfont="iconshuomingwenzi"
                        class="question-icon"
                        style="margin-left: 3px"
                        nz-tooltip
                        [nzTooltipTitle]="
                          'dj-选择输出目标，支持选择自定义流程变量，也可以将结果输出至当前节点的标准输出中' | translate
                        "
                      >
                      </i>
                    </nz-form-label>
                    <nz-form-control [nzErrorTip]="'dj-必填' | translate">
                      <app-variable-select-input
                        [variableRange]="outputRange"
                        formControlName="valueCode"
                        [valueType]="item.get('valueType').value"
                        [item]="item.value"
                        [nodeId]="state.currentSelectedNodeId"
                        (onChanged)="handleMappingTargetChange($event, 'output', i)"
                      >
                      </app-variable-select-input>
                    </nz-form-control>
                  </nz-form-item>
                  <i adIcon iconfont="iconjianhao" class="iconjian" (click)="removeMapping('output', i)"></i>
                </li>
              </ul>

              <button *ngIf="!disabledAll" ad-button adType="default" nzSize="large" class="add-button" (click)="handleAddMapping('output')">
                {{ 'dj-新增一组' | translate }}
              </button>
            </ng-container>
            <!-- 脚本模式 -->
            <ng-template #outscriptTemplate>
              <span>
                {{ 'dj-输出脚本设置' | translate }}
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  class="question-icon"
                  nzTooltipTrigger="hover"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-脚本中必须有return' | translate"
                >
                </i>
              </span>
              <nz-input-group [nzSuffix]="suffixIcon">
                <input readonly nz-input formControlName="script" (dblclick)="handleOpenSript('output')" />
              </nz-input-group>
              <ng-template #suffixIcon>
                <i adIcon iconfont="icongaodaima" (click)="handleOpenSript('output')"></i>
              </ng-template>
            </ng-template>
          </ng-container>
          <ng-template #collapseOutHeader>
            <span class="collapse-header">
              {{ 'dj-出参设置' | translate }}
              <i
                adIcon
                iconfont="iconshuomingwenzi"
                class="question-icon"
                nz-tooltip
                [nzTooltipTitle]="
                  'dj-默认出参为节点标准输出，可通过该模块将返回值存放至某个已定义的流程变量中' | translate
                "
              >
              </i>
            </span>
          </ng-template>
        </nz-collapse-panel>
      </nz-collapse>
    </form>
  </section>
</nz-spin>
<!--script脚本编辑器弹窗-->
<app-script-editor
  *ngIf="scriptModalVisible"
  [scriptModal]="scriptModalVisible"
  [script]="script"
  (confirm)="handleCloseSript('confirm', $event)"
  (close)="handleCloseSript('close', $event)"
></app-script-editor>
