import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { debounce, omit } from 'lodash';
import { ViewStoreService } from '../../../service/store.service';
import { ViewGraphService } from '../../../service/graph.service';

@Component({
  selector: 'app-auto-http-property',
  templateUrl: './auto-http-property.component.html',
  styleUrls: ['./auto-http-property.component.less'],
})
export class AutoHttpPropertyComponent implements OnInit, OnChanges {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup;
  scriptData: any;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  formGroupValidityFlag = false;
  // 多语言单独处理，name
  nameLang: any;
  // Domain 下拉选项
  domainList = [
    'default',
    'eoc',
    'iam',
    'smartdata',
    'thememap',
    'aim',
    'atmc',
    'flowengine',
    'taskengine',
    'emc',
    'lcdp',
    'abi',
    'aam',
    'mdc',
    'itsys',
    'cac',
    'airh',
  ];

  currScriptControlName: string;
  scriptModal: boolean;

  requestScript: string = `/*
脚本处理请求参数后return内容为接口入参
  var request = {
      'std_data': {
          'parameter': {
          }
      }
  };
  return request;
 */`;
  responseScript: string = `/*
处理返回结果并存储
var response = $(response);
  return {
    "success" : true,
    "processVariable" : {
      "key1" : "value1"
    },
    "errorMessage" : ""
  };
 */`;

  get disabledAll() {
    return !!this.viewGraphService?.fromManual;
  }

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    public viewStoreService: ViewStoreService,
    private viewGraphService: ViewGraphService,
  ) {}

  ngOnInit(): void {
    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  handleChangeValue() {
    // 为了patchValue不触发valueChanges
    this.formGroupValidityFlag = true;
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      scheduleRule:
        this.data?.serviceConfig?.scheduleRule?.schedule_type === '0'
          ? {
              schedule_type: this.data?.serviceConfig?.scheduleRule?.schedule_type,
              delay_seconds: this.data?.serviceConfig?.scheduleRule?.delay_seconds,
            }
          : {
              schedule_type: this.data?.serviceConfig?.scheduleRule?.schedule_type,
            },
      header:
        typeof this.data?.serviceConfig.header === 'string'
          ? this.data?.serviceConfig.header
          : JSON.stringify(this.data?.serviceConfig.header),
      domain: this.data?.serviceConfig.domain,
      url: this.data?.serviceConfig.url,
      method: this.data?.serviceConfig.method,
      requestScript: this.data?.serviceConfig.requestScript,
      responseScript: this.data?.serviceConfig.responseScript,
    });
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  handleClosePanel(): void {
    this.close.emit();
  }
  showScriptModal(controlName) {
    this.currScriptControlName = controlName;
    const defaultScript = controlName === 'requestScript' ? this.requestScript : this.responseScript;
    this.scriptData = this.dataFormGroup.get(controlName)?.value || defaultScript;
    this.scriptModal = true;
  }
  handleCloseSript(flag, value) {
    if (flag === 'confirm') {
      this.dataFormGroup.patchValue({
        [this.currScriptControlName]: value,
      });
    }
    this.scriptModal = false;
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    // 初始化fromgroup
    // 直接校验
    // 对父组件发出change事件主要里面混入isVerificationPassed
    let scheduleRule = this.fb.group({
      schedule_type: [
        { value: this.data?.serviceConfig?.scheduleRule?.schedule_type || '', disabled: this.disabledAll },
      ],
    });
    if (this.data?.serviceConfig?.scheduleRule?.schedule_type === '0') {
      scheduleRule = this.fb.group({
        schedule_type: [
          { value: this.data?.serviceConfig?.scheduleRule?.schedule_type || '', disabled: this.disabledAll },
        ],
        delay_seconds: [
          { value: this.data?.serviceConfig?.scheduleRule?.delay_seconds || '', disabled: this.disabledAll },
          [Validators.required],
        ],
      });
    }
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      scheduleRule: scheduleRule,
      header: [
        {
          value:
            typeof this.data?.serviceConfig.header === 'string'
              ? this.data?.serviceConfig.header
              : JSON.stringify(this.data?.serviceConfig.header),
          disabled: this.disabledAll,
        },
      ],
      domain: [{ value: this.data?.serviceConfig?.domain || '', disabled: this.disabledAll }],
      url: [{ value: this.data?.serviceConfig?.url || '', disabled: this.disabledAll }],
      method: [{ value: this.data?.serviceConfig?.method || '', disabled: this.disabledAll }],
      requestScript: [{ value: this.data?.serviceConfig?.requestScript || '', disabled: this.disabledAll }],
      responseScript: [{ value: this.data?.serviceConfig?.responseScript || '', disabled: this.disabledAll }],
    });
    this.nameLang = this.data.lang;
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleScheduleTypeChange(value) {
    const scheduleRuleGroup = this.dataFormGroup.get('scheduleRule') as FormGroup;
    if (value === '0') {
      scheduleRuleGroup.addControl('delay_seconds', this.fb.control(null, Validators.required));
      return;
    }
    scheduleRuleGroup.removeControl('delay_seconds');
  }

  // 获取当前最新的数据
  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      const deepValidateKey = ['schedule_type', 'delay_seconds'];
      for (let i of deepValidateKey) {
        this.dataFormGroup.get('scheduleRule').get(i)?.markAsDirty();
        this.dataFormGroup.get('scheduleRule').get(i)?.updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      // 混入isVerificationPassed 是否校验通过
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);

      if (currentData.header) {
        try {
          currentData.header = JSON.parse(currentData.header);
        } catch (error) {}
      }

      let serviceConfigData = { serviceConfig: { ...this.data.serviceConfig, ...omit(currentData, 'name') } };
      const returnData = {
        data: Object.assign(this.data, serviceConfigData, { name: currentData.name, lang: this.nameLang }),
        isVerificationPassed,
      };
      this.changeData.emit(returnData);
      return returnData;
    },
    150,
    { leading: false, trailing: true },
  );
}
