.add-field-value-body {
  margin-bottom: 8px;
  .search-content {
    margin-bottom: 12px;
    .search-label {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 16px;
        background-color: #6868ae;
        margin-right: 4px;
      }
    }
  }
  .tree-box-loading {
    margin: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 160px;
  }
  .tree-box-empty {
    margin: 16px;
    min-height: 160px;
  }
  .tree-box-top {
    margin-top: 2px;
    max-height: 450px;
    overflow-y: auto;
  }
  .tree-node-title {
    border-left: 1px solid #e5e5e5;
    border-top: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    padding: 8px;
    background-color: #f2f2f2;
    width: 100%;
    display: flex;
    .node-title-component-name {
      flex: auto;
      font-size: 13px;
      font-weight: 500;
    }
    .node-title-component-field {
      width: 305px;
      font-size: 13px;
      font-weight: 500;
    }
  }
  .hide-radio {
    visibility: hidden;
    pointer-events: none;
  }
  .node-line-expend {
    display: inline-flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    i {
      border-radius: 50%;
      overflow: hidden;
    }
  }
  .node-line-desc {
    display: flex;
    align-items: center;

    .icon {
      font-size: 16px;
      margin-right: 16px;
      margin-left: -12px;
    }
  }
  .node-line-desc-v {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .node-line-right {
    width: 100%;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .body-table {
    thead > tr > th {
      padding: 4px;
    }
  }
}
.add-field-value-footer {
  text-align: center;
  .mr20 {
    margin-right: 20px;
  }
}
::ng-deep {
  .athena-tree-node {
    background-color: transparent;
    &:has(.selected) {
      background-color: #eef0ff;
    }
  }
  .ant-tree-treenode-selected {
    background-color: transparent;
  }
}
