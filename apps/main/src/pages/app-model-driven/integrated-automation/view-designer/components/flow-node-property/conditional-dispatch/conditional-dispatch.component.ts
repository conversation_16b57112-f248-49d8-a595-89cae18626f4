import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, isEmpty, size, find } from 'lodash';
import {
  BindFormType,
  PannelTabsType,
  VariableType,
} from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { Subject } from 'rxjs';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';
import { EVariableRange } from 'pages/app-model-driven/integrated-automation/view-designer/components/flow-node-property/variable-select-input/components/variable-modal/variable-modal.component';
import { ViewStoreService } from 'pages/app-model-driven/integrated-automation/view-designer/service/store.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ConditionalDispatchService } from "./conditional-dispatch.service"

@Component({
  selector: 'app-conditional-dispatch',
  templateUrl: './conditional-dispatch.component.html',
  styleUrls: ['./conditional-dispatch.component.less'],
  providers: [ConditionalDispatchService],
})
export class ConditionalDispatchComponent implements OnInit {
  // 是否手动输入选项名称
  @Input() inputName = false;
  // 输入的渲染数据
  @Input() data: any;
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  /** 条件分派开关  */
  supportConditionAssign = false;
  /** 条件分派选项列表  */
  optionsList = this.fb.array([]);
  get inputRange() {
    const mode =
      EVariableRange.BUSINESS |
      EVariableRange.CUSTOM |
      EVariableRange.MODEL |
      EVariableRange.NODE |
      EVariableRange.SYSTEM;
    if (this.viewStoreService.isFusionMode()) return mode | EVariableRange.DTD;
    return mode;
  }
  get state() {
    return this.viewStoreService.state;
  }
  
  destroy$ = new Subject();

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    public service: ViewApiService,
    private viewStoreService: ViewStoreService,
    private message: NzMessageService,
    private conditionalDispatchService: ConditionalDispatchService,
  ) {
  }

  ngOnInit() {
    this.dataFormGroup = this.fb.group({
      optionsList: this.optionsList,
    });
    this.handleInit();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleInit();
      }
    }
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    this.supportConditionAssign = this.data?.supportConditionAssign;
    if (this.supportConditionAssign) {
      this.data?.optionsList?.map(item=>{
        const group = this.fb.group({
          valueCode: [item.column, Validators.required],
          valueType: [item.valueType],
          variableDesc: [item.field],
          valuePath: [item.valuePath],
          columnSuffix: [item.columnSuffix],
          variableType: [item.variableType],
          openWindowName: [item.openWindowName, Validators.required],
          openWindowKey: [item.openWindowKey],
          lang: [item.lang],
        });
        this.dataFormGroup.get('optionsList').push(group)
      })
    }
  }
  ngAfterViewInit() {
    this.getCurrentData();
  }

  getFormValidate() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    this.formGroupValidityFlag = false;
    let isVerificationPassed = this.dataFormGroup.valid;
    return isVerificationPassed;
  }

  // 获取当前最新的数据
  getCurrentData() {
    // 混入isVerificationPassed 是否校验通过
    let isVerificationPassed = this.getFormValidate();

    let currentData = this.dataFormGroup.value;
    // 多语言回填
    const returnData = {
      data: Object.assign({}, currentData, {supportConditionAssign: this.supportConditionAssign}),
      valid: isVerificationPassed,
      componentType: 'conditionalDispatch',
    };
    this.changeData.emit(returnData);
    return returnData;
  }
  /**
   * @description: 切换条件分派开关
   * @return {*}
   */
  handelDispatchChange(isOpen) {
    if (isOpen) {
      this.handleAddDispatch()
    } else {
      this.optionsList.clear()
    }
    this.getCurrentData();
  }
  handleAddDispatch() {
    const group = this.fb.group({
      valueCode: [null, Validators.required],
      valueType: [null],
      variableDesc: [null],
      valuePath: [null],
      columnSuffix: [null],
      variableType: [null],
      openWindowName: [null, Validators.required],
      openWindowKey: [null],
      lang: [null],
    });
    group.markAllAsTouched();
    this.optionsList.push(group);
  }
  /**
   * 映射目标Change回调
   * @param value
   */
  handleMappingTargetChange(
    data: { valueCode: string; valueType: string; variableDesc: string; variableCode?: string; variableSuffix?: string, variableType: string },
    index: number,
  ) {
    if (data.valueType) {
      const dispatchFormData = this.dataFormGroup.get('optionsList').value;
      const sameItemIndex = dispatchFormData?.findIndex(
        (item) =>
          item.valueType === data.valueType &&
          (([VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(data.valueType as any) &&
            item.valuePath === data.valueCode &&
            item.valueCode === data.variableCode) ||
            item.valueCode === data.valueCode),
      );
      if (sameItemIndex !== -1) {
        // 重复数据手动清除所选内容
        this.dataFormGroup.get(['optionsList', index]).patchValue({
          valueCode: [null, Validators.required],
          valueType: [null],
          variableDesc: [null],
          valuePath: [null],
          columnSuffix: [null],
          variableType: [null],
        });
        this.message.error(this.translate.instant('dj-存在重复数据，请检查!'));
        return;
      }
    }
      this.dataFormGroup.get(['optionsList', index, 'valuePath']).setValue(null);
      this.dataFormGroup.get(['optionsList', index, 'valueType']).setValue(data.valueType);
      this.dataFormGroup.get(['optionsList', index, 'variableDesc']).setValue(data.variableDesc);
      if (data.valueType) {
        this.addLang(data.variableDesc, index, 'field')
      }
      if (data.valueType === VariableType.BUSINESS_OBJECT || data.valueType === VariableType.MODEL) {
        // 如果是对象变量，将valuePath的值设为变量名
        this.dataFormGroup.get(['optionsList', index, 'valueCode']).setValue(data.variableCode);
        this.dataFormGroup.get(['optionsList', index, 'valuePath']).setValue(data.valueCode);
      } else {
        this.dataFormGroup.get(['optionsList', index, 'valueCode']).setValue(data.valueCode);
        if (data.valueType === VariableType.NODE) {
          this.dataFormGroup.get(['optionsList', index, 'valuePath']).setValue('');
        } else if (data.valueType === VariableType.Mechanism) {
          this.dataFormGroup.get(['optionsList', index, 'valuePath']).setValue(data.variableCode);
        }
      }
      this.dataFormGroup.get(['optionsList', index, 'columnSuffix']).setValue(data.variableSuffix);
      this.dataFormGroup.get(['optionsList', index, 'variableType']).setValue(data.variableType);
      this.dataFormGroup.get(['optionsList', index, 'valueCode']).markAsDirty();
      this.dataFormGroup.get(['optionsList', index, 'valueCode']).updateValueAndValidity();
  }
  /**
   * 删除映射
   * @param formArray
   * @param index
   */
  removeMapping(index: number, noTip = false): void {
    if (noTip || this.dataFormGroup.get('optionsList').controls.length > 1) {
      this.optionsList.removeAt(index);
    } else {
      this.message.warning(this.translate.instant('dj-至少添加一条数据'))
    }
  }
  /**
   * @description: 开窗参数选择
   * @param {*} data 所选参数
   * @param {*} index 第几条数据
   * @return {*}
   */
  async handleWindowParamsChange(data, index) {
    // 暂不校验选项内容唯一性
    // if (data.paramCode) {
    //   const dispatchFormData = this.dataFormGroup.get('optionsList').value;
    //   const sameItemIndex = dispatchFormData?.findIndex(item => item.openWindowKey === data.paramCode);
    //   if (sameItemIndex !== -1) {
    //     this.message.error(this.translate.instant('dj-存在重复数据，请检查!'));
    //     this.dataFormGroup.get(['optionsList', index, 'openWindowName']).setValue(null);
    //     this.dataFormGroup.get(['optionsList', index, 'openWindowKey']).setValue(null);
    //     return;
    //   }
    // }
    this.dataFormGroup.get(['optionsList', index, 'openWindowName']).setValue(data?.paramName);
    this.dataFormGroup.get(['optionsList', index, 'openWindowKey']).setValue(data?.paramCode);
    if (data?.paramName) {
      await this.addLang(data?.paramName, index)
    }
    this.dataFormGroup.get(['optionsList', index, 'openWindowName']).markAsDirty();
    this.dataFormGroup.get(['optionsList', index, 'openWindowName']).updateValueAndValidity();
  }
  async addLang(value: string, index, langIten = 'openWindowName') {
    let lang = Object.assign({}, this.dataFormGroup.get(['optionsList', index, 'lang']).value || {})
    const res = await this.conditionalDispatchService.translate({
      content: value,
      convertTypes: ['zh2Hant', 'zh2Hans']
    }).toPromise()
    if (res.code === 0) {
      lang[langIten] = {
        zh_CN: res.data?.zh2Hans,
        zh_TW: res.data?.zh2Hant,
      }
      this.dataFormGroup.get(['optionsList', index, 'lang']).setValue(lang);
    }
  }
  inputValueCodeChange(value, index) {
    let lang = Object.assign({}, this.dataFormGroup.get(['optionsList', index, 'lang']).value || {})
    lang['field'] = {
      zh_CN: value,
      zh_TW: value,
    }
    this.dataFormGroup.get(['optionsList', index, 'lang']).setValue(lang);
  }
}
