<div class="people-setting-property-root">
  <div class="form-item" [formGroup]="dataFormGroup" style="margin-bottom: 0">
    <div class="item-title" *ngIf="'approve' === personType">
      {{ 'dj-签核人设置' | translate }}
      <span class="item-required">*</span>
    </div>
    <div class="item-title" *ngIf="'notification' === personType">
      {{ 'dj-知会人设置' | translate }}
      <span class="item-required">*</span>
    </div>
    <div class="item-title" *ngIf="'execution' === personType">
      {{ 'dj-执行人设置' | translate }}
      <span class="item-required">*</span>
    </div>

    <nz-radio-group nzName="radiogroup" formControlName="type" ngDefaultControl style="margin-bottom: 8px">
      <label nz-radio nzValue="default" *ngIf="showDefault" [nzDisabled]="disabledAll">{{ 'dj-默认' | translate }}
        <i
          adIcon
          iconfont="iconshuomingwenzi"
          aria-hidden="true"
          class="question-icon"
          nzTooltipTrigger="hover"
          nz-tooltip
          [nzTooltipTitle]="
            (showDefault
              ? 'dj-若选择的职能中对应多个成员，则发多张卡'
              : 'dj-由用户发起时，当责者为具体发起人，发起人接受项目卡。自动发起时默认当责者为Athena'
            ) | translate
          "
        >
        </i>
      </label>
      <label nz-radio nzValue="personnel" [nzDisabled]="disabledAll">{{ 'dj-指定人员' | translate }}</label>
      <label nz-radio nzValue="duty" [nzDisabled]="disabledAll">{{ 'dj-指定职能' | translate }}
        <i
          adIcon
          iconfont="iconshuomingwenzi"
          aria-hidden="true"
          class="question-icon"
          nzTooltipTrigger="hover"
          nz-tooltip
          [nzTooltipTitle]="
            (showDefault
              ? 'dj-若选择的职能中对应多个成员，则发多张卡'
              : 'dj-若选择的职能中对应多个成员，则全部成员为审批人，均会发送任务卡'
            ) | translate
          "
        >
        </i>
      </label>
      <label nz-radio nzValue="superior" [nzDisabled]="disabledAll">{{ 'dj-直属主管' | translate }}
        <i
          adIcon
          iconfont="iconshuomingwenzi"
          aria-hidden="true"
          class="question-icon"
          nzTooltipTrigger="hover"
          nz-tooltip
          [nzTooltipTitle]="
            'dj-选择主管来源时，若获取到多个基准成员，系统将默认选择首个成员进行直属主管查询。' | translate
          "
        >
        </i>
      </label>
      <label nz-radio nzValue="director" [nzDisabled]="disabledAll">{{ 'dj-部门主管' | translate }}
        <i
          adIcon
          iconfont="iconshuomingwenzi"
          aria-hidden="true"
          class="question-icon"
          nzTooltipTrigger="hover"
          nz-tooltip
          [nzTooltipTitle]="
            'dj-选择主管来源时，若获取到多个基准成员，系统将默认选择首个成员进行直属主管查询。' | translate
          "
        >
        </i>
      </label>
      <label nz-radio nzValue="decision" *ngIf="'approve' === personType" [nzDisabled]="disabledAll">{{ 'dj-核决权限' | translate }}
        <i
          adIcon
          iconfont="iconshuomingwenzi"
          aria-hidden="true"
          class="question-icon"
          nzTooltipTrigger="hover"
          nz-tooltip
          [nzTooltipTitle]="'dj-该节点为多层级模式，将根据配置的「层级来源」进行逐级向上签核' | translate"
        >
        </i>
      </label>
    </nz-radio-group>
    <nz-form-item
      class="nz-form-item-content"
      *ngIf="['decision'].includes(dataFormGroup.get('type').value)"
      style="margin-bottom: 0px"
    >
      <nz-form-control [nzAutoTips]="errorTips">
        <!--层级来源-->
        <div
          class="form-item"
          style="padding: 8px; background-color: #f7f8fe; margin-bottom: 0px"
          formGroupName="decisionConfig"
        >
          <div class="item-title">
            {{ 'dj-层级来源' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-指定多级审批的层级来源，将会依据所选的层级进行审批动作' | translate"
            >
            </i>
          </div>
          <ad-select [nzPlaceHolder]="'dj-请选择' | translate" style="width: 100%" formControlName="levelType">
            <ad-option [nzLabel]="'dj-人员层级' | translate" [nzValue]="'B'"></ad-option>
          </ad-select>
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item
      class="nz-form-item-content"
      style="margin-bottom: 0px"
      *ngIf="'default' !== dataFormGroup.get('type').value"
    >
      <nz-form-control [nzAutoTips]="errorTips">
        <!--人员类型和人员的选择-->
        <div class="form-item" style="padding: 8px; background-color: #f7f8fe; margin-bottom: 0px">
          <div class="item-title" *ngIf="dataFormGroup.get('type').value === 'personnel'">
            {{ 'dj-人员来源' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-人员变量设置提示' | translate"
            >
            </i>
          </div>
          <div class="item-title" *ngIf="dataFormGroup.get('type').value === 'duty'">
            {{ 'dj-职能来源' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-人员变量设置提示' | translate"
            >
            </i>
          </div>
          <div class="item-title" *ngIf="['superior', 'director'].includes(dataFormGroup.get('type').value)">
            {{ 'dj-主管来源' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-人员变量设置提示' | translate"
            >
            </i>
          </div>
          <div class="item-title" *ngIf="['decision'].includes(dataFormGroup.get('type').value)">
            {{ 'dj-审批起点' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="
                'dj-指定多级审批的初始层级来源，若获取到多个用户，将以首个用户作为基准进行查询' | translate
              "
            >
            </i>
          </div>

          <!--人员的类型选择+人员的选择-->
          <div style="display: flex; align-items: center">
            <div class="person-type-sel" [class.disabled]="disabledAll">
              <i
                adIcon
                [iconfont]="currentPersonType"
                aria-hidden="true"
                class="question-icon"
                (click)="handleChangePersonType()"
                nz-tooltip
                [nzTooltipTitle]="getSourceTip()"
              ></i>
              <span adIcon type="down" theme="outline" (click)="handleChangePersonType()"></span>
              <div
                class="person-type-options"
                *ngIf="isShowPersonTypeOptions"
                (mouseleave)="handleClosePersonTypeOptions()"
              >
                <div nzCustomContent (click)="handleSelectPersonType('iconrenyuan1')">
                  <i adIcon iconfont="iconrenyuan1" aria-hidden="true" class="question-icon"></i>
                  {{ 'dj-人员' | translate }}
                </div>
                <div *ngIf="!disabledVariable" nzCustomContent (click)="handleSelectPersonType('iconbianliang')">
                  <i adIcon iconfont="iconbianliang" aria-hidden="true" class="question-icon"></i>
                  {{ 'dj-变量' | translate }}
                </div>
                <div
                  nzCustomContent
                  *ngIf="
                    ['director'].includes(dataFormGroup.get('type').value) &&
                    ['approve', 'execution']?.includes(personType) &&
                    !disabledVariable
                  "
                  (click)="handleSelectPersonType('iconzuzhijigou')"
                >
                  <i adIcon iconfont="iconzuzhijigou" aria-hidden="true" class="question-icon"></i>
                  {{ 'dj-来自部门' | translate }}
                </div>
                <!-- <div nzCustomContent class="custom-content" (click)="handleSelectPersonType('iconguanka')">
                  <i adIcon iconfont="iconguanka" aria-hidden="true" class="question-icon"></i>
                  {{ 'dj-来自关卡' | translate }}
                </div>
                <div nzCustomContent (click)="handleSelectPersonType('iconlaizibiaodan')">
                  <i adIcon iconfont="iconlaizibiaodan" aria-hidden="true" class="question-icon"></i>
                  {{ 'dj-来自模型' | translate }}
                </div> -->
                <!-- <div
                  nzCustomContent
                  *ngIf="['director'].includes(dataFormGroup.get('type').value)"
                  (click)="handleSelectPersonType('iconzuzhijigou')"
                >
                  <i adIcon iconfont="iconzuzhijigou" aria-hidden="true" class="question-icon"></i>
                  {{ 'dj-来自部门' | translate }}
                </div> -->
              </div>
            </div>
            <div class="person-sel" [ngClass]="{ 'people-input-require': !variable.variableCode }">
              <!--指定人员 多选-->
              <ng-container *ngIf="'personnel' === dataFormGroup.get('source').value">
                <ad-select
                  style="width: 150px; min-width: 100px; flex: auto"
                  [ngClass]="{ 'people-sel-require': peopleSelRequireClass }"
                  [nzShowSearch]="true"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  [(ngModel)]="personnel"
                  (ngModelChange)="handleSelChange()"
                  [ngModelOptions]="{ standalone: true }"
                  [nzDisabled]="disabledAll"
                  [nzMode]="
                    'personnel' === dataFormGroup.get('type').value
                      ? personSingle
                        ? 'default'
                        : 'multiple'
                      : 'default'
                  "
                >
                  <!--type==duty 的没有userId属性-->
                  <ad-option
                    *ngFor="let personnel of personnelList"
                    [nzLabel]="personnel.userId ? personnel.name + '(' + personnel.userId + ')' : personnel.name"
                    [nzValue]="personnel.id"
                  ></ad-option>
                </ad-select>
              </ng-container>
              <ng-container *ngIf="'variable' === dataFormGroup.get('source').value">
                <app-variable-select-input
                  style="width: 100%"
                  [inputable]="true"
                  [variableRange]="variableRange"
                  [nodeId]="nodeId"
                  [item]="item"
                  [sufixKey]="'variableSuffix'"
                  [maxWidth]="140"
                  [readonly]="disabledAll"
                  (onChanged)="handleValueChanged($event)"
                >
                </app-variable-select-input>
                <!-- <nz-input-group
                  [style]="{
                    width: ['superior', 'director'].includes(executor.type) ? '152px' : '229px'
                  }"
                  [prefix]="prefixTpl"
                  [nzSuffix]="modalTpl"
                >
                  <input
                    [style]="{
                      width: ['superior', 'director'].includes(executor.type) ? '152px' : '229px'
                    }"
                    [(ngModel)]="variableSuffix"
                    (ngModelChange)="handleSelChange()"
                    [ngModelOptions]="{ standalone: true }"
                    nz-input
                    type="text"
                    [placeholder]="!variableName ? ('dj-请选择' | translate) : ''"
                    nz-tooltip
                    nzTooltipTrigger="hover"
                    nzTooltipPlacement="topLeft"
                    [nzTooltipTitle]="getVariableTooltip()"
                    [readonly]="!variableName"
                  />
                </nz-input-group>
                <ng-template #prefixTpl>
                  <ng-container *ngIf="variableName">
                    <span
                      nz-tooltip
                      nzTooltipTrigger="hover"
                      nzTooltipPlacement="topLeft"
                      [nzTooltipTitle]="getVariableTooltip()"
                      [style]="{
                        maxWidth: '140px',
                        'white-space': 'nowrap',
                        overflow: 'hidden',
                        'text-overflow': 'ellipsis'
                      }"
                    >
                      {{ variableName }}
                    </span>
                  </ng-container>
                </ng-template>
                <ng-template #modalTpl>
                  <i class="icon" adIcon [iconfont]="'icon-kaichuang'" (click)="showVariableModal = true"></i>
                </ng-template> -->
              </ng-container>
              <ng-container *ngIf="'activity' === dataFormGroup.get('source').value">
                <ad-select
                  style="width: 150px; min-width: 100px; flex: auto"
                  [ngClass]="{ 'people-sel-require': peopleSelRequireClass }"
                  [nzShowSearch]="true"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  [(ngModel)]="activity"
                  (ngModelChange)="handleSelChange()"
                  [ngModelOptions]="{ standalone: true }"
                  [nzMode]="'personnel' === dataFormGroup.get('type').value ? 'multiple' : 'default'"
                >
                  <ad-option
                    *ngFor="let preManualNode of preManualNodes"
                    [nzLabel]="preManualNode.nodeName"
                    [nzValue]="preManualNode.id"
                  ></ad-option>
                </ad-select>
              </ng-container>
              <ng-container *ngIf="'formFiled' === dataFormGroup.get('source').value">
                <ad-select
                  style="width: 150px; min-width: 100px; flex: auto"
                  [ngClass]="{ 'people-sel-require': peopleSelRequireClass }"
                  [nzShowSearch]="true"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  [(ngModel)]="formFiled"
                  (ngModelChange)="handleSelChange()"
                  [ngModelOptions]="{ standalone: true }"
                  [nzMode]="'personnel' === dataFormGroup.get('type').value ? 'multiple' : 'default'"
                >
                  <ad-option
                    *ngFor="let filed of filedList"
                    [nzLabel]="filed.lang.description?.[('dj-LANG' | translate)]"
                    [nzValue]="filed.data_name"
                  ></ad-option>
                </ad-select>
              </ng-container>
              <ng-container *ngIf="'department' === dataFormGroup.get('source').value">
                <ad-select
                  style="width: 150px; min-width: 100px; flex: auto"
                  [ngClass]="{ 'people-sel-require': peopleSelRequireClass }"
                  [nzShowSearch]="true"
                  [nzPlaceHolder]="'dj-请选择' | translate"
                  [(ngModel)]="department"
                  (ngModelChange)="handleSelChange()"
                  [ngModelOptions]="{ standalone: true }"
                >
                  <ad-option *ngFor="let dep of deptList" [nzLabel]="dep.name" [nzValue]="dep.id"></ad-option>
                </ad-select>
              </ng-container>
            </div>
            <span
              *ngIf="['superior'].includes(dataFormGroup.get('type').value)"
              style="flex: none; font-size: 14px; margin-left: 5px"
            >
              {{ 'dj-的直属主管' | translate }}
            </span>
            <span
              *ngIf="['director'].includes(dataFormGroup.get('type').value)"
              style="flex: none; font-size: 14px; margin-left: 5px"
            >
              {{ 'dj-的部门主管' | translate }}
            </span>
          </div>
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item class="nz-form-item-content" *ngIf="['decision'].includes(dataFormGroup.get('type').value)">
      <nz-form-control [nzAutoTips]="errorTips">
        <!--审批终点 -->
        <div
          class="form-item"
          style="padding: 8px; background-color: #f7f8fe; margin-bottom: 0px"
          formGroupName="decisionConfig"
        >
          <div class="item-title">
            {{ 'dj-审批终点' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-指定核决终点，若无数据请前往云控制台进行配置' | translate"
            >
            </i>
          </div>
          <ad-select
            [nzPlaceHolder]="'dj-请选择' | translate"
            style="width: 100%"
            formControlName="level"
            [nzDisabled]="!dataFormGroup.get('decisionConfig').get('levelType').value || disabledAll"
          >
            <ad-option
              *ngFor="let level of levelList"
              [nzLabel]="level.levelName"
              [nzValue]="level.levelId"
            ></ad-option>
          </ad-select>
        </div>
      </nz-form-control>
    </nz-form-item>
  </div>
</div>

<app-select-variable-modal
  *ngIf="showVariableModal"
  showModal="showVariableModal"
  [nodeId]="nodeId"
  [initVariable]="variable"
  (close)="showVariableModal = false"
  (changeVariable)="handleChangeVariable($event)"
></app-select-variable-modal>
