import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NameValidators } from '../../../config/utils';
import { omit, debounce } from 'lodash';
import { ViewStoreService } from '../../../service/store.service';
import { MsgFieldSetService } from './components/msg-field-set/msg-field-set.service';
import { ViewApiService } from '../../../service/api.service';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { AppService } from 'pages/apps/app.service';
import { ViewGraphService } from '../../../service/graph.service';

@Component({
  selector: 'app-auto-msgsend-property',
  templateUrl: './auto-msgsend-property.component.html',
  styleUrls: ['./auto-msgsend-property.component.less'],
  providers: [MsgFieldSetService],
})
export class AutoMsgsendPropertyComponent implements OnInit, OnChanges, OnDestroy {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  nameLang: any;
  formGroupValidityFlag = false;

  fieldInfosData: any[] = [];

  // 场景数据
  sceneList: any[] = [];
  // 事件的数据格式
  explainBody: any[] = [];

  // 正在加载场景
  sceneLoading: boolean = false;
  // 正在获取场景绑定的事件的数据格式
  eventLoading: boolean = false;

  public drawerVisible = false;
  public params: any = {};

  private destory$ = new Subject();
  private fieldInfosPassed: boolean = true;

  get disabledAll() {
    return !!this.viewGraphService?.fromManual;
  }

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    public viewStoreService: ViewStoreService,
    private viewApiService: ViewApiService,
    private msgFieldService: MsgFieldSetService,
    private appService: AppService,
    private viewGraphService: ViewGraphService,
  ) {}

  ngOnInit(): void {
    this.handleInit();
    this.getCurrentData();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
    this.loadSceneList();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  ngOnDestroy(): void {
    this.destory$.next();
    this.destory$.complete();
  }

  handleSceneChange(sceneSid: string): void {
    this.fieldInfosData = [];
    this.msgFieldService.sceneChanged$.next();
    if (sceneSid) {
      const scene = this.sceneList.find((e) => e.sid === sceneSid);
      this.dataFormGroup.patchValue({ sceneId: scene.id });
    }
    this.queryEventBodyExplain(sceneSid);
  }

  handleChangeValue() {
    // 为了patchValue不触发valueChanges
    this.formGroupValidityFlag = true;
    this.fieldInfosData = this.data?.fieldInfos || [];
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      sceneId: this.data?.sceneId,
      sceneSid: this.data?.sceneSid, // -> sceneId: id , sceneSid: sid(唯一)
    });
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  /**
   * field-set 子组建back
   * @param param0
   */
  fieldInfosDataBack({ currentData, isVerificationPassed }) {
    this.fieldInfosData = currentData;
    this.fieldInfosPassed = isVerificationPassed;
    this.getCurrentData();
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]],
      name: [this.data?.name || '', [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      sceneSid: [{ value: this.data?.sceneSid, disabled: this.disabledAll }, [Validators.required]],
      sceneId: [this.data?.sceneId],
    });
    this.nameLang = this.data.lang;
    this.fieldInfosData = this.data?.fieldInfos || [];
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  // 获取当前最新的数据
  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(
        this.dataFormGroup.valid && this.fieldInfosPassed,
      );
      const returnData = {
        data: Object.assign(this.data, {
          sceneSid: currentData.sceneSid,
          sceneId: currentData.sceneId,
          name: currentData.name,
          lang: this.nameLang,
          fieldInfos: this.fieldInfosData,
        }),
        isVerificationPassed,
      };
      this.changeData.emit(returnData);
      return returnData;
    },
    150,
    { leading: false, trailing: true },
  );

  private async loadSceneList() {
    try {
      this.sceneLoading = true;
      const result = await this.viewApiService.queryAllOpenAimScenes().toPromise();
      const { data = [] } = result;
      this.sceneList = data;
      if (this.data.sceneSid) {
        if (!this.sceneList.some((e) => e.sid === this.data.sceneSid)) {
          this.dataFormGroup.patchValue({ sceneSid: undefined, sceneId: undefined });
        }
        this.queryEventBodyExplain(this.data.sceneSid);
      }
    } finally {
      this.sceneLoading = false;
    }
  }

  private async queryEventBodyExplain(sceneSid?: string) {
    if (!sceneSid) {
      this.explainBody = [];
      return;
    }
    try {
      this.eventLoading = true;
      const result = await this.viewApiService.queryEventBodyExplain(sceneSid).toPromise();
      const { data = [] } = result;
      this.explainBody = this.transformBodyExplain(data);
    } finally {
      this.eventLoading = false;
    }
  }

  private transformBodyExplain(data: any[]) {
    return data.map((item) => {
      if (item.field?.length) item.field = this.transformBodyExplain(item.field);
      return {
        fieldId: item.data_name,
        fieldName: item.description,
        fieldType: item.data_type,
        fullPath: item.fullPath,
        lang: item.lang,
        notNull: false,
      };
    });
  }

  handlePreview() {
    const sceneSid = this.dataFormGroup.get('sceneSid').value;
    this.params = {
      appCode: this.appService.selectedApp?.code,
      id: sceneSid,
      eventLevel: 2,
    };
    this.drawerVisible = true;
  }

  handleCloseDrawer() {
    this.params = {};
    this.drawerVisible = false;
  }
}
