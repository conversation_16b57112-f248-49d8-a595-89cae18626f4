// .field-form-content {
.field-form {
  display: flex;
  padding: 12px 12px 4px 12px;
  width: 100%;
  background-color: #f8fafd;
  margin-bottom: 14px;
  // ::ng-deep .ant-form-item-control-input {
  //   width: 140px !important;
  // }
  // ::ng-deep input {
  //   height: 32px;
  // }
  ::ng-deep nz-form-item {
    margin-bottom: 10px;
  }
  .line {
    text-align: left;
  }
}
.field-paramType.disabled {
  background-color: #f9f9f9;
  color: #ccc;
}
.field-form-value {
  flex-direction: column;
  position: relative;
}
.item-title {
  color: #333;
  font-size: 13px;
  margin-bottom: 8px;
  display: inline-block;

  .item-required {
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
  }
}
.add-item-row {
  color: #6868ae;
  padding-top: 8px;
  cursor: pointer;
  margin-bottom: 6px;
  .add-tanent-control {
    padding-top: 8px;
  }
}
.field-line {
  display: flex;
  align-items: center;
  position: relative;
}
.field-close {
  position: absolute;
  right: -5px;
  top: -5px;
  background-color: #6868ae;
  cursor: pointer;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  &::before {
    content: '';
    width: 60%;
    height: 1px;
    background-color: #fff;
    z-index: 1;
  }
}

.iconfont-disabled {
  background-color: #999 !important;
  cursor: not-allowed !important;
}
.field-add {
  width: 100%;
  border-width: 1px !important;
  &:hover {
    background: #ffffff;
  }
}
.field-form-item {
  ::ng-deep .ant-input-group-addon {
    padding: 0 6px;
  }
}
.field-setting {
  margin-bottom: 0;
  .form-item {
    margin-bottom: 0;
  }
}

.field-menu-item {
  padding: 4px 8px;
  margin: 0 4px;
  line-height: 20px;
  font-size: 13px;
  margin-bottom: 2px;
  .field-name {
    margin-left: 4px;
  }
}
.field-menu-item-active {
  background: #eef0ff;
  border: 1px solid #eef0ff;

  border-radius: 4px;
  color: #6a4cff;

  .field-name {
    margin-left: 4px;
  }
}

// }
.field-add-error {
  border: 1px solid #ff4d4f !important;
  color: #ff4d4f !important;
}
.field-add-error-tip {
  color: #ff4d4f;
}

::ng-deep .field-select-dropdown {
  .ant-select-item-empty {
    padding: 0;
    .ath-empty {
      margin: 0;
      .adp-select-empty-option-img {
        scale: 0.6;
      }
    }
  }
}
