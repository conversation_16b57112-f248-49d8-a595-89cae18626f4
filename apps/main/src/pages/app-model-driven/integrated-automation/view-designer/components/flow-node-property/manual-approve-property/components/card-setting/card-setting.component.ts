import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ManualApprovePropertyService } from '../../manual-approve-property.service';
import { NameValidators } from '../../../../../config/utils';
import { BindFormType, VariableType } from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { cloneDeep, find, omit } from 'lodash';
import { ViewStoreService } from 'pages/app-model-driven/integrated-automation/view-designer/service/store.service';
import { AppService } from 'pages/apps/app.service';
import { EActivitiyType, ITemplateParams } from 'components/bussiness-components/preview-modal/service/types';
import { ActivatedRoute } from '@angular/router';
import { ExtendedInfoComponent } from 'components/bussiness-components/extended-info/extended-info.component';
import { EVariableRange } from '../../../variable-select-input/components/variable-modal/variable-modal.component';
import dayjs from 'dayjs';
import { dataTypeToVariableType, variableTypeToDataType } from 'pages/app-model-driven/utils/utils';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';
import { ViewGraphService } from 'pages/app-model-driven/integrated-automation/view-designer/service/graph.service';

@Component({
  selector: 'app-approve-card-setting',
  templateUrl: './card-setting.component.html',
  styleUrls: ['./card-setting.component.less'],
})
export class ApproveCardSettingComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 表单数据改变通过校验后向父组件通知更新数据
  @Input() uiKey: string;
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  @Output() clearUiKey: EventEmitter<any> = new EventEmitter();
  @Output() copyData: EventEmitter<any> = new EventEmitter();

  dataFormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  // 多语言单独处理，name title
  lang: any;

  bindForm = {
    formCode: '', // 作业code
    modelCode: '', // 模型code
    serviceCode: '', // 服务code
    type: '', // 类型
  };
  // 当前选择的是否是自定义任务卡
  get isCustomizeCard() {
    return this.bindForm?.formCode && this.bindForm?.type === BindFormType.PageView;
  }

  // 是否正在请求任务界面
  loadingBasic: boolean = false;
  // 当前解决方案下的所有作业，供任务界面下拉使用 界面下拉列表=作业=数据录入
  formList = [];
  // 是否显示任务设计器界面
  workVisible: boolean = false;
  // 任务设计器界面数据
  workData: any;
  designType: any;
  // 存储自定义任务卡task数据
  task = {};
  // 存储自定义任务，只是已创建的任务，具体选择的还是存在bindForm中
  pageView = {
    code: '', // 与节点id一致
    name: '', // 名称
    lang: {
      // 多语言
      name: {
        zh_CN: '',
        zh_TW: '',
        en_US: '',
      },
    },
  };

  // 业务流程ID
  businessObjectId: string;

  previewVisible: boolean = false;
  params: ITemplateParams = {
    actionId: '',
    domain: 'DataEntry',
    pageCode: 'edit-page',
    taskCode: '',
    type: 'performer',
    activityType: EActivitiyType.BASE_DATA,
    category: '',
  };

  fieldType = {
    constant: {
      icon: 'iconchangliang1',
      name: this.translate.instant('dj-常量'),
    },
    variable: {
      icon: 'iconbianliang',
      name: this.translate.instant('dj-变量'),
    },
  };

  fieldTypeEum = ['constant', 'variable'];

  planEndTime: any = {};
  sourceData: any = {};

  isTenantProcessId: boolean = false;

  buttonNodes: any[] = [];

  get variableRange() {
    const mode =
      EVariableRange.BUSINESS |
      EVariableRange.CUSTOM |
      EVariableRange.MODEL |
      EVariableRange.NODE |
      EVariableRange.SYSTEM;
    if (this.viewStoreService.isFusionMode()) {
      return mode | EVariableRange.DTD;
    }
    return mode;
  }

  get item() {
    const { variableCode, variablePath, variableSuffix, variableType } = this.sourceData;
    if (!variableCode) return undefined;
    const type = dataTypeToVariableType(variableType);
    const isModel = [VariableType.BUSINESS_OBJECT, VariableType.MODEL].includes(type);
    return {
      variableSuffix: variableSuffix,
      valueType: type,
      valuePath: isModel ? variableCode : variablePath, // 模型/业务对象
      valueCode: isModel ? variablePath : variableCode, // 变量
    };
  }

  // 添加自定义任务按钮是否禁用，已添加自定义任务则不允许再添加
  get isCustomSelectDisabled() {
    return !!this.pageView?.code;
  }

  get state() {
    return this.viewStoreService.state;
  }

  inputData;

  get disabledAll() {
    return !!this.viewGraphService?.fromManual;
  }

  @ViewChild('extendedInfoRef') extendedInfoRef: ExtendedInfoComponent;

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    private manualApprovePropertyService: ManualApprovePropertyService,
    public viewStoreService: ViewStoreService,
    public appService: AppService,
    public route: ActivatedRoute,
    public service: ViewApiService,
    public viewGraphService: ViewGraphService,
  ) {
    this.route.paramMap.subscribe((params) => {
      this.businessObjectId = params.get('businessObjectId');
      this.isTenantProcessId = !!params.get('objectId');
    });
  }

  ngOnInit() {
    this.loadBasicByAppCode();
    this.dataFormGroup = this.fb.group({
      id: [this.data?.id || null, [Validators.required]], // 节点id
      name: [this.data?.name, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]], //节点名称
      taskName: [this.data?.taskName, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]], // 展示标题
      formCode: [this.data?.bindForm?.formCode], //任务界面
      milestone: [{ value: this.data?.milestone, disabled: this.disabledAll }], //设为里程碑节点
      urging: [{ value: this.data?.urging, disabled: this.disabledAll }], //任务催办
      isHideKey: [{ value: this.data?.isHideKey, disabled: this.disabledAll }], //设为里程碑节点
      transferButton: [
        {
          value: this.data?._buttons?.some((button) => button.id === 'workflow-act-reassignment'),
          disabled: this.disabledAll,
        },
      ], // 转派
      openTaskMerge: [{ value: this.data?.openTaskMerge ?? false, disabled: this.disabledAll }], // 开启任务卡合并
      openGroupSubmit: [{ value: this.data?.openGroupSubmit ?? false, disabled: this.disabledAll }], // 开启分批提交
      enableCustomDigest: [{ value: this.data?.enableCustomDigest ?? false, disabled: this.disabledAll }], // 开启自定义摘要
      planEndTime: this.fb.group({
        settingType: [this.data?.planEndTime?.settingType ?? 'no', [Validators.required]], // 节点设置类型
        sourceType: [this.data?.planEndTime?.sourceType ?? 'constant', [Validators.required]], // 节点设置类型
        sourceValue: [this.data?.planEndTime?.sourceValue, [Validators.required]], // 节点设置类型
        dateType: [this.data?.planEndTime?.dateType, [Validators.required]], // 节点设置类型
        dateValue: [this.data?.planEndTime?.dateValue, [Validators.required]], // 节点设置类型
        variableCode: [this.data?.planEndTime?.variableCode || '', [Validators.required]], // 节点设置类型
      }),
      // extendFields: this.fb.group({
      //   closeNeedConfirm: this.data?.extendFields?.closeNeedConfirm ?? false,
      // }),
    });
    this.handleInit();
    // 对父组件发出change事件主要里面混入isVerificationPassed
    this.dataFormGroup.get('formCode').valueChanges.subscribe((value) => {
      if (!this.formGroupValidityFlag) {
        const findForm = this.getFormItem(value);
        if (findForm) {
          this.bindForm = {
            formCode: value,
            modelCode: findForm?.simpleModelCode,
            serviceCode: findForm?.navigateModelServiceCode,
            type: findForm?.type,
          };
          // 把hasInitFlag缓存以nodeId+formCode现在切换的开头的缓存数据都清除,字段设置那边使用，这里是保证能获取第一次的差异对比逻辑的数据
          const key = this.data.id + this.bindForm.formCode;
          this.viewStoreService.setState((state) => {
            delete state.hasInitFlag[key];
          });
        } else {
          // 清空
          this.bindForm = {
            formCode: '',
            modelCode: '',
            serviceCode: '',
            type: '',
          };
        }
        this.manualApprovePropertyService.bindFormChange$.next(this.bindForm);
      }
    });
    this.dataFormGroup
      .get('planEndTime')
      ?.get('settingType')
      ?.valueChanges.subscribe((value) => {
        if (!this.formGroupValidityFlag) {
          this.setPlanEndTime(value);
        }
      });
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
    console.log('asd mmmm: ', this.viewGraphService);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      // id不相同认为是不同的节点需要重新构建formgroup，后期可以使用patchValue,而非重新初始化formgroup
      if (!changes.nodeId.firstChange) {
        // 选中人工签核/关卡面板并直接新增另一个同类型节点时，清理一下数据
        if (changes.nodeId.previousValue !== changes.nodeId.currentValue) {
          this.pageView = {
            code: '', // 与节点id一致
            name: '', // 名称
            lang: {
              // 多语言
              name: {
                zh_CN: '',
                zh_TW: '',
                en_US: '',
              },
            },
          };
          this.task = {};
          this.handleQueryManualNodes();
        }
        // 为了patchValue不触发valueChanges
        this.formGroupValidityFlag = true;
        this.dataFormGroup.patchValue({
          id: this.data?.id,
          name: this.data?.name,
          taskName: this.data?.taskName,
          formCode: this.data?.bindForm?.formCode,
          milestone: this.data?.milestone,
          urging: this.data?.urging,
          isHideKey: this.data?.isHideKey,
          transferButton: this.data?._buttons?.some((button) => button.id === 'workflow-act-reassignment'),
          strategyConfig: {
            multiplayerStrategy: this.data?.strategyConfig.multiplayerStrategy,
          },
          openTaskMerge: this.data?.openTaskMerge ?? false,
          openGroupSubmit: this.data?.openGroupSubmit ?? false,
          enableCustomDigest: this.data?.enableCustomDigest ?? false,
          planEndTime: {
            settingType: this.data?.planEndTime?.settingType ?? 'no', // 节点设置类型
            sourceType: this.data?.planEndTime?.sourceType ?? 'constant', // 节点设置类型
            sourceValue: this.data?.planEndTime?.sourceValue, // 节点设置类型
            dateType: this.data?.planEndTime?.dateType, // 节点设置类型
            dateValue: this.data?.planEndTime?.dateValue, // 节点设置类型
            variableCode: this.data?.planEndTime?.variableCode || '', // 节点设置类型
          },
          extendFields: this.data?.extendFields || {},
        });

        this.formGroupValidityFlag = false;
        this.handleInit();
      }
    }
    if (Object.keys(changes).includes('uiKey')) {
      if (this.uiKey) {
        this.handleEdit('detail');
      }
    }
  }

  // 根据输入的组装fromgroup
  handleInit(data?: any): void {
    const cData = data ?? this.data;
    this.lang = cloneDeep(cData?.lang);
    // bindForm的处理
    this.bindForm = cloneDeep(cData?.bindForm);
    if (cData?.pageView?.code) {
      this.pageView = cloneDeep(cData.pageView);
    }
    if (cData?.task) {
      this.task = cloneDeep(cData.task);
    }
    if (cData?.planEndTime) {
      this.planEndTime = cloneDeep(cData.planEndTime);
    }
    this.inputData = cloneDeep(cData?.config?.inputData);
    this.setPlanEndTime(this.planEndTime?.settingType);
  }

  async handleCopyData(data: any, oldKey: string) {
    this.formGroupValidityFlag = true;
    let newTaskCode = null;
    if (!!data?.pageView?.code) {
      const res = await this.service.queryCopyManualTaskData(data.pageView?.code).toPromise();
      if (res?.code === 0) {
        newTaskCode = res?.data;
      }
    }
    await this.service.queryCopyExtendInfo({ oldKey, newKey: data?.id }).toPromise();
    const pageView = {
      ...(data?.pageView ?? {}),
      code: newTaskCode,
    };
    const task = {
      ...(data?.task ?? {}),
      code: newTaskCode,
    };
    this.pageView = pageView;
    this.task = task;
    const newFormCode = data?.bindForm?.formCode === data?.pageView?.code ? newTaskCode : data?.bindForm?.formCode;
    this.dataFormGroup.patchValue({
      id: data?.id,
      name: data?.name,
      taskName: data?.taskName,
      formCode: newFormCode,
      milestone: data?.milestone,
      urging: data?.urging,
      isHideKey: data?.isHideKey,
      transferButton: data?._buttons?.some((button) => button.id === 'workflow-act-reassignment'),
      strategyConfig: {
        multiplayerStrategy: data?.strategyConfig.multiplayerStrategy,
      },
      openTaskMerge: data?.openTaskMerge ?? false,
      openGroupSubmit: data?.openGroupSubmit ?? false,
      enableCustomDigest: data?.enableCustomDigest ?? false,
      planEndTime: {
        settingType: data?.planEndTime?.settingType ?? 'no', // 节点设置类型
        sourceType: data?.planEndTime?.sourceType ?? 'constant', // 节点设置类型
        sourceValue: data?.planEndTime?.sourceValue, // 节点设置类型
        dateType: data?.planEndTime?.dateType, // 节点设置类型
        dateValue: data?.planEndTime?.dateValue, // 节点设置类型
        variableCode: data?.planEndTime?.variableCode || '', // 节点设置类型
      },
      extendFields: data?.extendFields || {},
    });
    this.formGroupValidityFlag = false;
    this.handleInit({
      ...data,
      pageView,
      task,
      bindForm: {
        ...(data?.bindForm ?? {}),
        formCode: newFormCode,
      },
    });
    this.getCurrentData();
  }

  ngAfterViewInit() {
    // 由170713发现的其他问题: 人员来源选择变量时选择了模型变量，无法回显数据。因为此时this.formList还没有数据。
    // loadBasicByAppCode方法中已调用该方法，这里暂时注释掉。
    // this.getCurrentData();
    this.handleQueryManualNodes();
  }

  handleQueryManualNodes() {
    const manualNodes = this.viewGraphService.getAllManualNodes(this.data?._nodeType, this.data?._nodeId);
    const { propertiesObj } = this.viewStoreService.state;
    const nodes =
      manualNodes?.map((node) => {
        return {
          ...(propertiesObj[node.nodeId] ?? {}),
        };
      }) ?? [];
    this.buttonNodes = nodes;
  }

  handleSelectCopyBtn(data: any) {
    // const { executor, ...rest } = data ?? {};
    const copyData = {
      data: {
        // ...rest,
        ...(data ?? {}),
        id: this.nodeId,
        _nodeId: this.data._nodeId,
        // executor: {
        //   type: 'personnel',
        //   source: 'personnel',
        //   personnel: [],
        // },
      },
      valid: false,
      componentType: 'approveCardSetting',
      oldKey: data?.id,
    };
    this.viewStoreService.setState((state) => {
      // 设置数据映射数据
      state.propertiesObj[this.data._nodeId].variablesMapping = data?.variablesMapping;
      state.propertiesObj[this.data._nodeId].actions = data?.actions;
      state.propertiesObj[this.data._nodeId].delayConfig = data?.delayConfig;
    });
    this.copyData.emit(copyData);
  }

  setPlanEndTime(settingType) {
    const { dateValue, dateType, sourceType } = this.planEndTime || {};
    if (!settingType || settingType === 'no') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.removeControl('dateValue');
      planEndTime.removeControl('dateType');
      planEndTime.removeControl('sourceType');
      planEndTime.removeControl('sourceValue');
      planEndTime.removeControl('variableCode');
    }
    if (settingType === 'calculation') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.addControl('dateValue', new FormControl(dateValue ?? '', [Validators.required]));
      planEndTime.addControl('dateType', new FormControl(dateType ?? '', [Validators.required]));
      planEndTime.removeControl('sourceType');
      planEndTime.removeControl('sourceValue');
      planEndTime.removeControl('variableCode');
    }
    if (settingType === 'specify') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      this.setSourceType(sourceType, this.planEndTime);
      planEndTime.removeControl('dateValue');
      planEndTime.removeControl('dateType');
    }
  }

  setSourceType(sourceType = 'constant', planEndTime) {
    const { sourceValue, variableCode, variablePath, variableSuffix, variableType } = planEndTime || {};
    if (sourceType === 'constant') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.addControl('sourceType', new FormControl('constant', [Validators.required]));
      planEndTime.addControl('sourceValue', new FormControl(sourceValue ?? '', [Validators.required]));
      this.sourceData = {};
      planEndTime.removeControl('variableCode');
    }
    if (sourceType === 'variable') {
      const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
      planEndTime.addControl('sourceType', new FormControl('variable', [Validators.required]));
      planEndTime.addControl('variableCode', new FormControl(variableCode || '', [Validators.required]));
      this.sourceData = {
        variableCode: variableCode ?? '',
        variablePath: variablePath ?? '',
        variableSuffix: variableSuffix ?? '',
        variableType: variableType ?? '',
      };
      planEndTime.removeControl('sourceValue');
    }
  }

  /**
   * 修改字段类型
   * @param type
   * @param i
   */
  changeParamType(type) {
    const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
    if (planEndTime.get('sourceType').value === type) return;
    planEndTime.patchValue({
      sourceType: type,
    });

    this.setSourceType(type, this.data.planEndTime);
  }

  handleValueChanged(event: any): void {
    const { valueType, variableDesc, variableCode, valueCode, variableSuffix } = event;
    const type = variableTypeToDataType(valueType);
    const isModel = ['businessObjectVariable', 'modelVariable'].includes(type);
    const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
    planEndTime.patchValue({
      variableCode: valueCode || '',
    });
    this.sourceData = {
      variableCode: valueCode,
      variablePath: isModel ? variableCode : '',
      variableSuffix: variableSuffix,
      variableType: type,
    };
    this.getCurrentData();
  }

  // 根据code获取相应的form item
  getFormItem(code: string): any {
    if (!code) return {};
    if (code === this.pageView?.code)
      return {
        ...cloneDeep(this.pageView),
        type: BindFormType.PageView,
      };
    const findItem = find(this.formList, (form) => form.code === code);
    if (!findItem) return {};
    return {
      ...(cloneDeep(findItem) as object),
      type: BindFormType.PageDesign,
    };
  }

  /**
   * 获取任务界面的数据
   */
  private async loadBasicByAppCode(): Promise<void> {
    this.loadingBasic = true;
    try {
      const res = await this.manualApprovePropertyService.loadBasicByAppCode().toPromise();
      this.formList = res.data;
      this.getCurrentData();
    } finally {
      this.loadingBasic = false;
    }
  }

  // 多语言栏位改变的事件处理
  handlePatchLang(key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({ [key]: data?.value });
  }

  /**
   * 处理扩展信息
   */
  handleExtendedInfo(event) {
    if (this.disabledAll) return;
    this.extendedInfoRef.handleOpenExtendedInfoModal(event);
  }

  // 默认task属性名称
  get customTaskName() {
    return {
      en_US: `${this?.data.lang?.name?.['en_US'] ?? this?.data?.name}'s Task Interface`,
      zh_CN: `${this?.data.lang?.name?.['zh_CN'] ?? this?.data?.name}的任务界面`,
      zh_TW: `${this?.data.lang?.name?.['zh_TW'] ?? this?.data?.name}的任務介面`,
    };
  }

  // 自定义任务弹窗的title
  get customTaskTitle() {
    const app = this.appService?.selectedApp;
    return {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${this.customTaskName['en_US']}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${this.customTaskName['zh_CN']}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${this.customTaskName['zh_TW']}`,
    };
  }

  // 默认前端生成的task信息
  get defaultTaskInfo() {
    return {
      name: this.data?.name, // 节点名称
      code: this.data?.id, // 任务code, 目前取节点id
      type: 'business', // 任务类型
      category: 'PROCESS', // 业务类型
      executeType: 'manual', // 执行方式
      pattern: 'BUSINESS', // 业务模式
      lang: {
        // 节点名称多语言
        name: this.customTaskName,
      },
    };
  }

  // 打开自定义任务弹窗
  openAddCustomTaskModal() {
    this.workData = {
      category: 'Activity',
      name: this.customTaskName[this.translate.instant('dj-LANG')],
      code: this.data?.id,
      processId: this.data?.processId,
      nodeType: this.data?._nodeType,
      title: this.customTaskTitle,
      // businessCode: this.businessObjectId,
      data: {
        ...this.defaultTaskInfo,
        taskPattern: this.defaultTaskInfo['pattern'],
      },
      extraData: {
        businessCode: this.businessObjectId,
        comeFrom: 'diyTask',
      },
    };
    this.workVisible = true;
  }

  // 新增自定义任务
  showAddCustomTaskModal() {
    if (this.isCustomSelectDisabled) return;
    this.designType = 'taskOnlyDetail';
    this.openAddCustomTaskModal();
  }

  // 编辑自定义任务
  handleEdit(type) {
    this.designType = type === 'detail' ? 'taskOnlyDetail' : 'taskOnlyCard';
    this.openAddCustomTaskModal();
  }

  handlePreview() {
    if (this.bindForm.formCode && this.bindForm.formCode !== '') {
      // 选了作业才能预览
      this.manualApprovePropertyService.getActionIdByCode(this.bindForm.formCode).subscribe(
        (data) => {
          if (data.data) {
            const { category } = this.formList.find((item) => item.code === this.bindForm.formCode) || {};
            this.params.actionId = data.data.detailAction;
            this.params.taskCode = this.bindForm.formCode;
            this.params.category = category;
            this.previewVisible = true;
          }
        },
        (err) => {},
      );
    }
  }

  /**
   * 处理任务设计器关闭
   */
  handleWorkPageClose(success) {
    this.clearUiKey.emit();
    if (success) {
      this.pageView = {
        code: this?.data?.id,
        name: this?.data?.name,
        lang: {
          name: this.customTaskName,
        },
      };
      this.task = this.defaultTaskInfo;
      this.getCurrentData();
    }
    this.workData = null;
    this.workVisible = false;
  }

  getFormValidate() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    const planEndTime = this.dataFormGroup.get('planEndTime') as FormGroup;
    for (const i of Object.keys(planEndTime.controls)) {
      planEndTime.controls[i].markAsDirty();
      planEndTime.controls[i].updateValueAndValidity();
    }
    this.formGroupValidityFlag = false;
    return this.dataFormGroup.valid;
  }

  // 获取当前最新的数据
  getCurrentData() {
    this.getFormValidate();

    let currentData = this.dataFormGroup.getRawValue();

    // 根据表单id回填bindForm
    const formCode = currentData.formCode;
    const findFromItem = this.getFormItem(formCode);
    currentData = omit(currentData, 'formCode');
    if (findFromItem) {
      currentData.bindForm = {
        formCode,
        modelCode: findFromItem.simpleModelCode,
        serviceCode: findFromItem.navigateModelServiceCode,
        type: findFromItem.type,
      };
    } else {
      currentData.bindForm = {
        formCode: '',
        modelCode: '',
        serviceCode: '',
        type: '',
      };
    }

    // 判断 bindForm 对象中是否存在值，不存在则返回 bindForm:{}
    if (!currentData?.bindForm?.formCode && !currentData?.bindForm?.modelCode && !currentData?.bindForm?.serviceCode) {
      currentData.bindForm = {};
    }
    if (currentData.planEndTime.sourceValue) {
      currentData.planEndTime.sourceValue = dayjs(currentData.planEndTime.sourceValue).format('YYYY-MM-DD HH:mm:ss');
    }
    if (currentData.planEndTime.settingType === 'specify' && currentData.planEndTime.sourceType === 'variable') {
      currentData.planEndTime = {
        ...currentData.planEndTime,
        ...this.sourceData,
      };
    }
    if (!currentData.config) {
      currentData.config = {};
    }
    currentData.config.inputData = cloneDeep(this.inputData);

    // 如果选择的是自定义任务卡，需要添加pageView和task属性
    currentData.pageView = cloneDeep(this.pageView);
    currentData.task = cloneDeep(this.task);
    const _buttons = cloneDeep(this.data._buttons || []);
    // 增加自定义任务卡场景：当选择的是自定义任务卡&转派勾选框选中
    if (this.isCustomizeCard) {
      const index = _buttons.findIndex((button) => button.id === 'workflow-act-reassignment');
      if (currentData?.transferButton) {
        if (index === -1) {
          _buttons.push({ id: 'workflow-act-reassignment' });
        }
      } else {
        if (index > -1) {
          _buttons.splice(index, 1);
        }
      }
    }
    // if (
    //   this.isCustomizeCard &&
    //   currentData?.transferButton &&
    //   _buttons.findIndex((button) => button.id === 'workflow-act-reassignment') === -1
    // ) {
    //   _buttons.push({ id: 'workflow-act-reassignment' });
    // }

    // 多语言回填
    const returnData = {
      data: Object.assign(this.data, currentData, { _buttons, lang: this.lang, taskCode: currentData.id }),
      valid: this.dataFormGroup.valid,
      componentType: 'approveCardSetting',
    };
    this.changeData.emit(returnData);
    return returnData;
  }

  handleBusinessKeySubmit(data) {
    this.inputData = data;
    this.getCurrentData();
  }
}
