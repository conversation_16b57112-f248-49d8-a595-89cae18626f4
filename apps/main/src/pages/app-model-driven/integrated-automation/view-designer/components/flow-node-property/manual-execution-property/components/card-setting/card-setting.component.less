.card-setting-content {
  font-weight: normal;
  height: 100%;
  overflow-y: auto;
  font-family: PingFang SC;
  font-weight: normal;
  padding-bottom: 16px;
  .nz-form-item-content {
    margin-bottom: 6px;
    &.form-item-planEndTime {
      width: 100%;
    }
    .page-select {
      display: flex;
      flex: 1;
      position: relative;
      max-width: 298px;
      .select-loading {
        display: inline-flex;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background-color: rgba(255, 255, 255, 0.7);
        align-items: center;
        padding-top: 5px;
        padding-left: 20px;
      }
    }
  }
  .form-item {
    margin-bottom: 8px;

    &.switch-form-item {
      display: flex;
      align-items: center;
      position: relative;
    }

    .item-title {
      color: #333;
      font-size: 13px;
      margin-bottom: 8px;
      display: inline-block;

      .item-required {
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
      }

      &.switch-item-title {
        margin-bottom: 0;
      }
    }

    .hasError {
      ::ng-deep .ant-input-affix-wrapper {
        border: 1px solid #ea3d46;
      }
    }

    .hasErrorBorder {
      border: 1px solid #ea3d46;
    }

    .error {
      color: #ff4d4f;
      font-size: 12px;
    }
  }

  .form-item-time {
    background: #f7f8fe;
    border-radius: 4px;
    padding: 16px;
    ::ng-deep .ant-form-item-explain.ant-form-item-explain-error {
      position: absolute;
      bottom: -21px;
    }
  }
  .time-type-select {
    min-width: 70px;
    ::ng-deep .ant-select-selector {
      height: 32px;
    }
  }
  .multi-form-item {
    ::ng-deep .ant-input-number {
      height: 32px;
      border-radius: 4px 0 0 4px;
    }
  }
  .flex-form-item {
    align-items: center;
    display: flex;
    span {
      font-size: 13px;
      font-weight: 400;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .page-select {
      display: flex;
      flex: 1;
      position: relative;
      .select-loading {
        display: inline-flex;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background-color: rgba(255, 255, 255, 0.7);
        align-items: center;
        padding-top: 5px;
        padding-left: 20px;
      }
    }
  }
  .question-icon {
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    line-height: 17px;
    vertical-align: middle;
  }

  .custom-project-card-switch {
    margin-bottom: 4px;
  }
  .edit-icon {
    color: #6a4cff;
    cursor: pointer;
    position: absolute;
    right: 0;
  }
  .ml4 {
    margin-left: 4px;
  }

  .extend-setting-icon {
    color: #6a4cff;
    &.disabled {
      color: #ccc;
    }
  }
  .copy-data-btn {
    width: calc(100% -320x);
    margin: 0 16px 16px 16px;
    background: linear-gradient(96deg, #a0a0ff -7%, #5d78ff 100%);
    border: none;
    border-radius: 4px;
    color: #fff;
    &:hover {
      background: linear-gradient(96deg, #a0a0ff -7%, #5d78ff 100%);
      border: none;
      color: #fff;
    }
  }
}
::ng-deep .customSelect {
  width: 100%;
  padding-top: 8px;
  .divider {
    width: 100%;
    height: 1px;
    background-color: #eee;
  }
  .customSelectItem {
    height: 28px;
    line-height: 28px;
    text-align: center;
    color: #6a4cff;
    padding-top: 3px;
    cursor: pointer;
    font-size: 13px;
  }
}
::ng-deep .custom-select-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .custom-select-item-label {
    width: 150px;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
  }
  .custom-select-item-tag {
    width: 68px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: rgb(21, 94, 254);
    background-color: rgb(232, 243, 255);
    border-radius: 2px;
    text-align: center;
  }
}

.customSelectDisabled {
  color: rgb(193, 200, 255) !important;
  cursor: not-allowed !important;
}
::ng-deep .copy-data-btn-dropdown {
  ul {
    max-height: 280px;
    overflow-x: hidden;
    overflow-y: scroll;
  }
  .ant-dropdown-menu-item {
    font-size: 13px;
    color: #555;
    padding: 4px 8px;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
    overflow: hidden;
  }
  .copy-node-empty {
    height: 120px;
    background-color: #fafafa;
    padding-top: 24px;
    color: #555;
    font-size: 13px;
    margin: 0;
  }
}
