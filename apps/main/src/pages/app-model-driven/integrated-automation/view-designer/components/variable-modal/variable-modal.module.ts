import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DirectiveModule } from 'common/directive/directive.module';
import { VariableModalComponent } from './variable-modal.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';

import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { VariableModalService } from './variable-modal.service';
import { ViewGraphService } from '../../service/graph.service';
import { ActionModalModule } from 'components/bussiness-components/action-modal/action-modal.module';

@NgModule({
  imports: [
    CommonModule,
    ActionModalModule,
    FormsModule,
    DirectiveModule,
    ReactiveFormsModule,
    TranslateModule,
    AdModalModule,
    NzFormModule,
    AdSelectModule,

    NzInputModule,
    NzCheckboxModule,
    AdIconModule,
    NzToolTipModule,
    NzSpinModule,
  ],
  declarations: [VariableModalComponent],
  exports: [VariableModalComponent],
  providers: [VariableModalService, ViewGraphService],
})
export class VariableModalModule {}
