<nz-layout class="integrate-automation-layout-content">
  <nz-sider
    nzCollapsible
    [nzCollapsedWidth]="0"
    [ngClass]="{ 'expend-panel': !isMenuzCollapsed }"
    [nzZeroTrigger]="expendBtnTmpl"
    [(nzCollapsed)]="isMenuzCollapsed"
    class="intro-app-step-1"
    style="flex: auto"
  >
    <div class="menu-title">
      <span class="text" nz-tooltip [nzTooltipTitle]="'dj-业务自动化' | translate">{{
        'dj-业务自动化' | translate
      }}</span>
      <i
        adIcon
        iconfont="iconzujianku123"
        nz-tooltip
        [nzTooltipTitle]="'dj-返回解决方案中心' | translate"
        *ngIf="hideBar"
        (click)="handleBackApps()"
        class="apps"
        aria-hidden="true"
      >
      </i>
    </div>
    <ul nz-menu nzMode="inline">
      <!-- 渲染menus -->
      <ng-container *ngFor="let menu of menus">
        <nz-divider *ngIf="menu.path === 'param-set'"></nz-divider>
        <ng-container>
          <!-- 有children则渲染一二级菜单,否则渲染一级link-->
          <ng-container *ngIf="menu.children?.length > 0">
            <ng-container *ngTemplateOutlet="secondaryMenuTpl; context: { menu }"></ng-container>
          </ng-container>

          <!-- 如果没有children则渲染一级link -->
          <ng-container *ngIf="!menu.children">
            <ng-container *ngTemplateOutlet="menuTpl; context: { menu, isSecondary: false }"></ng-container>
          </ng-container>
        </ng-container>
      </ng-container>

      <!-- Link菜单模板 -->
      <ng-template #menuTpl let-menu="menu" let-isSecondary="isSecondary">
        <ng-container>
          <li
            nz-menu-item
            routerLinkActive
            [nzDisabled]="menu.disabled"
            [nzSelected]="rla.isActive || (isDefault && menu.path === 'detect-rule')"
            #rla="routerLinkActive"
            [routerLinkActiveOptions]="isActiveMatchOptions"
            [nzPaddingLeft]="isSecondary ? 24 : 8"
          >
            <a
              [routerLink]="[menu.path]"
              [queryParams]="{ appCode: appService.selectedApp?.code }"
              style="display: flex; align-items: center"
            >
              <i adIcon [iconfont]="menu.icon" *ngIf="menu.icon"></i>
              <span class="program-title">
                {{ menu.id | translate }}
                <span *ngIf="menu.tag" class="title-tag" [ngClass]="{ 'title-tag-old': menu.tag.includes('V1.0') }">{{
                  menu.tag
                }}</span>
              </span>
            </a>
          </li>
        </ng-container>
      </ng-template>

      <!-- 一二级菜单模板 -->
      <ng-template #secondaryMenuTpl let-menu="menu">
        <li
          nz-submenu
          [nzTitle]="submenuTitle"
          [class]="{ 'submenu-title': true }"
          [nzDisabled]="menu.disabled"
          [nzOpen]="menu.open ?? true"
          routerLinkActive="parent-selected"
          [routerLinkActiveOptions]="{ exact: false }"
          [nzPaddingLeft]="8"
        >
          <!-- routerLink设置在空span上用于匹配一级菜单激活状态，如果放在li上会导致点击跳转 -->
          <span [routerLink]="menu.path"></span>
          <ul>
            <!-- 渲染Link菜单 -->
            <ng-container *ngFor="let item of menu.children">
              <ng-container *ngTemplateOutlet="menuTpl; context: { menu: item, isSecondary: true }"></ng-container>
            </ng-container>
          </ul>
        </li>
        <ng-template #submenuTitle>
          <i adIcon [iconfont]="menu.icon"></i>
          <span class="program-title">
            {{ menu.id | translate }} <span class="title-tag title-tag-old" *ngIf="menu.tag">{{ menu.tag }}</span>
          </span>
        </ng-template>
      </ng-template>
    </ul>
  </nz-sider>
  <nz-content class="content-box">
    <router-outlet></router-outlet>
  </nz-content>
</nz-layout>

<ng-template #expendBtnTmpl>
  <i
    adIcon
    [iconfont]="isMenuzCollapsed ? 'iconcaidanzhankai1' : 'iconcaidanshouqiicon1'"
    class="expend-btn iconfont"
  ></i>
</ng-template>
