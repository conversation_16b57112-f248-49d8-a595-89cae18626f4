.integrate-automation-layout-content {
  border-radius: 8px 8px 0 0;
  height: calc(100vh - 48px);
  .ant-layout-sider {
    background-color: #fff;
    box-shadow: 5px 0px 10px 0px rgba(0, 0, 0, 0.06);
    border-top-left-radius: 8px;
    .top-btn-box {
      margin-left: 16px;
      position: relative;
      padding-left: 24px;
    }
    .back-btn {
      position: absolute;
      top: 0;
      left: 0;
      width: 22px;
      height: 22px;
      text-align: center;
      line-height: 22px;
      cursor: pointer;
    }

    .menu-title {
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      color: #333333;
      line-height: 32px;
      height: 32px;
      border-bottom: 1px solid #e9e9e9;
      padding: 0 16px;
      text-overflow: ellipsis;
      overflow: hidden;
      display: inline-block;
      white-space: nowrap;
      max-width: 100%;
      width: 100%;
      padding-right: 35px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .apps {
        font-size: 16px;
        margin-left: 4px;
      }
    }

    .ant-menu {
      height: calc(100vh - 32px);

      ::ng-deep {
        .submenu-title {
          color: #333;
        }
        .ant-menu-item,
        .ant-menu-submenu-title {
          padding-left: 0px !important;
          padding: 0px 8px !important;
        }

        .ant-menu-sub {
          .ant-menu-item {
            padding-left: 32px !important;
          }
        }

        .ant-divider-horizontal {
          margin: 12px 0px;
          border-top: 1px solid #dddddd;
        }
      }
    }
  }
  .content-box {
    ::ng-deep app-drive-execution {
      background: white;
    }
    ::ng-deep .drive-execution {
      margin-left: 10px;
    }

    ::ng-deep .param-set {
      background: white;
    }
    ::ng-deep .extend-info {
      background: white;
      padding: 10px;
    }
  }
  ::ng-deep .ant-layout-sider-children {
    // position: relative;
    overflow: hidden;
  }
  ::ng-deep .expend-panel {
    z-index: 1;
    transition: none !important;
    & .ant-layout-sider-zero-width-trigger {
      box-shadow: none;
      right: 7px;
      height: 30px;
    }
  }
  ::ng-deep .ant-layout-sider-zero-width-trigger {
    right: -31px;
    top: 0;
    width: 30px;
    height: 40px;
    line-height: 40px;
    opacity: 1;
    background: #ffffff;
    border-radius: 0 8px 8px 0;
    box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    &:hover {
      background: #ffffff;
      .expend-btn,
      .collapsed-btn {
        color: #6a4cff;
      }
    }
    .expend-btn {
      font-size: 16px;
      color: #333333;
      &:hover {
        color: #6a4cff;
      }
    }

    .collapsed-btn {
      font-size: 16px;
      color: #333333;
      &:hover {
        color: #6a4cff;
      }
    }
  }

  ::ng-deep .program-title {
    display: flex;
    align-items: center;
    .title-tag {
      padding: 0px 4px;
      margin-left: 10px;
      height: 14px;
      line-height: 14px;
      background: linear-gradient(90deg, #ffb836 0%, #ff7b00);
      border-radius: 2px;
      font-size: 10px;
      font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif, 'PingFangSC-Regular', 'PingFang SC', sans-serif-400;
      font-weight: normal;
      color: #ffffff;
    }

    .title-tag-old {
      background: linear-gradient(136deg, #82c5f6 2%, #29a2f9 97%);
    }
  }
}
