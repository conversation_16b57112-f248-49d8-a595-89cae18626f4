import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { IntegratedAutomationLayoutComponent } from './integrated-automation-layout/integrated-automation-layout.component';
import { ViewDesignerComponent } from './view-designer/view-designer.component';
import { ViewDesignerDetectComponent } from './view-designer-detect/view-designer-detect.component';
import { IntegratedDetectListComponent } from './components/integrated-detect-list/integrated-detect-list.component';
import { IntegratedExecutionListComponent } from './components/integrated-execution-list/integrated-execution-list.component';
import { IntegratedSoListComponent } from './components/integrated-so-list/integrated-so-list.component';
import { IntegratedActionListComponent } from './components/integrated-action-list/integrated-action-list.component';
import { IntegratedActionDetailComponent } from './components/integrated-action-detail/integrated-action-detail.component';
import { IntegratedSoDetailComponent } from './components/integrated-so-detail/integrated-so-detail.component';
import { ContainerComponent } from './container/container.component';
import { MessageEventComponent } from './components/message-event/message-event.component';
import { MessageNotifyComponent } from './components/message-notify/message-notify.component';
import { MessageEventComponent as MessageEventComponent2 } from './components/event/message-event/message-event.component';
import { MessageNotifyDetailComponent } from './components/message-notify/message-notify-detail/message-notify-detail.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'prefix',
    component: IntegratedAutomationLayoutComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'data-execution-new',
      },
      {
        path: 'data-execution-new',
        pathMatch: 'prefix',
        component: IntegratedExecutionListComponent,
      },
      {
        path: 'data-execution-manual',
        pathMatch: 'prefix',
        component: IntegratedExecutionListComponent,
      },
      {
        path: 'data-execution/detect-rule',
        pathMatch: 'prefix',
        component: IntegratedDetectListComponent,
      },
      {
        path: 'data-execution/old',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../../app/dtd/drive-execution-new/drive-execution-new.module').then((m) => m.DriveExecutionNewModule),
      },
      {
        path: 'so',
        pathMatch: 'prefix',
        component: IntegratedSoListComponent,
      },
      {
        path: 'action',
        pathMatch: 'prefix',
        component: IntegratedActionListComponent,
      },
      {
        path: 'param-set',
        pathMatch: 'prefix',
        loadChildren: () => import('pages/app/km/param-set/param-set.module').then((m) => m.ParamSetModule),
      },
      {
        path: 'extend-info',
        pathMatch: 'prefix',
        loadChildren: () => import('pages/app/other/extend-info/extend-info.module').then((m) => m.ExtendInfoModule),
      },
      // {
      //   path: 'param/:id',
      //   pathMatch: 'prefix',
      //   component: IntegratedParamListComponent,
      // }
      {
        path: 'messages/event',
        pathMatch: 'prefix',
        component: MessageEventComponent,
      },
      {
        path: 'messages/notify',
        pathMatch: 'prefix',
        loadChildren: () =>
          import('./components/message-notify/message-notify-index.module').then((m) => m.MessageNotifyIndexModule),
      },
    ],
  },
  {
    // 流程设计器
    path: 'view-designer/:id',
    pathMatch: 'prefix',
    component: ContainerComponent,
    data: { keepAliveKey: 'TTTDesigner' },
  },
  {
    // 流程设计器
    path: 'dtd-designer-view',
    pathMatch: 'prefix',
    loadChildren: (): any =>
      import('./dtd-designer-view/dtd-designer-view.module').then((m) => m.DtdDesignerViewModule),
  },
  {
    // 流程设计器(租户级)
    path: 'view-designer/:id/:objectId',
    pathMatch: 'prefix',
    component: ContainerComponent,
  },
  {
    // 数据侦测
    path: 'view-designer-detect/:id',
    pathMatch: 'prefix',
    component: ViewDesignerDetectComponent,
  },
  {
    // 行动
    path: 'view-designer-aciton/:id',
    pathMatch: 'prefix',
    component: IntegratedActionDetailComponent,
  },
  {
    // 服务编排
    path: 'view-designer-so/:id',
    pathMatch: 'prefix',
    component: IntegratedSoDetailComponent,
  },
  {
    // 消息事件
    path: 'message-event',
    pathMatch: 'prefix',
    component: MessageEventComponent2,
  },
  {
    path: 'message-notify-detail',
    component: MessageNotifyDetailComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class IntegratedAutomationRoutingModule {}
