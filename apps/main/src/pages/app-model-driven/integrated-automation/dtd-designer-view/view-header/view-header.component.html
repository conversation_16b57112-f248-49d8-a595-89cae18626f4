<section class="header-wrapper">
  <div class="header-left">
    <span class="font-class" adIcon type="arrow-left" nzTheme="outline" (click)="goback()"></span>
    <span class="header-title">{{ selectedProject?.name }}</span>
  </div>

  <div class="header-right">
    <a class="option" (click)="openModifyHistoryModal()">
      <i adIcon [iconfont]="'iconshixiaokongzhi'"></i>
      {{ 'dj-修改历史' | translate }}
    </a>

    <!-- todo流程多版本 -->
    <button
      class="ant-btn-primary-version ant-btn-primary-border version-dropdown-button"
      nz-button
      nzType="default"
      nz-dropdown
      nzTrigger="click"
      #versionBtn
      [nzLoading]="loadingVersion"
      [nzDropdownMenu]="menu"
      nzPlacement="bottomLeft"
      (nzVisibleChange)="loadVersionChange($event)"
    >
      <div class="version-link">
        <span class="version-link-text">{{ designerViewService?.currentSelectedVersion?.adpRemark }}</span>
        <span
          class="version-link-tag"
          [ngClass]="versionLinkTagStatusClassMap[designerViewService?.currentSelectedVersion?.adpStatus]"
        >
          {{ versionLinkTagStatusMap[designerViewService?.currentSelectedVersion?.adpStatus] | translate }}
        </span>
        <!-- <span class="dot"></span> -->
      </div>
    </button>
    <ng-container *operateAuth="{ prefix: 'update', tenantPaas: isTenantActive }">
      <button
        class="ant-btn-primary-version ant-btn-primary-border"
        (click)="handleAddVersion()"
        nz-button
        [nzLoading]="addVersionLoading"
        nzType="default"
        *ngIf="showCreate"
      >
        <span class="create-new-version-item">{{ 'dj-创建新版本' | translate }}</span>
      </button>
      <app-module-publish-button
        #publishButton
        [module]="isTenantActive ? 'tenant_dtd' : 'dtd'"
        [needTenant]="true"
        [needSave]="true"
        [needEffect]="true"
        [effectFrom]="effectFrom"
        [tenantHeaders]="designerViewService.tenantHeaders"
        [adpVersionHeaders]="designerViewService.adpVersionHeaders"
        [isTenant]="isTenantActive"
        [isTenantApi]="isTenantActive"
        [tenantUserId]="tenantUserId"
        [atdpUniqueId]="atdpUniqueId"
        [eocInfo]="eocInfo"
        [supportEOC]="isTenantActive"
        [effectData]="effectData"
        [pkValue]="designerViewService.currentPropsCode"
        (clickPublicAction)="handlePublish()"
        (publicAction)="handlePublishAction($event)"
        (effectChange)="handleEffectChange($event)"
        (upDateState)="handleUpDateState($event)"
      ></app-module-publish-button>
    </ng-container>
    <ng-container *operateAuth="{ prefix: 'update', tenantPaas: isTenantActive }">
      <button ad-button adType="primary" (click)="handleSave(true)">
        {{ 'dj-保存' | translate }}
      </button>
    </ng-container>
  </div>
</section>

<app-modify-history-modal
  *ngIf="historyModalProps.transferModal"
  [historyModalProps]="historyModalProps"
  [application]="appService.selectedApp?.code"
  [extendHeader]="designerViewService.adpVersionHeaders"
  (closeModal)="historyModalProps.transferModal = false"
>
</app-modify-history-modal>

<app-manage-version-modal
  *ngIf="manageVersionVisible"
  [visible]="manageVersionVisible"
  (visibleChange)="handleManageVisible($event)"
  (refresh)="handleRefreshVersionListAndCanvas($event)"
></app-manage-version-modal>

<app-version-remark-modal
  *ngIf="versionRemarkVisible"
  [visible]="versionRemarkVisible"
  [data]="designerViewService.currentSelectedVersion"
  (visibleChange)="versionRemarkVisible = false"
  (updateRemark)="handleUpdateRemarkOK($event)"
></app-version-remark-modal>

<nz-dropdown-menu class="link-menu-container" #menu="nzDropdownMenu">
  <ul nz-menu class="ul-link-menu-container" [ngStyle]="{ width: buttonWidth + 'px' }">
    <div (click)="handleSelectVersion(version)" *ngFor="let version of versionList; let i = index">
      <li nz-menu-item>
        <div
          class="version-link"
          [ngClass]="{
            'version-link': true,
            'version-link-active': designerViewService?.currentSelectedVersion?.adpVersion === version.adpVersion
          }"
        >
          <span nz-tooltip [nzTooltipTitle]="version.adpRemark" class="version-link-text">{{ version.adpRemark }}</span>
          <span class="version-link-tag" [ngClass]="versionLinkTagStatusClassMap[version?.adpStatus]">
            {{ versionLinkTagStatusMap[version?.adpStatus] | translate }}
          </span>
        </div>
      </li>
    </div>

    <ng-container *operateAuth="{ prefix: 'update', tenantPaas: isTenantActive }">
      <li nz-menu-divider style="margin: 4px 12px" *ngIf="versionList?.length"></li>
      <li nz-menu-item>
        <div class="version-link version-link-manage" (click)="handleUpdateRemark()">
          <i adIcon iconfont="iconcharu" aria-hidden="true"></i
          ><span class="update-version-remark-item">{{ 'dj-修改版本备注' | translate }}</span>
        </div>
      </li>
      <li nz-menu-item>
        <div class="version-link version-link-manage" (click)="handleVersionManagement()">
          <i adIcon iconfont="iconsheding" aria-hidden="true" class="submitIcon"></i
          ><span class="update-version-remark-item">{{ 'dj-版本管理' | translate }}</span>
        </div>
      </li>
    </ng-container>
  </ul>
</nz-dropdown-menu>
