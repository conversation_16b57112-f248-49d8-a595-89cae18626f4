import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService, DW_APP_AUTH_TOKEN } from 'common/service/system-config.service';
import { GlobalService } from 'common/service/global.service';
import { AdUserService } from 'pages/login/service/user.service';
import { DtdDesignerViewService } from './dtd-designer-view.service';

@Injectable()
export class DtdDesignerViewApiService {
  headerConfig = {};
  adesignerUrl: string;
  isTenantActive = false; // 是否租户激活
  constructor(
    private http: HttpClient,
    public adUserService: AdUserService,
    private configService: SystemConfigService,
    private dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
      // this.adesignerUrl = 'http://localhost:4201';
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  /**
   * 查询流程版本列表 dropdown
   */
  loadDtdVersionList(params: any): Observable<any> {
    let headers = Object.assign({}, this.headerConfig);
    if (this.isTenantActive) {
      headers = Object.assign({}, this.headerConfig, this.dtdDesignerViewService.tenantHeaders);
    }
    return this.http.get(`${this.adesignerUrl}/athena-designer/groupHistory/getDataGroupHistory`, {
      params,
      headers,
    });

    // const url = '../../../assets/api/getDataGroupHistory.json';
    // return this.http.get(url);
  }

  // table带搜索
  queryVersionList(params: any): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/groupHistory/queryList`, {
      params,
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.tenantVersionHeaders),
    });
  }

  /**
   * 新增流程版本
   * @param params
   * @returns
   */
  createDtdVersion(params: any): Observable<any> {
    const { adpVersionHeaders, tenantVersionHeaders } = this.dtdDesignerViewService;
    const headers = Object.assign(
      {},
      this.headerConfig,
      this.isTenantActive ? tenantVersionHeaders : adpVersionHeaders,
    );
    console.log(headers);
    return this.http.post(`${this.adesignerUrl}/athena-designer/groupHistory/create`, params, {
      headers,
    });
  }

  /**
   * 更新流程版本
   * @param params
   * @returns
   */
  updateDtdRemark(params: any): Observable<any> {
    const { tenantHeaders } = this.dtdDesignerViewService;
    return this.http.post(`${this.adesignerUrl}/athena-designer/groupHistory/updateRemark`, params, {
      headers: Object.assign({}, this.headerConfig, this.isTenantActive ? tenantHeaders : {}),
    });
  }

  /**
   * 删除流程版本
   * @param params
   * @returns
   */
  removeDtdVersion(params: any): Observable<any> {
    const { tenantHeaders } = this.dtdDesignerViewService;
    return this.http.post(`${this.adesignerUrl}/athena-designer/groupHistory/remove`, params, {
      headers: Object.assign({}, this.headerConfig, this.isTenantActive ? tenantHeaders : {}),
    });
  }

  queryProjectInfo(param: any): Observable<any> {
    if (this.isTenantActive) {
      const url = `${this.adesignerUrl}/athena-designer/tenant/project/getProject/${param}`;
      // 租户模式下只有编辑
      return this.http.get(url, {
        headers: this.dtdDesignerViewService.tenantVersionHeaders,
      });
    } else {
      const url = `${this.adesignerUrl}/athena-designer/project/getProject/${param}`;
      return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
    }
  }
}
