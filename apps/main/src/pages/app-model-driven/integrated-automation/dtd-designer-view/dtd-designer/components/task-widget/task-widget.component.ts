import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, ValidationErrors, Validators } from '@angular/forms';
import { Observable, Observer, Subscription } from 'rxjs';
import { LocaleService } from 'common/service/locale.service';
import { TaskWidgetService } from './service/task-widget.service';
import { GlobalService } from 'common/service/global.service';
import { AppService } from 'pages/apps/app.service';
import { AdUserService } from 'pages/login/service/user.service';
import { cloneDeep } from 'lodash';
import { taskTypeMap, TaskTypes } from './types';
import { ApiService } from './service/api.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import CodeEnum from '../../../../../../../common/config/code';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';

const taskSteps = [
  { index: 0, title: '基础信息录入', code: 'basic', status: 'wait', display: true, icon: 'file-text' },
  { index: 1, title: '推送数据', code: 'push', status: 'wait', display: true, icon: 'login' },
  { index: 2, title: '数据状态', code: 'stateMaps', status: 'wait', display: true, icon: 'line-chart' },
  { index: 3, title: '执行人', code: 'executor', status: 'wait', display: true, icon: 'user' },
  { index: 4, title: '方案设计', code: 'exception', status: 'wait', display: true, icon: 'save' },
];

@Component({
  selector: 'app-task-widget',
  templateUrl: './task-widget.component.html',
  styleUrls: ['./task-widget.component.less'],
})
export class TaskWidgetComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  loading: boolean;
  clickPriority: boolean = false;
  timeout;
  anchorContainer: any;
  extra: any;
  currentTask: any;
  executeType: any;
  typeData: any;
  allTaskData: any;
  currentGraphData: any;
  isLoaded: boolean;

  historyModalProps: HistoryModalProps = {
    transferModal: false,
    code: '',
    collection: 'task',
  }; // 修改历史弹窗展示

  taskStatusSubscription: Subscription;
  oldCode: string;
  taskType: string = 'commonMan';

  @Input() visible: boolean;
  @Input() modalFlag: any;
  @Input() currentTaskData: any;
  @Input() addedTask: any; // 要新增的任务
  @Input() applicationCodeProxy;
  @Input() favouriteCode: string;
  @Input() from: string;
  @Input() isTenantProcessId: boolean;

  @Output() afterSave: EventEmitter<any> = new EventEmitter();
  @Output() cancel: EventEmitter<any> = new EventEmitter();

  get isFromTemplate(): boolean {
    return !!this.applicationCodeProxy;
  }

  constructor(
    private fb: FormBuilder,
    public appService: AppService,
    private translateService: TranslateService,
    private message: NzMessageService,
    private languageService: LocaleService,
    protected userService: AdUserService,
    private taskWidgetService: TaskWidgetService,
    public globalService: GlobalService,
    private apiService: ApiService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.favouriteCode?.currentValue) {
      this.apiService.headerConfig = { templateId: this.favouriteCode };
    }
    if (changes.applicationCodeProxy?.currentValue) {
      this.apiService.applicationCodeProxy = this.applicationCodeProxy;
    }
  }

  ngOnInit(): void {
    this.handleInit();
    this.handleLoadAppData();
    this.handleSelectStep(0);
    this.handleLoadDataStatus();
  }

  ngOnDestroy() {
    this.taskStatusSubscription?.unsubscribe();
  }

  // 点击步骤
  handleSelectStep(index: any, needValidate?: any): void {
    let validate = true;
    if (index > this.currentTask.activeIndex && needValidate) {
      validate = this.handleValidate();
      this.currentTask.formDirty = true;
    }
    if (!validate) {
      return;
    }
    if (this.currentTask.steps[index].status === 'wait') {
      this.currentTask.steps[index].status = 'process';
    }
    this.currentTask.activeIndex = index;
    this.currentTask.activeCode = this.currentTask.steps[index].code;
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.anchorContainer = document.getElementById('taskWidget');
    });
  }

  handleAnchor() {
    const { steps } = this.currentTask;
    const heightArr = [];
    let height_s = 0;
    steps.forEach((s) => {
      height_s += document.getElementById(s.code).offsetHeight + 24;
      heightArr.push(height_s);
    });
    const scroll_top = document.querySelector('.task-content').scrollTop;
    if (this.clickPriority) {
      this.clickPriority = false;
      return;
    }
    if (scroll_top > heightArr[0] && scroll_top < heightArr[1]) {
      this.currentTask.activeCode = 'push';
    } else if (scroll_top > heightArr[1] && scroll_top < heightArr[2]) {
      this.currentTask.activeCode = 'stateMaps';
    } else if (scroll_top > heightArr[2] && scroll_top < heightArr[3]) {
      this.currentTask.activeCode = 'executor';
    } else if (scroll_top > heightArr[3] && scroll_top < heightArr[4]) {
      this.currentTask.activeCode = 'exception';
    } else if (scroll_top > heightArr[4] && scroll_top < heightArr[5]) {
      this.currentTask.activeCode = 'assign';
    } else {
      this.currentTask.activeCode = 'basic';
    }
  }

  codeValidator = (control) =>
    new Observable((observer: Observer<ValidationErrors | null>) => {
      const { value = '' } = control;
      const str = new RegExp(`^(?!.*__.*$)^(?!.*-.*$)`, 'g');
      if (!str.test(value)) {
        observer.next({
          error: true,
          duplicated: true,
        });
      } else {
        observer.next(null);
      }
      observer.complete();
    });

  // 初始化
  handleInit(): void {
    if (this.modalFlag === 'edit') {
      this.taskType = this.taskWidgetService.getTaskType(this.currentTaskData);
    } else {
      this.taskType = this.addedTask.expandType;
    }

    this.executeType = [
      { value: 'auto', label: this.translateService.instant('dj-自动') },
      { value: 'manual', label: this.translateService.instant('dj-人工') },
    ];
    this.typeData = [
      {
        value: 'business',
        label: 'business (' + this.translateService.instant('dj-业务型任务(默认)') + ')',
      },
      // { value: 'check', label: this.translateService.instant('dj-检查型') },
      { value: 'approve', label: 'approve (' + this.translateService.instant('dj-签核型') + ')' },
      { value: 'solve', label: 'solve (' + this.translateService.instant('dj-异常排除') + ')' },
      // {
      //   value: 'signal',
      //   label: this.translateService.instant('dj-signal信号型'),
      // },
      { value: 'wait', label: 'wait (' + this.translateService.instant('dj-等待类型') + ')' },
      { value: 'assist', label: 'assist (' + this.translateService.instant('dj-辅助类型') + ')' },
    ];
    this.currentTask = {
      steps: taskSteps,
      activeIndex: 0,
      activeCode: 'basic',
      basicForm: this.fb.group({
        code: [null, [Validators.required]],
        name: [null, [Validators.required]],
        groupCode: [null, [Validators.required]],
        tenantId: [null, [Validators.required]],
        // pluginId: [null],
        category: [{ value: null, disabled: true }, [Validators.required]],
        pattern: [null, [Validators.required]],
        priority: [null],
        milestone: [null],
        executeType: [{ value: null, disabled: true }, [Validators.required]],
        relaDataEntryCode: [null],
        type: [{ value: null, disabled: true }, [Validators.required]],
        driveType: [{ value: null, disabled: true }, [Validators.required]], // 驱动类型
        merge: [null],
        flowCode: [null],
        supportPart: [null],
        supportSplit: [null],
        pageCode: [null],
        dueDateBus: [null],
        dueDateValue: [null],
        dueDateVariable: [null],
        dueDateType: [null],
        dueDateFormat: [null],
        dueDateComponentValue: [null],
        dueDateComponentDataType: [null],
        startApproveActivity: [null],
        extendFields: this.fb.group({
          closeNeedConfirm: false,
        }),
      }),
      atmcDatas: [],
      stateMaps: [],
      personOnDuty: {
        processType: '',
        choosePolicy: '',
        identities: [],
      },
      businessExecutor: [],
      assignConfig: {
        assignAble: false,
      },
      configGroupField: '',
      resolvePlans: [],
      config: {
        approve: '',
      },
      dataFeatures: [], // 数据特征
      versionNumber: null, // 变更版本
      manualAble: null,
      dimension: {
        categories: [],
      },
      evlModelCode: '', // 评价模型
    };
    this.extra = {
      modalFlag: this.modalFlag,
      executeType: this.executeType,
      typeData: this.typeData,
      performerType: [
        { value: 'user', label: this.translateService.instant('dj-用户') },
        { value: 'duty', label: this.translateService.instant('dj-职能') },
        {
          value: 'deptDirector',
          label: this.translateService.instant('dj-部门主管'),
        },
        {
          value: 'deptUser',
          label: this.translateService.instant('dj-部门人员'),
        },
        {
          value: 'personInCharge',
          label: this.translateService.instant('dj-当责者'),
        },
        { value: 'activity', label: this.translateService.instant('dj-活动') },
        {
          value: 'userSuperior',
          label: this.translateService.instant('dj-直属主管'),
        },
      ],
      processType: [
        {
          value: 'signOr',
          label: this.translateService.instant('dj-仅需一人处理'),
        },
        {
          value: 'signAnd',
          label: this.translateService.instant('dj-所有人都需处理'),
        },
      ],
      choosePolicy: [
        { value: 'single', label: this.translateService.instant('dj-单人') },
        { value: 'all', label: this.translateService.instant('dj-多人') },
      ],
    };
    this.apiService.loadLcdpDataEntryData(this.appService?.selectedApp?.code).subscribe(
      (res) => {
        this.extra.relaDataEntryCodes = res.data.map((item) => ({ value: item.code, label: item.name })) || [];
      },
      () => {},
    );
  }

  get lang() {
    return this.languageService.currentLanguage;
  }

  // 获取appData
  handleLoadAppData(): void {
    this.loading = true;
    this.apiService.loadTaskData(this.appService?.selectedApp?.code).subscribe(
      (res) => {
        if (res.code === 0) {
          this.allTaskData = [];
          (res.data || []).forEach((d) => {
            const { children = [] } = d;
            d.children = children.map((s) => {
              const hasLang = s.lang?.name && JSON.stringify(s.lang?.name || {}) !== '{}';
              if (hasLang) s.name = s.lang.name[this.lang] || s.name;
              return {
                ...s,
              };
            });
            d.openTask = true;
            this.allTaskData = [
              ...(this.allTaskData || []),
              ...children.map((s) => {
                return { value: s.code, label: s.name, lang: s.lang };
              }),
            ];
          });
          if (res.data.length > 0) {
            // 目前数据群落只会有1个，所以这里只取第一个
            this.currentGraphData = res.data[0];
          }
          this.currentTask.groupData = (res.data || []).map((s) => {
            return { value: s.code, label: s.name };
          });
          this.loading = false;

          if (this.modalFlag === 'edit') {
            this.handleEdit();
          } else {
            this.handleAddModal();
          }
        }
      },
      () => {
        this.loading = false;
      },
    );
  }

  // 获取数据状态
  async handleLoadDataStatus(): Promise<void> {
    const { groupCode, code } = this.appService.selectedApp || {};
    const res = await this.apiService.loadDataStates({ application: code, groupCode }).toPromise();
    if (res.code === 0) {
      this.extra.allDataStatus = (res.data || []).map((s) => {
        return { value: s.code, label: s.name };
      });
    }
  }

  // 添加
  async handleAddModal() {
    // 设置任务代号 task0001递增
    let taskcode = '';
    let applicationCode = this.applicationCodeProxy
      ? this.applicationCodeProxy
      : this.appService?.selectedApp?.code ?? '';
    applicationCode = applicationCode.replace(/-/g, '_');
    const nameList = [...new Set(this.currentGraphData.children?.map((item) => item.code))];
    const nameTaskList = nameList.filter((item: string) => item.startsWith(`${applicationCode}_task_`));
    // if (nameTaskList.length > 0) {
    const taskIndexList = nameTaskList
      .map((item: string) => {
        const n = Number(item.split('task_')[1]);
        if (!Number.isNaN(n)) return n;
      })
      .filter((item) => item);
    if (taskIndexList.length === 0) taskIndexList.push(0);
    if (this.appService.selectedApp?.code) {
      taskcode = await this.appService.getCodeByType(CodeEnum.TK);
    } else {
      const maxIndex = Math.max(...taskIndexList) + 1;
      taskcode = `${applicationCode}_task_${String(maxIndex).padStart(4, '0')}`;
    }
    const taskName = this.translateService.instant(taskTypeMap[this.taskType || TaskTypes.BACKSTAGE]) || taskcode;

    this.oldCode = taskcode;
    this.currentTask['dataFeatures'] = [];
    this.currentTask['versionNumber'] = null;

    this.currentTask['steps'] = cloneDeep(taskSteps);
    this.extra.currTaskData = cloneDeep(this.allTaskData || []);
    this.currentTask.formDirty = false;
    this.currentTask['lang'] = {
      // name: { zh_CN: '', zh_TW: '', en_US: '' },
      name: { zh_CN: taskName, zh_TW: taskName, en_US: `${this.taskType} task` }, // p4s1 新增任务名称默认值
    };
    this.currentTask.basicForm.reset();
    this.currentTask.basicForm.get('code').enable();
    this.currentTask.basicForm.patchValue({
      code: taskcode, // p4s1 新增[任务代号]默认值
      tenantId: 'SYSTEM', // p4s1 新增[默认租户]默认值
      flowCode: taskcode, // p4s1 新增[活动序号]默认值
      category: 'PROCESS', // p4s1 新增[业务类型]默认值
      pattern: 'BUSINESS', // p4s1 新增[业务模式]默认值
      priority: '0', // p4s1 新增[优先级]默认值
      executeType: 'auto', // p4s1 新增[执行方式]默认值
      relaDataEntryCode: '', // 表单关联code默认空
      type: 'business', // p4s1 新增[任务类型]默认值
      dueDateComponentValue: 1, // p4s1 新增[业务值]默认值
      dueDateComponentDataType: 'DAY', // p4s1 新增[业务值]默认值
      name: taskName,
      milestone: false,
      supportPart: false,
      supportSplit: false,
      dueDateBus: false,
      merge: false,
      driveType: this.addedTask.driveType || 'flow',
      extendFields: {
        closeNeedConfirm: false,
      },
    });

    if (this.taskType === 'backstage') {
      this.currentTask.basicForm.patchValue({
        type: 'business',
        category: 'PROCESS',
        executeType: 'auto',
      });
    } else if (this.taskType === 'approve') {
      this.currentTask.basicForm.patchValue({
        type: 'approve',
        category: 'APPROVAL',
        executeType: 'manual',
      });
    } else if (this.taskType === 'solve') {
      this.currentTask.basicForm.patchValue({
        type: 'solve',
        category: 'SOLVE',
        executeType: 'manual',
      });
    } else if (this.taskType === 'reply') {
      this.currentTask.basicForm.patchValue({
        type: 'business',
        category: 'REPLY',
        executeType: 'manual',
      });
    } else if (this.taskType === 'form') {
      this.currentTask.basicForm.patchValue({
        type: 'business',
        category: 'DATA_ENTRY',
        executeType: 'manual',
      });
    } else if (this.taskType === 'commonMan') {
      this.currentTask.basicForm.patchValue({
        type: 'business',
        category: 'PROCESS',
        executeType: 'manual',
      });
    } else if (this.taskType === 'assist') {
      this.currentTask.basicForm.patchValue({
        type: 'assist',
        category: 'PROCESS',
        executeType: 'auto',
      });
    } else if (this.taskType === 'wait') {
      this.currentTask.basicForm.patchValue({
        type: 'wait',
        category: 'PROCESS',
        executeType: 'auto',
      });
    }
    // #46617
    this.currentTask.atmcDatas = [
      { athenaKey: 'personInCharge', proVarKey: 'personInCharge' },
      { athenaKey: 'dueDate', proVarKey: 'dueDate' },
      { athenaKey: 'sourceIds', proVarKey: 'sourceIds' },
    ];
    this.currentTask.stateMaps = [];
    this.currentTask.personOnDuty = {
      processType: 'signOr', // p4s1 新增[任务处理方式]默认值
      choosePolicy: 'single', // p4s1 新增[人员选择策略]默认值
      // #46617
      identities: [
        {
          isBusiness: false,
          performerName: '用户',
          performerType: 'user',
          performerValue: '$(personInCharge)',
        },
      ],
    };
    this.currentTask.dimension = { categories: ['cate2_541005141869121'] };
    this.currentTask.businessExecutor = [];
    this.currentTask.config = {
      approve: '',
    };
    this.extra.allowSubLevelApprove = false;
    this.currentTask.subApproveConditionDropDown = '';
    this.currentTask.configGroupField = '';
    this.currentTask.resolvePlans = [];
    this.currentTask.basicForm.patchValue({
      groupCode: this.currentGraphData?.code || '',
    });
    this.isLoaded = true;
  }

  // 编辑获取数据
  handleEdit(): void {
    this.loading = true;
    this.apiService.loadCurrentTaskData(this.currentTaskData.code).subscribe(
      (res) => {
        this.loading = false;
        this.currentTask['groupCode'] = this.currentGraphData.code;
        this.currentTask['manualAble'] = !!this.currentTaskData?.extendFields?.projectCode;
        if (res.code === 0) {
          this.handleEditData(res.data || {});
        }
      },
      () => {
        this.loading = false;
        this.isLoaded = true;
      },
    );
  }

  // 编辑
  async handleEditData(data: any): Promise<void> {
    await this.handleLoadDataStatus();
    this.extra.currTaskData = (this.allTaskData || []).filter((s) => s.value !== data['code']);
    this.currentTask['lang'] = { ...(data.lang || { name: {} }) };
    this.currentTask['lang'].name[this.languageService?.currentLanguage || 'zh_CN'] =
      this.currentTask['lang'].name?.[this.languageService?.currentLanguage || 'zh_CN'] || data.name;
    this.currentTask['steps'] = cloneDeep(taskSteps);
    this.currentTask.formDirty = false;
    this.currentTask.basicForm.patchValue({
      code: data['code'] || null,
      name: data['name'] || null,
      groupCode: data['groupCode'] || this.currentTask['groupCode'] || '',
      tenantId: data['tenantId'] || null,
      category: data['category'] || null,
      pattern: data['pattern'] || null,
      priority: String(data['priority']) || null,
      milestone: !!data['milestone'],
      executeType: data['executeType'] || null,
      relaDataEntryCode: data['relaDataEntryCode'] || null,
      type: data['type'] || null,
      flowCode: data['flowCode'] || null,
      supportPart: !!data?.config?.['supportPart'],
      supportSplit: !!data?.config?.['supportSplit'],
      pageCode: data['pageCode'] || null,
      dueDateBus: !!data?.dueDate?.isBusiness,
      dueDateValue: data?.dueDate?.value || 0,
      dueDateVariable: data?.dueDate?.timeVariable || null,
      dueDateType: data?.dueDate?.datatype || null,
      dueDateFormat: data?.dueDate?.format || null,
      dueDateComponentValue: data?.dueDate?.componentValue || 0,
      dueDateComponentDataType: data?.dueDate?.componentDataType || 'DAY',
      merge: !!data?.merge,
      startApproveActivity: data['startApproveActivity']?.[0] || null,
      driveType: data.driveType || 'flow',
      extendFields: data.extendFields || null,
    });

    this.currentTask.basicForm.get('driveType').disable();

    this.currentTask['dataFeatures'] = data['dataFeatures'] || [];
    this.currentTask['versionNumber'] = data['versionNumber'];

    if (data?.subApproveConditionDropDown) {
      this.extra.allowSubLevelApprove = true;
      this.currentTask.subApproveConditionDropDown = data?.subApproveConditionDropDown;
    } else {
      this.extra.allowSubLevelApprove = false;
      this.currentTask.subApproveConditionDropDown = '';
    }
    this.currentTask.basicForm.get('code').disable();
    this.currentTask.atmcDatas = data?.atmcDatas || [];
    this.currentTask.stateMaps = (data?.stateMaps || []).map((s) => {
      const { input, output } = s;
      return {
        input,
        output: (Object.keys(output) || []).map((d) => {
          return {
            key: d,
            value: output[d],
          };
        }),
      };
    });
    this.currentTask.personOnDuty = {
      processType: data?.personOnDuty?.processType || '',
      choosePolicy: data?.personOnDuty?.choosePolicy || '',
      identities: data?.personOnDuty?.identities || [],
    };
    this.currentTask.assignConfig = data?.assignConfig;
    this.currentTask.configGroupField = data?.config?.groupField || '';
    this.currentTask.resolvePlans = data?.resolvePlans || [];
    this.currentTask.config = {
      approve: data.config?.approve,
    };
    this.currentTask.businessExecutor = data?.businessExecutor || [];
    this.currentTask.projectCode = data?.projectCode;

    this.currentTask.dimension = data?.dimension || { categories: [] };
    this.currentTask.evlModelCode = data?.evlModelCode || '';
    this.handleResolveLang();
    this.isLoaded = true;
  }

  // 处理异常排除中的多语言
  handleResolveLang(): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    (this.currentTask?.resolvePlans || []).forEach((s) => {
      const { planName, lang } = s;
      const temp = {
        zh_CN: lang?.planName?.zh_CN || '',
        zh_TW: lang?.planName?.zh_TW || '',
        en_US: lang?.planName?.en_US || '',
      };
      if (!temp[language]) {
        temp[language] = planName || '';
      }
      s['lang'] = { planName: temp };
    });
  }

  // 校验
  handleValidate(): boolean {
    let validate = true;
    // const activeCode = this.currentTask.activeCode;
    this.currentTask['errorKeys'] = [];
    // activeCode=basic的invalid
    for (const i of Object.keys(this.currentTask.basicForm?.controls)) {
      if (i === 'relaDataEntryCode') {
        if (
          'manual' === this.currentTask.basicForm.controls['executeType'].value &&
          'DATA_ENTRY' === this.currentTask.basicForm.controls['category'].value &&
          'BUSINESS' === this.currentTask.basicForm.controls['pattern'].value
        ) {
          // relaDataEntryCode 只有 人工+DATA_ENTRY 业务类型+ BUSINESS业务模式 才校验必填
          this.currentTask.basicForm.controls[i].setValidators([Validators.required]);
        } else {
          // 取消必填校验
          this.currentTask.basicForm.controls[i].setValidators([]);
        }
      }
      this.currentTask.basicForm.controls[i].markAsDirty();
      this.currentTask.basicForm.controls[i].updateValueAndValidity();
    }

    if (this.currentTask.basicForm.invalid) {
      validate = false;
    }
    // activeCode=push的invalid
    this.currentTask.atmcDatas.forEach((s) => {
      const { proVarKey, athenaKey } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(proVarKey)) {
        s['errorKeys'].push('proVarKey');
        validate = false;
      }
      if ([undefined, null, ''].includes(athenaKey)) {
        s['errorKeys'].push('athenaKey');
        validate = false;
      }
    });
    // activeCode=executor的invalid
    const { subApproveConditionDropDown } = this.currentTask;
    if (this.extra?.allowSubLevelApprove && !subApproveConditionDropDown) {
      validate = false;
      this.currentTask['errorKeys'].push('subApproveConditionDropDown');
    }
    const { resolvePlans } = this.currentTask;
    const hasPro = this.currentTask['basicForm']?.get('type')?.value === 'solve';
    resolvePlans.forEach((s) => {
      const { planId, planName, projectCode } = s;
      s['errorKeys'] = [];
      if ([undefined, null, ''].includes(planId)) {
        s['errorKeys'].push('planId');
        validate = false;
      }
      if ([undefined, null, ''].includes(planName)) {
        s['errorKeys'].push('planName');
        validate = false;
      }
      if (!!hasPro && [undefined, null, ''].includes(projectCode)) {
        s['errorKeys'].push('projectCode');
        validate = false;
      }
    });
    return validate;
  }

  // 获取按钮是否可点击
  handleValid(): boolean {
    let validate = true;
    // 针对relaDataEntryCode 不展现要清空之前的校验
    const basicForm = this.currentTask.basicForm;
    if (
      'manual' !== basicForm.controls['executeType'].value ||
      'DATA_ENTRY' !== basicForm.controls['category'].value ||
      'BUSINESS' !== basicForm.controls['pattern'].value
    ) {
      basicForm.controls['relaDataEntryCode'].setErrors(null);
    }

    const { type, code, executeType, category, pattern } = this.currentTask['basicForm']?.getRawValue();
    if (executeType === 'manual' && category === 'DATA_ENTRY' && pattern === 'BUSINESS') {
      const data = this.currentTask.atmcDatas;
      const index = data.findIndex((s) => s.proVarKey === `data` && s.athenaKey === 'query_info');
      if (index !== -1) {
        this.currentTask.atmcDatas[index]['readOnly'] = true;
      } else {
        this.currentTask.atmcDatas.unshift({
          proVarKey: `data`,
          athenaKey: 'query_info',
          readOnly: true,
        });
      }
    } else {
      this.currentTask.atmcDatas = this.currentTask.atmcDatas.map((item) => {
        if (item.proVarKey === `data` && item.athenaKey === 'query_info') {
          item.readOnly = false;
        }
        return item;
      });
    }
    if (type === 'solve') {
      const data = this.currentTask.atmcDatas;
      const index = data.findIndex(
        (s) =>
          (s.proVarKey === `=${code}_problematicData` || s.proVarKey === `=${this.oldCode}_problematicData`) &&
          s.athenaKey === 'taskProblematicData',
      );
      if (index !== -1) {
        if (this.oldCode && this.oldCode !== code) {
          this.currentTask.atmcDatas[index].proVarKey = `=${code}_problematicData`;
          this.oldCode = code;
        }
        this.currentTask.atmcDatas[index]['readOnly'] = true;
      } else {
        this.currentTask.atmcDatas.unshift({
          proVarKey: `=${code}_problematicData`,
          athenaKey: 'taskProblematicData',
          readOnly: true,
        });
      }
    } else {
      this.currentTask.atmcDatas = this.currentTask.atmcDatas.map((item) => {
        if (item.proVarKey === `=${code}_problematicData` && item.athenaKey === 'taskProblematicData') {
          item.readOnly = false;
        }
        return item;
      });
    }

    // activeCode=basic的invalid
    if (this.currentTask.basicForm.invalid) {
      validate = false;
    }
    // activeCode=push的invalid
    for (const s of this.currentTask.atmcDatas || []) {
      const { proVarKey, athenaKey } = s;
      if ([undefined, null, ''].includes(proVarKey) || [undefined, null, ''].includes(athenaKey)) {
        validate = false;
        break;
      }
    }
    const hasPro = this.currentTask['basicForm']?.get('type')?.value === 'solve';
    const { resolvePlans = [] } = this.currentTask;
    if (validate) {
      for (const s of resolvePlans) {
        const { planId, planName, projectCode } = s;
        if (
          [undefined, null, ''].includes(planId) ||
          [undefined, null, ''].includes(planName) ||
          (!!hasPro && [undefined, null, ''].includes(projectCode))
        ) {
          validate = false;
          break;
        }
      }
    }

    // const { categories = [] } = this.currentTask.dimension;
    // if (executeType !== 'auto') {
    //   if (categories && categories.length === 0) {
    //     validate = false;
    //   }
    // }

    return validate;
  }

  handleSiderClick(step) {
    this.currentTask.activeCode = step.code;
    this.clickPriority = true;
    document.querySelector(`#${step.code}`).scrollIntoView({
      behavior: 'smooth',
    });
  }

  // 关闭
  handleClose(): void {
    this.cancel.emit();
  }

  // 保存
  handleSaveTask(val?: any): void {
    this.currentTask.formDirty = true;
    if (!this.handleValidate()) {
      return;
    }
    const flag = this.extra['modalFlag'];
    const {
      basicForm,
      atmcDatas = [],
      /* businessExecutor = [], */
      /* personOnDuty = {}, */
      resolvePlans = [],
      configGroupField,
      stateMaps = [],
      assignConfig = {},
      lang,
      config,
      subApproveConditionDropDown,
      dataFeatures,
      versionNumber,
      dimension,
      evlModelCode,
    } = this.currentTask;
    const {
      supportPart,
      supportSplit,
      dueDateBus,
      dueDateValue,
      dueDateVariable,
      dueDateFormat,
      dueDateComponentDataType,
      startApproveActivity,
      dueDateComponentValue,
      ...param
    } = basicForm.getRawValue();
    let paramScene;
    if (val?.type === 'scene' && val?.data && flag === 'add') {
      paramScene = {
        ...val.data.createTaskJson,
        ...param,
        name: val.data.createTaskJson?.name,
        driveType: val.data.createTaskJson?.driveType,
        application: this.appService?.selectedApp?.code,
      };
    } else {
      param['evlModelCode'] = evlModelCode;
      param['dataFeatures'] = dataFeatures;
      param['versionNumber'] = versionNumber;
      param['application'] = this.appService?.selectedApp?.code;
      param['startApproveActivity'] = !!startApproveActivity ? [startApproveActivity] : [];
      param['startApproveActivityName'] = !!startApproveActivity
        ? this.allTaskData?.find((s) => s.value === startApproveActivity)?.label || ''
        : '';
      param['atmcDatas'] = atmcDatas;
      param['config'] = {
        supportPart,
        supportSplit,
      };
      if (param['type'] === 'approve') {
        param['config']['approve'] = !!config?.approve ? config?.approve : null;
      }
      param['dueDate'] = {
        isBusiness: dueDateBus,
        value: dueDateValue,
        datatype: !!dueDateBus ? 'string' : 'second',
        format: dueDateBus ? dueDateFormat : null,
        componentValue: dueDateComponentValue,
        componentDataType: dueDateComponentDataType,
      };
      if (this.extra?.allowSubLevelApprove) {
        param['subApproveConditionDropDown'] = subApproveConditionDropDown || null;
      }
      if (dueDateBus) {
        param['dueDate']['timeVariable'] = dueDateVariable;
      }
      param['priority'] = Number(param['priority']);
      param['resolvePlans'] = resolvePlans.map((s) => {
        if (param['type'] !== 'solve' && s.hasOwnProperty('projectCode')) {
          delete s['projectCode'];
        }
        return s;
      });
      param['config']['groupField'] = configGroupField;
      param['stateMaps'] = (stateMaps || []).map((s) => {
        const { input, output } = s;
        const temp = {};
        output.forEach((d) => {
          temp[d.key] = d.value;
        });
        return {
          input,
          output: temp,
        };
      });
      param['assignConfig'] = assignConfig;
      if (param['executeType'] === 'auto') {
        param['dimension'] = { categories: [] };
      } else {
        param['dimension'] = dimension;
      }
      param['lang'] = lang;
      /* param['businessExecutor'] = businessExecutor; */
      // 去除“任务代号”、“流程序号”空格,如果是编辑的就不能改了，如果之前是有空格的，编辑保存还是要保留不处理，不然数据关联不上了
      if (flag !== 'edit') {
        param['code'] = param['code'].replace(/\s+/g, '');
      }
      param['flowCode'] = param['flowCode'].replace(/\s+/g, '');
      param['allowSubLevel'] = this.extra?.allowSubLevelApprove || false;
      if (this.currentTask?.projectCode) {
        param['projectCode'] = this.currentTask?.projectCode;
      }
      if (this.currentTaskData?.patternType) {
        param['patternType'] = this.currentTaskData?.patternType;
      }
    }
    this.loading = true;
    this.apiService.saveTask(flag, paramScene ?? param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-保存成功！')).onClose!.subscribe(() => {
            this.loading = false;
            this.afterSave.emit({
              flag,
              param: paramScene ?? param,
              taskType: this.taskType,
            });
          });
          //
        } else {
          this.loading = false;
        }
      },
      () => {
        this.loading = false;
      },
    );
  }

  openModifyHistoryModal() {
    this.historyModalProps.code = this.currentTask['basicForm'].get('code')?.value;
    this.historyModalProps.transferModal = true;
  }
}
