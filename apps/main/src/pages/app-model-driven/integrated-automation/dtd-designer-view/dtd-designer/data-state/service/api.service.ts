import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { AdUserService } from 'pages/login/service/user.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

@Injectable()
export class ApiService {
  adesignerUrl: string;
  isTenantActive = false; // 是否租户激活

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public adUserService: AdUserService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  /**
   * 获状态数据
   * @param application
   * @param groupCode
   * @returns
   */
  fetchDataList(application: string, groupCode: string = ''): Observable<any> {
    if (this.isTenantActive) {
      return this.http.get(`${this.adesignerUrl}/athena-designer/tenant/task/getDataList`, {
        params: { application, groupCode },
        headers: this.dtdDesignerViewService.tenantVersionHeaders,
      });
    } else {
      return this.http.get(`${this.adesignerUrl}/athena-designer/task/getDataList`, {
        params: { application, groupCode },
        headers: this.dtdDesignerViewService.adpVersionHeaders,
      });
    }
  }

  deleteDataDescriptionByCode(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/deleteDataDescriptionByCode/${param}`;
    return this.http.get(url, {
      headers: this.dtdDesignerViewService.adpVersionHeaders,
    });
  }
}
