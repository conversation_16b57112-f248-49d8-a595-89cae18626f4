<nz-collapse>
  <nz-collapse-panel [nzHeader]="'dj-来源详情API' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <div class="source-detail">
      <div
        class="content"
        [ngClass]="{
          'has-action':
            !!currentProject.sourceDetailAction.actionId ||
            currentProject.sourceDetailAction['actionParams']?.length > 0
        }"
      >
        <form class="form-content" nz-form nzLayout="vertical" [nzNoColon]="true">
          <div class="action">
            <nz-form-item>
              <nz-form-label>{{ 'dj-请选择action' | translate }}</nz-form-label>
              <nz-form-control>
                <app-modal-window
                  [innerLabel]="false"
                  [attr]="{ name: '请选择action' }"
                  [editable]="editable"
                  [value]="currentProject.sourceDetailAction.actionName"
                  (clear)="handleDeleteAction()"
                  (callBack)="handleOpenAction()"
                ></app-modal-window>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div
            class="action-param"
            *ngFor="let data of currentProject.sourceDetailAction['actionParams']; let i = index"
          >
            <!-- 名称 -->
            <nz-form-item>
              <nz-form-label nzRequired>{{ 'dj-名称' | translate }}</nz-form-label>
              <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('name') ? 'error' : 'success'">
                <div class="duty">
                  <app-modal-input
                    [innerLabel]="false"
                    [attr]="{
                      name: '名称',
                      required: false,
                      error: data['errorKeys']?.includes('name'),
                      readOnly: !editable
                    }"
                    [value]="data.name"
                    (callBack)="handleSourceData('name', $event, i)"
                  >
                  </app-modal-input>

                  <div class="error-text" *ngIf="data['errorKeys']?.includes('name')">
                    {{ 'dj-请输入' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- 参数类型 -->
            <nz-form-item>
              <nz-form-label nzRequired>{{ 'dj-参数类型' | translate }}</nz-form-label>
              <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('type') ? 'error' : 'success'">
                <div class="duty">
                  <app-modal-input
                    [innerLabel]="false"
                    [attr]="{
                      name: '参数类型',
                      required: false,
                      error: data['errorKeys']?.includes('type'),
                      readOnly: !editable
                    }"
                    [value]="data.type"
                    (callBack)="handleSourceData('type', $event, i)"
                  >
                  </app-modal-input>

                  <div class="error-text" *ngIf="data['errorKeys']?.includes('type')">
                    {{ 'dj-请输入' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- 值 -->
            <nz-form-item>
              <nz-form-label nzRequired>{{ 'dj-值' | translate }}</nz-form-label>
              <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('value') ? 'error' : 'success'">
                <div class="duty">
                  <app-modal-input
                    [innerLabel]="false"
                    [attr]="{
                      name: '值',
                      required: false,
                      error: data['errorKeys']?.includes('value'),
                      readOnly: !editable
                    }"
                    [value]="data.value"
                    (callBack)="handleSourceData('value', $event, i)"
                  >
                  </app-modal-input>

                  <div class="error-text" *ngIf="data['errorKeys']?.includes('value')">
                    {{ 'dj-请输入' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- remove icon -->
            <i
              *ngIf="editable"
              adIcon
              iconfont="iconshanchuzhencetiaojianzu"
              aria-hidden="true"
              class="delete iconfont"
              (click)="handleDeleteSource(i)"
            ></i>
          </div>

          <div class="add-action-param" *ngIf="!!currentProject.sourceDetailAction.actionId && editable">
            <span class="add-area" (click)="handleAddSource()">
              <!-- <i adIcon iconfont="icondianji" aria-hidden="true" class="iconfont"> </i> -->
              {{ 'dj-新增参数' | translate }}
            </span>
          </div>
        </form>
      </div>
    </div>
  </nz-collapse-panel>
</nz-collapse>

<!--action开窗组件-->
<app-action-modal
  *ngIf="transferModal"
  [transferModal]="transferModal"
  [transferData]="transferData"
  (callBack)="handleSelectAction($event)"
  (closeModal)="transferModal = false"
>
</app-action-modal>
