import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { ProjectPropertyApiService } from '../../project-property-api.service';
import { ProjectSetService } from '../../../../project-widget/service/project-set.service';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { LocaleService } from 'common/service/locale.service';

@Component({
  selector: 'app-milestone',
  templateUrl: './milestone.component.html',
  styleUrls: ['./milestone.component.less'],
})
export class MilestoneComponent implements OnInit {
  @Input() currentProject: any;
  @Input() modalFlag: string;
  @Input() invalid: boolean;
  @Input() editable: boolean = true;

  @Output() patchValue = new EventEmitter<any>();
  @Output() update = new EventEmitter<any>();
  @Output() inited = new EventEmitter<any>();

  modalVisible = false;

  noTaskData = true;

  isCustom = false;
  loading: boolean;
  allTaskData: any = [];

  constructor(
    private fb: FormBuilder,
    private appService: AppService,
    private projectPropertyApiService: ProjectPropertyApiService,
    private projectSetService: ProjectSetService,
    private translateService: TranslateService,
    private languageService: LocaleService,
  ) {}

  onToggleMilestoneSetting(value) {
    this.currentProject.milestone = value;

    this.update.emit();
  }

  handlePatch(key: string, data: unknown): void {
    this.patchValue.emit({
      key,
      data,
    });
  }

  onSettingClick() {
    this.modalVisible = true;
  }

  handleCancel() {
    this.modalVisible = false;
  }

  handleOk(event: { phases: FormGroup[]; allTaskData: any[] }) {
    const { phases, allTaskData = [] } = event;
    this.currentProject.phases = phases;
    this.allTaskData = allTaskData;
    this.modalVisible = false;
    this.update.emit();
  }

  loadTasksByDataState(): void {
    const currentInit = this.currentProject.basicForm.get('init').value;
    const currentEnd = this.currentProject.basicForm.get('end').value;
    const currentGroupCode = this.currentProject.basicForm.get('groupCode').value;

    this.loading = true;

    const params = {
      startStateCode: currentInit,
      endStateCode: currentEnd,
      application: this.appService?.selectedApp?.code,
      groupCode: currentGroupCode,
    };
    this.projectPropertyApiService.loadTasksByDataState(params).subscribe(
      (res) => {
        this.loading = false;
        if (res.code === 0) {
          this.allTaskData = (res.data || []).map((s) => {
            return { ...s, checked: false };
          });
          this.noTaskData = this.allTaskData.length === 0;

          this.handleInit();
        }
      },
      () => {
        this.loading = false;
        this.noTaskData = true;
      },
    );
  }

  handleInit(): void {
    if (this.noTaskData) {
      this.currentProject.milestone = false;
      this.inited.emit();
      return;
    }

    if (this.modalFlag === 'add') {
      // 新增
      this.currentProject.milestone = true;
      this.addFristMilepost();
    } else {
      // 编辑
      const currentInit = this.currentProject.basicForm.get('init').value;
      const currentEnd = this.currentProject.basicForm.get('end').value;
      const currentGroupCode = this.currentProject.basicForm.get('groupCode').value;
      const { init, end, groupCode, phases, milestone } = this.projectSetService.orignProBaseInfo;

      if (currentInit === init && currentEnd === end && currentGroupCode === groupCode) {
        // 从上一步进来，且初始和结束状态没有改变
        this.currentProject.phases = phases;
        this.currentProject.milestone = milestone;
      } else {
        this.currentProject.phases = [];
        this.currentProject.milestone = true;
      }
      // 对于里程碑数据初始化变换
      if (this.currentProject.phases.length === 0) {
        this.addFristMilepost();
      } else {
        // 有里程碑
        this.transferPhases();
      }
    }

    this.inited.emit();
  }

  getBaseMilepost() {
    const nameTpl = this.translateService.instant('dj-里程碑名称');

    return this.fb.group({
      key: [new Date().getTime()],
      name: [nameTpl, [Validators.required]],
      code: [null, [Validators.required]],
      taskCodes: [[], [Validators.required]],
      disabled: [true],
      isHideKey: [false],
      lang: [
        {
          name: {
            en_US: '',
            zh_CN: '',
            zh_TW: '',
            [this.languageService.currentLanguage]: nameTpl,
          },
        },
      ],
    });
  }

  transferPhases(): void {
    this.currentProject.phases = this.currentProject.phases.map((element) => {
      const iForm = this.getBaseMilepost();
      iForm.patchValue({ ...element, disabled: false });
      return iForm;
    });
  }

  // 添加首个里程碑
  addFristMilepost(): void {
    const currentMilepost = this.getBaseMilepost();

    this.currentProject.phases = [currentMilepost];
  }

  ngOnInit(): void {
    this.loadTasksByDataState();
  }
}
