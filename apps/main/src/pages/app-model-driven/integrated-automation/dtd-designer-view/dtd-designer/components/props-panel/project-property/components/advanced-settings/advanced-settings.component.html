<nz-collapse>
  <nz-collapse-panel [nzHeader]="'dj-高级设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <form nz-form nzLayout="vertical" [formGroup]="currentProject['basicForm']" [nzNoColon]="true" class="form-info">
      <!-- 项目合并 -->
      <nz-form-item>
        <!-- <nz-form-label>{{ 'dj-开启合并配置' | translate }}</nz-form-label> -->
        <nz-form-control>
          <span class="title">{{ 'dj-开启合并配置' | translate }}</span>
          <nz-switch formControlName="merge" [nzDisabled]="!editable" nzSize="small"></nz-switch>
        </nz-form-control>
      </nz-form-item>

      <!-- 隐藏项目卡 -->
      <nz-form-item>
        <nz-form-control [nzAutoTips]="errorTips">
          <div class="item-title switch-item-title">
            {{ 'dj-隐藏项目卡' | translate }}
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              NzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-开启选项后，运行时将不展示项目卡' | translate"
            >
            </i>
            <nz-switch [nzDisabled]="!editable" formControlName="daemon" nzSize="small" style="margin-left: 8px"></nz-switch>
          </div>
        </nz-form-control>
      </nz-form-item>

      <ng-container *ngIf="currentProject['basicForm'].get('merge')?.value">
        <!-- 合并字段 -->
        <nz-form-item>
          <nz-form-label>{{ 'dj-合并字段' | translate }}</nz-form-label>
          <nz-form-control>
            <app-modal-multi
              class="full-width"
              [attr]="{ name: '合并字段' }"
              [editable]="editable"
              [value]="currentProject['meta'].mergeFields"
              (callBack)="handlePatch('mergeFields', $event)"
              formControlName="mergeFields"
              ngDefaultControl
            >
            </app-modal-multi>
          </nz-form-control>
        </nz-form-item>

        <!-- 项目时距 -->
        <nz-form-item>
          <nz-form-label>{{ 'dj-项目时距' | translate }}</nz-form-label>
          <nz-form-control>
            <app-modal-select
              [attr]="{ name: '项目时距', options: dateType }"
              [editable]="editable"
              [value]="currentProject['basicForm'].get('dueDateType')?.value"
              (callBack)="handlePatch('dueDateType', $event)"
              formControlName="dueDateType"
              ngDefaultControl
            >
            </app-modal-select>
          </nz-form-control>
        </nz-form-item>
      </ng-container>
    </form>
  </nz-collapse-panel>
</nz-collapse>
