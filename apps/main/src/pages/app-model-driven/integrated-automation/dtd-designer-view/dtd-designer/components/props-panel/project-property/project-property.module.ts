import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { InputModule } from 'components/form-components/input/input.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { AdTreeSelectModule } from 'components/ad-ui-components/ad-tree-select/ad-tree-select.module';
import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { ExtendedInfoModule } from 'components/bussiness-components/extended-info/extended-info.module';
import { ProjectWorkDesignModule } from 'components/page-design/entries/project-work-design/project-work-design.module';
import { ProjectWorkDesignNewModule } from 'components/page-design/entries/project-work-design-new/project-work-design-new.module';
import { ProjectWorkDesignNewMobileModule } from 'components/page-design/entries/project-work-design-new-mobile/project-work-design-new-mobile.module';
import { CaseModalModule } from 'components/bussiness-components/case-modal/case-modal.module';
import { ActionModalModule } from 'components/bussiness-components/action-modal/action-modal.module';
import { ExtendEditorModalModule } from 'components/bussiness-components/extend-editor-modal/extend-editor-modal.module';
import { TaskProjectCardDesignModule } from 'components/page-design/entries/task-project-card-design/task-project-card-design.module';

import { ProjectWidgetModule } from '../../project-widget/project-widget.module';
import { ProjectPropertyApiService } from './project-property-api.service';
import { ProjectPropertyComponent } from './project-property.component';
import { BasicInfoComponent } from './components/basic-info/basic-info.component';
import { AdvancedSettingsComponent } from './components/advanced-settings/advanced-settings.component';
import { MilestoneComponent } from './components/milestone/milestone.component';
import { DataPushComponent } from './components/data-push/data-push.component';
import { ResponserComponent } from './components/responser/responser.component';
import { MilestoneModalComponent } from './components/milestone-modal/milestone-modal.component';
import { TasksSelectComponent } from './components/tasks-select/tasks-select.component';
import { SourceComponent } from './components/source/source.component';
import { ReassignComponent } from './components/reassign/reassign.component';
import { DirectiveModule } from 'common/directive/directive.module';
import { PersonSettingModule } from './components/responser/person-setting/person-setting.module';
import { BusinessKeyModule } from 'components/bussiness-components/business-key/business-key.module';
import { FlowNodePropertyModule } from "pages/app-model-driven/integrated-automation/view-designer/components/flow-node-property/flow-node-property.module"

@NgModule({
  imports: [
    CommonModule,
    ProjectWidgetModule,
    NzCollapseModule,
    FormsModule,
    ReactiveFormsModule,
    AdSelectModule,
    NzFormModule,
    NzSpinModule,
    AdIconModule,
    TranslateModule,
    NzCheckboxModule,
    NzInputModule,
    InputModule,
    AdModalModule,
    NzMessageModule,
    AdTreeSelectModule,
    NzSwitchModule,
    AdTabsModule,
    NzTableModule,
    NzTabsModule,
    AdButtonModule,
    ExtendedInfoModule,
    NzMenuModule,
    ProjectWorkDesignModule,
    ProjectWorkDesignNewModule,
    ProjectWorkDesignNewMobileModule,
    CaseModalModule,
    ActionModalModule,
    ExtendEditorModalModule,
    TaskProjectCardDesignModule,
    DirectiveModule,
    NzToolTipModule,
    PersonSettingModule,
    BusinessKeyModule,
    FlowNodePropertyModule,
  ],
  declarations: [
    ProjectPropertyComponent,
    BasicInfoComponent,
    AdvancedSettingsComponent,
    MilestoneComponent,
    DataPushComponent,
    ResponserComponent,
    MilestoneModalComponent,
    TasksSelectComponent,
    SourceComponent,
    ReassignComponent,
  ],
  exports: [ProjectPropertyComponent],
  providers: [ProjectPropertyApiService],
})
export class ProjectPropertyComponentModule {}
