import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { Graph, Node } from '@antv/x6';
import { GraphService } from './service/graph.service';
import { ComponentRefKeys } from '../types';
import { AppService } from 'pages/apps/app.service';
import { to } from 'common/utils/core.utils';
import { ApiService } from './service/api.service';
import { Dnd } from '@antv/x6-plugin-dnd';
import { cloneDeep, isEmpty, set, delay } from 'lodash';
import { edgeAttrs, listItemStyles, registerTools } from './utils/graph.util';
import { TaskListComponent } from '../task/task-list.component';
import { DataStateListComponent } from '../data-state/data-state-list.component';
import { nodeOperatTools } from './utils/tool';
import { ModalsComponent } from './modals/modals.component';
import { GlobalService } from 'common/service/global.service';
import { AutoLayoutService } from './service/auto-layout.service';
import { ProjectListComponent } from '../project/project-list.component';
import { GraphHeaderComponent } from './graph-header/graph-header.component';
import { DtdDesignerViewService } from '../../service/dtd-designer-view.service';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { CompareModalComponent } from './components/compare-modal/compare-modal.component';
import { takeUntil } from 'rxjs/operators';
import { DtdDesignerComponent } from '../dtd-designer.component';
import { deepCopy } from '@angular-devkit/core/src/utils/object';

// 注册Tool
registerTools();
nodeOperatTools();

@Component({
  selector: 'dtd-designer-graph',
  exportAs: 'driveExecutionGraph',
  templateUrl: './graph.component.html',
  styleUrls: ['./graph.component.less'],
  providers: [GraphService],
})
export class GraphComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  @Input() taskComponentRef: TaskListComponent;
  @Input() dataStateComponentRef: DataStateListComponent;
  @Input() projectComponentRef: ProjectListComponent;
  @Input() dtdDesignerRef: DtdDesignerComponent;

  // 模板管理有关
  @Input() applicationCodeProxy: string;
  @Input() favouriteCode: string;
  @Input() params: any;

  @ViewChild('graphWrapper', { static: true }) graphWrapper: ElementRef<HTMLDivElement>;
  @ViewChild('graphContainer', { static: true }) graphContainer: ElementRef<HTMLDivElement>;
  @ViewChild('graphModalsRef') graphModalsRef: ModalsComponent;
  @ViewChild('graphHeaderRef') graphHeaderRef: GraphHeaderComponent;

  ref = this;
  loading: boolean = true; // 加载状态
  graph: Graph; // 画布实例
  dnd: Dnd;
  taskNodeMap: Map<string, any> = new Map(); // 画布节点映射表<taskCode, nodeData> 注意：该属性仅用于判断任务是否在画布中，value可能是不完整的任务数据
  stateNodeMap: Map<string, any> = new Map(); // 画布节点映射表<stateCode, nodeData>注意：该属性仅用于判断任务是否在画布中，value可能是不完整的状态数据
  loadingTips: any;
  validMap = new Map<string, boolean>(); // 未通过校验的节点
  isShowAlert: boolean = true;

  public compareData: { data: { dataStateTaskInfo: any; graph: any; [key: string]: any } } | null;
  public showCompareModal: boolean = false;
  get dataStateComponent(): DataStateListComponent {
    return this.graphService.getComponentRef(ComponentRefKeys.DATA_STATE) as DataStateListComponent;
  }

  get taskComponent(): TaskListComponent {
    return this.graphService.getComponentRef(ComponentRefKeys.TASK) as TaskListComponent;
  }

  get taskList() {
    return this.taskComponent?.originTaskList || [];
  }

  get dataStateMap() {
    return this.dataStateComponent?.dataStateMap;
  }
  get originDataStateList() {
    return this.dataStateComponent?.originDataStateList;
  }

  get isGraphChanged() {
    return this.graphService.isGraphChanged;
  }

  set isGraphChanged(val) {
    this.graphService.isGraphChanged = val;
  }

  get modalsRef() {
    return this.graphModalsRef;
  }

  get isBCApp() {
    return this.appService?.selectedApp?.tag?.sourceComponent === 'BC';
  }

  get panelShow() {
    return this.dtdDesignerRef?.visible;
  }

  showDrawer: boolean = false;
  errorNode: any = [];

  destroy$ = new Subject();

  constructor(
    public graphService: GraphService,
    private autoLayoutService: AutoLayoutService,
    private appService: AppService,
    public apiService: ApiService,
    private globalService: GlobalService,
    public designerViewService: DtdDesignerViewService,
    private modal: NzModalService,
    private translateService: TranslateService,
  ) {
    this.graphService.setComponentRef(ComponentRefKeys.GRAPH, this);
    this.designerViewService.showNodesError$.pipe(takeUntil(this.destroy$)).subscribe((res) => {
      const { showErrorNotByResult, errorData } = res;
      if (!isEmpty(errorData) || showErrorNotByResult) {
        this.errorNode = errorData || [];
        this.showDrawer = true;
      } else {
        this.errorNode = [];
        this.showDrawer = false;
      }
    });
    this.designerViewService.showCompareGraphSubject$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.fetchGraphData({
        isCaptureComparePng: true,
      });
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.applicationCodeProxy?.currentValue) {
      this.apiService.applicationCodeProxy = this.applicationCodeProxy;
    }
    if (changes.favouriteCode?.currentValue) {
      this.apiService.headerCofig = { templateId: this.favouriteCode };
      this.graphService.favouriteCode = this.favouriteCode;
      this.graph?.zoomTo(1);
      if (!changes.favouriteCode?.isFirstChange()) {
        this.fetchGraphData({
          showError: true,
        });
      }
    }
    if (changes.taskComponentRef?.currentValue) {
      this.graphService.setComponentRef(ComponentRefKeys.TASK, changes.taskComponentRef.currentValue);
    }
    if (changes.dataStateComponentRef?.currentValue) {
      this.graphService.setComponentRef(ComponentRefKeys.DATA_STATE, changes.dataStateComponentRef.currentValue);
    }
    if (changes.params) {
      this.graphService.panelParams = changes.params.currentValue;
    }
  }

  ngOnDestroy(): void {
    // 注销组件引用
    this.graphService.clearComponentRefs();
    this.destroy$.next();
    this.destroy$.complete();
  }

  async ngOnInit() {
    // 初始化
    this.init();

    this.taskComponentRef?.fetchTaskList$?.subscribe((taskList) => {
      // 根据 taskList 组装 taskOutputMap
      this.graphService.transformOutput(taskList);
    });

    (window as any).graph = this.graph;
  }

  ngAfterViewInit(): void {}

  public getGraph() {
    return this.graph;
  }

  async init() {
    this.initGraph(); // 初始化画布实例
    // await this.fetchGraphData({
    //   showError: true,
    // }); // 获取画布数据及渲染
    // // 通知项目列表组件：画布加载完成
    // this.projectComponentRef?.emitGraphComplete();
    // 用于节点批量操作时可能会导致多次接口请求，使用该Subject做防抖处理
    // this.modalsRef?.subscribeBatch();
  }

  /**
   * 该方法在 view-header 组件中调用
   * 获取画布数据及渲染
   */
  async initPage() {
    await this.fetchGraphData({
      showError: true,
    }); // 获取画布数据及渲染
    // 通知项目列表组件：画布加载完成
    this.projectComponentRef?.emitGraphComplete();
    // 用于节点批量操作时可能会导致多次接口请求，使用该Subject做防抖处理
    this.modalsRef?.subscribeBatch();
  }

  async initDataByVersion() {
    await this.fetchGraphData({
      showError: true,
    }); // 获取画布数据及渲染
    // 通知项目列表组件：画布加载完成
    this.projectComponentRef?.emitGraphComplete();
  }

  initGraph() {
    if (this.graph) {
      this.graph.dispose();
    }
    const graph = this.graphService.initGraph(this.graphContainer.nativeElement, this.graphWrapper.nativeElement);
    this.dnd = new Dnd({ target: graph, getDropNode: (node) => this.endDrag(node) });
    this.graph = graph;
    this.autoLayoutService.graph = graph;
    ['node:change:position', 'node:added', 'node:removed'].forEach((eventName) => {
      this.graph.on(eventName, () => {
        if (!this.globalService.hollowPromise) return;
        if (this.loading) {
          return;
        }
        this.graphService.isGraphChanged = true;
      });
    });

    this.graph.on('node:added', ({ node }) => {
      // 对新增到画布上的节点进行验证样式添加
      const valid = this.taskComponentRef?.validMap?.get(node.data.code);
      if (!!valid) {
        this.setNodeValidateState(node.data.code, node.data.type, false);
      }
    });
  }

  /**
   *
   * @param data showError 是否展示错误抽屉
   * @param data showErrorNotByResult  展示错误抽屉。 并且无错误的时候，抽屉也show，只不过是空值
   * @param data isCaptureComparePng 版本对比功能
   * @returns
   */
  async fetchGraphData(
    data: { showError?: boolean; showErrorNotByResult?: boolean; isCaptureComparePng?: boolean } = {},
  ) {
    const { showError, showErrorNotByResult, isCaptureComparePng } = data || {};
    this.loading = true;
    const { code, groupCode } = this.appService.selectedApp || {};
    const [err, res] = await to(this.apiService.fetchGraphData(code, groupCode, isCaptureComparePng).toPromise());
    if (res?.code === 0) {
      this.loading = false;
      // 版本对比功能
      if (isCaptureComparePng) {
        this.handleCompareGraphVersion(deepCopy(res));
        return;
      }
      // 展示错误信息
      showError && this.handleNodeErrors(res, showErrorNotByResult);
      this.handleGraphCells(res);
    }
    if (err) {
      this.designerViewService.rebackSelectedVersion();
    }
  }

  /**
   * 处理两个版本的数据对比差异
   */
  handleCompareGraphVersion(res) {
    this.showCompareModal = true;
    this.compareData = res?.data || {};
  }

  async handleGraphCells(newRes) {
    newRes.data.graph?.cells?.forEach(this.graphService.intercept); // 对原数据处理
    this.dtdLayout(newRes.data); // 布局，渲染画布
    this.initTaskAndStateMap(newRes.data.graph.cells); // 初始化任务和数据状态节点映射表
    await this.renderGraph(); // 渲染画布
  }

  /**
   * 渲染画布
   * 1.如果该应用是引用的，由于中台过来的数据可能会更新，以data.dataStateTaskInfo为准
   * 是否走自动布局需要判断graph.cells中是否有相同id的节点，如果没有则走自动布局，如果有则使用graph.cells的样式及坐标
   * 2.非引用应用按之前逻辑渲染(是否自动布局 ? autoLayout : rednerGraph)
   * @param data 画布数据
   */
  async dtdLayout(data) {
    const autoLayoutNodes = data.dataStateTaskInfo.nodes || [];
    const graphCells = data.graph.cells || [];
    const isReference = this.appService.selectedApp?.tag?.sourceComponent === 'BC'; // 是否是引用应用

    if (isReference) {
      // 引用的DTD，以data.dataStateTaskInfo数据为准，
      const normalNodes = []; // 引用dtd中可正常渲染的节点（这类节点是开发平台用户保存过的）
      const incompleteNodes = []; // 引用dtd中不完整节点（这类节点是中台新推的,开发平台未保存过的)

      autoLayoutNodes.forEach((node) => {
        const type = node.labels[0].toLocaleLowerCase();
        const matchNode = graphCells.find(
          (n) => n?.data?.code === `${node?.properties?.code}` && type === n.data?.type,
        );

        if (matchNode) {
          // 若引用节点在本地中存在，表示之前用户在本地保存过引用节点，则使用本地节点样式及坐标
          const nodeProps = this.autoLayoutService.referenceToNormal(node, matchNode);
          normalNodes.push(nodeProps);
        } else {
          // 否则走自动布局
          incompleteNodes.push(node);
        }
      });
      this.graph?.clearCells();
      // 布局
      this.autoLayoutService.autoLayout({
        nodes: incompleteNodes,
        normalNodes: normalNodes,
        relationships: data.dataStateTaskInfo.relationships,
      });
      this.handleChangeVersionCanNotSave([...incompleteNodes, ...normalNodes]);
      return;
    }

    if (!data.graph?.autoLayout) {
      this.graph?.clearCells();
      this.autoLayoutService.autoLayout({
        nodes: data.dataStateTaskInfo.nodes || [],
        normalNodes: [],
        relationships: data.dataStateTaskInfo.relationships,
      });
      this.handleChangeVersionCanNotSave([...(data?.dataStateTaskInfo?.nodes || [])]);
    } else {
      this.setGraphData(data); // 渲染画布
    }
  }

  setGraphData(data) {
    data.graph.cells.forEach((c) => {
      // 去除
      if (c.shape !== 'edge') c.ports = {};
    });
    this.graph.clearCells();
    this.graph.fromJSON({ cells: data.graph.cells || [] });
    this.designerViewService.isChangeVersionCanNotSave = false;
  }

  /**
   * 根据画布数据渲染画布
   * @param cellsData
   */
  renderGraph() {
    return new Promise((resolve) => {
      delay(() => {
        const nodes = this.graph.getNodes().filter((node) => node.isVisible());
        const { l, t, r, b } = this.getNodesRect(nodes) || { l: 0, t: 0, r: 0, b: 0 };
        this.graph.centerPoint(l + (r - l) / 2, t + (b - t) / 2);
        resolve(true);
      }, 200);
    });
  }

  private getOneNodeRect(node: Node<Node.Properties>) {
    const position = node.position();
    const size = node.size();
    return {
      l: position.x,
      t: position.y,
      r: position.x + size.width,
      b: position.y + size.height,
    };
  }

  private getNodesRect(nodes: Node<Node.Properties>[]) {
    if (nodes.length) {
      const firstRect = this.getOneNodeRect(nodes[0]);
      let minX = firstRect.l;
      let minY = firstRect.t;
      let maxX = firstRect.r;
      let maxY = firstRect.b;
      for (let i = 1; i < nodes.length; i++) {
        const { l, t, r, b } = this.getOneNodeRect(nodes[i]);
        minX = Math.min(minX, l);
        minY = Math.min(minY, t);
        maxX = Math.max(maxX, r);
        maxY = Math.max(maxY, b);
      }
      return {
        l: minX,
        t: minY,
        r: maxX,
        b: maxY,
      };
    }
    return undefined;
  }

  /**
   * 初始化任务和数据状态节点映射表，用于判断任务列表中的任务是否在画布中
   * @param cellsData
   */
  initTaskAndStateMap(cellsData: any[] = []) {
    this.taskNodeMap.clear();
    this.stateNodeMap.clear();
    cellsData.forEach((item) => {
      if (item.shape === 'edge') return;
      if (item.data.type === 'task') {
        this.taskNodeMap.set(item.data.code, item);
      }
      if (item.data.type === 'datastate') {
        this.stateNodeMap.set(item.data.code, item);
      }
    });
  }

  /**
   * 添加映射到任务和状态节点映射表
   * @param data
   * @param type 'task'|'datastate'
   */
  addToTaskStateMap(data, type) {
    if (type === 'task') {
      if (this.taskNodeMap.has(data.code)) return;
      this.taskNodeMap.set(data.code, data);
    }

    if (type === 'datastate') {
      if (this.stateNodeMap.has(data.code)) return;
      this.stateNodeMap.set(data.code, data);
    }
  }

  /**
   * 根据 value 选中节点
   * @param value 任务或数据状态的值
   */
  public selectNodeByCode(code: string) {
    const node = this.graph.getNodes().find((node) => node.data?.code === code);

    if (!node || !node.isVisible()) return;

    this.graph.cleanSelection();
    this.graph.select(node);
    this.graph.centerCell(node);
  }

  /**
   * 从左侧列表中拖拽到画布上
   * @param e 事件对象
   * @param itemData 拖拽的数据
   * @param type task|datastate
   */
  public startDrag(e, itemData, type) {
    const nodeProps: any = cloneDeep(listItemStyles);
    nodeProps.attrs.label.text = itemData.name;

    nodeProps.data = { ...itemData, nodeType: type };
    const node = this.graph.createNode(nodeProps);

    if (node) {
      this.dnd.start(node, e);
    }
  }

  /**
   * 拖拽结束
   * @param node
   * @returns
   */
  endDrag(node: Node) {
    const { nodeType, ...itemData } = node.getData();
    const newNode = this.graphService.createNode(this.graph, itemData, nodeType);

    if (newNode) {
      if (nodeType === 'task') {
        this.taskNodeMap.set(itemData.code, itemData);
      }
      if (nodeType === 'datastate') {
        this.stateNodeMap.set(itemData.code, itemData);
      }
      return newNode;
    }
  }

  /**
   * 该方法用于左侧列表中任务或状态的删除
   * 因此该方法里没有对stateMaps做处理
   * @param code
   * @param type
   */
  public deleteNodeByCode(code, type) {
    const node = this.graph.getNodes().find((node) => {
      const nodeData = node.getData();
      return code === nodeData.code && type === nodeData.type;
    });
    if (node) {
      this.graph.unselect(node);
      this.graph.removeNode(node);
      // 删除节点映射
      if (type === 'task') {
        this.taskNodeMap.delete(code);
      }
      if (type === 'datastate') {
        this.stateNodeMap.delete(code);
      }
      this.saveGraph(false);
    }
  }

  /**
   * 保存画布
   * @isNeesTips 是否需要提示，默认true
   */
  async saveGraph(isNeesTips: boolean = true) {
    this.loading = true;
    await this.graphService.saveGraph(this.graph, isNeesTips, this.designerViewService.isChangeVersionCanNotSave);
    this.loading = false;
    this.designerViewService.isChangeVersionCanNotSave = false;
  }

  /**
   * 新增任务节点到画布
   * 默认添加至画布中心，传position参数可指定位置
   * @param code
   * @param name
   * @param position 指定位置
   */
  public addTaskNodeToGraph(taskData, position?: { x: number; y: number }): void {
    const positionData = position || this.graphService.getGraphCenter(this.graph, this.graphWrapper);
    this.graphService.addTaskNodeToGraph(this.graph, taskData, positionData);
  }

  public addDataStateNodeToGraph(code, dataCode, name, position?: { x: number; y: number }): void {
    const positionData = position || this.graphService.getGraphCenter(this.graph, this.graphWrapper);
    this.graphService.addDataStateNodeToGraph(this.graph, code, dataCode, name, positionData);
  }

  /**
   * 获取画布可视区的中心坐标
   * @returns
   */
  public getGraphCenter() {
    return this.graphService.getGraphCenter(this.graph, this.graphWrapper);
  }

  /**
   * 更新画布中节点的名称
   * @param nodeType 'task'|'datastate'
   * @param code
   * @param name
   */
  public updateNodeName(nodeType: string, code: string, name: string, lang: any): void {
    const node = this.graph.getNodes().find((node) => {
      const { type, code: nodeCode } = node.getData();
      return code === nodeCode && type === nodeType;
    });
    if (node) {
      node.setData({
        ngArguments: {
          name,
          lang,
        },
      });
    }
  }

  handlePasting({ loading, tips }) {
    this.loading = loading;
    this.loadingTips = tips;
  }

  public isInGraph(code, type) {
    return this.graphService.isInGraph(this.graph, code, type);
  }

  /**
   * 设置节点校验状态
   * 将属性面板的验证结果在对应节点上通过样式体现出来
   * @param code 任务code或状态code
   * @param nodeType 'task'|'datastate' 节点类型 任务节点or数据状态节点
   * @param valid 验证结果 通过true 未通过false
   */
  public setNodeValidateState(code: string, nodeType: 'task' | 'datastate', valid: boolean): void {
    this.graphService.setNodeValidateState(this.graph, code, nodeType, valid);
    if (valid) {
      this.validMap.delete(nodeType + code);
      return;
    }
    this.validMap.set(nodeType + code, true);
  }

  /**
   * 展示错误信息
   * @param data
   */
  handleNodeErrors(data, showErrorNotByResult) {
    const { dataStateTaskInfo } = data.data;
    const errorData = dataStateTaskInfo?.nodes?.filter(
      (node) => node.properties?.adpStatus === 'draft' && node.properties?.bcReference,
    );
    this.designerViewService._showNodesError$.next({
      showErrorNotByResult,
      errorData: errorData,
    });
  }

  /**
   * 获取画布报错信息
   */
  handleShowError() {
    this.fetchGraphData({
      showError: true,
      showErrorNotByResult: true,
    });
  }

  /**
   * 关闭抽屉
   */
  handleCloseDrawer() {
    this.showDrawer = false;
    this.errorNode = null;
  }

  /**
   * 选中节点
   * @param data
   */
  async handleFocusNode(data) {
    console.log(data, 'datadata');
    const canPass = await this.designerViewService.checkPropsPanelCanContinue(true);
    if (!canPass) return;
    if (data.labels[0].toLocaleLowerCase() === 'task') {
      const itemData = this.taskComponentRef?.taskList?.find((item) => item.code === data?.properties?.code);
      if (isEmpty(itemData)) return;
      // 开属性面板
      this.dtdDesignerRef?.handleOpenPanel({
        ...itemData,
        taskCategory: itemData.adpType || itemData.driveType || 'flow',
        type: 'task',
      });
    } else if (data.labels[0].toLocaleLowerCase() === 'datastate') {
      const itemData = this.dataStateComponentRef?.dataStateList
        .flatMap((item) => item.dataStateList)
        ?.find((item) => item.code === data?.properties?.code);
      if (isEmpty(itemData)) return;
      this.dtdDesignerRef?.handleOpenPanel({ ...itemData, type: 'datastate' });
    }
    this.selectNodeByCode(data?.properties?.code);
  }

  handleChangeVersionCanNotSave(node) {
    if (!node || node?.length === 0) {
      this.designerViewService.isChangeVersionCanNotSave = false;
    }
  }

  /**
   * 清楚节点
   * @param item
   */
  async handleClearNode(data) {
    const { item, cb } = data;
    const currentCell = (this.graph.getCells() || []).find((node) => {
      const { type, code } = node.getData();
      return code === item.properties.code && type === item.labels[0].toLocaleLowerCase();
    });
    if (currentCell) {
      await this.graphService.handleRemoveNodes(this.graph, [currentCell as Node]);
      this.taskComponent?.fetchTasks();
      if (cb) cb();
    }
  }
}
