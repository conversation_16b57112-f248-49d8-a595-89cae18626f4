<nz-collapse>
  <nz-collapse-panel [nzHeader]="'dj-推送数据' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <form class="form-content" nz-form nzLayout="vertical" [nzNoColon]="true">
      <div class="push-item" *ngFor="let data of currentProject.atmcDatas; let i = index">
        <!-- 流程变量名 -->
        <nz-form-item>
          <nz-form-label nzRequired>{{ 'dj-流程变量名' | translate }}</nz-form-label>

          <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('proVarKey') ? 'error' : 'success'">
            <div class="atmc">
              <app-modal-input
                [innerLabel]="false"
                [attr]="{
                  name: '流程变量名',
                  required: true,
                  error: data['errorKeys']?.includes('proVarKey'),
                  readOnly: !editable
                }"
                [value]="data.proVarKey"
                (callBack)="handlePushData(i, 'proVarKey', $event)"
              >
              </app-modal-input>

              <div class="error-text" *ngIf="data['errorKeys']?.includes('proVarKey')">
                {{ 'dj-请输入' | translate }}
              </div>
            </div>
          </nz-form-control>
        </nz-form-item>

        <!-- athena变量名 -->
        <nz-form-item>
          <nz-form-label nzRequired>{{ 'dj-athena变量名' | translate }}</nz-form-label>
          <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('athenaKey') ? 'error' : 'success'">
            <div class="atmc">
              <app-modal-input
                [innerLabel]="false"
                [attr]="{
                  name: 'athena变量名',
                  required: true,
                  error: data['errorKeys']?.includes('athenaKey'),
                  readOnly: !editable
                }"
                [value]="data.athenaKey"
                (callBack)="handlePushData(i, 'athenaKey', $event)"
              >
              </app-modal-input>

              <div class="error-text" *ngIf="data['errorKeys']?.includes('athenaKey')">
                {{ 'dj-请输入' | translate }}
              </div>
            </div>
          </nz-form-control>
        </nz-form-item>

        <i
          *ngIf="editable"
          adIcon
          iconfont="iconshanchuzhencetiaojianzu"
          aria-hidden="true"
          (click)="handleDeletePush(i)"
          class="delete iconfont"
        ></i>
      </div>
    </form>

    <div *ngIf="editable" class="add-push" (click)="handleAddPush()">
      <span class="add-area">
        <!-- <i adIcon iconfont="icondianji" aria-hidden="true" class="iconfont"></i> -->
        {{ 'dj-新增参数' | translate }}
      </span>
    </div>
  </nz-collapse-panel>
</nz-collapse>
