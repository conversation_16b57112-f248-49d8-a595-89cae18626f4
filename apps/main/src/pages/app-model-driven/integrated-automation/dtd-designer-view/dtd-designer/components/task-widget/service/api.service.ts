import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { AdUserService } from 'pages/login/service/user.service';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';

@Injectable()
export class ApiService {
  adesignerUrl: string;

  applicationCodeProxy: string;
  headerConfig = {};
  isTenantActive = false; // 是否租户激活

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public adUserService: AdUserService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  loadCurrentTaskData(params: any): Observable<any> {
    const { code, bcReference, ...rest } = params;
    let header = bcReference ? rest : this.dtdDesignerViewService.adpVersionHeaders;
    const url = `${this.adesignerUrl}/athena-designer/task/getTask/${code}`;
    return this.http.get(url, { headers: Object.assign({}, this.headerConfig, header) });
  }

  deleteTaskTree(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/deleteTask/${param}`;
    return this.http.get(url, {
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  loadTaskData(applicationCode: string): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      applicationCode = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/task/taskTree`;
    return this.http.get(url, {
      params: { applicationCode },
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  // 获取task数据
  loadLcdpDataEntryData(applicationCode: string): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      applicationCode = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/lcdpDataEntry/${applicationCode}`;
    return this.http.get(url);
  }

  loadDataStates(params: any): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      params.application = this.applicationCodeProxy;
      params.groupCode = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/data/findDataStatesByApplication`;
    return this.http.get(url, {
      params,
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  saveTask(flag: any, param: any): Observable<any> {
    if (this.isTenantActive) {
      // 租户模式下只有编辑
      const url = `${this.adesignerUrl}/athena-designer/tenant/task/updateTenantTask`;
      if (this.applicationCodeProxy) {
        param.application = this.applicationCodeProxy;
      }
      return this.http.post(url, param, {
        headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.tenantVersionHeaders),
      });
    } else {
      const url = {
        add: `${this.adesignerUrl}/athena-designer/task/saveTask`, // 保存
        edit: `${this.adesignerUrl}/athena-designer/task/updateTask`, // 编辑
      };
      if (this.applicationCodeProxy) {
        param.application = this.applicationCodeProxy;
      }
      return this.http.post(url[flag], param, {
        headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
      });
    }
  }

  // 根据任务code获取数据特征
  getDataFeatures(params): Observable<any> {
    const { code, bcReference, ...rest } = params;
    let header = bcReference ? rest : this.dtdDesignerViewService.adpVersionHeaders;
    const url = `${this.adesignerUrl}/athena-designer/data/dataFeatures/${code}`;
    return this.http.get(url, {
      headers: Object.assign({}, this.headerConfig, header),
    });
  }

  /**
   * 获取决策模型下拉选项
   * @returns
   */
  getDecisionList(appCode: string) {
    const url = `${this.adesignerUrl}/athena-designer/data/findDecisionByApplication?application=${appCode}`;
    return this.http.get(url, { headers: this.dtdDesignerViewService.adpVersionHeaders });
  }
}
