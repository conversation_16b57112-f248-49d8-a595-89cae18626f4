import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'data-state-list-item',
  templateUrl: './data-state-list-item.component.html',
  styleUrls: ['./data-state-list-item.component.less'],
})
export class DataStateListItemComponent implements OnInit, OnChanges {
  @Input() itemData: any;
  @Input() selectedDataState: any;
  @Input() graphStateNodeMap: Map<string, any>;
  @Input() validMap: Map<string, any>;
  @Input() editable: boolean = true;
  @Output() itemMouseDown: EventEmitter<any> = new EventEmitter();
  @Output() itemDelete: EventEmitter<any> = new EventEmitter();
  @Output() itemEdit: EventEmitter<any> = new EventEmitter();

  childrenVisible: boolean = false;

  get isShowGroup() {
    if (
      this.itemData?.dataStateList?.length > 0 &&
      this.itemData?.dataStateList?.some((item) => !item.bcReference) > 0
    ) {
      return true;
    }
    return false;
  }

  get dtdReference() {
    return !(this.appService?.selectedApp?.tag?.sourceComponent === 'BC');
  }

  constructor(private appService: AppService) {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges): void {}

  handleMouseDown(e, itemData, isInGraph) {
    e.stopPropagation();
    this.itemMouseDown.emit({ e, itemData, isInGraph });
  }

  handleDelete(itemData) {
    this.itemDelete.emit(itemData);
  }

  handleEdit(itemData) {
    this.itemEdit.emit(itemData);
  }

  toggleChildrenVisible() {
    this.childrenVisible = !this.childrenVisible;
  }
}
