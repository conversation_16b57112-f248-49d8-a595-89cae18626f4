import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { AdUserService } from 'pages/login/service/user.service';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';
@Injectable()
export class ApiService {
  adesignerUrl: string;
  headerCofig = {};
  applicationCodeProxy;
  isTenantActive = false; // 是否租户激活

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public adUserService: AdUserService,
    private dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  saveDataState(param: any): Observable<any> {
    if (this.isTenantActive) {
      if (this.applicationCodeProxy) {
        param.application = this.applicationCodeProxy;
      }
      const url = `${this.adesignerUrl}/athena-designer/tenant/data/saveTenantDataState`;
      return this.http.post(url, param, {
        headers: Object.assign({}, this.headerCofig, this.dtdDesignerViewService.tenantHeaders),
      });
    } else {
      if (this.applicationCodeProxy) {
        param.application = this.applicationCodeProxy;
      }
      const url = `${this.adesignerUrl}/athena-designer/data/saveDataState`;
      return this.http.post(url, param, { headers: this.headerCofig });
    }
  }

  /**
   * 获状态数据
   * @param application
   * @param groupCode
   * @returns
   */
  fetchDataList(application: string, groupCode: string = ''): Observable<any> {
    if (this.applicationCodeProxy) {
      application = this.applicationCodeProxy;
      groupCode = this.applicationCodeProxy;
    }
    return this.http.get(`${this.adesignerUrl}/athena-designer/task/getDataList`, {
      params: { application, groupCode },
      headers: this.headerCofig,
    });
  }
}
