import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';

import { Subject } from 'rxjs/internal/Subject';

@Component({
  selector: 'app-task-scheme-design',
  templateUrl: './scheme-design.component.html',
  styleUrls: ['./scheme-design.component.less'],
})
export class SchemeDesignComponent implements OnInit {
  @Input() currentTask: any;
  @Input() extra: any;
  @Input() dataFormGroup: any;
  @Input() isFromDtdReference: boolean = false; // 是否来自dtd引用
  @Input() editable: boolean = true;
  @Output() dataChange: EventEmitter<any> = new EventEmitter();
  @Output() translateLoading: EventEmitter<any> = new EventEmitter();

  // plansFormGroup: any;
  public lang: Record<
    string,
    {
      zh_CN: string;
      zh_TW: string;
      en_US: string;
    }
  > = {};

  constructor(private languageService: LocaleService, private fb: FormBuilder, public translate: TranslateService) {}

  public readonly errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  private destory$ = new Subject();
  ngOnInit(): void {
    this.init();
  }
  ngOnChanges(change) {
    this.init();
  }
  ngOnDestroy(): void {
    this.destory$.next();
    this.destory$.complete();
  }
  init(): void {
    if (this.currentTask?.resolvePlans?.length) {
      this.handleResolveLang();
      this.dataFormGroup.get('resolvePlans').clear();
      this.currentTask.resolvePlans.forEach((plan) => {
        this.dataFormGroup.get('resolvePlans').push(
          this.fb.group({
            planId: [plan.planId || '', [Validators.required]],
            planName: [plan.planName || '', [Validators.required]],
            lang: [plan.lang],
            projectCode: [plan.projectCode || '', this.currentTask.type === 'solve' ? [Validators.required] : null],
          }),
        );
      });
    } else if (this.currentTask?.resolvePlans?.length === 0 || !this.currentTask?.resolvePlans) {
      this.dataFormGroup.get('resolvePlans').clear();
    }
  }
  // 异常排除数据
  handleException(key: any, data: any, index?: number): void {
    if ([undefined, null].includes(index)) {
      this.currentTask[key] = data.value;
    } else {
      this.currentTask.resolvePlans[index][key] = data.value;
      if (data.needLang) {
        this.currentTask.resolvePlans[index].lang = {
          ...(this.currentTask.resolvePlans[index].lang || {}),
          [key]: data.lang,
        };
      }
    }
  }

  // 增加异常排除
  handleAddException(): void {
    const temp = this.fb.group({
      planId: ['', [Validators.required]],
      planName: ['', [Validators.required]],
      lang: [this.lang],
      projectCode: ['', this.currentTask.type === 'solve' ? [Validators.required] : null],
    });

    (this.dataFormGroup.get('resolvePlans') as FormArray).push(temp);
    this.currentTask.resolvePlans?.push(temp.value);
    const index = this.dataFormGroup.get('resolvePlans').value.length - 1;
    this.dataFormGroup.get('resolvePlans').controls[index].get('planId').markAsDirty();
    this.dataFormGroup.get('resolvePlans').controls[index].get('planId').updateValueAndValidity({ onlySelf: true });
    this.dataFormGroup.get('resolvePlans').controls[index].get('planName').markAsDirty();
    this.dataFormGroup.get('resolvePlans').controls[index].get('planName').updateValueAndValidity({ onlySelf: true });
    if (this.currentTask.type === 'solve') {
      this.dataFormGroup.get('resolvePlans').controls[index].get('projectCode').markAsDirty();
      this.dataFormGroup
        .get('resolvePlans')
        .controls[index].get('projectCode')
        .updateValueAndValidity({ onlySelf: true });
    }
  }

  // 删除异常排除
  handleDeleteException(index: any): void {
    (this.dataFormGroup.get('resolvePlans') as FormArray).removeAt(index);
  }

  // 修改方案
  handlePlansData(index, key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.get('resolvePlans').controls[index].patchValue({ [key]: data?.value, lang: this.lang });
    this.dataFormGroup.get('resolvePlans').controls[index].get(key).markAsDirty();
    this.dataFormGroup.get('resolvePlans').controls[index].get(key).updateValueAndValidity({ onlySelf: true });
  }
  // 处理异常排除中的多语言
  handleResolveLang(): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    (this.currentTask?.resolvePlans || []).forEach((s) => {
      const { planName, lang } = s;
      const temp = {
        zh_CN: lang?.planName?.zh_CN || '',
        zh_TW: lang?.planName?.zh_TW || '',
        en_US: lang?.planName?.en_US || '',
      };
      if (!temp[language]) {
        temp[language] = planName || '';
      }
      s['lang'] = { planName: temp };
    });
  }
  /**
   * 是否有文案正在翻译
   * @param loading
   */
  public handleTranslateLoading(event: { loading: boolean }) {
    this.translateLoading.emit({ loading: event.loading });
  }
}
