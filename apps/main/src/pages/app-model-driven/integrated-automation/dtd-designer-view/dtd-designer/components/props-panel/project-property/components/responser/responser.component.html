<nz-collapse>
  <nz-collapse-panel [nzHeader]="header" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <form nz-form nzLayout="vertical" [nzNoColon]="true" *ngIf="!currentProject?.executor">
      <!-- 人员选择策略 -->
      <nz-form-item>
        <nz-form-label nzRequired>{{ 'dj-人员选择策略' | translate }}</nz-form-label>
        <nz-form-control
          [nzValidateStatus]="currentProject.personInCharge['errorKeys'] === 'choosePolicy' ? 'error' : 'success'"
        >
          <div class="head">
            <app-modal-select
              [innerLabel]="false"
              [attr]="{
                name: '人员选择策略',
                options: choosePolicy,
                required: true,
                error: currentProject.personInCharge['errorKeys'] === 'choosePolicy',
                readOnly: !editable
              }"
              [value]="currentProject.personInCharge.choosePolicy"
              (callBack)="handleChargeData('choosePolicy', $event)"
            ></app-modal-select>
            <div class="error-text" *ngIf="currentProject.personInCharge['errorKeys'] === 'choosePolicy'">
              {{ 'dj-请选择' | translate }}
            </div>
          </div>
        </nz-form-control>
      </nz-form-item>

      <!-- 条件组？ -->
      <div class="charge-data" *ngFor="let data of currentProject.personInCharge?.identities; let i = index">
        <div class="content">
          <div class="checkData">
            <label nz-checkbox [nzDisabled]="!editable" [(ngModel)]="data.isBusiness" [ngModelOptions]="{ standalone: true }"></label>
            <span class="word">isBusiness</span>
          </div>
          <div class="others">
            <!-- 用户类型 -->
            <nz-form-item>
              <nz-form-label nzRequired>{{ 'dj-用户类型' | translate }}</nz-form-label>
              <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('performerType') ? 'error' : 'success'">
                <div class="duty">
                  <app-modal-select
                    [innerLabel]="false"
                    [attr]="{
                      name: '用户类型',
                      options: performerType,
                      required: true,
                      error: data['errorKeys']?.includes('performerType') && currentProject.formDirty,
                      readOnly: !editable
                    }"
                    [value]="data.performerType"
                    (callBack)="handleChargeData('performerType', $event, i)"
                  ></app-modal-select>

                  <div
                    class="error-text"
                    *ngIf="data['errorKeys']?.includes('performerType') && currentProject.formDirty"
                  >
                    {{ 'dj-请选择' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- 名称 -->
            <nz-form-item>
              <nz-form-label nzRequired>{{ 'dj-名称' | translate }}</nz-form-label>
              <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('performerName') ? 'error' : 'success'">
                <div class="duty">
                  <app-modal-input
                    [innerLabel]="false"
                    [attr]="{
                      name: '名称',
                      required: true,
                      error: data['errorKeys']?.includes('performerName') && currentProject.formDirty,
                      readOnly: !editable
                    }"
                    [value]="data.performerName"
                    (callBack)="handleChargeData('performerName', $event, i)"
                  >
                  </app-modal-input>
                  <div
                    class="error-text"
                    *ngIf="data['errorKeys']?.includes('performerName') && currentProject.formDirty"
                  >
                    {{ 'dj-请输入' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- 变量名 -->
            <nz-form-item *ngIf="!!data.isBusiness">
              <nz-form-label nzRequired>{{ 'dj-变量名' | translate }}</nz-form-label>
              <nz-form-control
                [nzValidateStatus]="data['errorKeys']?.includes('performerVariable') ? 'error' : 'success'"
              >
                <div class="duty">
                  <app-modal-input
                    [innerLabel]="false"
                    [attr]="{
                      name: '变量名',
                      required: true,
                      error: data['errorKeys']?.includes('performerVariable') && currentProject.formDirty,
                      readOnly: !editable
                    }"
                    [value]="data.performerVariable"
                    (callBack)="handleChargeData('performerVariable', $event, i)"
                  >
                  </app-modal-input>
                  <div
                    class="error-text"
                    *ngIf="data['errorKeys']?.includes('performerVariable') && currentProject.formDirty"
                  >
                    {{ 'dj-请输入' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- 具体值 -->
            <nz-form-item>
              <nz-form-label nzRequired>{{ 'dj-具体值' | translate }}</nz-form-label>
              <nz-form-control [nzValidateStatus]="data['errorKeys']?.includes('performerValue') ? 'error' : 'success'">
                <div class="duty">
                  <app-modal-input
                    [innerLabel]="false"
                    [attr]="{
                      name: '具体值',
                      required: true,
                      error: data['errorKeys']?.includes('performerValue') && currentProject.formDirty,
                      readOnly: !editable
                    }"
                    [value]="data.performerValue"
                    (callBack)="handleChargeData('performerValue', $event, i)"
                  >
                  </app-modal-input>
                  <div
                    class="error-text"
                    *ngIf="data['errorKeys']?.includes('performerValue') && currentProject.formDirty"
                  >
                    {{ 'dj-请输入' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
        <!-- remove icon -->
        <i
          *ngIf="editable"
          adIcon
          iconfont="iconshanchuzhencetiaojianzu"
          aria-hidden="true"
          class="delete iconfont"
          (click)="handleDeleteCharge(i)"
        ></i>
      </div>

      <!-- 新增条件组 -->
      <div
        class="add-charge"
        *ngIf="(!currentProject.personInCharge?.identities || currentProject.personInCharge?.identities?.length === 0) && editable"
        (click)="handleAddCharge()"
      >
        <span class="add-area">
          <!-- <i adIcon iconfont="icondianji" aria-hidden="true" class="iconfont"> </i> -->
          {{ 'dj-新增条件组' | translate }}
        </span>
      </div>

      <!-- 执行人 -->
      <div>
        <div class="executor-data" *ngFor="let data of currentProject?.presetVariables; let i = index">
          <div class="content">
            <div class="others">
              <!-- 固定用户 -->
              <nz-form-item>
                <nz-form-label nzRequired>{{ 'dj-固定用户' | translate }}</nz-form-label>
                <nz-form-control>
                  <div class="duty">
                    <app-modal-input
                      [innerLabel]="false"
                      [attr]="{
                        name: '固定用户',
                        required: true,
                        readOnly: true,
                        error: data['errorKeys']?.includes('performerType') && currentProject.formDirty,
                        readOnly: !editable
                      }"
                      [value]="data.performerType"
                      (callBack)="handleBusinessExecutorData('performerType', $event, i)"
                    >
                    </app-modal-input>
                    <div
                      class="error-text"
                      *ngIf="data['errorKeys']?.includes('performerType') && currentProject.formDirty"
                    >
                      {{ 'dj-请输入' | translate }}
                    </div>
                  </div>
                </nz-form-control>
              </nz-form-item>

              <!-- 执行人名称 -->
              <nz-form-item>
                <nz-form-label nzRequired>{{ 'dj-执行人名称' | translate }}</nz-form-label>
                <nz-form-control
                  [nzValidateStatus]="data['errorKeys']?.includes('performerName') ? 'error' : 'success'"
                >
                  <div class="duty">
                    <app-modal-input
                      [innerLabel]="false"
                      [attr]="{
                        name: '执行人名称',
                        required: true,
                        lang: data['lang']?.performerName,
                        needLang: true,
                        error: data['errorKeys']?.includes('performerName') && currentProject.formDirty,
                        readOnly: !editable
                      }"
                      [value]="data['lang']?.performerName?.[('dj-LANG' | translate)]"
                      (callBack)="handleExceptionLang('performerName', $event, i)"
                    >
                    </app-modal-input>
                    <div
                      class="error-text"
                      *ngIf="data['errorKeys']?.includes('performerName') && currentProject.formDirty"
                    >
                      {{ 'dj-请输入' | translate }}
                    </div>
                  </div>
                </nz-form-control>
              </nz-form-item>

              <!-- 具体值 -->
              <nz-form-item>
                <nz-form-label nzRequired>{{ 'dj-具体值' | translate }}</nz-form-label>
                <nz-form-control
                  [nzValidateStatus]="data['errorKeys']?.includes('performerValue') ? 'error' : 'success'"
                >
                  <div class="duty">
                    <app-modal-input
                      [innerLabel]="false"
                      [attr]="{
                        name: '具体值',
                        required: true,
                        error: data['errorKeys']?.includes('performerValue') && currentProject.formDirty,
                        readOnly: !editable
                      }"
                      [value]="data.performerValue"
                      (callBack)="handleBusinessExecutorData('performerValue', $event, i)"
                    >
                    </app-modal-input>
                    <div
                      class="error-text"
                      *ngIf="data['errorKeys']?.includes('performerValue') && currentProject.formDirty"
                    >
                      {{ 'dj-请输入' | translate }}
                    </div>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <i
            *ngIf="editable"
            adIcon
            iconfont="iconshanchuzhencetiaojianzu"
            aria-hidden="true"
            (click)="handleDeleteBusinessExecutor(i)"
            class="delete iconfont"
          ></i>
        </div>
      </div>

      <div *ngIf="editable" class="add-executor" (click)="handleAddBusinessExecutor()">
        <span class="add-area">
          {{ 'dj-新增业务执行人' | translate }}
        </span>
      </div>
    </form>
    <form nz-form nzLayout="vertical" [nzNoColon]="true" *ngIf="currentProject?.executor">
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'dj-项目当责者' | translate }}
          <i
            adIcon
            iconfont="iconshuomingwenzi"
            aria-hidden="true"
            class="question-icon"
            NzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="'dj-项目当责者为项目卡的收卡人，可通过项目卡进行执行情况追踪' | translate"
          >
          </i>
        </nz-form-label>
        <app-person-setting
          [showDefault]="true"
          [personSingle]="true"
          personType=""
          [bindForm]="{}"
          [nodeId]="undefined"
          [editable]="editable"
          [executor]="currentProject?.executor"
          [decisionConfig]="{}"
          [preManualNodes]="[]"
          (changeData)="handlePeopleSettingChange($event)"
        ></app-person-setting>
      </nz-form-item>
      <nz-form-item class="conditional-dispatch-form-item">
        <app-conditional-dispatch
          [inputName]="true"
          [data]="dispatchData"
          (changeData)="handleConditionalDispatchChange($event)"
        ></app-conditional-dispatch>
      </nz-form-item>
    </form>
  </nz-collapse-panel>
  <ng-template #header>
    <div>
      {{ 'dj-当责者' | translate }}
      <i
        adIcon
        iconfont="iconqiehuanshujuyuan"
        class="exchange"
        nz-tooltip
        [nzTooltipTitle]="'dj-切换为新配置' | translate"
        aria-hidden="true"
        *ngIf="!currentProject?.executor && editable"
        (click)="handleExchange($event)"
      >
      </i>
    </div>
  </ng-template>
</nz-collapse>
