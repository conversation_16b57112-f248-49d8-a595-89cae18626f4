<nz-collapse>
  <nz-collapse-panel [nzHeader]="'dj-里程碑' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
    <div class="content">
      <span class="title">{{ 'dj-里程碑设置' | translate }}</span>
      <nz-switch
        class="switch"
        [(ngModel)]="currentProject.milestone"
        [nzDisabled]="noTaskData || !editable"
        (ngModelChange)="onToggleMilestoneSetting($event)"
      ></nz-switch>

      <a class="btn" *ngIf="currentProject.milestone && editable" (click)="onSettingClick()">
        <i class="op-icon font3" adIcon iconfont="iconshezhi123"></i>
        {{ 'dj-配置' | translate }}
      </a>
    </div>
    <div class="error-text" *ngIf="invalid">
      {{ 'dj-请配置里程碑' | translate }}
    </div>
    <!-- <form nz-form nzLayout="vertical" [formGroup]="currentProject['basicForm']" [nzNoColon]="true">
      <nz-form-item>
        <nz-form-control>

        </nz-form-control>
      </nz-form-item>
    </form> -->
  </nz-collapse-panel>
</nz-collapse>

<app-milestone-modal
  *ngIf="modalVisible"
  [currentProject]="currentProject"
  [modalFlag]="modalFlag"
  [allTaskData]="allTaskData"
  [noTaskData]="noTaskData"
  [loading]="loading"
  (cancel)="handleCancel()"
  (ok)="handleOk($event)"
></app-milestone-modal>
