:host {
  .executor-data,
  .charge-data {
    background: #f8fafd;
    border: 1px solid #eeeeee;
    position: relative;
    padding: 16px 8px;
    margin-top: 16px;

    .delete {
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(50%, -50%);
      font-size: 12px;
      color: #6868ae;
    }
  }

  .executor-data {
    margin-top: 15px;
  }

  .add-executor,
  .add-charge {
    margin-top: 16px;
    cursor: pointer;
    background: #ffffff;
    border: 1px solid #6868ae;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 400;
    text-align: center;
    color: #6868ae;
    height: 28px;
    line-height: 28px;
    transition: all 0.3s ease-in-out;

    &:hover {
      background: #f4f5ff;
      border-color: #5d5d9c;
      color: #5d5d9c;
    }
  }
  .question-icon {
    margin-left: 8px;
  }
  .exchange {
    color: #6a4cff;
  }
  .conditional-dispatch-form-item{
    ::ng-deep .ant-form-item {
      flex-direction: unset !important;
    }
  }
}
