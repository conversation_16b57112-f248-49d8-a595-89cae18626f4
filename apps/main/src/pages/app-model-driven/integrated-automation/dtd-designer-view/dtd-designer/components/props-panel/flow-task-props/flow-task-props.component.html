<div class="app-flow-task-props submit-form">
  <div class="header">
    <span>{{ 'dj-任务编辑' | translate }}</span>
  </div>

  <section class="content props-variable">
    <form nz-form class="content" [formGroup]="dataFormGroup" [nzAutoTips]="errorTips">
      <nz-spin [nzSpinning]="loading">
        <nz-collapse [nzBordered]="false">
          <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-任务代号' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <app-component-input
                    [attr]="{
                      name: '任务代号',
                      required: true,
                      needLang: false,
                      readOnly: true
                    }"
                    style="width: 100%"
                    formControlName="code"
                    [value]="dataFormGroup.get('code')?.value"
                    ngDefaultControl
                  >
                  </app-component-input>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-任务名称' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <app-modal-input
                    formControlName="name"
                    ngDefaultControl
                    [innerLabel]="false"
                    [attr]="{
                      name: '任务名称',
                      required: true,
                      needLang: true,
                      lang: lang?.name,
                      readOnly: disabledAll
                    }"
                    [nzDisabled]="isReferenceNode"
                    style="width: 100%"
                    [value]="lang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                    (callBack)="handlePatchLang('name', $event)"
                    (translateLoading)="handleTranslateLoading($event)"
                  >
                  </app-modal-input>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-执行方式' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzOptions]="executeTypes" formControlName="executeType"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-任务类型' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzDisabled]="true" [nzOptions]="taskTypes" formControlName="type"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-业务模式' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzOptions]="businessModelType" formControlName="pattern"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-业务类型' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzDisabled]="true" [nzOptions]="businessType" formControlName="category"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-优先级' | translate }}
                    <!-- <span class="item-required">*</span> -->
                  </div>
                  <ad-select [nzOptions]="priorities" formControlName="priority"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-数据群落' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzDisabled]="true" [nzOptions]="extra.groupData" formControlName="groupCode"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-提交签核的任务' | translate }}
                    <!-- <span class="item-required">*</span> -->
                  </div>
                  <ad-select [nzOptions]="exceptCurrentTaskList" formControlName="startApproveActivity"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-驱动类型' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzDisabled]="true" [nzOptions]="driveOptions" formControlName="driveType"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item
              class="nz-form-item-content"
              *ngIf="
                dataFormGroup.get('executeType')?.value === 'manual' &&
                dataFormGroup.get('category')?.value === 'DATA_ENTRY' &&
                dataFormGroup.get('pattern')?.value === 'BUSINESS'
              "
            >
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-表单' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <ad-select [nzOptions]="extra.relaDataEntryCodes" formControlName="relaDataEntryCode"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-divider nzDashed></nz-divider>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ '任务基础设置' | translate }}
                  </div>
                  <div class="url" *ngIf="dataFormGroup.get('driveType')?.value === 'flow'" (click)="handleMenuFlow()">
                    <i adIcon iconfont="iconhuodongsheji" class="iconfont" aria-hidden="true"></i>
                    {{ 'dj-活动设计' | translate }}
                  </div>
                  <div class="url" *ngIf="dataFormGroup.get('driveType')?.value === 'api'" (click)="handleApiDesign()">
                    <i adIcon iconfont="iconhuodongsheji" class="iconfont" aria-hidden="true"></i>
                    {{ 'dj-API设计' | translate }}
                  </div>
                  <div class="url" (click)="handlePageDesign('taskOnlyDetail')">
                    <i adIcon iconfont="icona-jiemiansheji" class="iconfont" aria-hidden="true"></i>
                    {{ 'dj-界面设计' | translate }}
                  </div>
                  <div
                    *ngIf="!!currentTaskItem?.extendFields?.hasActivityConfig && workType === 0"
                    class="url"
                    (click)="handlePageDesign('taskOnlyCard')"
                  >
                    <i adIcon iconfont="iconzidingyi" class="iconfont" aria-hidden="true"></i>
                    {{ 'dj-自定义卡面设计' | translate }}
                  </div>
                  <div class="url" (click)="handleMobilePageDesign()">
                    <i adIcon iconfont="iconshouji" class="iconfont" aria-hidden="true"></i>
                    {{ 'dj-mobile界面设计' | translate }}
                  </div>
                  <div *ngIf="!!currentTask" class="url">
                    <app-extended-info
                      [code]="currentTask?.code"
                      [sceneCode]="'datamapTask'"
                      [appCode]="currentTask.application"
                      [isFromDtdReference]="isReferenceNode"
                      [extendHeader]="extendHeader"
                      [showName]="true"
                      [isTenantProcessId]="isTenantActive"
                    >
                    </app-extended-info>
                  </div>
                  <div class="url" [ngClass]="{ 'not-allow': isReference }" (click)="handleIndividualCase()">
                    <i adIcon type="copy" theme="outline" class="iconfont"></i>
                    {{ 'dj-个案' | translate }}
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-divider nzDashed></nz-divider>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ '预计完成日从业务数据获取' | translate }}
                    <nz-switch formControlName="dueDateBusiness" ngDefaultControl></nz-switch>
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-业务值' | translate }}
                  </div>
                  <div *ngIf="!dataFormGroup.get('dueDateBusiness')?.value" class="noBusiness">
                    <nz-input-number
                      formControlName="dueDateComponentValue"
                      ngDefaultControl
                      [nzMin]="1"
                      [nzStep]="1"
                      [nzSize]="'small'"
                    ></nz-input-number>
                    <ad-select
                      class="select"
                      [nzOptions]="dateUnitTypes"
                      formControlName="dueDateComponentDataType"
                    ></ad-select>
                  </div>

                  <app-modal-input
                    *ngIf="dataFormGroup.get('dueDateBusiness')?.value"
                    formControlName="dueDateValue"
                    ngDefaultControl
                    [innerLabel]="false"
                    [attr]="{
                      name: '业务值',
                      required: true
                    }"
                    style="width: 100%"
                    [innerLabel]="false"
                    [value]="dataFormGroup.get('dueDateValue')?.value"
                  >
                  </app-modal-input>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content" *ngIf="dataFormGroup.get('dueDateBusiness')?.value">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ '日期格式化' | translate }}
                  </div>
                  <app-modal-input
                    formControlName="dueDateFormat"
                    ngDefaultControl
                    [innerLabel]="false"
                    [attr]="{
                      name: '业务值',
                      required: true
                    }"
                    style="width: 100%"
                    [innerLabel]="false"
                    [value]="dataFormGroup.get('dueDateFormat')?.value"
                  >
                  </app-modal-input>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content" *ngIf="dataFormGroup.get('dueDateBusiness')?.value">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ '变量名' | translate }}
                  </div>
                  <app-modal-input
                    formControlName="dueDateVariable"
                    ngDefaultControl
                    [innerLabel]="false"
                    [attr]="{
                      name: '业务值',
                      required: true
                    }"
                    style="width: 100%"
                    [innerLabel]="false"
                    [value]="dataFormGroup.get('dueDateVariable')?.value"
                  >
                  </app-modal-input>
                </div>
              </nz-form-control>
            </nz-form-item>

            <nz-divider nzDashed></nz-divider>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-是否里程碑' | translate }}
                    <nz-switch formControlName="milestone" ngDefaultControl></nz-switch>
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-是否分批' | translate }}
                    <nz-switch formControlName="supportPart" ngDefaultControl></nz-switch>
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-是否拆行' | translate }}
                    <nz-switch formControlName="supportSplit" ngDefaultControl></nz-switch>
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-是否合并' | translate }}
                    <nz-switch formControlName="merge" ngDefaultControl></nz-switch>
                  </div>
                </div>
              </nz-form-control>
            </nz-form-item>

            <!-- <ng-container formGroupName="extendFields">
              <nz-form-item class="nz-form-item-content">
                <nz-form-control>
                  <div class="form-item">
                    <div class="item-title">
                      {{ 'dj-是否卡控' | translate }}
                      <nz-switch formControlName="closeNeedConfirm" ngDefaultControl></nz-switch>
                    </div>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </ng-container> -->
          </nz-collapse-panel>
          <nz-collapse-panel [nzHeader]="'dj-推送数据' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item">
                  <app-task-push-data
                    #taskPushData
                    *ngIf="!!currentTask"
                    formArrayName="atmcDatas"
                    ngDefaultControl
                    [dataFormGroup]="dataFormGroup"
                    [atmcDatas]="currentTask?.atmcDatas"
                    [isReference]="isReferenceNode"
                    [editable]="!disabledAll"
                  ></app-task-push-data>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-collapse-panel>
          <nz-collapse-panel [nzHeader]="'dj-数据状态' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
            <app-task-data-status *ngIf="!!currentTask" [data]="currentTask.stateMaps" [extra]="extra">
            </app-task-data-status>
            <nz-form-item class="nz-form-item-content" *ngIf="!currentTask?.['manualAble']">
              <nz-form-control>
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-数据特征' | translate }}
                  </div>
                  <ad-select nzMode="multiple" formControlName="featureSets">
                    <ad-option *ngFor="let fl of featureList" [nzValue]="fl.code" [nzLabel]="fl.name"></ad-option>
                  </ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-collapse-panel>
          <nz-collapse-panel
            *ngIf="currentTask?.category === 'APPROVAL'"
            [nzHeader]="'dj-执行人' | translate"
            [nzActive]="true"
            [nzExpandedIcon]="'caret-right'"
          >
            <app-sign-off-executor
              id="executor"
              [currentTask]="currentTask"
              [extra]="extra"
              [dataFormGroup]="dataFormGroup"
            >
            </app-sign-off-executor>
          </nz-collapse-panel>
          <nz-collapse-panel [nzHeader]="'dj-方案设计' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
            <app-task-scheme-design
              #taskSchemaDesign
              id="exception"
              formArrayName="atmcDatas"
              ngDefaultControl
              [dataFormGroup]="dataFormGroup"
              [currentTask]="currentTaskData"
              [extra]="extra"
              [editable]="!disabledAll"
              [isFromDtdReference]="isReferenceNode"
              (translateLoading)="handleTranslateLoading($event)"
            >
            </app-task-scheme-design>
          </nz-collapse-panel>
          <nz-collapse-panel
            *ngIf="currentTask?.executeType !== 'auto'"
            [nzHeader]="'dj-关联场景' | translate"
            [nzActive]="true"
            [nzExpandedIcon]="'caret-right'"
          >
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-关联场景' | translate }}
                  </div>
                  <nz-tree-select
                    *ngIf="relatedScenes"
                    formControlName="relatedCategories"
                    [nzNodes]="relatedScenes"
                    nzShowSearch
                    nzCheckStrictly
                  ></nz-tree-select>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-collapse-panel>
          <nz-collapse-panel [nzHeader]="'dj-转派' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
            <nz-form-item class="nz-form-item-content">
              <nz-form-control>
                <div class="form-item" (click)="openShowJsonModal($event, 'assignAble')">
                  <div class="item-title"></div>
                  {{ 'dj-转派' | translate }}
                  <nz-switch formControlName="assignAble" ngDefaultControl></nz-switch>
                </div>
              </nz-form-control>
              <nz-form-control *ngIf="dataFormGroup.get('assignAble').value">
                <div class="form-item">
                  <span class="check-label-json" (click)="openShowJsonModal($event, 'jsonModal')">
                    {{ 'dj-转派业务逻辑' | translate }}&lt;/&gt;
                  </span>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-collapse-panel>
          <nz-collapse-panel
            *ngIf="currentTask?.executeType !== 'auto'"
            [nzHeader]="'dj-评价模型' | translate"
            [nzActive]="true"
            [nzExpandedIcon]="'caret-right'"
          >
            <nz-form-item class="nz-form-item-content">
              <nz-form-control [nzAutoTips]="errorTips">
                <div class="form-item">
                  <div class="item-title">
                    {{ 'dj-评价模型' | translate }}
                  </div>
                  <ad-select [nzOptions]="decisions" formControlName="evlModelCode"></ad-select>
                </div>
              </nz-form-control>
            </nz-form-item>
          </nz-collapse-panel>
        </nz-collapse>
      </nz-spin>
    </form>
  </section>
  <ng-container *operateAuth="{ prefix: 'update', guards: [!isReferenceNode] }">
    <div class="footer">
      <div class="save" (click)="handleSave()">
        <i adIcon iconfont="iconbaocun2" class="save-icon"></i>
        <span class="save-text">{{ 'dj-保存设置项' | translate }}</span>
      </div>
    </div>
  </ng-container>
</div>

<app-extend-editor-modal
  *ngIf="isShowJsonModal"
  [data]="currentTask?.assignConfig"
  [width]="'640px'"
  (ok)="handleSaveSet($event)"
  (close)="isShowJsonModal = false"
></app-extend-editor-modal>
