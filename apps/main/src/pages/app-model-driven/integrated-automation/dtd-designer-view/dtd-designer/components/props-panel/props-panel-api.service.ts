import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { AdUserService } from 'pages/login/service/user.service';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';

@Injectable()
export class PropsPanelApiService {
  adesignerUrl: string;
  headerConfig = {};
  applicationCodeProxy;
  uibotUrl: string;
  isTenantActive = false; // 是否租户激活

  constructor(
    private http: HttpClient,
    private configService: SystemConfigService,
    public adUserService: AdUserService,
    public dtdDesignerViewService: DtdDesignerViewService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.configService.get('uibotUrl').subscribe((url) => {
      this.uibotUrl = url;
    });
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  updateTask(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    if (this.isTenantActive) {
      // 租户模式下只有编辑
      const url = `${this.adesignerUrl}/athena-designer/tenant/task/updateTenantTask`;
      return this.http.post(url, param, {
        headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.tenantVersionHeaders),
      });
    } else {
      return this.http.post(`${this.adesignerUrl}/athena-designer/task/updateTask`, param, {
        headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
      });
    }
  }

  loadTaskData(applicationCode: string): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.applicationCodeProxy) {
      applicationCode = this.applicationCodeProxy;
    }
    const url = `${this.adesignerUrl}/athena-designer/task/taskTree`;
    return this.http.get(url, {
      params: { applicationCode },
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  loadDataStates(param: any): Observable<any> {
    const { bcReferenceHeader, ...rest } = param;
    const url = `${this.adesignerUrl}/athena-designer/data/findDataStatesByApplication`;
    return this.http.get(url, {
      params: rest,
      headers: Object.assign({}, this.headerConfig, bcReferenceHeader || this.dtdDesignerViewService.adpVersionHeaders),
    });
  }

  loadCurrentTaskData(params: any): Observable<any> {
    const { code, bcReference, ...rest } = params;
    let header = bcReference ? rest : this.dtdDesignerViewService.adpVersionHeaders;
    // 模板管理替换application并在头信息中添加tempalteId
    if (this.isTenantActive) {
      const url = `${this.adesignerUrl}/athena-designer/tenant/task/getTask/${code}`;
      return this.http.get(
        url,
        {
          headers: Object.assign({}, this.headerConfig, header, this.dtdDesignerViewService.tenantHeaders)
        },
      );
    } else {
    const url = `${this.adesignerUrl}/athena-designer/task/getTask/${code}`;
      return this.http.get(url, { headers: Object.assign({}, this.headerConfig, header) });
    }
  }

  // 根据任务code获取数据特征
  getDataFeatures(params): Observable<any> {
    // 模板管理替换application并在头信息中添加tempalteId
    const { code, bcReference, ...rest } = params;
    let header = bcReference ? rest : this.dtdDesignerViewService.adpVersionHeaders;
    const url = `${this.adesignerUrl}/athena-designer/data/dataFeatures/${code}`;
    return this.http.get(url, {
      headers: Object.assign({}, this.headerConfig, header),
    });
  }

  postUpsertProcess(params: any): Observable<any> {
    if (this.isTenantActive) {
      const url = `${this.adesignerUrl}/athena-designer/tenant/process/upsertTenantProcess`;
      return this.http.post(
        url,
        { ...params },
        {
          headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.tenantVersionHeaders)
        },
      );
    } else {
        const url = `${this.adesignerUrl}/athena-designer/process/upsertProcess`;
      return this.http.post(
      url,
      { ...params },
      { headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders) },
    );
    }
  }

  saveDataState(param: any): Observable<any> {
    if (this.applicationCodeProxy) {
      param.application = this.applicationCodeProxy;
    }
    if (this.isTenantActive) {
      const url = `${this.adesignerUrl}/athena-designer/tenant/data/saveTenantDataState`;
      return this.http.post(url, param, {
        headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.tenantVersionHeaders)
      });
    } else {
      const url = `${this.adesignerUrl}/athena-designer/data/saveDataState`;
      return this.http.post(url, param, {
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
    }
  }

  /** 查询所有场景维度 */
  getCategory(): Observable<any> {
    const url = `${this.uibotUrl}/api/ai/v1/bot/designer/view/category`;
    return this.http.get(url);
  }

  /**
   * 查询状态详情
   * @param code
   * @returns
   */
  getDataStateGroup(code: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/getDataListByCode`;
    return this.http.get(url, {
      params: { dataCode: code },
      headers: Object.assign({}, this.headerConfig, this.dtdDesignerViewService.adpVersionHeaders),
    });
  }
  /**
   * 查询DTD列表
   * @param params
   * @returns
   */
  saveBigTVariableToData(params: any): Observable<any> {
    const { bcReferenceHeader, ...rest } = params;
    const url = `${this.adesignerUrl}/athena-designer/data/saveBigTVariableToData`;
    return this.http.post(url, rest, {
      headers: bcReferenceHeader || this.dtdDesignerViewService.adpVersionHeaders,
    });
  }
  /**
   * 保存DTD变量
   * @param params
   * @returns
   */
  toProcess(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/data/toProcess`;
    return this.http.post(url, params, {
      headers: this.dtdDesignerViewService.adpVersionHeaders,
    });
  }
}
