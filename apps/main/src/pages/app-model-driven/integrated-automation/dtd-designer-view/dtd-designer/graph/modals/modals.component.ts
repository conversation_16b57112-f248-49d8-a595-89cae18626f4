import { Component, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { environment } from 'environments/environment';
import { GraphService } from '../service/graph.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { ApiService } from '../service/api.service';
import { Cell, Edge, Graph, Node } from '@antv/x6';
import { DataStateListComponent } from '../../data-state/data-state-list.component';
import { ComponentRefKeys, WorkDesignType } from '../../types';
import { TaskListComponent } from '../../task/task-list.component';
import { GraphComponent } from '../graph.component';
import { AssociatedDataComponent } from '../components/associated-data/associated-data.component';
import { AssociatedTaskComponent } from '../components/associated-task/associated-task.component';
import { to } from 'common/utils/core.utils';
import { DtdStatusComponent } from '../../components/add-data/components/dtd-status/dtd-status.component';
import { GlobalService } from 'common/service/global.service';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';
import { debounceTime } from 'rxjs/operators';
import {
  EType,
  NodeType,
  PannelTabsType,
  SDNodeType,
} from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { cloneDeep } from 'lodash';
import { initFlowGraph } from 'pages/app-model-driven/integrated-automation/view-designer/config/init-graph';
import { createUUID } from 'pages/app-model-driven/integrated-automation/view-designer/config/utils';
import { DtdDesignerViewService } from '../../../service/dtd-designer-view.service';
import { AppService } from 'pages/apps/app.service';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-modals',
  templateUrl: './modals.component.html',
  styleUrls: ['./modals.component.less'],
})
export class ModalsComponent implements OnInit {
  @Input() graph: Graph;
  @Input() graphComponentRef: GraphComponent;
  @Input() applicationCodeProxy: string;
  @Input() favouriteCode: string;
  @Input() panelParams: any;
  @ViewChild('associatedData') associatedDataRef: AssociatedDataComponent;
  @ViewChild('associatedTask') associatedTaskRef: AssociatedTaskComponent;
  @ViewChild('dtdDataStatus') dtdDataStatus: DtdStatusComponent;

  notify$: Subject<any> = new Subject(); // 用于节点批量操作时可能会导致多次接口请求，使用该Subject做防抖处理

  taskItemMenuVisible: boolean = false; // 任务列表菜单是否可见
  taskItemMenuPosition: { x: number; y: number }; // 任务列表菜单位置
  dataStateModalVisible: boolean = false; // 状态弹窗是否可见
  dataStateModalData: any; // 状态开窗所需数据
  dataStateModalFeatureList: any; // 状态开窗所需数据
  dataStateModalType: 'add' | 'edit'; // 状态弹窗类型
  nodeAddToolInfo: any; // 节点添加工具信息{ node, e, direction }
  taskModalVisible: boolean = false; // 任务编辑弹窗是否显示
  addedTask = null; // 新增任务数据
  modalFlag: 'add' | 'edit'; // 新增任务或编辑任务
  currentTaskData = null; // 当前任务数据，用于任务开窗编辑该任务
  associatedDataModalVisible: boolean = false; // 关联状态弹窗是否可见
  associatedDataModalTitle: string; // 关联状态弹窗是否可见
  associatedDataActiveStateCodes: any; // 关联状态弹窗所需任务状态
  associatedDataModalType: 'source' | 'target'; // 关联状态弹窗类型
  associatedTaskStateMaps: any; // 关联状态弹窗时，任务的状态节点映射表
  associatedTaskStateNode: Node; // 关联状态或任务弹窗时的任务节点或状态节点
  associatedDataLoading: boolean = false; // 关联状态弹窗加载状态
  associatedTaskModalVisible: boolean = false; // 关联任务开窗是否可见
  associatedTaskModalTitle: string; // 关联任务开窗title
  associatedTaskModalType: 'source' | 'target'; // 关联任务弹窗类型
  associatedTaskActiveTasks: any; // 关联任务弹窗当前任务
  associatedTaskLoading: boolean = false; // 关联任务弹窗加载状态
  graphNodeMenuVisible: boolean = false; // 节点菜单是否可见
  graphNodeMenuPosition: { x: number; y: number }; // 节点菜单位置
  graphNodeMenuData: any; // 节点菜单数据
  graphNodeMenuNode: Node; // 节点菜单当前节点
  formWorkVisible: boolean = false; // 特殊类型的任务界面设计：表单 是否显示
  workVisible: any; // 任务界面设计
  workData: any; // 任务界面设计数据
  workType: any; // 设计类型 0: 任务界面设计, 1:手工发起项目, 2:原始界面设计
  isMobile: boolean = false; // 是否是移动端,用于界面设计
  apiDesignModalVisible: boolean = false; // api设计弹窗是否显示
  apiDesignTask: any = null; // api设计任务数据
  flowVisible: boolean = false; // 流程图设计开窗是否显示
  flowTask: any; // flow所需的task
  workDesignType: WorkDesignType; // 界面设计类型
  // 新T
  newTModalVisible: boolean = false;
  isTenantActive = false; // 租户级 开发平台 是否激活

  get dataStateComponent(): DataStateListComponent {
    return this.graphService.getComponentRef(ComponentRefKeys.DATA_STATE) as DataStateListComponent;
  }

  get taskComponent(): TaskListComponent {
    return this.graphService.getComponentRef(ComponentRefKeys.TASK) as TaskListComponent;
  }

  get taskList() {
    return this.taskComponent?.originTaskList || [];
  }

  get dataStateMap() {
    return this.dataStateComponent?.dataStateMap;
  }
  get originDataStateList() {
    return this.dataStateComponent?.originDataStateList;
  }
  // 画布是否有改动
  get isGraphChanged() {
    return this.graphService.isGraphChanged;
  }

  get isReference() {
    return this.appService.selectedApp?.tag?.sourceComponent === 'BC'; // 是否是引用应用
  }

  taskVersion: any;

  get disabledAll() {
    return !!this.dtdDesignerService?.fromManual;
  }

  constructor(
    private router: Router,
    private graphService: GraphService,
    private translateService: TranslateService,
    private message: NzMessageService,
    private apiService: ApiService,
    public globalService: GlobalService,
    private dtdDesignerService: DtdDesignerViewService,
    private appService: AppService,
    public adUserService: AdUserService,
  ) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(e: MouseEvent) {
    this.graphNodeMenuVisible = false;
  }

  @HostListener('document:mousedown', ['$event'])
  onDocumentMouseDown(e: MouseEvent) {
    this.taskItemMenuVisible = false;
  }

  ngOnInit() {
    this.graphEvents();
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  /**
   * 用于节点批量操作时可能会导致多次接口请求，使用该Subject做防抖处理
   */
  subscribeBatch() {
    this.notify$.pipe(debounceTime(300)).subscribe((info) => {
      if (info.type === 'add') {
        this.graphComponentRef.saveGraph(false);
      }
      if (info.type === 'remove') {
        this.taskComponent?.fetchTasks();
      }
    });
  }

  graphEvents() {
    // 节点直接连线
    this.graph.on('edge:connected', async ({ isNew, edge }) => {
      await this.graphService.edgeConnected(edge, this.graph);
      this.taskComponent?.fetchTasks();
      this.setDataStateByProTask(edge, this.graph);
    });

    this.graph.on('node:click', ({ e, x, y, cell, view }) => {
      // 高亮显示输入输出线
      this.graphService.highlightEdgesByNode(this.graph, cell);
      // 点击节点，左侧列表同步选中
      this.syncSelectedListItem(cell);
    });

    this.graph.on('node:dblclick', async ({ e, x, y, cell, view }) => {
      // 双击打开编辑开窗
      const { type, code, taskCategory, dataCode, bcReference, ngArguments } = cell.getData();
      let taskData = this.taskList?.find((item) => item.code === code);
      if (type === 'datastate') return;

      // if (!taskData) {
      //   const res = await this.apiService.queryTaskDetail(code).toPromise();
      //   taskData = res.data;
      // }
      if (taskCategory === 'workflowTask') {
        this.handleTTTFlow(taskData);
      } else {
        taskData = {
          ...taskData,
          isFromDtdReference: bcReference || ngArguments?.bcReference,
        };
        this.handleMenuFlow({ data: taskData });
      }
    });

    this.graph.on('node:mouseenter', (e) => {
      if (!this.globalService.hollowPromise) return;
      if (this.isReference) return; // 如果是bcReference节点，则不显示
      const type = e.node?.data?.type;
      const taskCategory = e.node?.data?.taskCategory;
      const isWorkflowTask = type === 'task' && taskCategory === 'workflowTask';
      if (isWorkflowTask || !this.disabledAll) {
        // 节点链接桩显示
        this.graphService.togglePortVisible(e.node, true);
        // 输入输出删除按钮显示
        this.graphService.setNodeOptionsTool(this.graph, e.node, true, {
          in: (cell: Node) => {
            if (cell.getData().type === 'task') {
              this.handleInOutState(cell, 'source');
              return;
            }
            this.handleInOutTask(cell, 'source');
          },
          out: (cell: Node) => {
            if (cell.getData().type === 'task') {
              this.handleInOutState(cell, 'target');
              return;
            }
            this.handleInOutTask(cell, 'target');
          },
          delete: async (cells: Node[]) => {
            await this.graphService.handleRemoveNodes(this.graph, cells);
            this.taskComponent?.fetchTasks();
          },
        });
      }
    });

    this.graph.on('node:mouseleave', (e) => {
      // 节点链接桩隐藏
      this.graphService.togglePortVisible(e.node, false);
      // 输入输出删除按钮隐藏
      this.graphService.setNodeOptionsTool(this.graph, e.node, false);
    });

    this.graph.on('node:added', ({ node }) => {
      this.notify$.next({ type: 'add' });
      if (node.getData().type === 'task') {
        this.graphComponentRef.taskNodeMap.set(node.getData().code, node.getData());
      }
      if (node.getData().type === 'datastate') {
        this.graphComponentRef.stateNodeMap.set(node.getData().code, node.getData());
      }
    });

    this.graph.on('node:selected', (e) => {
      if (this.isReference) return; // 该节点为引用的节点
      if (this.graph.getSelectedCellCount() > 1) return;
      if (e.cell.isVisible() === false) return;
      const type = e.node?.data?.type;
      const taskCategory = e.node?.data?.taskCategory;
      const isWorkflowTask = type === 'task' && taskCategory === 'workflowTask';
      if (isWorkflowTask || !this.disabledAll) {
        // 节点周围加号Tool
        this.graphService.addNodeAddTool(e, (info) => {
          const { node, e, direction } = info;
          const { type } = node.getData();
          this.nodeAddToolInfo = info;
          if (type === 'datastate') {
            e.stopPropagation();
            this.newTModalVisible = true;
            // TODO:暂时注掉添加任务下拉，仅直接添加新T
            // const { clientX, clientY } = this.graphService.handlePosition(e);
            // this.taskItemMenuPosition = { x: clientX, y: clientY };
            // this.taskItemMenuVisible = true;
            return;
          }
          if (type === 'task') {
            // 打开状态开窗
            this.dataStateModalData = null;
            this.dataStateModalFeatureList = null;
            this.dataStateModalVisible = true;
            this.dataStateModalType = 'add';
            return;
          }
        });
      }
    });

    this.graph.on('node:unselected', (e) => {
      e.node.removeTools();
    });

    this.graph.bindKey(['backspace', 'delete'], async (cell) => {
      const nodes = this.graph.getSelectedCells() as Node[];
      if (this.isReference) return; // 含有dtd引用的节点，不可删
      // 删除节点
      await this.graphService.handleRemoveNodes(this.graph, nodes);
    });

    this.graph.on('edge:mouseenter', ({ cell }) => {
      if (this.isReference) return; // 含有dtd引用的节点，不可删
      // 连线删除按钮显示
      this.graphService.toogleEdgeRemogeTool(this.graph, cell as Edge);
    });

    this.graph.on('edge:mouseleave', ({ cell }) => {
      cell.removeTools();
    });

    this.graph.on('node:removed', ({ node }) => {
      if (node.getData().type === 'datastate') {
        this.graphComponentRef.stateNodeMap.delete(node.getData().code);
      }
      if (node.getData().type === 'task') {
        this.graphComponentRef.taskNodeMap.delete(node.getData().code);
      }
      this.notify$.next({ type: 'remove' });
      // todo
    });

    this.graph.on('node:contextmenu', ({ e, x, y, cell, view }) => {
      const { clientX, clientY } = e;
      this.graphNodeMenuPosition = { x: clientX - 450, y: clientY - 55 };
      this.graphNodeMenuNode = cell;
      if (cell.getData().type === 'task') {
        this.graphNodeMenuData = this.taskList.find((item) => item.code === cell.getData().code);
      } else {
        this.graphNodeMenuData = this.originDataStateList.find(
          (item) => item.dataDescription.code === cell.getData().dataCode,
        );
      }
      this.graphNodeMenuVisible = true;
    });
  }

  /**
   * 打开关联状态开窗
   * @param taskNode
   */
  handleInOutState(taskNode: Node, type: 'source' | 'target') {
    let currentTaskStates = [];
    const allStateMaps = this.graphService.refreshTaskStateMaps(this.graph, [taskNode]);
    const stateMaps = allStateMaps?.[0]?.stateMaps;
    /*
  [ {code:'',stateMaps:[{input:'',output:{}}]} ]
*/
    stateMaps.forEach((item) => {
      if (type === 'source') {
        currentTaskStates = stateMaps.map((item) => item.input);
      } else {
        const output = item.output;
        for (const key in output) {
          currentTaskStates.push(output[key]);
        }
      }
    });
    this.associatedTaskStateMaps = { code: taskNode.getData().code, stateMaps };
    this.associatedTaskStateNode = taskNode;

    if (type === 'source') {
      this.associatedDataModalTitle = this.translateService.instant('dj-关联来源数据');
    } else if (type === 'target') {
      this.associatedDataModalTitle = this.translateService.instant('dj-关联目标数据');
    }
    this.associatedDataModalType = type;
    this.associatedDataActiveStateCodes = [...new Set(currentTaskStates)];
    this.associatedDataModalVisible = true;
  }

  /**
   * 打开关联任务开窗
   * @param stateNode
   */
  handleInOutTask(stateNode: Node, type: 'source' | 'target') {
    const title = { source: 'dj-关联来源任务', target: 'dj-关联目标任务' };
    let activeTaskNodes = this.graph.getNeighbors(stateNode, {
      [type === 'source' ? 'incoming' : 'outgoing']: true,
    });
    const activeCodes = activeTaskNodes.map((node) => node.getData().code);
    this.associatedTaskStateNode = stateNode;
    this.associatedTaskModalType = type;
    // 当前选中的任务
    this.associatedTaskActiveTasks = this.taskList?.filter((task) => activeCodes.includes(task.code));
    this.associatedTaskModalTitle = this.translateService.instant(title[type]);
    this.associatedTaskModalVisible = true;
  }

  handleAssociatedTaskCancel() {
    this.associatedTaskModalVisible = false;
  }

  /**
   * 关联任务开窗点击确定回调
   */
  async handleAssociatedTaskSave() {
    const { deleted, added, selected, type } = this.associatedTaskRef?.getResult();
    const stateNodeId = this.associatedTaskStateNode.getProp().id;
    const addedCodes = added.map((item) => item.code);
    if (deleted.length === 0 && added.length === 0) {
      return;
    }
    this.associatedTaskLoading = true;

    await this.graphService.batchUpdate(this.graph, () => {
      // 根据added新增节点到画布并连线
      this.graphService.associateAdded(this.graph, added, this.associatedTaskStateNode, type);
      // 根据deleted删除关联的线
      this.graphService.associateDeleted(this.graph, deleted, stateNodeId, type);
    });

    /* 更新 stateMaps */
    const taskNodes = this.graph
      .getNodes()
      .filter((node) => node.getData().type === 'task' && addedCodes.includes(node.getData().code));
    const allStateMaps = this.graphService.refreshTaskStateMaps(this.graph, taskNodes);

    // 保存节点关系
    try {
      await this.apiService.saveTaskDataState(allStateMaps).toPromise();
      // 保存画布
      await this.graphComponentRef.saveGraph();
      this.graphComponentRef.graphService.noticeDataStatesChanged(allStateMaps);
    } catch (error) {
      this.graphService.undo(this.graph);
    } finally {
      this.associatedTaskModalVisible = false;
      this.associatedTaskLoading = false;
    }

    // 当前选中的状态节点 this.associatedTaskStateNode
    // 当前激活面板的数据  this.panelParams
    // 改变了当前选中节点的输入
    if (type === 'source' && (added?.length > 0 || deleted?.length > 0)) {
      //画布上操作的节点 === 激活面板的节点
      const associatedTaskStateNode = this.associatedTaskStateNode.getData();
      if (
        associatedTaskStateNode.code == this.panelParams?.code &&
        associatedTaskStateNode.dataCode == this.panelParams?.dataCode
      ) {
        this.dtdDesignerService._isGetVariableByProTask$.next(true);
      }
    }
  }

  /**
   *
   * @param edge
   * @param graph
   */
  // todo 完善一下 输入是状态/任务的， 输出是任务的三种场景
  setDataStateByProTask(edge, graph) {
    // const sourceNode = graph.getCellById(edge.source?.cell);
    const targetNode = graph.getCellById(edge.target?.cell);
    // const sourceData = sourceNode?.getData();
    const targetData = targetNode?.getData();
    // 输出是状态
    if (
      targetData?.type === 'datastate' &&
      targetData?.code === this.panelParams?.code &&
      targetData?.dataCode === this.panelParams?.dataCode
    ) {
      this.dtdDesignerService._isGetVariableByProTask$.next(true);
    }
  }

  /**
   * 关联任务开窗内新建任务后触发该回调
   * 用于通知任务列表更新
   */
  handleAssociatedTaskUpdateTask() {
    this.taskComponent?.fetchTasks();
  }

  /**
   * 状态开窗点击确定
   */
  async handleStateSave() {
    const formValidate = this.dtdDataStatus.handleFormValidate();
    const formValue = this.dtdDataStatus.statusForm.getRawValue();

    if (!formValidate) {
      if (this.dataStateModalType === 'add') {
        const params = this.graphService.generateDataStateData('add', formValue);
        const [err, res] = await to(this.apiService.saveDefaultState(params).toPromise());
        if (res?.code === 0) {
          const { dataStates } = params;
          const { code, name, dataCode } = dataStates?.[0];
          const position = this.graphService.getStatePosition(this.nodeAddToolInfo);
          // 新增状态节点
          const node = this.graphService.addDataStateNodeToGraph(this.graph, code, dataCode, name, position, false);
          // 创建连线,连接新增的节点
          const edge = this.graphService.connectNode(this.graph, this.nodeAddToolInfo.node, node);
          // 更新任务stateMaps
          this.graphService.edgeConnected(edge, this.graph);
        }
      }
      if (this.dataStateModalType === 'edit') {
        // 更新状态节点名称
        this.graphComponentRef.updateNodeName(
          'datastate',
          this.dataStateModalData.code,
          formValue.name,
          formValue.lang,
        );
        const params = this.graphService.generateDataStateData('edit', { ...this.dataStateModalData, ...formValue });
        const [err, res] = await to(this.apiService.saveDataState(params).toPromise());
        if (res?.code === 0) {
          // 保存画布节点关系
          this.graphComponentRef.saveGraph(false);
        }
      }
      // 刷新状态列表
      this.dataStateComponent?.fetchDataList();
      this.dataStateModalVisible = false;
    }
  }

  /**
   * 点击新增任务
   * @param taskInfo
   */
  async handleAddTask(taskInfo) {
    if (await this.dtdDesignerService.checkPropsPanelCanContinue()) {
      if (taskInfo.driveType === 'workflowTask') {
        this.newTModalVisible = true;
        return;
      }
      this.modalFlag = 'add';
      this.currentTaskData = null;
      this.taskModalVisible = true;
      this.addedTask = taskInfo;
    }
  }

  /**
   * 任务开窗保存后回调
   * @param info 保存后的任务数据 { flag: 'add'|'edit', param, taskType}
   * *保存后如果是新增任务，则新增任务节点至画布中间；如果是编辑任务，则更新画布上的任务节点
   */
  handleAfterSaveModalSave(info): void {
    const { flag, param } = info;
    const { name, code, lang } = param;
    this.taskModalVisible = false;
    this.newTModalVisible = false;
    this.currentTaskData = null;
    this.addedTask = null;

    if (flag === 'add') {
      // 同时新增任务节点
      const position = this.graphService.getTaskPosition(this.nodeAddToolInfo);
      // 新增节点
      const node = this.graphService.addTaskNodeToGraph(this.graph, param, position, false);
      // 创建连线,连接新增的节点
      const edge = this.graphService.connectNode(this.graph, this.nodeAddToolInfo.node, node);
      // 更新任务stateMaps
      this.graphService.edgeConnected(edge, this.graph);
    }
    if (flag === 'edit') {
      // 更新任务节点名称
      this.graphComponentRef.updateNodeName('task', code, name, lang);
      // 保存画布节点关系
      this.graphComponentRef.saveGraph(false);
    }
    // 刷新任务列表
    this.taskComponent?.fetchTasks();
  }

  /**
   * 根据节点同步选中左侧列表
   * @param cell 节点实例
   * @returns
   */
  syncSelectedListItem(cell: Cell) {
    const { type, code } = cell.getData();

    if (this.taskComponent && type === 'task') {
      if (this.taskComponent?.selectedTask?.code === code) return;
      // 同步选中任务列表对应的任务
      this.taskComponent?.setSelectedTask?.(code);
    }
    if (this.dataStateComponent && type === 'datastate') {
      if (this.dataStateComponent?.selectedDataState?.code === code) return;
      // 同步选中数据状态列表对应的状态
      this.dataStateComponent?.setSelectedDataState?.(code);
    }
  }

  /**
   * 关联数据状态开窗取消
   */
  handleAssociatedDataCancel() {
    this.associatedDataModalVisible = false;
  }

  /**
   * 关联数据状态开窗保存
   */
  async handleAssociatedDataSave() {
    const { deleted, added, type } = this.associatedDataRef?.getResult() || {};
    if (deleted.length === 0 && added.length === 0) {
      return;
    }
    this.associatedDataLoading = true;
    const taskNodeId = this.associatedTaskStateNode.getProp().id;

    await this.graphService.batchUpdate(this.graph, () => {
      // 根据added新增状态的节点到画布并连线
      this.graphService.associateAdded(this.graph, added, this.associatedTaskStateNode, type);
      // 根据deleted删除关联的线
      this.graphService.associateDeleted(this.graph, deleted, taskNodeId, type);
    });

    const taskStateMaps = this.graphService.refreshTaskStateMaps(this.graph, [this.associatedTaskStateNode]);

    try {
      // 保存节点关系
      await this.apiService.saveTaskDataState(taskStateMaps).toPromise();
      // 保存画布
      await this.graphComponentRef.saveGraph();
      this.graphComponentRef.graphService.noticeDataStatesChanged(taskStateMaps);
    } catch (error) {
      this.graphService.undo(this.graph);
    } finally {
      this.associatedDataModalVisible = false;
      this.associatedDataLoading = false;
    }
    console.log('关联数据状态开窗保存========》');
    // 当前选中的状态节点 this.associatedTaskStateNode
    // 当前激活面板的数据  this.panelParams
    // 改变了当前选中节点的输入
    if (type === 'target' && (added?.length > 0 || deleted?.length > 0)) {
      // 目标节点包含当前面板显示节点
      const mergeList = added.concat(deleted);
      const codeList = mergeList.map((item) => item.code);
      const datacodeList = deleted.map((item) => item.dataDescriptionCode);
      if (codeList.includes(this.panelParams.code) || datacodeList.includes(this.panelParams.dataCode)) {
        this.dtdDesignerService._isGetVariableByProTask$.next(true);
      }
    }
  }

  /**
   * 关联状态开窗更新状态的回调
   */
  handleAssociatedDataUpdateStates() {
    this.dataStateComponent?.fetchDataList();
  }

  /**
   * 右击菜单编辑
   * @param info
   */
  handleMenuEdit({ data, nodeData }: any): void {
    if (nodeData.type === 'task') {
      this.modalFlag = 'edit';
      this.currentTaskData = data;
      this.taskModalVisible = true;
    } else {
      const state = data.dataStateList.find((item: any) => item.code === nodeData.code);
      this.dataStateModalData = { ...state, conditionExpression: state?.condition?.expression };
      this.dataStateModalFeatureList = data.dataFeatures || [];
      this.dataStateModalVisible = true;
      this.dataStateModalType = 'edit';
    }
  }

  /**
   * 界面设计
   * @param taskData 任务数据
   * @param isMobile 是否是移动端
   */
  handlePageDesign(info, isMobile): void {
    const { data: taskData, fromType } = info;
    this.isMobile = isMobile;
    this.workDesignType = fromType;
    // 新任务，选择模式
    if (['new', 'dsl'].includes(taskData._pageModel)) {
      // new和dls都视为dsl，否则是tag模式
      this.pageDesignSetting({ ...taskData, _pageModel: 'dsl' });
      return;
    }
    this.pageDesignSetting('_pageModel' in taskData ? taskData : { ...taskData, _pageModel: 'pageView' });
  }

  /**
   * 界面设计组件渲染模所需数据
   */
  pageDesignSetting(data) {
    const workData = this.graphService.getBaseWorkData(data);
    // 执行方式:人工 + 业务类型：DATA_ENTRY + 业务模式：BUSINESS  = 特殊的表单类型的界面设计器
    if ('manual' === data.executeType && 'DATA_ENTRY' === data.category && 'BUSINESS' === data.pattern) {
      this.workData = this.graphService.getWorkData(workData, data);
      this.formWorkVisible = true;
    } else {
      // 在这里做判断
      this.workType = data['designType'] === 'jsonDesign' ? 2 : !!data.projectCode ? 1 : 0;
      this.workData = this.graphService.getWorkData(workData, data);
      this.workVisible = true;
    }
  }

  /**
   * 右击菜单删除
   * @param data
   */
  async handleMenuDelete(info) {
    await this.graphService.handleRemoveNodes(this.graph, [this.graphNodeMenuNode]);
    this.taskComponent?.fetchTasks();
  }

  /**
   * 右击菜单打开Flow
   * @param data
   */
  handleMenuFlow(info): void {
    console.log(info, 'info');
    const { data } = info;
    if (data.adpType === 'workflowTask') {
      this.handleTTTFlow(data);
    } else if (data.driveType === 'api') {
      this.handleMenuApiFlow(info);
    } else {
      this.taskVersion = {
        adpVersion: this.dtdDesignerService.adpVersionHeaders.adpVersion,
        adpStatus: this.dtdDesignerService.adpVersionHeaders.adpStatus,
      };
      if (data.isFromDtdReference) {
        this.taskVersion = {
          adpVersion: data.adpVersion,
          adpStatus: data.adpStatus,
        };
      }
      this.flowTask = data;
      this.flowVisible = true;
    }
  }

  async handleTTTFlow(taskInfo): Promise<void> {
    let processId = taskInfo.processId;
    let isReference = '';
    let adpVersion = this.dtdDesignerService.adpVersionHeaders.adpVersion;
    let adpStatus = this.dtdDesignerService.adpVersionHeaders.adpStatus;
    const node = this.graph.getNodes().find((node) => {
      const data = node.getData();
      return data.type === 'task' && data.code === taskInfo.code;
    });

    if (node) {
      isReference = node.getData().bcReference || node.getData().ngArguments?.bcReference;
    }

    // 引用类型的节点，如果没有processId，则不可以进入
    if (!!isReference && !processId) {
      this.message.error(this.translateService.instant('dj-当前引用的任务下无流程，请联系框架应用管理员'));
      return;
    }

    // 引用类型的节点，需要获取当前节点的adpVersion和adpStatus，而不是整个流程的版本号
    if (!!isReference) {
      const currentData = this.taskComponent.originTaskList.find((e) => e.code === taskInfo.code);
      adpVersion = currentData?.adpVersion;
      adpStatus = currentData?.adpStatus;
    }

    if (!processId) {
      processId = await this.generateProcess(taskInfo);
      if (!processId) return;
      this.taskComponent.originTaskList.forEach((e) => {
        if (e.code === taskInfo.code) {
          e.processId = processId;
        }
      });
      // 更新面板组件中的processId
      const prosContentComponentRef: any = this.dtdDesignerService.propsComponentRef?.getPropsContentRef();
      if (prosContentComponentRef?.data?.type === 'task' && prosContentComponentRef?.data?.code === taskInfo.code) {
        prosContentComponentRef.loadTaskInfo();
      }
    }
    // 兼容租户场景，
    let targetUrl = `/app/integrated-automation/view-designer/${processId}`;
    // 租户跳转添加参数
    if (this.isTenantActive) {
      targetUrl += `/${taskInfo.objectId}`;
    }
    const queryParams: any = {
      appCode: this.appService.selectedApp?.code,
      viewDesignerCode: taskInfo.application,
      projectCategory: 'combined', // 组合项目标识
      pageSource: 'workflow-task',
      isFromDtdReference: isReference,
      atdpUniqueId: taskInfo?.atdpUniqueId,
      adpVersion: adpVersion,
      adpStatus: adpStatus,
    };
    if (this.dtdDesignerService.fromManual) {
      queryParams.from = 'manual';
    }
    this.router.navigate([targetUrl], {
      queryParams,
    });
  }

  handleFlowDrawerClose() {
    this.flowVisible = false;
  }

  /**
   * 打开API Flow
   * @param data
   */
  handleMenuApiFlow(info): void {
    const { data } = info;
    const { stateMaps } = data;
    if (!(stateMaps?.[0]?.input && Object.keys(stateMaps?.[0]?.output || {}).length)) {
      // 如果没有输入或输出状态 则提示
      this.message.error(this.translateService.instant('dj-请关联数据状态'));
      return;
    }
    this.apiDesignTask = data;
    this.apiDesignModalVisible = true;
  }

  /**
   * 打开界面设计
   */
  handleMenuPageDesign(data): void {
    this.handlePageDesign(data, false);
  }

  /**
   * 移动端界面设计
   * @param data
   */
  handleMenuMobilePageDesign(data): void {
    this.handlePageDesign(data, true);
  }

  handlePageClose() {
    this.isMobile = false;
    this.workType = null;
    this.workData = null;
    this.taskComponent?.fetchTasks();
  }

  // 创建流程
  private async generateProcess(taskInfo: any): Promise<string | undefined> {
    let params: any = {
      adpType: 'workflowTask',
      taskCode: taskInfo?.code,
      application: taskInfo?.application,
      addSourceType: 'auto_perspective',
      processName: taskInfo?.name,
      triggerType: 'page',
      triggerConfig: {
        businessCode: null,
        eventId: null,
      },
    };
    const { flowGraph, processConfig } = this.initFlowGraphAndConfig(params.triggerType);
    params = {
      ...params,
      flowGraph: flowGraph,
      processConfig: processConfig,
      bindForm: { type: EType.NONE },
      dtdVariable: this.dtdDesignerService.defaultDtdVariable,
      lang: {
        sourceName: {
          zh_CN: '未命名',
          zh_TW: '未命名',
          en_US: 'Untitled',
        },
        subjectRule: {
          zh_CN: '未命名标题',
          zh_TW: '未命名標題',
          en_US: 'Untitled title',
        },
        projectName: {
          zh_CN: '项目名称',
          en_US: 'Project Name',
          zh_TW: '專案名稱',
        },
        processName: cloneDeep(taskInfo?.lang?.name || {}),
      },
    };
    const result = await this.apiService.postUpsertProcess(params).toPromise();
    return result.data?.processId;
  }

  // 新增流程初始化配置项
  private initFlowGraphAndConfig(type) {
    const { nodes, links }: any = initFlowGraph(type, this.translateService);
    const configNodes = nodes.map((node) => {
      const { nodeId, nodeType } = node?.data || {};
      let defaultConfig: any;
      if (nodeType === NodeType.START_MANUAL) {
        const configNodeId = createUUID();
        defaultConfig = {
          id: `${SDNodeType.START_NODE}_${configNodeId}`,
          type: SDNodeType.START_NODE,
          name: this.translateService.instant('dj-开始'),
          lang: {
            name: {
              zh_CN: '开始',
              zh_TW: '開始',
              en_US: 'Start',
            },
          },
          _nodeId: configNodeId,
          _nodeType: NodeType.START_MANUAL,
        };
        node.data.nodeName = this.translateService.instant('dj-开始');
        node.attrs.label.text = this.translateService.instant('dj-开始');
      } else if (nodeType === NodeType.END_FLOW) {
        const configNodeId = createUUID();
        defaultConfig = {
          id: `${SDNodeType.END_NODE}_${configNodeId}`,
          type: SDNodeType.END_NODE,
          name: this.translateService.instant('node-结束'),
          lang: {
            name: {
              zh_CN: '结束',
              zh_TW: '結束',
              en_US: 'end',
            },
          },
          _nodeId: configNodeId,
          _nodeType: NodeType.END_FLOW,
          _isValidPassed: { [PannelTabsType.BASE]: true },
        };
        defaultConfig._events = [{ type: EType.NONE }];
      }
      return {
        ...defaultConfig,
        id: `${defaultConfig.type}_${nodeId}`,
        _nodeId: nodeId,
        _nodeType: nodeType,
      };
    });
    const configLinks =
      links?.map((m) => {
        return {
          id: m.id,
          fromId: configNodes?.find((item) => item._nodeId === m.source.cell).id,
          toId: configNodes?.find((item) => item._nodeId === m.target.cell).id,
        };
      }) || [];

    const flowGraph = {
      nodes: nodes,
      links: links,
    };
    const processConfig = {
      sourceName: this.translateService.instant('dj-未命名'),
      subjectRule: this.translateService.instant('dj-未命名标题'),
      projectName: this.translateService.instant('dj-项目名称'),
      nodes: configNodes,
      links: configLinks,
    };

    return { flowGraph, processConfig };
  }
}
