<list-search
  [placeholder]="'dj-请输入数据状态名称或代号' | translate"
  (search)="handleSearch($event)"
  (clear)="handleSearch('')"
  (addClick)="handleAddClick()"
></list-search>

<div class="datastate-list-container">
  <!-- loading -->
  <nz-spin *ngIf="loading" [nzSpinning]="loading"></nz-spin>
  <!-- 项目列表 -->
  <ng-container *ngIf="!loading && !!dataStateList?.length">
    <ng-container *ngFor="let itemData of dataStateList">
      <data-state-list-item
        #listItem
        [itemData]="itemData"
        [validMap]="validMap"
        [editable]="!disabledAll"
        [selectedDataState]="selectedDataState"
        [graphStateNodeMap]="graphStateNodeMap"
        (itemMouseDown)="handleMouseDown($event)"
        (itemEdit)="handleEdit($event)"
        (itemDelete)="handleDelete($event)"
      ></data-state-list-item>
    </ng-container>
  </ng-container>
  <!-- 无数据提示 -->
  <ad-empty *ngIf="noData" size="small"></ad-empty>
</div>

<!-- 数据开窗 -->
<ad-modal
  [(nzVisible)]="dataStateModalVisible"
  [nzTitle]="dataStateModalTitle"
  [nzWidth]="'520px'"
  [nzClosable]="true"
  (nzOnCancel)="dataStateModalVisible = false"
  [nzMaskClosable]="false"
  [nzFooter]="null"
>
  <ng-container *adModalContent>
    <app-add-data
      *ngIf="dataStateModalVisible"
      [data]="dataStateModalData"
      [type]="dataStateModalType"
      (afterSave)="afterDataStateModalSave($event)"
      (cancel)="dataStateModalVisible = false"
    >
    </app-add-data>
  </ng-container>
</ad-modal>
