import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ProjectSetService } from '../../../../project-widget/service/project-set.service';

@Component({
  selector: 'app-data-push',
  templateUrl: './data-push.component.html',
  styleUrls: ['./data-push.component.less'],
})
export class DataPushComponent implements OnInit {
  @Input() currentProject: any;
  @Input() editable: boolean = true;

  @Output() patchValue = new EventEmitter<any>();
  @Output() update = new EventEmitter<any>();
  @Output() validChanged = new EventEmitter<boolean>();

  constructor(private projectSetService: ProjectSetService) {}

  ngOnInit() {}

  // 增加推送数据
  handleAddPush(): void {
    this.currentProject.atmcDatas.push({
      proVarKey: '',
      athenaKey: '',
    });
  }

  // 修改推送数据
  handlePushData(index: any, key: any, data: any): void {
    this.currentProject.atmcDatas[index][key] = data.value;

    this.validItem(this.currentProject.atmcDatas[index], key);

    this.validChanged.emit(this.currentProject.atmcDatas.every((item) => !item?.errorKeys?.length));
  }

  // 校验
  handleValidate(): boolean {
    let validate = true;

    this.currentProject.atmcDatas.forEach((item) => {
      validate = this.validItem(item);
    });

    return validate;
  }

  private validItem(item: { proVarKey?: string; athenaKey?: string }, key?: string) {
    const { proVarKey, athenaKey } = item;
    let validate = true;

    item['errorKeys'] = key ? (item['errorKeys'] ?? []).filter((s) => s !== key) : [];

    if ((key ? key === 'proVarKey' : true) && [undefined, null, ''].includes(proVarKey)) {
      item['errorKeys'].push('proVarKey');
      validate = false;
    }

    if ((key ? key === 'athenaKey' : true) && [undefined, null, ''].includes(athenaKey)) {
      item['errorKeys'].push('athenaKey');
      validate = false;
    }

    return validate;
  }

  // 删除推送数据
  handleDeletePush(index: any): void {
    this.currentProject.atmcDatas.splice(index, 1);
  }

  handlePatch(key: string, data: unknown): void {
    this.patchValue.emit({
      key,
      data,
    });
  }

  handleUpdate(evt) {
    this.update.emit(evt);
  }
}
