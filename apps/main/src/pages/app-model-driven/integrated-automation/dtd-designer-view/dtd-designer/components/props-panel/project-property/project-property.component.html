<div class="project-property">
  <div class="header">
    <span>{{ 'dj-项目设置' | translate }}</span>
  </div>

  <div class="project-property-container">
    <nz-spin class="project-property-spin" [nzSpinning]="projectLoading">
      <!-- FIXME: 初始状态不存在，模版报错，先简单处理 -->
      <ng-container *ngIf="projectLoading === false">
        <div #contentRef class="content">
          <!-- 基本信息 -->
          <app-basic-info
            [currentProject]="currentProject"
            [modalFlag]="modalFlag"
            [isMainPro]="isMainPro"
            [isRoot]="isRoot"
            [editable]="!disabledAll"
            [contentRef]="contentRef"
            [checkContentChangeWithoutSave]="checkContentChangeWithoutSave.bind(this)"
            (openDesigner)="handlePageDesign($event)"
            (patchValue)="handlePatch($event)"
            (update)="handleUpdate($event)"
            (initOrEndChanged)="initOrEndChanged()"
            (reload)="handleReloadProject()"
            (refresh)="handleRefreshProjects()"
            (triggerSave)="triggerSave($event)"
            (saveAndRefresh)="handleSaveAndRefresh()"
            (refreshDataStateList)="handleRefreshDataStateList()"
          ></app-basic-info>

          <!-- 只有可追踪才能走如下配置项 -->
          <ng-container *ngIf="isTraceable">
            <!-- 高级设置 -->
            <app-advanced-settings
              [editable]="!disabledAll"
              [currentProject]="currentProject"
              (patchValue)="handlePatch($event)"
            ></app-advanced-settings>

            <!-- 里程碑 -->
            <app-milestone
              #milestoneComponentRef
              [currentProject]="currentProject"
              [modalFlag]="modalFlag"
              [editable]="!disabledAll"
              [invalid]="milestoneInvalid"
              (inited)="handleMilestoneInited()"
              (update)="handleValidMilestone()"
            ></app-milestone>

            <!-- 推送数据 -->
            <app-data-push
              [editable]="!disabledAll"
              [currentProject]="currentProject"
              (validChanged)="handleValidChanged('pushData', $event)"
            ></app-data-push>

            <!-- 当责者 -->
            <!-- choosePolicy基本上就相当于单选，加上兼容以前的值，直接给写死了 -->
            <app-responser
              [editable]="!disabledAll"
              [currentProject]="currentProject"
              (validChanged)="handleValidChanged('responser', $event)"
            ></app-responser>

            <!-- 来源 -->
            <app-source
              [editable]="!disabledAll"
              [currentProject]="currentProject"
              (validChanged)="handleValidChanged('source', $event)"
            ></app-source>

            <!-- 转派 -->
            <app-reassign
              *ngIf="currentProject.steps[currentProject.steps.length - 1].display"
              [editable]="!disabledAll"
              [currentProject]="currentProject"
              (validChanged)="handleValidChanged('reassign', $event)"
            ></app-reassign>
          </ng-container>
        </div>

        <!-- 保存 -->
        <ng-container *operateAuth="{ prefix: 'update', tenantPaas: isTenantActive, guards: [!disabledAll] }">
          <div class="save-wrapper">
            <button [nzLoading]="isSaving" ad-button adType="link" (click)="handleSave()">
              <i adIcon [iconfont]="'iconbaocun2'" aria-hidden="true" class="icon"></i
              ><span>{{ 'dj-保存设置项' | translate }}</span>
            </button>
          </div>
        </ng-container>
      </ng-container>
    </nz-spin>
  </div>
</div>
