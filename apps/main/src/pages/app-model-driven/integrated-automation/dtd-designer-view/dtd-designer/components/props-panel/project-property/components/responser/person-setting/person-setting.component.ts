import { AfterViewInit, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { PeopleSettingPropertyService } from './people-setting-property.service';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { cloneDeep, isArray, size, map, find, forEach, omit, delay } from 'lodash';
import { BindFormType } from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';

@Component({
  selector: 'app-person-setting',
  templateUrl: './person-setting.component.html',
  styleUrls: ['./person-setting.component.less'],
  providers: [PeopleSettingPropertyService],
})
export class PersonSettingComponent implements OnInit, OnChanges {
  // 是不是显示默认
  @Input() showDefault: boolean = false;
  @Input() personSingle: boolean = false;
  // 界面共用的区分
  @Input() personType: 'approve' | 'notification' | 'execution' = 'approve';
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 执行人的设定
  @Input() executor;
  // 核决层级配置
  @Input() decisionConfig;
  // 绑定的表单,用于请求来自模型的下拉值
  @Input() bindForm;
  // 前面的人工节点
  @Input() preManualNodes;
  @Input() editable: boolean = true;
  // 指定人员这边的数据改变
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  dataFormGroup: FormGroup;

  // 是否显示人员类型的下拉选项
  isShowPersonTypeOptions = false;
  // 当前选择的人员类型
  currentPersonType = 'iconrenyuan1';

  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  // 人员的选择列表
  personnel: any[] | string = [];
  // 变量选择列表
  showVariableModal: boolean = false;
  variable: any = {};
  // 通过关卡选择的人员
  activity: any = [];
  // 通过模型字段选择的人员
  formFiled: any = [];
  // 部门下拉选择的部门
  department: any[] = [];
  // 来自模型的字段下拉
  filedList: any[] = [];
  // 人员选择框是否标识标红
  peopleSelRequireClass = false;
  // 下拉选的值特殊处理的字段 personnel activity formFiled
  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    private peopleSettingPropertyService: PeopleSettingPropertyService,
    private viewApiService: ViewApiService,
  ) {}

  ngOnInit() {
    this.dataFormGroup = this.fb.group({
      type: [{ value: this.executor?.type, disabled: !this.editable }],
      source: [this.executor?.source],
      decisionConfig: this.fb.group({
        levelType: [{ value: this.decisionConfig?.levelType, disabled: !this.editable }, [Validators.required]],
        level: [{ value: this.decisionConfig?.level, disabled: !this.editable }, [Validators.required]],
      }),
    });
    this.dataFormGroup.get('type').valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.resetPersonnelSel();
      }
    });
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
    this.handleInit();
  }

  handleInit(): void {
    // 根据source的类型把下面的图标切到对于的图标上
    if ('personnel' === this.executor?.source) {
      this.currentPersonType = 'iconrenyuan1';
    } else if ('variable' === this.executor?.source) {
      this.currentPersonType = 'iconbianliang';
    } else if ('activity' === this.executor?.source) {
      this.currentPersonType = 'iconguanka';
    } else if ('formFiled' === this.executor?.source) {
      this.currentPersonType = 'iconlaizibiaodan';
    } else if ('department' === this.executor?.source) {
      this.currentPersonType = 'iconzuzhijigou';
    }
    // 对选择人员的回显处理
    if ('variable' === this.executor?.source) {
      this.variable = cloneDeep(this.executor.variable[0] ?? {});
    } else {
      if (this.executor.type === 'duty') {
        // 这个是个非数组
        if (size(this.executor.personnel) > 0) {
          this.personnel = this.executor.personnel[0].dutyCode;
        } else {
          this.personnel = '';
        }
      } else if (this.executor.type === 'personnel') {
        if (this.personSingle) {
          this.personnel = this.executor.personnel?.[0]?.empId;
        } else {
          // 这个是个数组
          this.personnel = cloneDeep(map(this.executor.personnel || [], (item) => item.empId));
        }
      } else {
        if (size(this.executor.personnel) > 0) {
          this.personnel = this.executor.personnel[0].empId;
        } else {
          this.personnel = '';
        }
      }
    }
    // 对选择来自关卡的回显处理
    this.activity = cloneDeep((this.executor.activity || []).map((item) => item.activityId));
    // [{"fieldId":"creater"}]=> ['creater'];
    // 对选择来自模型的回显处理
    this.formFiled = cloneDeep(map(this.executor.formFiled || [], (item) => item.fieldId));
    // 对选择来自部门的回显处理 ,部门这里肯定是 单选的
    this.department = size(this.executor.department) > 0 ? this.executor.department[0].deptNo : '';
    // 上面回显都是数组 适合personnel 类型的，非personnel类型的是单选
    if (this.executor.type !== 'personnel') {
      // 来自关卡、来自模型、来自部门是单选，就取第1个
      if (size(this.activity) > 0) {
        this.activity = this.activity[0];
      } else {
        this.activity = '';
      }
      if (size(this.formFiled) > 0) {
        this.formFiled = this.formFiled[0];
      } else {
        this.formFiled = '';
      }
    }

    delay(() => this.getCurrentData(), 200);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('bindForm')) {
      // 作业code的改变需要重新从后台拉取数据
      if ('' === this.bindForm.formCode) {
        // 需要置空下拉
        this.filedList = [];
      } else {
        this.loadFiledByModelCode();
      }
      this.formFiled = [];
    }
  }
  // 重置
  resetPersonnelSel() {
    this.handleSelectPersonType('iconrenyuan1');
    this.personnel = [];
    this.variable = {};
    this.activity = [];
    this.formFiled = [];
  }

  // 人员选择的改变
  handleSelChange() {
    this.updataRequireClassStatue();
    this.getCurrentData();
  }
  updataRequireClassStatue() {
    // 判断人员是否为空，为空红色标识处理
    const source = this.dataFormGroup.get('source').value;
    if ('personnel' === source) {
      if (isArray(this.personnel)) {
        this.peopleSelRequireClass = size(this.personnel) === 0;
      } else {
        if (this.personnel) {
          this.peopleSelRequireClass = false;
        } else {
          this.peopleSelRequireClass = true;
        }
      }
    } else if ('variable' === source) {
      if (this.variable.variableCode) {
        this.peopleSelRequireClass = false;
      } else {
        this.peopleSelRequireClass = true;
      }
    } else if ('activity' === source) {
      if (isArray(this.activity)) {
        this.peopleSelRequireClass = size(this.activity) === 0;
      } else {
        if (this.activity) {
          this.peopleSelRequireClass = false;
        } else {
          this.peopleSelRequireClass = true;
        }
      }
    } else if ('formFiled' === source) {
      if (isArray(this.formFiled)) {
        this.peopleSelRequireClass = size(this.formFiled) === 0;
      } else {
        if (this.formFiled) {
          this.peopleSelRequireClass = false;
        } else {
          this.peopleSelRequireClass = true;
        }
      }
    } else if ('department' === source) {
      if (isArray(this.department)) {
        this.peopleSelRequireClass = size(this.department) === 0;
      } else {
        if (this.department) {
          this.peopleSelRequireClass = false;
        } else {
          this.peopleSelRequireClass = true;
        }
      }
    }
  }

  requiredValidator(): ValidatorFn {
    return (group: FormGroup): ValidationErrors => {
      const sourceControl = group.controls['source'];
      const personnelControl = group.controls['personnel'];
      const activityControl = group.controls['activity'];
      const formFiledControl = group.controls['formFiled'];
      if (sourceControl.value === 'personnel') {
        if (size(personnelControl.value) > 0) {
          personnelControl.setErrors(null);
        } else {
          personnelControl.setErrors({ required: true });
        }
      } else if (sourceControl.value === 'activity') {
        if (size(activityControl.value) > 0) {
          activityControl.setErrors(null);
        } else {
          activityControl.setErrors({ required: true });
        }
      } else if (sourceControl.value === 'formFiled') {
        if (size(formFiledControl.value) > 0) {
          formFiledControl.setErrors(null);
        } else {
          formFiledControl.setErrors({ required: true });
        }
      }
      return;
    };
  }
  // 人员类型的改变
  handleChangePersonType(): void {
    if (!this.editable) return;
    this.isShowPersonTypeOptions = !this.isShowPersonTypeOptions;
  }

  handleSelectPersonType(type): void {
    this.currentPersonType = type;
    if ('iconrenyuan1' === type) {
      this.dataFormGroup.get('source').setValue('personnel');
      this.variable = {};
    } else if ('iconbianliang' === type) {
      this.dataFormGroup.get('source').setValue('variable');
      this.personnel = [];
    } else if ('iconguanka' === type) {
      return;
    } else if ('iconlaizibiaodan' === type) {
      this.dataFormGroup.get('source').setValue('formFiled');
    } else if ('iconzuzhijigou' === type) {
      this.dataFormGroup.get('source').setValue('department');
    }
    this.getCurrentData();
    this.isShowPersonTypeOptions = false;
  }

  getSourceTip() {
    const type = this.currentPersonType;
    if ('iconrenyuan1' === type) {
      return this.translate.instant('dj-人员');
    } else if ('iconguanka' === type) {
      return this.translate.instant('dj-来自关卡');
    } else if ('iconlaizibiaodan' === type) {
      return this.translate.instant('dj-来自模型');
    } else if ('iconzuzhijigou' === type) {
      return this.translate.instant('dj-来自部门');
    }
  }
  // 当鼠标移除是自动关闭人员选择下拉选项
  handleClosePersonTypeOptions(): void {
    this.isShowPersonTypeOptions = false;
  }

  get personnelList() {
    const type = this.dataFormGroup.get('type').value;
    let retList = [];
    if (['personnel', 'superior', 'director', 'decision'].includes(type)) {
      retList = this.viewApiService.empList;
    }
    if ('duty' === type) {
      retList = this.viewApiService.dutyList;
    }
    return retList;
  }

  // 当也页面数据改变时，对外暴露最新的数据
  getCurrentData() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    //decisionConfig
    this.dataFormGroup.get('decisionConfig').get('levelType').markAsDirty();
    this.dataFormGroup.get('decisionConfig').get('levelType').updateValueAndValidity();
    this.dataFormGroup.get('decisionConfig').get('level').markAsDirty();
    this.dataFormGroup.get('decisionConfig').get('level').updateValueAndValidity();
    this.formGroupValidityFlag = false;
    this.updataRequireClassStatue();
    let currentData = this.dataFormGroup.getRawValue();
    if ('personnel' === currentData.source) {
      currentData.variable = [];
      // 这里有可能是存部门信息
      if (currentData.type === 'duty') {
        // 选择了部门
        if (isArray(this.personnel)) {
          currentData.personnel = [];
          forEach(this.personnel, (item) => {
            const findPersonnelItem = find(this.viewApiService.dutyList, (duty) => duty.id === item);
            if (findPersonnelItem) {
              currentData.personnel.push({
                dutyId: findPersonnelItem.sid,
                dutyCode: findPersonnelItem.id,
                dutyName: findPersonnelItem.name,
              });
            }
          });
        } else {
          currentData.personnel = [];
          const findPersonnelItem = find(this.viewApiService.dutyList, (duty) => duty.id === this.personnel);
          if (findPersonnelItem) {
            currentData.personnel.push({
              dutyId: findPersonnelItem.sid,
              dutyCode: findPersonnelItem.id,
              dutyName: findPersonnelItem.name,
            });
          }
        }
      } else {
        // 选择了人
        if (isArray(this.personnel)) {
          currentData.personnel = [];
          forEach(this.personnel, (item) => {
            const findPersonnelItem = find(this.viewApiService.empList, (emp) => emp.id === item);
            if (findPersonnelItem) {
              currentData.personnel.push({
                userId: findPersonnelItem.userId,
                empId: findPersonnelItem.id,
                empName: findPersonnelItem.name,
              });
            }
          });
        } else {
          currentData.personnel = [];
          const findPersonnelItem = find(this.viewApiService.empList, (emp) => emp.id === this.personnel);
          if (findPersonnelItem) {
            currentData.personnel.push({
              userId: findPersonnelItem.userId,
              empId: findPersonnelItem.id,
              empName: findPersonnelItem.name,
            });
          }
        }
      }
    } else if ('variable' === currentData.source) {
      currentData.personnel = [];
      if (this.variable.variableCode) {
        currentData.variable = [{ ...this.variable }];
      } else {
        currentData.variable = [];
      }
    } else if ('activity' === currentData.source) {
      if (isArray(this.activity)) {
        currentData.activity = map(this.activity, (item) => ({ activityId: item })) || [];
      } else {
        if (this.activity) {
          currentData.activity = [{ activityId: this.activity }];
        } else {
          currentData.activity = [];
        }
      }
    } else if ('formFiled' === currentData.source) {
      // ['creater']=>[{"fieldId":"creater"}];
      if (isArray(this.formFiled)) {
        currentData.formFiled = map(this.formFiled, (item) => ({ fieldId: item })) || [];
      } else {
        if (this.formFiled) {
          currentData.formFiled = [{ fieldId: this.formFiled }];
        } else {
          currentData.formFiled = [];
        }
      }
    } else if ('department' === currentData.source) {
      // 直接转数组
      const findDeptItem = find(this.viewApiService.deptList, (dept) => dept.id === this.department);
      if (findDeptItem) {
        currentData.department = [
          {
            deptId: findDeptItem.sid,
            deptNo: findDeptItem.id,
            deptName: findDeptItem.name,
          },
        ];
      } else {
        currentData.department = [];
      }
    }
    // decisionConfig 要移到外层
    const decisionConfig = currentData.decisionConfig;
    currentData = omit(currentData, 'decisionConfig');
    const returnData = {
      executor: currentData,
      decisionConfig,
    };
    if (returnData?.executor) {
      // 这个一定会有值，没有值认为是异常场景就不再给父组件，避免异常数据被存储起来
      this.changeData.emit(returnData);
    }
    return returnData;
  }

  loadFiledByModelCode() {
    if (
      this.bindForm.modeCode &&
      this.bindForm.serviceCode &&
      this.bindForm.formCode &&
      this.bindForm.formCode !== '' &&
      this.bindForm?.type !== BindFormType.PageView
    ) {
      this.peopleSettingPropertyService.findFiledByModelCode(this.bindForm).subscribe((res) => {
        this.filedList = res.data;
      });
    }
  }

  get variableName(): string {
    const { variableCode, variableType, variablePath } = this.variable;
    if (variableCode) {
      if (['businessObjectVariable', 'modelVariable'].includes(variableType)) {
        return variablePath;
      } else {
        return variableCode;
      }
    }
  }

  handleValueChanged(event: any): void {
    this.variable = {
      variableCode: event,
    };
    this.handleSelChange();
  }
}
