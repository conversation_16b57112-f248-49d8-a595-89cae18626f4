import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { ApiService } from '../../task-widget/service/api.service';

import { GraphComponent } from '../../../graph/graph.component';
import { TaskListComponent } from '../../../task/task-list.component';
import { PropsPanelApiService } from '../props-panel-api.service';
import { clone, isInternalTenant } from 'common/utils/core.utils';
import { LocaleService } from 'common/service/locale.service';
import { AdUserService } from 'pages/login/service/user.service';

import { takeUntil } from 'rxjs/internal/operators/takeUntil';
import { debounceTime } from 'rxjs/internal/operators/debounceTime';
import { Subject } from 'rxjs/internal/Subject';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PushDataComponent } from './components/push-data/push-data.component';
import { SchemeDesignComponent } from './components/scheme-design/scheme-design.component';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';
import { delay, isEqual, pick } from 'lodash';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { AppService } from 'pages/apps/app.service';
import { GlobalService } from 'common/service/global.service';

@Component({
  selector: 'app-flow-task-props',
  templateUrl: './flow-task-props.component.html',
  styleUrls: ['./flow-task-props.component.less'],
  providers: [],
})
export class FlowTaskPropsComponent implements OnInit, AfterViewInit {
  // 输入的渲染数据
  @Input() params: any = {};
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  @Output() clearUiKey: EventEmitter<any> = new EventEmitter();
  @Input() graphComponentRef: GraphComponent;
  @Input() taskComponentRef: TaskListComponent;
  @Output() formChange: EventEmitter<any> = new EventEmitter();

  @ViewChild('taskPushData') taskPushData: PushDataComponent;
  @ViewChild('taskSchemaDesign') taskSchemaDesign: SchemeDesignComponent;

  public readonly errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  public readonly driveOptions: { label: string; value: string }[] = [
    { label: this.translate.instant('dj-驱动Flow'), value: 'flow' },
    { label: this.translate.instant('dj-驱动API'), value: 'api' },
  ];
  public readonly executeTypes = [
    { value: 'auto', label: this.translate.instant('dj-自动') },
    { value: 'manual', label: this.translate.instant('dj-人工') },
  ];
  public readonly taskTypes = [
    {
      value: 'business',
      label: 'business (' + this.translate.instant('dj-业务型任务(默认)') + ')',
    },
    { value: 'approve', label: 'approve (' + this.translate.instant('dj-签核型') + ')' },
    { value: 'solve', label: 'solve (' + this.translate.instant('dj-异常排除') + ')' },
    { value: 'wait', label: 'wait (' + this.translate.instant('dj-等待类型') + ')' },
    { value: 'assist', label: 'assist (' + this.translate.instant('dj-辅助类型') + ')' },
  ];

  public readonly businessModelType = [
    { value: 'BUSINESS', label: `BUSINESS (${this.translate.instant('dj-业务处理')})` },
    { value: 'CUSTOM', label: `CUSTOM (${this.translate.instant('dj-自定义')})` },
    { value: 'NONE', label: `NONE (${this.translate.instant('dj-不定义')})` },
  ];
  public readonly businessType = [
    { value: 'PROCESS', label: `PROCESS (${this.translate.instant('dj-数据处理')})` },
    { value: 'APPROVAL', label: `APPROVAL (${this.translate.instant('dj-签核')})` },
    { value: 'REPLY', label: `REPLY (${this.translate.instant('dj-回复')})` },
    { value: 'SOLVE', label: `SOLVE (${this.translate.instant('dj-异常排除')})` },
    { value: 'DATA_ENTRY', label: `DATA_ENTRY (${this.translate.instant('dj-表单')})` },
  ];

  public readonly priorities = [
    { value: -1, label: this.translate.instant('dj-低') },
    { value: 0, label: this.translate.instant('dj-中') },
    { value: 1, label: this.translate.instant('dj-高') },
  ];
  public readonly dateUnitTypes = [
    { value: 'DAY', label: this.translate.instant('dj-天') },
    { value: 'HOUR', label: this.translate.instant('dj-小时') },
    { value: 'MINUTE', label: this.translate.instant('dj-分钟') },
  ];
  public loading: boolean = false;

  public lang: Record<
    string,
    {
      zh_CN: string;
      zh_TW: string;
      en_US: string;
    }
  > = {};

  public dataFormGroup: FormGroup;

  // 当前任务的数据
  public currentTask: any;
  // 当前的数据
  public datamapList: any[] = [];
  // 数据特征
  public featureList: any[] = [];

  public allTaskData: any[] = [];
  //当前应用数据群落
  public currentGraphData: any = undefined;
  public stateMaps: any[] = [];
  public exceptCurrentTaskList: any[] = [];
  currentTaskData: any;
  isLoaded: boolean;
  isShowJsonModal: boolean;
  extra: {
    relaDataEntryCodes: any[]; //表单类型的任务卡下拉选项
    allowSubLevelApprove: boolean;
    allDataStatus: any;
    modalFlag: any;
    executeType: any;
    typeData: any;
    performerType: { value: string; label: any }[];
    processType: { value: string; label: any }[];
    choosePolicy: { value: string; label: any }[];
    groupData: any[]; //数据群落
  };
  originTaskList: any;
  relatedScenes: any[];
  decisions: any[] = [];
  private destory$ = new Subject();
  originData: any;
  generateProcessLoading: boolean;
  taskList: any; //当前应用的所有任务，从画布中获取的
  locale: string;
  application: string;
  private flagLeave: boolean = false; // 是否离开
  translating: boolean;
  extendHeader: any;
  isTenantActive = false; // 租户级 开发平台 是否激活

  get isReference() {
    return this.appService.selectedApp?.tag?.sourceComponent === 'BC'; // 是否是引用应用
  }

  get isReferenceNode() {
    return this.params?.bcReference || this.params?.ngArguments?.bcReference;
  }

  get workType() {
    return this.currentTaskItem?.['designType'] === 'jsonDesign' ? 2 : !!this.currentTaskItem?.projectCode ? 1 : 0;
  }

  public currentTaskItem: any;

  get disabledAll() {
    return !!this.dtdDesignerService?.fromManual;
  }

  constructor(
    public translate: TranslateService,
    private userService: AdUserService,
    private fb: FormBuilder,
    private apiService: ApiService,
    private propsPanelService: PropsPanelApiService,
    private message: NzMessageService,
    public dtdDesignerService: DtdDesignerViewService,
    private modal: AdModalService,
    private languageService: LocaleService,
    private appService: AppService,
  ) {
    this.locale = this.languageService?.currentLocale || 'zh_CN';
    this.initForm();
  }
  get teamId(): string {
    return this.userService.getUser('teamId');
  }
  async ngOnInit() {
    await this.prepareData();
    this.dtdReferenceDisabled();
    this.isTenantActive = !!this.userService.getUser('isTenantActive');
  }
  async ngOnChanges(change) {
    if (!change.params.firstChange && change.params.currentValue.code !== change.params.previousValue.code) {
      this.dataFormGroup.reset();
      await this.prepareData();
    }
    this.dtdReferenceDisabled();
  }

  dtdReferenceDisabled() {
    if (this.isReferenceNode || this.disabledAll) {
      this.dataFormGroup.disable();
    } else {
      this.dataFormGroup?.enable();
    }
  }

  ngAfterViewInit() {
    // 在这里执行渲染完成后的操作
    this.dataFormGroup.valueChanges.pipe(takeUntil(this.destory$), debounceTime(100)).subscribe((value) => {
      this.checkContentChangeWithoutSave((isChange) => {
        // 如果表单变动，则重置离开标识
        isChange && (this.flagLeave = false);
      });
      this.dataFormGroup.get('driveType').disable();
      this.formChange.emit(this.dataFormGroup);
    });
    this.dtdDesignerService.setContentChangeCheckObject({
      contentComponent: this,
      checkFunction: 'checkContentChangeWithoutSave',
    });
  }
  ngOnDestroy(): void {
    this.dtdDesignerService.resetContentChangeCheckObject();
    this.destory$.next();
    this.destory$.complete();
  }
  initForm() {
    this.loading = true;

    //构建任务form
    this.dataFormGroup = this.fb.group({
      code: [null, [Validators.required]],
      name: [null, [Validators.required]],
      groupCode: [null, [Validators.required]], //数据群落
      tenantId: [null, [Validators.required]],
      category: [{ value: null, disabled: true }, [Validators.required]], //业务类型
      pattern: [null, [Validators.required]], //业务模式
      priority: [null], //优先级
      milestone: [null], //是否里程碑
      isHideKey: [null],
      executeType: [{ value: null, disabled: true }, [Validators.required]], //执行方式
      relaDataEntryCode: [null], //表单类型任务卡的表单
      type: [{ value: null, disabled: true }, [Validators.required]], //任务类型
      driveType: [{ value: null, disabled: true }, [Validators.required]], // 驱动类型
      merge: [null], //是否合并
      flowCode: [null],
      supportPart: [null],
      supportSplit: [null],
      pageCode: [null],
      dueDateBusiness: [false],
      dueDateValue: [null],
      dueDateVariable: [null],
      dueDateType: [null],
      dueDateFormat: [null],
      dueDateComponentValue: [null],
      dueDateComponentDataType: [null],
      startApproveActivity: [null], //提交签核的任务
      relatedCategories: [null],
      assignAble: [null],
      evlModelCode: [null], //评价模型
      atmcDatas: new FormArray([]), //推送数据
      groupField: [null], //方案字段
      resolvePlans: new FormArray([]), //处理方案
      allowSubLevel: [false],
      subApproveConditionDropDown: [null],
      featureSets: [null],
      extendFields: this.fb.group({
        closeNeedConfirm: false, //是否卡控
      }),
    });

    //一些特殊任务类型需要的参数
    this.extra = {
      relaDataEntryCodes: [],
      groupData: [], //数据群落
      allowSubLevelApprove: true,
      modalFlag: 'edit',
      executeType: this.executeTypes,
      typeData: this.taskTypes,
      performerType: [
        { value: 'user', label: this.translate.instant('dj-用户') },
        { value: 'duty', label: this.translate.instant('dj-职能') },
        {
          value: 'deptDirector',
          label: this.translate.instant('dj-部门主管'),
        },
        {
          value: 'deptUser',
          label: this.translate.instant('dj-部门人员'),
        },
        {
          value: 'personInCharge',
          label: this.translate.instant('dj-当责者'),
        },
        { value: 'activity', label: this.translate.instant('dj-活动') },
        {
          value: 'userSuperior',
          label: this.translate.instant('dj-直属主管'),
        },
      ],
      processType: [
        {
          value: 'signOr',
          label: this.translate.instant('dj-仅需一人处理'),
        },
        {
          value: 'signAnd',
          label: this.translate.instant('dj-所有人都需处理'),
        },
      ],
      choosePolicy: [
        { value: 'single', label: this.translate.instant('dj-单人') },
        { value: 'all', label: this.translate.instant('dj-多人') },
      ],
      allDataStatus: [],
    };
    this.dataFormGroup.valueChanges.pipe(takeUntil(this.destory$), debounceTime(100)).subscribe((value) => {
      this.formChange.emit(this.dataFormGroup);
    });
  }
  private async prepareData(): Promise<void> {
    //当前应用的所有任务，从画布中获取的
    // this.currentTaskData = this.taskComponentRef.taskList?.find(
    //   (item: { code: string }) => item.code === this.params.code,
    // );
    this.application = this.taskComponentRef.queryParams.appCode;
    this.loading = true;
    // console.log("9999=======", this.currentTaskData, this, this.taskComponentRef.queryParams)
    try {
      await this.loadCurrentTaskData();
      this.loadCategory();
      this.loadTaskData();
      this.loadLcdpDataEntryData();
      this.loadDataFeatures();
      this.addObserver();
    } finally {
      this.loading = false;
    }
  }
  private addObserver() {
    this.graphComponentRef.graphService.dataState$.pipe(takeUntil(this.destory$)).subscribe((value) => {
      if (value === null) return;
      const item = value.find((e) => e.code === this.params.code);
      if (!item) return;
      this.currentTask.stateMaps = item?.stateMaps;
    });
  }
  /**
   * 加载表单列表
   * @returns
   */
  private async loadLcdpDataEntryData(): Promise<void> {
    const result = (await this.apiService.loadLcdpDataEntryData(this.application).toPromise()) as any;
    if (result.data?.length) {
      //表单类型任务卡的下拉选项
      this.extra.relaDataEntryCodes = result.data.map((item) => ({ value: item.code, label: item.name })) || [];
    }
  }
  /**
   * 请求当前应用下的所有任务
   * @returns
   */
  private async loadTaskData(): Promise<void> {
    const res = (await this.apiService.loadTaskData(this.application).toPromise()) as any;
    if (res.code === 0) {
      this.allTaskData = [];
      (res.data || []).forEach((d) => {
        const { children = [] } = d;
        d.children = children.map((s) => {
          const hasLang = s.lang?.name && JSON.stringify(s.lang?.name || {}) !== '{}';
          if (hasLang) s.name = s.lang.name['zh_CN'] || s.name;
          return {
            ...s,
          };
        });
        d.openTask = true;
        //当前应用所有任务，只包括code和name
        this.allTaskData = [
          ...(this.allTaskData || []),
          ...children.map((s) => {
            return { value: s.code, label: s.name, lang: s.lang };
          }),
        ];
      });
      if (res.data.length > 0) {
        // 目前数据群落只会有1个，所以这里只取第一个，当前应用数据群落
        this.currentGraphData = res.data[0];
        // 画布中的数据会有延迟更新的情况，导致当前任务数据拿不到，这里从接口中重新获取一次
        if (!this.currentTaskData) {
          this.currentTaskData = this.currentGraphData.children?.find(
            (item: { code: string }) => item.code === this.params.code,
          );
        }
      }
      //数据群落下拉数据
      this.extra.groupData = (res.data || []).map((s) => {
        return { value: s.code, label: s.name };
      });
    }
  }
  /**
   * 当前租户下的所有应用
   * @returns
   */
  private async loadCategory(): Promise<void> {
    const res = (await this.propsPanelService.getCategory().toPromise()) as any;
    const { code, data, message } = res;
    if (code === 0) {
      data.forEach((item) => {
        const hasLang = item.nameLang && JSON.stringify(item.nameLang) !== '{}';
        if (hasLang) item.name = item.nameLang[this.locale];
        if (isInternalTenant(this.teamId)) {
          if (item.code === 'cate1_541005141869120' || item.code === 'cate2_541005141869121') {
            item.disabled = true;
          }
        }
        if (!item.parentCode && item.level === 1) item.disabled = true;
      });
      //关联场景的下拉数据
      this.relatedScenes = this.handleBuildSceneTree('', data);
    }
  }
  //整理数据
  handleBuildSceneTree(pCode: string, sceneList: any[]): any[] {
    const child = sceneList.filter((item) => item.parentCode === pCode);
    return child.map((item) => {
      const children = this.handleBuildSceneTree(item.code, sceneList);
      if (children.length > 0) {
        return {
          ...item,
          key: item.code,
          children: children,
          title: item.name,
          isLeaf: false,
        };
      } else {
        return {
          ...item,
          key: item.code,
          title: item.name,
          isLeaf: true,
        };
      }
    });
  }

  private loadDataFeatures() {
    this.featureList = [];
    const params = {
      code: this.params.code,
      ...(this.getReferenceTask() || {}),
    };
    this.apiService.getDataFeatures(params).subscribe((res) => {
      this.featureList = [];
      res.data?.forEach((r) => {
        this.featureList.push(...r.features);
      });
    });
  }

  private async loadCurrentTaskData(): Promise<void> {
    const params = {
      code: this.params.code,
      ...(this.getReferenceTask() || {}),
    };
    const res = (await this.apiService.loadCurrentTaskData(params).toPromise()) as any;
    // this.currentTask['groupCode'] = this.currentGraphData.code;
    // this.currentTask['manualAble'] = !!this.currentTaskData?.extendFields?.projectCode;
    if (res.code === 0) {
      this.handleEditData(res.data || {});
      this.originTaskList = this.taskComponentRef.originTaskList.find((e) => e.code === this.currentTaskData.code);
      this.exceptCurrentTaskList = this.allTaskData.filter((data) => data.value != this.currentTaskData.code);
    }
  }
  async handleEditData(data: any) {
    this.handleLoadDecisionList();
    this.dataFormGroup.patchValue({
      code: data.code,
      name: data.name,
      groupCode: data.groupCode,
      tenantId: data.tenantId,
      category: data.category,
      pattern: data.pattern || '',
      priority: data.priority,
      milestone: data.milestone || false,
      isHideKey: data.isHideKey || false,
      executeType: data.executeType || '',
      relaDataEntryCode: data.relaDataEntryCode || '',
      type: data.type || '',
      driveType: data.driveType || 'flow',
      merge: data.merge || false,
      flowCode: data.flowCode || '',
      supportPart: data.config?.supportPart || false,
      supportSplit: data.config?.supportSplit || false,
      pageCode: data.pageCode,
      dueDateBusiness: data.dueDate?.isBusiness || false,
      dueDateValue: data.dueDate?.value || '',
      dueDateVariable: data.dueDate?.timeVariable || null,
      dueDateType: data.dueDate?.datatype || '',
      dueDateFormat: data.dueDate?.format || '',
      dueDateComponentValue: data.dueDate?.componentValue || '',
      dueDateComponentDataType: data.dueDate?.componentDataType || '',
      startApproveActivity: data.startApproveActivity?.[0] || '',
      relatedCategories: data.dimension?.categories?.[0] || '',
      assignAble: data.assignConfig?.assignAble || false,
      evlModelCode: data.evlModelCode || null,
      groupField: data?.config?.groupField || '', //方案字段
      allowSubLevel: data.allowSubLevel || false,
      subApproveConditionDropDown: data.subApproveConditionDropDown,
      featureSets: data.dataFeatures?.map((df) => df.code) || [],
      extendFields: data.extendFields || {},
    });

    this.currentTaskItem = this.taskComponentRef?.taskList?.find((task) => task.code === data.code);
    this.currentTask = data;
    this.currentTaskData = data;
    this.originData = this.buildData(this.dataFormGroup.getRawValue());
    this.originData.atmcDatas = clone(data.atmcDatas) || [];
    this.originData.resolvePlans = clone(data.resolvePlans) || [];
    this.extra.allowSubLevelApprove = data.allowSubLevel || false;
    this.lang = data.lang;

    // this.currentTask['groupCode'] = this.currentGraphData?.code;
    this.currentTask['manualAble'] = !!this.currentTaskData?.extendFields?.projectCode;
    let extendHeader = this.getReferenceTask();
    if (extendHeader) {
      this.extendHeader = {
        adpVersion: extendHeader.adpVersion,
        adpStatus: extendHeader.adpStatus,
      };
    } else {
      this.extendHeader = this.dtdDesignerService.adpVersionHeaders;
    }
    if (this.dtdDesignerService.adpVersionHeaders) {
      this.extendHeader = Object.assign(this.extendHeader, this.dtdDesignerService.adpVersionHeaders)
    }
    await this.handleLoadDataStatus();
    this.loading = false;
  }
  // 获取数据状态
  async handleLoadDataStatus(): Promise<void> {
    const { groupCode, code, application } = this.currentTaskData || {};
    const res = await this.apiService.loadDataStates({ application, groupCode }).toPromise();
    if (res.code === 0) {
      this.extra.allDataStatus = (res.data || []).map((s) => {
        return { value: s.code, label: s.name };
      });
    }
  }
  async handleLoadDecisionList(): Promise<void> {
    this.apiService.getDecisionList(this.application).subscribe((res: any) => {
      this.decisions = (res.data || []).map((item) => {
        return {
          name: item.name,
          code: item.code,
        };
      });
    });
  }

  openShowJsonModal(e, type) {
    e.stopPropagation();
    e.preventDefault();
    if (!this.currentTask) return;
    if (type === 'assignAble') {
      if (!this.currentTask?.assignConfig) {
        this.currentTask.assignConfig = {};
      }
      this.currentTask.assignConfig.assignAble = this.dataFormGroup.get('assignAble').value;
    }
    if (type === 'jsonModal' || this.dataFormGroup.get('assignAble').value) {
      this.isShowJsonModal = true;
    }
  }

  handleClosePanel(): void {
    this.close.emit();
  }
  // 赋值lang
  handlePatchLang(key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({ [key]: data?.value });
    this.dataFormGroup.get(key).markAsDirty();
    this.dataFormGroup.get(key).updateValueAndValidity({ onlySelf: true });
  }

  /**
   * 保存
   * 外部也可用
   * @returns
   */
  public async handleSave(needTips = true): Promise<any> {
    if (this.isReferenceNode) return; // 引用任务不保存
    if (this.translating) {
      this.message.error(this.translate.instant('dj-正在翻译多语言，请稍后再保存'));
      return false;
    }
    this.currentTask.formDirty = true;
    if (!this.handleValidate()) {
      return false;
    }
    const flag = this.extra['modalFlag'];
    let paramScene;
    const param = this.buildData(this.dataFormGroup.getRawValue());
    // this.currentTask = this.buildData(this.dataFormGroup.getRawValue());
    this.loading = true;
    try {
      const res = await this.apiService.saveTask(flag, paramScene ?? param).toPromise();
      if (res.code === 0) {
        //
        needTips && this.message.success(this.translate.instant('dj-保存成功！'));
        this.loading = false;
        // 更新任务节点名称
        this.graphComponentRef.updateNodeName('task', param.code, param.name, param.lang);
        // 保存画布节点关系
        this.graphComponentRef.saveGraph(false);
        // 刷新任务列表
        this.graphComponentRef.taskComponent?.fetchTasks();
        this.originData = param;
      } else {
        this.loading = false;
      }
    } catch (error) {
      this.loading = false;
    }
  }
  buildData(data) {
    const { stateMaps = [], versionNumber } = this.currentTask || {};

    const {
      supportPart,
      supportSplit,
      dueDateBusiness,
      dueDateValue,
      dueDateVariable,
      dueDateFormat,
      dueDateComponentDataType,
      startApproveActivity,
      dueDateComponentValue,
      relatedCategories,
      assignAble,
      relaDataEntryCode,
      atmcDatas,
      resolvePlans,
      groupField,
      allowSubLevel,
      subApproveConditionDropDown,
      featureSets,
      evlModelCode,
      ...param
    } = data;

    param['relaDataEntryCode'] = relaDataEntryCode || '';
    if (featureSets?.length > 0) {
      param['dataFeatures'] = featureSets.map((e) => {
        return {
          code: e,
          weight: 1,
        };
      });
    } else {
      param['dataFeatures'] = [];
    }
    param['evlModelCode'] = evlModelCode;
    param['versionNumber'] = versionNumber;
    param['application'] = this.application;
    param['startApproveActivity'] = !!startApproveActivity ? [startApproveActivity] : [];
    param['startApproveActivityName'] = !!startApproveActivity
      ? this.allTaskData?.find((s) => s.value === startApproveActivity)?.label || ''
      : '';
    param['atmcDatas'] = atmcDatas || [];
    param['config'] = {
      supportPart,
      supportSplit,
    };

    param['dueDate'] = {
      isBusiness: dueDateBusiness,
      value: dueDateValue,
      datatype: !!dueDateBusiness ? 'string' : 'second',
      componentValue: dueDateComponentValue,
      componentDataType: dueDateComponentDataType,
    };
    if (dueDateBusiness) {
      param['dueDate']['format'] = dueDateFormat;
    }

    if (dueDateBusiness) {
      param['dueDate']['timeVariable'] = dueDateVariable;
    }
    param['priority'] = Number(param['priority']);
    param['resolvePlans'] = resolvePlans.map((s) => {
      if (param['type'] !== 'solve' && s.hasOwnProperty('projectCode')) {
        delete s['projectCode'];
      }
      return s;
    });
    param['config']['groupField'] = groupField;
    param['stateMaps'] = stateMaps;
    param['assignConfig'] = {
      assignAble: assignAble || false,
    };
    param['dimension'] = relatedCategories ? { categories: [relatedCategories] } : { categories: [] };
    param['lang'] = this.lang;
    param['flowCode'] = param['flowCode']?.replace(/\s+/g, '');
    param['allowSubLevel'] = allowSubLevel || false;

    if (allowSubLevel) {
      param['subApproveConditionDropDown'] = subApproveConditionDropDown || '';
    }

    if (this.currentTask?.projectCode) {
      param['projectCode'] = this.currentTask?.projectCode;
    }
    if (this.currentTaskData?.patternType) {
      param['patternType'] = this.currentTaskData?.patternType;
    }
    return param;
  }
  handleValidate() {
    let validate = true;

    this.currentTask['errorKeys'] = [];

    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      if (i === 'relaDataEntryCode') {
        if (
          'manual' === this.dataFormGroup.controls['executeType'].value &&
          'DATA_ENTRY' === this.dataFormGroup.controls['category'].value &&
          'BUSINESS' === this.dataFormGroup.controls['pattern'].value
        ) {
          // relaDataEntryCode 只有 人工+DATA_ENTRY 业务类型+ BUSINESS业务模式 才校验必填
          this.dataFormGroup.controls[i].setValidators([Validators.required]);
        } else {
          // 取消必填校验
          this.dataFormGroup.controls[i].setValidators([]);
        }
      }
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }

    if (this.dataFormGroup.invalid) {
      validate = false;
    }

    // activeCode=executor的invalid
    const { subApproveConditionDropDown } = this.dataFormGroup.getRawValue();
    if (this.extra?.allowSubLevelApprove && !subApproveConditionDropDown) {
      validate = false;
      this.currentTask['errorKeys'].push('subApproveConditionDropDown');
    }

    return validate;
  }

  // 界面设计 / 自定义卡面设计
  public handlePageDesign(type): void {
    this.taskComponentRef.handlePageDesign(
      {
        ...this.currentTask,
        fromType: type,
        ...this.dataFormGroup.getRawValue(),
        isFromDtdReference: this.isReferenceNode,
      },
      false,
    );
    this.taskComponentRef.interfaceDesignClose$.pipe(takeUntil(this.destory$)).subscribe((res) => {
      if (!res.isSuccess) {
        return;
      }
      if (res.isCustom) {
        this.dataFormGroup.get('pattern').setValue('CUSTOM');
        this.currentTask.pattern = 'CUSTOM';
        this.originData.pattern = 'CUSTOM';
      } else {
        this.dataFormGroup.get('pattern').setValue('BUSINESS');
        this.currentTask.pattern = 'BUSINESS';
        this.originData.pattern = 'BUSINESS';
      }
    });
  }

  // mobile界面设计
  public handleMobilePageDesign(): void {
    this.taskComponentRef.handlePageDesign({ ...this.currentTask, isFromDtdReference: this.isReferenceNode }, true);
  }
  // 活动设计
  public handleMenuFlow(): void {
    if (this.checkContentChangeWithoutSave()) {
      this.modal.info({
        nzTitle: this.translate.instant('dj-数据未保存，请先保存后再进行其他操作！'),
        nzOkText: this.translate.instant('dj-确定'),
        nzOnOk: () => {},
      });
      return;
    }
    this.graphComponentRef.modalsRef.handleMenuFlow({
      data: { ...this.currentTask, isFromDtdReference: this.isReferenceNode },
    });
  }
  // API设计
  public handleApiDesign(): void {
    this.taskComponentRef.handleApiDesign({ ...this.currentTask, isFromDtdReference: this.isReferenceNode });
  }
  //专案
  public handleIndividualCase(): void {
    this.taskComponentRef.handleCase(this.currentTask);
  }

  public checkContentChangeWithoutSave(callback?: Function): boolean {
    if (this.loading) return false;

    const newData = this.buildData(this.dataFormGroup.getRawValue());
    const currendValue = pick(
      newData,
      'assignConfig',
      'atmcDatas',
      'config',
      'dataFeatures',
      'dimension',
      'dueDate',
      'evlModelCode',
      'merge',
      'milestone',
      'isHideKey',
      'name',
      'priority',
      'relaDataEntryCode',
      'resolvePlans',
      'startApproveActivity',
      'startApproveActivityName',
      'stateMaps',
      'pattern',
      'allowSubLevel',
      'subApproveConditionDropDown',
    );
    const values = pick(
      this.originData,
      'assignConfig',
      'atmcDatas',
      'config',
      'dataFeatures',
      'dimension',
      'dueDate',
      'evlModelCode',
      'merge',
      'milestone',
      'isHideKey',
      'name',
      'priority',
      'relaDataEntryCode',
      'resolvePlans',
      'startApproveActivity',
      'startApproveActivityName',
      'stateMaps',
      'pattern',
      'allowSubLevel',
      'subApproveConditionDropDown',
    );
    const isChanged = !isEqual(currendValue, values);

    callback && callback(isChanged);
    return isChanged && !this.flagLeave;
  }

  /**
   * 设置离开标识
   * @param value
   */
  public setFlagLeave(value: boolean) {
    this.flagLeave = value;
  }

  /**
   * 是否有文案正在翻译
   * @param loading
   */
  public handleTranslateLoading(event: { loading: boolean }) {
    this.translating = event.loading;
  }

  // 更新json
  handleSaveSet(data) {
    this.isShowJsonModal = false;
    if (!this.currentTask?.assignConfig) {
      this.currentTask.assignConfig = {};
    }
    this.currentTask.assignConfig = data || {};
    this.dataFormGroup.get('assignAble').setValue(this.currentTask.assignConfig?.assignAble);
  }

  getReferenceTask(): any {
    let headerInfo = null;
    if (this.params?.bcReference) {
      const currentData = this.taskComponentRef.originTaskList.find((e) => e.code === this.params.code);
      if (!!currentData) {
        headerInfo = {
          bcReference: this.params?.bcReference,
          adpStatus: currentData?.adpStatus,
          adpVersion: currentData?.adpVersion,
        };
      }
    }
    return headerInfo;
  }
}
