<!-- 列表 -->
<div class="list-item">
  <i
    *ngIf="isShowGroup"
    class="prefix-arrow"
    adIcon
    [type]="childrenVisible ? 'caret-down' : 'caret-right'"
    nzTheme="outline"
    (click)="toggleChildrenVisible()"
  ></i>

  <ng-container
    [ngTemplateOutlet]="itemTpl"
    [ngTemplateOutletContext]="{ data: itemData.dataDescription, isGroup: true }"
  ></ng-container>
</div>

<!-- 子列表 -->
<div *ngIf="itemData?.dataStateList?.length > 0 && childrenVisible" class="children">
  <ng-container
    *ngFor="let item of itemData?.dataStateList"
    [ngTemplateOutlet]="itemTpl"
    [ngTemplateOutletContext]="{ data: item, isGroup: false }"
  ></ng-container>
</div>

<!-- 列表项模板 -->
<ng-template #itemTpl let-data="data" let-isGroup="isGroup">
  <div
    *ngIf="isGroup || !data.bcReference"
    class="item-content"
    [ngClass]="{
      active: !isGroup && data?.code === selectedDataState?.code,
      'in-graph': graphStateNodeMap?.has(data.code),
      group: isGroup,
      valid: !!validMap?.get(data.code)
    }"
    (mousedown)="!isGroup && handleMouseDown($event, data, graphStateNodeMap?.has(data.code))"
  >
    <span class="name">{{ data.name }}</span>
    <div class="options-bar" *ngIf="isGroup">
      <i
        *ngIf="editable"
        adIcon
        iconfont="iconshezhianniu"
        class="iconfont options"
        nz-tooltip
        (click)="handleEdit(itemData)"
        [nzTooltipTitle]="'dj-设置' | translate"
      >
      </i>
      <ng-container *operateAuth="{ prefix: 'delete', guards: [dtdReference, editable] }">
        <i
          adIcon
          iconfont="icondelete3"
          class="iconfont options"
          nz-tooltip
          (click)="handleDelete(itemData)"
          [nzTooltipTitle]="'dj-删除' | translate"
        >
        </i>
      </ng-container>
    </div>
  </div>
</ng-template>
