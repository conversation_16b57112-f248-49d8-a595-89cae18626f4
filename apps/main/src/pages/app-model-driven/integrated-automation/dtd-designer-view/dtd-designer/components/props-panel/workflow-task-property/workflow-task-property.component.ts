import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { PropsPanelApiService } from '../props-panel-api.service';
import { AppService } from 'pages/apps/app.service';
import { GlobalService } from 'common/service/global.service';
import { cloneDeep, isEqual, pick } from 'lodash';
import { DtdDesignerViewService } from '../../../../service/dtd-designer-view.service';
import { GraphComponent } from '../../../graph/graph.component';
import { TaskListComponent } from '../../../task/task-list.component';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { WorkDesignType } from '../../../types';
import {
  EType,
  NodeType,
  PannelTabsType,
  SDNodeType,
} from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { initFlowGraph } from 'pages/app-model-driven/integrated-automation/view-designer/config/init-graph';
import { createUUID } from 'pages/app-model-driven/integrated-automation/view-designer/config/utils';
import { Router } from '@angular/router';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { validatorForm } from 'common/utils/core.utils';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-workflow-task-property',
  templateUrl: './workflow-task-property.component.html',
  styleUrls: ['./workflow-task-property.component.less'],
})
export class WorkflowTaskPropertyComponent implements OnInit, OnChanges, OnDestroy {
  @Input() data: any | undefined;
  @Input() graphComponentRef: GraphComponent;
  @Input() taskComponentRef: TaskListComponent;
  @Output() formChange: EventEmitter<any> = new EventEmitter();
  private flagLeave: boolean;

  private translating: boolean = false;
  isTenantActive = false; // 租户级 开发平台 是否激活

  get workType() {
    return this.taskTreeData?.['designType'] === 'jsonDesign' ? 2 : !!this.taskTreeData?.projectCode ? 1 : 0;
  }

  public readonly WorkDesignType = WorkDesignType;

  public readonly errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  public readonly driveOptions: { label: string; value: string }[] = [
    {
      label: '驱动Flow',
      value: 'flow',
    },
  ];
  // 是否正在获取数据
  public loading: boolean = false;

  // 是否正在生成流程
  public generateProcessLoading: boolean = false;

  public lang: Record<
    string,
    {
      zh_CN: string;
      zh_TW: string;
      en_US: string;
    }
  > = {};

  public dataFormGroup: FormGroup;

  public taskTreeData: any;
  // 当前任务的数据
  public currentTask: any | undefined = undefined;
  // 当前的数据
  public datamapList: any[] = [];
  // 数据特征
  public featureList: any[] = [];
  public currentGraphData: any = undefined;
  public stateMaps: any[] = [];

  // 原始数据，用于对比有没有修改过
  private originData: any;
  private destory$ = new Subject();

  // 获取是否为引用
  get isReference() {
    return this.data?.bcReference || this.data?.ngArguments?.bcReference;
  }

  constructor(
    private router: Router,
    private translate: TranslateService,
    private fb: FormBuilder,
    private apiService: PropsPanelApiService,
    private appService: AppService,
    public dtdDesignerService: DtdDesignerViewService,
    public globalService: GlobalService,
    private modal: AdModalService,
    private message: NzMessageService,
    public adUserService: AdUserService,
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.dtdDesignerService.setContentChangeCheckObject({
      contentComponent: this,
      checkFunction: 'checkContentChangeWithoutSave',
    });
    this.loadRemoteData();
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes.data.firstChange) {
      if (changes.data.previousValue?.code !== changes.data?.currentValue?.code) {
        this.currentGraphData = undefined;
        this.currentTask = undefined;
        this.datamapList = [];
        this.loadRemoteData();
      }
    }
    if (changes.data?.currentValue) {
      if (this.isReference) {
        this.dataFormGroup.disable();
      } else {
        this.dataFormGroup.enable();
      }
    }
  }

  ngOnDestroy(): void {
    this.dtdDesignerService.resetContentChangeCheckObject();
    this.destory$.next();
    this.destory$.complete();
  }

  public checkContentChangeWithoutSave(callback?: Function): boolean {
    if (this.loading) return false;
    const values = this.dataFormGroup.getRawValue();
    const currendValue = pick(values, 'name', 'featureSets');
    const isChanged = !isEqual(this.originData, {
      ...currendValue,
      lang: this.lang,
      inputData: this.currentTask?.inputData || {},
    });
    callback && callback(isChanged);

    return isChanged && !this.flagLeave;
  }

  // 多语言栏位改变的事件处理
  public handlePatchLang(key: string, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({ [key]: data?.value });
  }

  // 活动设计
  public async handleActive(): Promise<void> {
    if (this.checkContentChangeWithoutSave()) {
      this.modal.info({
        nzTitle: this.translate.instant('dj-数据未保存，请先保存后再进行其他操作！'),
        nzOkText: this.translate.instant('dj-确定'),
        nzOnOk: () => {},
      });
      return;
    }

    // 引用类型的节点，如果没有processId，则不可以进入
    if (!!this.isReference && !this.currentTask?.processId) {
      this.message.error(this.translate.instant('dj-当前引用的任务下无流程，请联系框架应用管理员'));
      return;
    }

    if (!this.currentTask?.processId) {
      // 没有流程。默认生成流程
      try {
        this.generateProcessLoading = true;
        const processId = await this.generateProcess();
        if (!processId) return;
        this.currentTask.processId = processId;
        this.loadTaskInfo();
        this.taskComponentRef.originTaskList.forEach((e) => {
          if (e.code === this.currentTask.code) {
            e.processId = processId;
          }
        });
      } finally {
        this.generateProcessLoading = false;
      }
    }

    const isFromDtdReference = this.data?.bcReference || this.data?.ngArguments?.bcReference;

    // 引用类型的节点，需要获取当前节点的adpVersion和adpStatus，而不是整个流程的版本号
    const versionData = this.getReferenceTask() || {
      adpVersion: this.dtdDesignerService.adpVersionHeaders.adpVersion,
      adpStatus: this.dtdDesignerService.adpVersionHeaders.adpStatus,
    };
    let targetUrl = `/app/integrated-automation/view-designer/${this.currentTask.processId}`
    // 租户跳转添加参数
    if (this.isTenantActive) {
      targetUrl += `/${this.currentTask.objectId}`
    }
    const queryParams: any = {
      appCode: this.appService.selectedApp?.code,
      viewDesignerCode: this.currentTask.application,
      projectCategory: 'combined', // 组合项目标识
      pageSource: 'workflow-task',
      isFromDtdReference: isFromDtdReference,
      atdpUniqueId: this?.currentTask?.atdpUniqueId,
      adpVersion: versionData.adpVersion,
      adpStatus: versionData.adpStatus,
    };
    if (this.dtdDesignerService?.fromManual) {
      queryParams.from = 'manual';
    }
    this.router.navigate([targetUrl], {
      queryParams,
    });
  }

  // 转化
  public handleTransFormTag(recover?: boolean): void {
    this.taskComponentRef.handleTransFormTag({
      data: this.taskTreeData,
      recover,
    });
  }

  // 界面设计 / 自定义卡面设计
  public handlePageDesign(type: WorkDesignType): void {
    this.taskComponentRef.handlePageDesign(
      {
        ...this.taskTreeData,
        fromType: type,
      },
      false,
    );
  }

  // mobile界面设计
  public handleMobilePageDesign(): void {
    this.taskComponentRef.handlePageDesign(this.taskTreeData, true);
  }

  public handleApiDesign(): void {
    this.taskComponentRef.handleApiDesign(this.taskTreeData);
  }

  public handleCase(): void {
    this.taskComponentRef.handleCase(this.taskTreeData);
  }

  /**
   * 保存
   * 外部可用
   * @param needTips
   * @returns
   */
  public async handleSave(needTips = true): Promise<any> {
    console.log('cdcdcd: ', 111);
    if (this.isReference) return; // 引用任务不保存
    if (this.translating) {
      this.message.error(this.translate.instant('dj-正在翻译多语言，请稍后再保存'));
      return false;
    }
    validatorForm(this.dataFormGroup);

    if (this.dataFormGroup.invalid) {
      const dom = document.getElementsByClassName('workflow-task-property')?.[0].getElementsByClassName('content')?.[0];
      if (dom) {
        dom.scrollTop = 0;
      }
      return false;
    }
    this.loading = true;
    try {
      const dataFeatureMap =
        this.currentTask.dataFeatures?.reduce((pre, curr) => {
          pre[curr.code] = curr.weight;
          return pre;
        }, {}) || {};
      const formValues = this.dataFormGroup.getRawValue();
      const clone = Object.assign({}, cloneDeep(this.currentTask), {
        name: formValues.name,
        lang: this.lang,
        dataFeatures:
          formValues.featureSets?.map((e) => ({
            code: e,
            weight: dataFeatureMap[e] || 1,
          })) || [],
      });
      const result = await this.apiService.updateTask(clone).toPromise();
      if (result.code === 0) {
        this.syncData(clone);
        needTips && this.message.success(this.translate.instant('dj-保存成功'));
      }
    } finally {
      this.loading = false;
    }
  }

  private initForm(): void {
    this.dataFormGroup = this.fb.group({
      code: [null, [Validators.required]],
      name: [null, [Validators.required]],
      driveType: [null, [Validators.required]],
      featureSets: [[]],
    });
    this.dataFormGroup.valueChanges.pipe(takeUntil(this.destory$), debounceTime(100)).subscribe((value) => {
      this.checkContentChangeWithoutSave((isChange) => {
        // 如果表单变动，则重置离开标识
        isChange && (this.flagLeave = false);
      });
      this.formChange.emit(this.dataFormGroup);
    });
  }

  private async loadRemoteData(): Promise<void> {
    this.loading = true;
    try {
      await this.loadTaskData();
      await this.loadTaskInfo();
      await this.loadDataStates();
      await this.loadFeatureList();
      this.setOriginData();
      this.addObserver();
    } finally {
      this.loading = false;
    }
  }

  private addObserver() {
    this.graphComponentRef.graphService.dataState$.pipe(takeUntil(this.destory$)).subscribe((value) => {
      console.log('loadTaskData', value);
      if (value === null) return;
      const item = value.find((e) => e.code === this.data.code);
      if (!item) return;
      this.loadFeatureList();
      this.currentTask.stateMaps = item.stateMaps;
      this.transferStateMaps();
    });
  }

  /**
   * 设置原始值
   */
  private setOriginData(): void {
    this.originData = {
      name: this.currentTask?.name,
      lang: this.currentTask?.lang,
      featureSets: this.currentTask?.dataFeatures?.map((df) => df.code) || [],
      inputData: cloneDeep(this.currentTask?.inputData || {}),
    };
  }

  private syncData(clone: any): void {
    this.currentTask = clone;
    this.setOriginData();
    this.graphComponentRef?.updateNodeName('task', this.data.code, clone.name, clone.lang);
    this.graphComponentRef?.saveGraph(false);
    this.taskComponentRef?.fetchTasks();
  }

  /**
   * 加载任务列表
   * @returns
   */
  private async loadTaskData(): Promise<void> {
    const result = (await this.apiService.loadTaskData(this.appService.selectedApp?.code).toPromise()) as any;
    if (result.data?.length) {
      this.currentGraphData = result.data[0];
    }
  }

  getReferenceTask(): any {
    let headerInfo = null;
    if (this.data?.bcReference) {
      const currentData = this.taskComponentRef.originTaskList.find((e) => e.code === this.data.code);
      if (!!currentData) {
        headerInfo = {
          bcReference: this.data?.bcReference,
          adpStatus: currentData?.adpStatus,
          adpVersion: currentData?.adpVersion,
        };
      }
    }
    return headerInfo;
  }

  /**
   * 加载当前任务信息
   */
  private async loadTaskInfo(): Promise<void> {
    const params = {
      code: this.data.code,
      ...(this.getReferenceTask() || {}),
    };
    const result = await this.apiService.loadCurrentTaskData(params).toPromise();
    if (result.code === 0) {
      this.currentTask = result.data || {};
      this.taskTreeData = this.taskComponentRef.originTaskList.find((e) => e.code === this.data.code);
      this.transferStateMaps();
      this.dataFormGroup.patchValue({
        code: this.currentTask.code,
        name: this.currentTask.name,
        driveType: this.currentTask.driveType,
        featureSets: this.currentTask?.dataFeatures?.map((df) => df.code) || [],
      });
      this.lang = this.currentTask.lang;
    }
  }

  private transferStateMaps(): void {
    this.stateMaps = (this.currentTask.stateMaps || []).map((stateMap) => {
      return {
        ...stateMap,
        outputList:
          Object.keys(stateMap.output || {}).map((key) => ({
            key,
            value: stateMap.output[key],
          })) || [],
      };
    });
  }

  /**
   * 加载数据列表
   */
  private async loadDataStates(): Promise<void> {
    const bcReferenceHeader = this.data?.bcReference ? this.getReferenceTask() : null;
    const result = await this.apiService
      .loadDataStates({
        application: this.currentTask?.application,
        groupCode: this.currentTask?.groupCode,
        bcReferenceHeader,
      })
      .toPromise();
    if (result.code === 0) {
      this.datamapList = (result.data || []).map((e) => ({
        ...e,
        label: e.name,
        value: e.code,
      }));
    } else {
      this.datamapList = [];
    }
  }

  /**
   * 加载数据特征
   */
  private async loadFeatureList(): Promise<void> {
    const params = {
      code: this.data.code,
      ...(this.getReferenceTask() || {}),
    };
    const result = await this.apiService.getDataFeatures(params).toPromise();
    if (result.code === 0) {
      const list = [];
      result.data.forEach((r) => list.push(...r.features));
      this.featureList = list;
    } else {
      this.featureList = [];
    }
  }

  // 创建流程
  private async generateProcess(): Promise<string | undefined> {
    let params: any = {
      adpType: 'workflowTask',
      taskCode: this.data.code,
      application: this.currentTask?.application,
      addSourceType: 'auto_perspective',
      processName: this.currentTask?.name,
      triggerType: 'page',
      triggerConfig: {
        businessCode: null,
        eventId: null,
      },
    };
    const { flowGraph, processConfig } = this.initFlowGraphAndConfig(params.triggerType);
    params = {
      ...params,
      flowGraph: flowGraph,
      processConfig: processConfig,
      bindForm: { type: EType.NONE },
      dtdVariable: this.dtdDesignerService.defaultDtdVariable,
      lang: {
        sourceName: {
          zh_CN: '未命名',
          zh_TW: '未命名',
          en_US: 'Untitled',
        },
        subjectRule: {
          zh_CN: '未命名标题',
          zh_TW: '未命名標題',
          en_US: 'Untitled title',
        },
        projectName: {
          zh_CN: '项目名称',
          en_US: 'Project Name',
          zh_TW: '專案名稱',
        },
        processName: cloneDeep(this.currentTask?.lang?.name || {}),
      },
    };
    const result = await this.apiService.postUpsertProcess(params).toPromise();
    return result.data?.processId;
  }

  // 新增流程初始化配置项
  private initFlowGraphAndConfig(type) {
    const { nodes, links }: any = initFlowGraph(type, this.translate);
    const configNodes = nodes.map((node) => {
      const { nodeId, nodeType } = node?.data || {};
      let defaultConfig: any;
      if (nodeType === NodeType.START_MANUAL) {
        const configNodeId = createUUID();
        defaultConfig = {
          id: `${SDNodeType.START_NODE}_${configNodeId}`,
          type: SDNodeType.START_NODE,
          name: this.translate.instant('dj-开始'),
          lang: {
            name: {
              zh_CN: '开始',
              zh_TW: '開始',
              en_US: 'Start',
            },
          },
          _nodeId: configNodeId,
          _nodeType: NodeType.START_MANUAL,
        };
        node.data.nodeName = this.translate.instant('dj-开始');
        node.attrs.label.text = this.translate.instant('dj-开始');
      } else if (nodeType === NodeType.END_FLOW) {
        const configNodeId = createUUID();
        defaultConfig = {
          id: `${SDNodeType.END_NODE}_${configNodeId}`,
          type: SDNodeType.END_NODE,
          name: this.translate.instant('node-结束'),
          lang: {
            name: {
              zh_CN: '结束',
              zh_TW: '結束',
              en_US: 'end',
            },
          },
          _nodeId: configNodeId,
          _nodeType: NodeType.END_FLOW,
          _isValidPassed: { [PannelTabsType.BASE]: true },
        };
        defaultConfig._events = [{ type: EType.NONE }];
      }
      return {
        ...defaultConfig,
        id: `${defaultConfig.type}_${nodeId}`,
        _nodeId: nodeId,
        _nodeType: nodeType,
      };
    });
    const configLinks =
      links?.map((m) => {
        return {
          id: m.id,
          fromId: configNodes?.find((item) => item._nodeId === m.source.cell).id,
          toId: configNodes?.find((item) => item._nodeId === m.target.cell).id,
        };
      }) || [];

    const flowGraph = {
      nodes: nodes,
      links: links,
    };
    const processConfig = {
      sourceName: this.translate.instant('dj-未命名'),
      subjectRule: this.translate.instant('dj-未命名标题'),
      projectName: this.translate.instant('dj-项目名称'),
      nodes: configNodes,
      links: configLinks,
    };

    return { flowGraph, processConfig };
  }

  /**
   * 设置离开标识
   * @param value
   */
  public setFlagLeave(value: boolean) {
    this.flagLeave = value;
  }

  /**
   * 是否有文案正在翻译
   * @param loading
   */
  public handleTranslateLoading(event: { loading: boolean }) {
    this.translating = event.loading;
  }

  handleBusinessKeySubmit(data) {
    this.currentTask.inputData = data;
  }
}
