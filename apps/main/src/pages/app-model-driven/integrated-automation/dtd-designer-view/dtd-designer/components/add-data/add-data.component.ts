import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { AppService } from 'pages/apps/app.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AddDataService } from './add-data.service';
import { DataStatusComponent } from './data-status/data-status.component';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-add-data',
  templateUrl: './add-data.component.html',
  styleUrls: ['./add-data.component.less'],
})
export class AddDataComponent implements OnInit, OnChanges, OnDestroy {
  dataForm: FormGroup; // 数据form
  dataStatusForm: FormGroup;
  currentDataNode: any;
  dataStatusList: Array<{ id: number; controlInstance: string }> = [];
  btnLoading: boolean = false;
  dataStructTree: any[] = [];
  destroy$ = new Subject();
  showDataStructTree: boolean = false;
  collectionData: CollectionEum = 'dataState';
  historyModalProps: HistoryModalProps = {
    transferModal: false,
    code: '',
    collection: 'dataState',
  }; // 修改历史弹窗展示
  // json编辑器弹窗的错误
  parseError: any;
  // 是否显示json编辑器的开关
  jsonEditorVisible = false;
  // json编辑器给用户编辑的数据
  jsonEditorData;
  beforeData;
  @ViewChild('dataStatus') dataStatusRef: DataStatusComponent;
  @Input() data: any;
  @Input() type: string;
  @Input() applicationCodeProxy;
  @Input() favouriteCode: string;
  @Input() effectAdpVersion: string;
  @Output() afterSave: EventEmitter<any> = new EventEmitter();
  @Output() cancel: EventEmitter<any> = new EventEmitter();

  get dtdReference() {
    return !(this.appService?.selectedApp?.tag?.sourceComponent === 'BC');
  }

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private message: NzMessageService,
    public appService: AppService,
    private addDataService: AddDataService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.favouriteCode?.currentValue) {
      this.addDataService.headerConfig = { templateId: this.favouriteCode };
    }
    if (changes.applicationCodeProxy?.currentValue) {
      this.addDataService.applicationCodeProxy = this.applicationCodeProxy;
    }
    if (changes.data?.currentValue) {
      console.log('--->', this.data);
      this.beforeData = cloneDeep(this.data);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit(): void {
    if (this.type === 'ADD') {
      const code = this.addDataService.creatUUID();
      this.addDataService.setDataDescription({ dataDescription: { code } });
    } else if (this.type === 'EDIT') {
      this.addDataService.setDataDescription(this.data);
    }
    const dataDescription = this.addDataService.getDataDescription();
    this.dataForm = this.fb.group({
      name: [null, [Validators.required]],
      code: [null],
      uniKeys: [null, [Validators.required]],
    });
    this.dataForm.patchValue({
      name: dataDescription.name,
      code: dataDescription.dataCode,
    });
    this.addDataService.updateStructTree$.pipe(takeUntil(this.destroy$)).subscribe((tree) => {
      this.dataStructTree = tree || [];
      // 当树有删除的时候，更新unikeys，并且uniKeys必定是children里面的
      const keyArr = [];
      this.getDataStructTreeChildrenKeys(tree, keyArr);
      const uniKeys_update = [];
      const uniKeys = this.addDataService.getDataDescription().uniKeys;
      keyArr.forEach((k) => {
        if (uniKeys.includes(k)) {
          uniKeys_update.push(k);
        }
      });
      this.handlePatchData('uniKeys', { value: uniKeys_update });
    });
  }

  getDataStructTreeChildrenKeys(tree = [], cbArr: any[]) {
    tree?.map((t) => {
      if (t.key) {
        cbArr.push(t.key);
      }
      if (t.children && t.children.length) {
        this.getDataStructTreeChildrenKeys(t.children, cbArr);
      }
    });
  }

  handlePatchData(key: any, data: any): void {
    this.dataForm.patchValue({ [key]: data?.value });
    this.addDataService.setDataDescriptionAttr({ key, value: data?.value });
  }

  handlePatchDataStatus(key: any, data: any): void {
    this.dataStatusForm.patchValue({ [key]: data?.value });
  }

  handleCancel() {
    this.addDataService.resetDataDescription();
    this.cancel.emit();
  }

  onUniKeyChange(e) {
    this.addDataService.setDataDescriptionAttr({ key: 'uniKeys', value: e });
  }

  handleSaveData(): void {
    const data_s_f = this.addDataService.getDataDescription();
    const { uniKeys, dataDescription, dataFeatures, dataStates, decision } = data_s_f;
    // 检验是否有重复定义的字段
    if (dataDescription.dataStruct.dataType === 'OBJECT' && dataDescription.dataStruct.fields) {
      const isRepeat = this.hasRepeatField(dataDescription.dataStruct.fields);
      if (isRepeat) {
        this.message.error(this.translateService.instant('dj-存在重复数据，请检查!'));
        this.btnLoading = false;
        return;
      }
    }
    const param = {
      dataDescription: Object.assign(dataDescription, {
        uniKeys,
      }),
      dataFeatures: dataFeatures,
      dataStates: dataStates,
      decision: decision,
    };
    this.btnLoading = true;
    this.addDataService
      .saveDataDescriptionAndState({
        param,
        effectAdpVersion: this.effectAdpVersion,
      })
      .subscribe(
        (res) => {
          this.btnLoading = false;
          if (res.code === 0) {
            this.addDataService.setIsAddDataState(false);
            this.addDataService.setIsAddDataDesc(true);
            this.addDataService.setAddDataCode(res.data.code || '');
            if (this.type === 'ADD') {
              this.message.success(this.translateService.instant('dj-新增成功！'));
            } else if (this.type === 'EDIT') {
              this.message.success(this.translateService.instant('dj-成功'));
            }
            this.afterSave.emit({ type: this.type, data: param, beforeData: this.beforeData });
          }
        },
        () => {
          this.btnLoading = false;
        },
      );
  }

  hasRepeatField(dataArray): boolean {
    // 先检查同一层
    const nameArr = dataArray.map((item) => item.key);
    const nameArrLen = nameArr.length;
    const _nameArr = [...new Set(nameArr)];
    const _nameArrLen = _nameArr.length;
    if (nameArrLen > _nameArrLen) {
      return true;
    } else {
      // 如果同一层没有重复了就检查每个item里面，进行迭代
      for (let index = 0; index < dataArray.length; index++) {
        if (dataArray[index].dataType === 'OBJECT' && dataArray[index].fields) {
          const sign = this.hasRepeatField(dataArray[index].fields);
          if (sign) {
            return sign;
          }
        }
      }
      return false;
    }
  }

  handleTabChange(e): void {
    const collectionEum = {
      0: 'dataState',
      1: 'dataDescription',
      2: 'dataFeatureSet',
      3: 'decisionModel',
    };
    this.collectionData = collectionEum[e.index || 0];
  }

  openJsonEditorModal() {
    const data_s_f = this.addDataService.getDataDescription();
    // 只放出4个节点来给用户编辑
    this.jsonEditorData = cloneDeep(data_s_f);
    this.jsonEditorVisible = true;
  }

  handleJsonChange(data) {
    if (data.contentErrors?.parseError) return;
    this.parseError = null;
    this.jsonEditorData = data.data;
  }

  handleErrorEvent(errors): void {
    // json 错误才会走到这个回调
    const { contentErrors } = errors;
    if (contentErrors?.parseError?.message) {
      this.parseError = contentErrors?.parseError;
    }
  }

  handleSaveJsonEditorData() {
    if (this.parseError === null) {
      // 没有错误可以更新并执行保存
      this.addDataService.upDataDescription(this.jsonEditorData);
      this.handleSaveData();
    }
    this.jsonEditorVisible = false;
  }

  openModifyHistoryModal() {
    this.historyModalProps.collection = this.collectionData;
    this.historyModalProps.code = this.addDataService.getDataDescription().dataDescription.code;
    this.historyModalProps.transferModal = true;
  }
}
