<div class="workflow-task-property">
  <div class="header">
    <span>{{ 'dj-任务编辑' | translate }}</span>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <nz-spin [nzSpinning]="loading">
      <nz-collapse [nzBordered]="false">
        <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-任务代号' | translate }}
                  <span class="item-required">*</span>
                </div>
                <app-component-input
                  [attr]="{
                    name: '任务代号',
                    required: true,
                    needLang: false,
                    readOnly: true
                  }"
                  style="width: 100%"
                  formControlName="code"
                  [value]="dataFormGroup.get('code')?.value"
                  ngDefaultControl
                >
                </app-component-input>
              </div>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-任务名称' | translate }}
                  <span class="item-required">*</span>
                </div>
                <app-modal-input
                  formControlName="name"
                  ngDefaultControl
                  [innerLabel]="false"
                  [attr]="{
                    name: '任务名称',
                    required: true,
                    needLang: true,
                    lang: lang?.name
                  }"
                  style="width: 100%"
                  [innerLabel]="false"
                  [nzDisabled]="dataFormGroup.disabled"
                  [value]="lang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                  (callBack)="handlePatchLang('name', $event)"
                  (translateLoading)="handleTranslateLoading($event)"
                >
                </app-modal-input>
              </div>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-驱动类型' | translate }}
                  <span class="item-required">*</span>
                </div>
                <ad-select [nzDisabled]="true" [nzOptions]="driveOptions" formControlName="driveType"></ad-select>
              </div>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="nz-form-item-content">
            <nz-form-control>
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-业务主键' | translate }}
                </div>
                <app-business-key
                  [inputData]="currentTask?.inputData || []"
                  (submit)="handleBusinessKeySubmit($event)"
                ></app-business-key>
              </div>
            </nz-form-control>
          </nz-form-item>

          <div class="split-line"></div>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control>
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-任务基础设置' | translate }}
                </div>
                <div class="list">
                  <!-- <ng-container *operateAuth="{ prefix: 'update' }">
                    <div
                      class="item"
                      *ngIf="
                        !!taskTreeData?.extendFields?.hasActivityConfig &&
                        !['dsl', 'new'].includes(taskTreeData?._pageModel)
                      "
                      (click)="handleTransFormTag()"
                    >
                      <i adIcon iconfont="iconzhuanhua" class="icon" aria-hidden="true"></i>
                      <span>{{ 'dj-TAG转DSL' | translate }}</span>
                    </div>
                  </ng-container> -->
                  <!-- <ng-container *operateAuth="{ prefix: 'update' }">
                    <div
                      class="item"
                      *ngIf="
                        !!taskTreeData?.extendFields?.hasActivityConfig &&
                        ['dsl', 'new'].includes(taskTreeData?._pageModel) &&
                        !!taskTreeData?.fromUiBoot
                      "
                      (click)="handleTransFormTag(true)"
                    >
                      <i adIcon iconfont="iconzhuanhua" class="icon" aria-hidden="true"></i>
                      <span>{{ 'dj-回退DSL为TAG' | translate }}</span>
                    </div>
                  </ng-container> -->
                  <nz-spin [nzSpinning]="generateProcessLoading" nzSize="small">
                    <div class="item" (click)="handleActive()">
                      <i adIcon iconfont="iconhuodongsheji" class="iconfont" aria-hidden="true"></i>
                      <span>{{ 'dj-活动设计' | translate }}</span>
                    </div>
                  </nz-spin>
                  <!-- <div
                    class="item"
                    *ngIf="!!taskTreeData?.extendFields?.hasActivityConfig"
                    (click)="handlePageDesign(WorkDesignType.taskOnlyDetail)"
                  >
                    <i adIcon iconfont="iconjiemiansheji" class="iconfont" aria-hidden="true"></i>
                    <span>{{ 'dj-界面设计' | translate }}</span>
                  </div> -->
                  <!-- <div
                    class="item"
                    *ngIf="!!taskTreeData?.extendFields?.hasActivityConfig && workType === 0"
                    (click)="handlePageDesign(WorkDesignType.taskOnlyCard)"
                  >
                    <i adIcon iconfont="iconzidingyi" class="iconfont" aria-hidden="true"></i>
                    <span>{{ 'dj-自定义卡面设计' | translate }}</span>
                  </div> -->
                  <!-- <div
                    class="item"
                    *ngIf="!!taskTreeData?.extendFields?.hasActivityConfig"
                    (click)="handleMobilePageDesign()"
                  >
                    <i adIcon type="mobile" class="iconfont" theme="outline"></i>
                    <span>{{ 'dj-mobile界面设计' | translate }}</span>
                  </div> -->
                  <div class="item" *ngIf="taskTreeData?.driveType === 'api'" (click)="handleApiDesign()">
                    <i adIcon iconfont="iconapi" class="iconfont" aria-hidden="true"></i>
                    <span>{{ 'dj-API设计' | translate }}</span>
                  </div>
                  <!-- <div class="item" (click)="handleCase()">
                    <i adIcon type="copy" theme="outline" class="iconfont"></i>
                    <span>{{ 'dj-个案' | translate }}</span>
                  </div> -->
                </div>
              </div>
            </nz-form-control>
          </nz-form-item>
        </nz-collapse-panel>
        <nz-collapse-panel [nzHeader]="'dj-数据设置' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title mb0">
                  {{ 'dj-数据状态' | translate }}
                </div>
                <div class="data-list" *ngFor="let stateMap of stateMaps">
                  <app-modal-select
                    [attr]="{
                      name: '输入状态',
                      options: datamapList,
                      readOnly: true
                    }"
                    [value]="stateMap.input"
                    ngDefaultControl
                  ></app-modal-select>
                  <div class="data-out" *ngFor="let output of stateMap.outputList">
                    <app-modal-input [attr]="{ name: '输出状态key', readOnly: true }" [value]="output.key">
                    </app-modal-input>
                    <app-modal-select
                      [attr]="{
                        name: '输出状态',
                        options: datamapList,
                        readOnly: true
                      }"
                      [value]="output.value"
                      ngDefaultControl
                    ></app-modal-select>
                  </div>
                </div>
              </div>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item class="nz-form-item-content">
            <nz-form-control [nzAutoTips]="errorTips">
              <div class="form-item">
                <div class="item-title">
                  {{ 'dj-数据特征' | translate }}
                </div>
                <ad-select nzMode="multiple" formControlName="featureSets">
                  <ad-option *ngFor="let fl of featureList" [nzValue]="fl.code" [nzLabel]="fl.name"></ad-option>
                </ad-select>
              </div>
            </nz-form-control>
          </nz-form-item>
        </nz-collapse-panel>
        <nz-collapse-panel [nzHeader]="'dj-扩展' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
          <div *ngIf="!!currentTask" [ngClass]="{ 'not-allow': isReference }">
            <app-extended-info
              class="info-icon"
              [code]="currentTask?.code"
              [sceneCode]="'datamapTask'"
              [appCode]="currentTask.application"
              [extendHeader]="dtdDesignerService.adpVersionHeaders"
              [showName]="true"
              [isTenantProcessId]="isTenantActive"
            >
            </app-extended-info>
          </div>
        </nz-collapse-panel>
      </nz-collapse>
    </nz-spin>
  </form>
  <ng-container
    *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise, guards: [!isReference] }"
  >
    <div class="footer">
      <div class="save" (click)="handleSave()">
        <i adIcon iconfont="iconbaocun2" class="save-icon"></i>
        <span class="save-text">{{ 'dj-保存设置项' | translate }}</span>
      </div>
    </div>
  </ng-container>
</div>
