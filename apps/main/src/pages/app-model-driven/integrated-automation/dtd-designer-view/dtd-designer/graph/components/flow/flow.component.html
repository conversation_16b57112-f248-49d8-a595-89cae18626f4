<!-- 活动flow弹窗 -->
<nz-drawer
  *ngIf="visible"
  nzWrapClassName="flow-drawer"
  [nzMaskClosable]="false"
  [nzWidth]="'90%'"
  [nzVisible]="visible"
  [nzTitle]="flowTitle"
  (nzOnClose)="handleCloseFlowDrawer()"
>
  <ng-container *nzDrawerContent>
    <app-activity-graph-header
      *ngIf="flowGraph && notice$"
      [notice$]="notice$"
      [graph]="flowGraph"
      [pkValue]="task?.code"
      [applicationCodeProxy]="applicationCodeProxy"
      [isFromDtdReference]="isFromDtdReference"
      (copy)="handleActivityFlowCopy()"
      (paste)="handleActivityFlowPaste()"
      (remove)="handleActivityFlowRemove()"
      (clickHistory)="handleActivityFlowClickHistory()"
      (collect)="handleCollect('flow')"
      (publish)="handleActivityFlowPublish()"
      (publishAction)="handleActivityFlowLoadingAction($event)"
      (save)="handleActivityFlowSave()"
      (pasteDtd)="handlePasteFlow($event)"
    ></app-activity-graph-header>
    <!-- 活动flow -->
    <app-activity-flow
      #systemFlow
      class="system-flow"
      showType="bottom"
      [isChanged]="isChanged"
      [taskCode]="task?.code"
      [tenantId]="task?.tenantId"
      [favouriteCode]="favouriteCode"
      [extendHeader]="extendHeader"
      [applicationCode]="task?.application || appService?.selectedApp?.code"
      [applicationCodeProxy]="applicationCodeProxy"
      [athena_card_data]="haveAthenaCardData()"
      (getFlowGraph)="handlegetFlowGraph($event)"
      (error)="handleFlowError($event)"
      (hideFlowVisible)="handleCloseFlowDrawer()"
    ></app-activity-flow>
  </ng-container>
</nz-drawer>

<ng-template #flowTitle>
  <div class="info" nz-tooltip [nzTooltipTitle]="task?.lang?.[currentLang] || task?.name">
    {{task?.lang?.[currentLang] || task?.name}}
  </div>
</ng-template>
<!-- 收藏开窗 -->
<template-collection-modal
  *ngIf="collectVisible"
  [(visible)]="collectVisible"
  [title]="collectTitle"
  (confirm)="handleCollectConfirm($event)"
  (cancel)="handleCollectCancel()"
>
</template-collection-modal>
