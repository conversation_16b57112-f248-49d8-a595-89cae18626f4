import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-reassign',
  templateUrl: './reassign.component.html',
  styleUrls: ['./reassign.component.less']
})
export class ReassignComponent implements OnInit {
  @Input() currentProject: any;
  @Input() editable: boolean = true;
  isShowJsonModal: boolean = false;

  constructor() { }

  ngOnInit() {
  }

  // 启用
  handleAssignAbleChange() {
    if (this.currentProject.assignConfig.assignAble == true) {
      this.isShowJsonModal = true;
    }
  }

  // 打开 json 编辑器
  handleClickLabelJson() {
    this.isShowJsonModal = true;
  }

  // 更新json
  handleSaveSet(data) {
    this.isShowJsonModal = false;
    if (!this.currentProject.assignConfig) {
      this.currentProject.assignConfig = {};
    }
    this.currentProject.assignConfig = data || {};
  }
}
