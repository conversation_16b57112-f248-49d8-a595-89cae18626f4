import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { dateType } from '../../../../project-widget/project-set';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-advanced-settings',
  templateUrl: './advanced-settings.component.html',
  styleUrls: ['./advanced-settings.component.less'],
})
export class AdvancedSettingsComponent implements OnInit {
  @Input() currentProject: any;
  @Input() editable: boolean = true;

  @Output() patchValue = new EventEmitter<any>();
  @Output() update = new EventEmitter<any>();

  dateType = dateType; // 时距

  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };

  constructor(public translate: TranslateService) {}

  handlePatch(key: string, data: unknown): void {
    this.patchValue.emit({
      key,
      data,
    });
  }

  handleUpdate(evt) {
    this.update.emit(evt);
  }

  ngOnInit() {}
}
