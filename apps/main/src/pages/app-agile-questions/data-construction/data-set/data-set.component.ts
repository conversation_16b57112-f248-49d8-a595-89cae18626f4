import { TranslateService } from '@ngx-translate/core';
import { Component, OnInit, ViewChild } from '@angular/core';
import { EManageType, TManageType } from 'pages/app-agile-data/utils/constant';
import { StepNode, StepsHelperService } from 'pages/app-agile-questions/components/agile-steps/steps-helper.service';
import { getDataSetColumnDefs } from 'pages/app-agile-questions/data-construction/col';
import { INzTableConfig } from 'pages/app-agile-questions/components/agile-question-nz-table/agile-question-nz-table.component';
import { AgileNzTableOperationComponent } from 'pages/app-agile-data/components/agile-nz-table-operation/agile-nz-table-operation.component';
import { TextSegment, ButtonConfig } from 'pages/app-agile-questions/components/agile-empty/agile-empty.component';
import { DataSetTypeComponent } from './data-set-type/data-set-type.component';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AgileAuthStoreService } from 'pages/app-agile-data/services/agile-auth-store.service';
import { DataSetFinishComponent } from './data-set-finish/data-set-finish.component';
import { DataSetQuestionComponent } from './data-set-question/data-set-question.component';
import { DataSetConfigComponent } from './data-set-config/data-set-config.component';
import { NavigateHelperService } from 'pages/app-agile-questions/components/agile-steps-navigate/navigate-helper.service';
import { GetModelComponent } from './get-model/get-model.component';
import { ConfigFieldsComponent } from './config-fields/config-fields.component';
import { ActivatedRoute } from '@angular/router';
import { IAgileHeaderConfig } from 'pages/app-agile-questions/components/agile-question-page-header/agile-question-page-header.component';
import { DataConstructionService } from '../data-construction.service';
import { ApiService } from 'pages/app-agile-questions/api/api.service';
import { AppService } from 'pages/apps/app.service';
import { LocaleService } from 'common/service/locale.service';
import { AgileStepsNavigateComponent } from 'pages/app-agile-questions/components/agile-steps-navigate/agile-steps-navigate.component';
import { cloneDeep } from 'lodash';
import { repeatCountMap } from './data-set.utils';
@Component({
  selector: 'app-data-set',
  templateUrl: './data-set.component.html',
  styleUrls: ['./data-set.component.less'],
  providers: [StepsHelperService, ApiService],
})
export class DataSetComponent implements OnInit {
  @ViewChild(AgileStepsNavigateComponent, { static: false }) stepsNavigate!: AgileStepsNavigateComponent;

  messageSegments: TextSegment[] = [{ text: 'dj-当前项目内还没有创建过数据集，您可以立即开始创建' }];
  emptyButtonConfig: ButtonConfig[] = [
    {
      text: 'dj-新建数据集',
      type: 'primary',
      onClick: this.onCreateDataset.bind(this),
    },
  ];
  currentLanguage: string = '';
  dataSetList = [];
  searchValue: string = '';
  isEmpty = false; // 是否是空数据
  isInitialized = true; // 初始化标志
  headerConfig: IAgileHeaderConfig[] = [];
  tableConfig: INzTableConfig = {
    indexCol: true,
    total: 0,
    pageNumber: 1,
    pageSize: 10,
    loading: false,
    showQuickJumper: true,
    pageNumberChanged: this.pageNumberChange.bind(this),
    pageSizeChanged: this.pageSizeChange.bind(this),
  };

  constructor(
    private t: TranslateService,
    private stepsHelper: StepsHelperService,
    private modal: NzModalService,
    private message: NzMessageService,
    private agileAuth: AgileAuthStoreService,
    private navigateHelper: NavigateHelperService,
    private route: ActivatedRoute,
    private dataSourceService: DataConstructionService,
    private api: ApiService,
    private appService: AppService,
    private languageService: LocaleService,
  ) {
    this.currentLanguage = this.languageService?.currentLanguage || 'zh_CN';
    this.tableConfig.cols = [
      ...getDataSetColumnDefs(t, this.currentLanguage),
      {
        field: 'operations',
        pinned: 'right',
        width: '100px',
        headerName: this.t.instant('dj-操作'),
        cell: {
          component: AgileNzTableOperationComponent,
          compProps: {
            maxShowCount: 2,
            operationList: [
              {
                icon: 'edit',
                iconFunc: this.handleEditDataSet.bind(this),
                toolTips: this.t.instant('dj-编辑'),
              },
              {
                icon: 'delete',
                iconFunc: this.handleDeleteDataSet.bind(this),
                toolTips: this.t.instant('dj-删除'),
                showBtn: (data) => {
                  // 仅支持创建者和超管删除
                  return this.agileAuth.isSuperAdmin || data.createBy === this.agileAuth.userId;
                },
              },
            ],
          },
        },
      },
    ];

    const stepsTree: StepNode[] = [
      {
        step: '1',
        title: this.t.instant('dj-选择类型'),
        content: DataSetTypeComponent,
        valid: true,
        children: [],
        outputs: {
          dataChanged: (data: any) => {
            this.dataSourceObj.dataSetType = data.dataSetType;
          },
        },
      },
      {
        step: '2',
        title: this.t.instant('dj-配置连接'),
        content: DataSetConfigComponent,
        valid: true,
        children: [],
      },
      {
        step: '3',
        title: this.t.instant('dj-问数配置'),
        content: DataSetQuestionComponent,
        valid: true,
        children: [],
        inputs: {
          data: null,
        },
        outputs: {
          dataChanged: (data) => {
            const _ = cloneDeep(data);
            if (_.quickQuestion === 'manual') {
              _.questions = _.manual_questions;
            }
            if (_.quickQuestion === 'AI') {
              _.questions = _.AI_questions;
            }
            Reflect.deleteProperty(_, 'manual_questions');
            Reflect.deleteProperty(_, 'AI_questions');
            Object.assign(this.dataSourceObj, _);
          },
        },
      },
      {
        step: '4',
        title: this.t.instant('dj-完成'),
        content: DataSetFinishComponent,
        valid: true,
        children: [],
        outputs: {
          goBack$: () => {
            this.drawerClose();
          },
        },
      },
    ];
    this.stepsHelper.initStepsTree(stepsTree);
  }

  ngOnInit() {
    this.dataSourceObj = { notify: true };
    this.navigateHelper.reset();
    this.route.queryParams.subscribe((params) => {
      const { isAdd, dataSetType, dataSourceId, dataSourceName } = params;
      if (isAdd === 'true') {
        // this.onCreateDataset();
        this.type = EManageType.ADD;
        this.drawerVisible = true;
        this.drawerTitle = 'dj-新建数据集';
        this.dataSourceObj = {
          notify: true,
          dataSetType,
          dataSourceId: Number(dataSourceId) ?? dataSourceId,
          dataSourceName,
        };
        this.navigateHelper.setStepChanged('1');
        const nextCount = 1;
        for (let count = 0; count < nextCount; count++) {
          setTimeout(() => {
            this.stepsNavigate.next();
          });
        }
      }
    });

    this.queryList(true);
    this.updateHeaderConfig();
  }

  private updateHeaderConfig(): void {
    this.headerConfig =
      this.dataSetList?.length || !this.isEmpty
        ? [
            {
              type: 'search',
              func: this.handleSearch.bind(this),
            },
            {
              name: 'dj-新建数据集',
              func: this.onCreateDataset.bind(this),
              btnType: 'primary',
              disabled: this.dataSetList.length >= 1000,
              disabledReason: this.t.instant('dj-数据集最多支持1000个'),
            },
          ]
        : [];
  }

  // 获取列表
  queryList(isInit?: boolean) {
    const params = {
      keyword: this.searchValue,
      pageNum: this.tableConfig.pageNumber,
      pageSize: this.tableConfig.pageSize,
      application: this.appService.selectedApp?.code,
    };
    this.tableConfig.loading = true;
    this.dataSourceService.getPageList(params).subscribe(
      (res) => {
        if (this.isInitialized) this.isInitialized = false;
        this.tableConfig.loading = false;
        if (res.code === 0) {
          this.tableConfig.total = res.data?.total;
          this.dataSetList = res.data?.list || [];
          if (!this.dataSetList.length) {
            this.isEmpty = isInit || false;
          } else {
            this.isEmpty = false;
          }
          this.updateHeaderConfig();
        }
      },
      () => {
        this.tableConfig.loading = false;
      },
    );
  }
  pageNumberChange(pageNumber: number): void {
    if (pageNumber === this.tableConfig.pageNumber) {
      return;
    }
    this.tableConfig.pageNumber = pageNumber;
    this.queryList();
  }

  pageSizeChange(pageSize: number): void {
    if (pageSize === this.tableConfig.pageSize) {
      return;
    }
    this.tableConfig.pageSize = pageSize;
    this.queryList();
  }

  onCreateDataset(): void {
    this.type = EManageType.ADD;
    this.navigateHelper.setStepChanged('1');
    this.drawerVisible = true;
    this.drawerTitle = 'dj-新建数据集';
    this.dataSourceObj = { notify: true };
  }

  /************** drawer相关 开始 ********************/

  type: TManageType; // 新建或编辑
  drawerVisible: boolean = false;
  drawerTitle: string = 'dj-新建数据集';
  dataSourceObj: any = { notify: true };
  drawerLoading: boolean = false;

  get stepTree(): StepNode[] {
    return this.stepsHelper.stepsTree;
  }

  drawerClose(): void {
    this.drawerVisible = false;
    this.dataSourceObj = {};
    if (this.stepsNavigate.isLastStep) {
      this.queryList();
    }
    this.navigateHelper.reset();
  }

  onStepChange(event: {
    nextStep: string;
    node: StepNode;
    type: 'prev' | 'next';
    proceed: () => void;
    cancel: () => void;
  }): void {
    this.validateStep(event.nextStep, event.node, event.type).then((isValid) => {
      if (isValid) {
        event.proceed(); // 继续执行导航
      } else {
        event.cancel(); // 取消导航
      }
    });
  }

  private async validateStep(nextStep: string, node: StepNode, type: 'prev' | 'next'): Promise<boolean> {
    // console.log('验证步骤', nextStep, this.stepsHelper.findStepNode(this.stepTree, nextStep), node);
    if (nextStep === '1' && type === 'prev') {
      if (this.type === EManageType.ADD) {
        this.dataSourceObj = {
          dataSetType: this.dataSourceObj.dataSetType,
          notify: true,
        };
      }
    }
    if (nextStep === '2') {
      const target = this.stepTree.find((node) => node.step === nextStep);
      const subData = [
        {
          step: '2-1',
          title: this.t.instant('dj-字段选择'),
          content: GetModelComponent,
          valid: this.type === EManageType.EDIT ? true : false,
          children: [],
          inputs: {},
          outputs: {
            dataChanged: (data: any = []) => {
              if (data.length > 0) {
                this.stepsHelper.updateStepValid(this.stepTree, '2-1', true);
              } else {
                this.stepsHelper.updateStepValid(this.stepTree, '2-1', false);
              }
              this.dataSourceObj.$$_field = data;
            },
            dataSourceChanged: (data: any) => {
              Object.assign(this.dataSourceObj, data);
            },
            modelChanged: (data: any) => {
              Object.assign(this.dataSourceObj, data);
              this.dataSourceObj.newField = [];
              if (!data.modelCode) {
                this.dataSourceObj.$$_field = [];
                this.stepsHelper.updateStepValid(this.stepTree, '2-1', false);
                // 更新2-1中的newField
                const target = this.stepsHelper.findStepNode(this.stepTree, '2-1');
                (target.instance as GetModelComponent).newField = [];
              }
            },
            graphChanged: (graph: any) => {
              this.dataSourceObj.graph = graph;
            },
            tableFieldsChanged: (data: any) => {
              // 表字段
              this.dataSourceObj.tableFields = data;
            },
            resizeStatusChanged: (data) => {
              this.dataSourceObj.hasDrag = true;
              this.dataSourceObj.resizeStatus = data;
            },
          },
        },
        {
          step: '2-2',
          title: this.t.instant('dj-配置字段'),
          content: ConfigFieldsComponent,
          valid: true,
          children: [],
          inputs: {},
          outputs: {
            dataChanged: ({ dimensions, measures }) => {
              const fields = [...dimensions, ...measures];
              const valid = [];
              fields.forEach((field) => {
                if (field?.verified) {
                  valid.push(field?.verified(fields, field, '$$_name_repeat'));
                }
              });
              const hasInvalid = valid.some((value) => value === false);
              if (hasInvalid) {
                this.stepsHelper.updateStepValid(this.stepTree, '2-2', false);
              } else {
                this.stepsHelper.updateStepValid(this.stepTree, '2-2', true);
                Object.assign(this.dataSourceObj, { _dimensions: dimensions, _measures: measures });
              }
            },
          },
        },
      ];
      target.inputs = {
        stepsTree: subData,
      };
      this.stepsHelper.addChildrenToStep(this.stepsHelper.stepsTree, nextStep, subData);
    }
    if (nextStep === '2-1') {
      const target = this.stepsHelper.findStepNode(this.stepTree, '2-1');
      const dimensions = this.dataSourceObj?.dimensions ?? [];
      const measures = this.dataSourceObj?.measures ?? [];
      let newField = [...dimensions, ...measures];
      if (type === 'prev') {
        const component = node?.instance as ConfigFieldsComponent;
        newField = component.fields;
        if (newField.length > 0) {
          this.stepsHelper.updateStepValid(this.stepTree, '2-1', true);
        } else {
          this.stepsHelper.updateStepValid(this.stepTree, '2-1', false);
        }
      }
      // $$_field为2-1页面中table勾选的数据
      this.dataSourceObj.$$_field = newField;
      // newField是2-2中table的字段
      this.dataSourceObj.newField = newField;
      target.inputs = {
        dataSource: {
          dataSourceId: this.dataSourceObj?.dataSourceId ?? '',
        },
        model: {
          modelId: this.dataSourceObj?.modelId ?? null,
          modelCode: this.dataSourceObj?.modelCode ?? null,
        },
        graph: {
          graph: this.dataSourceObj?.graph ?? null,
        },
        newField: newField,
        hasDrag: this.dataSourceObj?.hasDrag ?? false,
        resizeStatus: this.dataSourceObj?.resizeStatus ?? null,
      };
    }
    if (nextStep === '2-2') {
      const previousFields = this.dataSourceObj.$$_field ?? [];
      // 字段名称重复标记
      const nameCountMap = repeatCountMap(previousFields, 'name');
      previousFields.forEach((field) => {
        field.$$_name_repeat = nameCountMap.get(field.name)! > 1;
      });
      // 字段数量超过40条提示
      if (previousFields.length > 40 && this.dataSourceObj.notify) {
        const modalPromise = new Promise<void>((resolve) => {
          const modal: NzModalRef = this.modal.create({
            nzTitle: this.t.instant('dj-moreThen40tips'),
            nzContent: null,
            nzWidth: '600px',
            nzFooter: [
              {
                label: this.t.instant('dj-不再提示'),
                type: 'default',
                onClick: () => {
                  modal.destroy(); // 关闭弹窗
                  this.dataSourceObj.notify = false;
                  resolve(); // 触发 Promise 的完成
                },
              },
              {
                label: this.t.instant('dj-知道了'),
                type: 'primary',
                onClick: () => {
                  modal.destroy(); // 关闭弹窗
                  resolve(); // 触发 Promise 的完成
                },
              },
            ],
          });
        });
        await modalPromise;
      }
      const target = this.stepsHelper.findStepNode(this.stepTree, '2-2');
      let newField = this.dataSourceObj?.newField ?? [];
      const addedFields = previousFields.filter(
        (field) => !newField.some((prev) => prev.map_field === field.map_field),
      );
      const removedFields = newField.filter(
        (field) => !previousFields.some((newF) => newF.map_field === field.map_field),
      );
      if (addedFields.length > 0) {
        newField = [...newField, ...addedFields];
      }
      if (removedFields.length > 0) {
        newField = newField.filter((field) => !removedFields.some((newF) => newF.map_field === field.map_field));
      }
      // 更新 newField 上 latitudeMeasurement、nullStyle
      newField.forEach((field) => {
        const target = this.dataSourceObj?.tableFields?.find((tf) => tf.map_field === field.map_field);
        if (target) {
          field.latitudeMeasurement = target?.latitudeMeasurement;
        }
        field.nullStyle = field?.nullStyle ?? '-';
      });
      target.inputs = {
        fields: newField,
      };
      this.dataSourceObj.newField = [];
    }
    if (nextStep === '3') {
      const target = this.stepTree.find((node) => node.step === nextStep);
      const dimensions = this.dataSourceObj?._dimensions ?? [];
      const measures = this.dataSourceObj?._measures ?? [];
      const allFields = [...dimensions, ...measures];
      const nameSet = new Set();
      const hasDuplicateName = allFields.some((field) => {
        if (nameSet.has(field.name)) {
          return true; // 如果 name 已存在，表示重复
        }
        nameSet.add(field.name);
        return false;
      });
      if (hasDuplicateName) {
        this.message.error(this.t.instant('dj-字段名称不允许重复'));
        return false; // 校验失败
      }
      target.inputs = {
        data: {
          name: this.dataSourceObj?.name ?? '',
          description: this.dataSourceObj?.description ?? '',
          dataSourceId: this.dataSourceObj.dataSourceId,
          dimensions: this.dataSourceObj?._dimensions ?? [],
          measures: this.dataSourceObj?._measures ?? [],
          questions: this.dataSourceObj?.questions ?? [],
          quickQuestion: this.dataSourceObj?.quickQuestion ?? 'manual',
          lang: this.dataSourceObj?.lang ?? {},
        },
      };
    }
    if (node.step === '3' && type === 'next') {
      const component = node?.instance as DataSetQuestionComponent;
      if (!component?.verified()) return false;
      const params = cloneDeep(this.dataSourceObj);
      params.dimensions = params._dimensions ?? params.dimensions;
      params.measures = params._measures ?? params.measures;
      Reflect.deleteProperty(params, '_dimensions');
      Reflect.deleteProperty(params, '_measures');
      Reflect.deleteProperty(params, '$$_field');
      Reflect.deleteProperty(params, 'newField');
      Reflect.deleteProperty(params, 'tableFields');
      Reflect.deleteProperty(params, 'hasDrag');
      Reflect.deleteProperty(params, 'resizeStatus');
      params.dimensions.forEach((field) => {
        Reflect.deleteProperty(field, 'verified');
        Reflect.deleteProperty(field, '$$_name_repeat');
      });
      params.measures.forEach((field) => {
        Reflect.deleteProperty(field, 'verified');
        Reflect.deleteProperty(field, '$$_name_repeat');
      });
      this.drawerLoading = true;
      if (this.type === EManageType.EDIT) {
        try {
          const res = await this.api.updateDataSet(params).toPromise();
          this.drawerLoading = false;
          if (res.code === 0) return true;
        } catch (error) {
          console.error(error);
          this.drawerLoading = false;
          return false;
        }
      }
      if (this.type === EManageType.ADD) {
        try {
          const res = await this.api.addDataSet(params).toPromise();
          this.drawerLoading = false;
          if (res.code === 0) return true;
        } catch (error) {
          console.error(error);
          this.drawerLoading = false;
          return false;
        }
      }
      return false;
    }
    // 实现具体的验证逻辑
    return true; // 返回验证结果
  }

  /************** drawer相关 结束 ********************/

  handleSearch(value: string): void {
    this.searchValue = value;
    this.queryList();
  }

  handleEditDataSet({ data }): void {
    this.tableConfig.loading = true;
    this.type = EManageType.EDIT;
    this.api.getDataSetDetail(data.code).subscribe(
      (res) => {
        this.tableConfig.loading = false;
        const { code, data } = res;
        if (code === 0) {
          this.dataSourceObj = data;
          this.drawerTitle = 'dj-编辑数据集';
          this.navigateHelper.setStepChanged('1');
          this.drawerVisible = true;
          const nextCount = 1;
          for (let count = 0; count < nextCount; count++) {
            setTimeout(() => {
              this.stepsNavigate.next();
            });
          }
        }
      },
      () => {},
      () => {
        this.tableConfig.loading = false;
      },
    );
  }
  // 删除数据集
  handleDeleteDataSet({ data }): void {
    this.modal.confirm({
      nzTitle: `${this.t.instant('dj-删除')}【 ${data?.lang?.name[this.currentLanguage]} 】`,
      nzContent: this.t.instant('dj-删除将无法恢复，确认删除吗？'),
      nzOkText: this.t.instant('dj-确定'),
      nzOkType: 'primary', // 使用合法类型
      nzOkDanger: true,
      nzCancelText: this.t.instant('dj-取消'),
      nzIconType: 'info-circle',
      nzOnOk: () => {
        // 删除逻辑
        const { messageId } = this.message.loading(this.t.instant('dj-正在删除...'), { nzDuration: 0 });
        this.dataSourceService.delete({ codes: [data?.code] }).subscribe(
          () => {
            this.message.remove(messageId);
            this.message.success(this.t.instant('dj-删除成功'));
            this.tableConfig.pageNumber =
              this.dataSetList.length === 1 && this.tableConfig.pageNumber > 1
                ? this.tableConfig.pageNumber - 1
                : this.tableConfig.pageNumber;
            this.queryList(true);
          },
          () => {
            this.message.remove(messageId);
          },
        );
      },
    });
  }
}
