<form nz-form [formGroup]="questionForm" #questionComponent>
  <nz-form-item>
    <div class="item-layout">
      <nz-form-label [nzSpan]="3" nzRequired>{{ 'dj-名称' | translate }}</nz-form-label>
      <nz-form-control [nzSpan]="12" [nzErrorTip]="'dj-必填' | translate">
        <app-modal-input-agile
          class="lang-input"
          ngDefaultControl
          formControlName="name"
          [attr]="{
            code: 'name',
            needLang: true,
            lang: { value: lang?.name },
            maxLength: 16,
            placeholder: '请命名'
          }"
          [innerLabel]="false"
          (callBack)="handlePatchLang('name', $event)"
          (loading)="handleLoadingLang($event)"
        >
        </app-modal-input-agile>
      </nz-form-control>
    </div>
    <p nz-col [nzPush]="3" class="tips-text">{{ 'dj-dataSet-name-tip' | translate }}</p>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSpan]="3" nzRequired>{{ 'dj-描述' | translate }}</nz-form-label>
    <nz-form-control [nzSpan]="12" [nzErrorTip]="'dj-必填' | translate">
      <app-textarea-input-agile
        class="lang-input"
        ngDefaultControl
        formControlName="description"
        [attr]="{
          code: 'description',
          needLang: true,
          type: 'textarea',
          lang: { value: lang?.description },
          maxLength: 255,
          rows: 3,
          placeholder: 'dj-dataSet-description-placeholder' | translate
        }"
        (callBack)="handlePatchLang('description', $event)"
      >
      </app-textarea-input-agile>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSpan]="3" [nzTooltipTitle]="'dj-quickQuestion-tips' | translate">{{
      'dj-快速提问' | translate
    }}</nz-form-label>
    <nz-form-control [nzSpan]="12">
      <nz-radio-group formControlName="quickQuestion" (ngModelChange)="handleQuickQuestionChanged($event)">
        <label class="dataSet-radio-label" nz-radio nzValue="manual">{{ 'dj-手动设置' | translate }}</label>
        <label class="dataSet-radio-label" nz-radio nzValue="AI">{{ 'dj-大模型推荐' | translate }}</label>
      </nz-radio-group>
    </nz-form-control>
  </nz-form-item>
  <div nz-col [nzPush]="3" [nzSpan]="21" class="quickQuestion-content">
    <ng-container *ngIf="questionForm.get('quickQuestion').value === 'manual'">
      <div nz-row [nzSpan]="24" class="quickQuestion-content-manual" cdkDropList (cdkDropListDropped)="drop($event)">
        <ng-container *ngFor="let manual_question of manual_questions.controls; let i = index">
          <div 
            cdkDrag 
            cdkDragPreviewContainer="parent"
            nz-form
            [nzNoColon]="true"
            [formGroup]="manual_question"
            class="quickQuestion-content-manual-item">
            <div nz-col [nzSpan]="1" class="drag-warp">
              <i adIcon iconfont="icontuozhuaiIC" class="iconfont drag" cdkDragHandle></i>
            </div>
            <nz-form-item nz-col [nzSpan]="22" class="question-form-item">
              <nz-form-control [nzSpan]="24">
                <app-modal-input-agile
                  class="lang-input"
                  [innerLabel]="false"
                  formControlName="question"
                  [attr]="{
                    code: 'question',
                    needLang: true,
                    lang: { value: manual_lang[i]?.question },
                    maxLength: 50,
                    placeholder: '请输入快捷提问'
                  }"
                  (callBack)="handleQuestionPatchLang('question', $event, i)"
                >
                </app-modal-input-agile>
              </nz-form-control>
            </nz-form-item>
            <div nz-col [nzSpan]="1" class="delete-warp">
              <i adIcon iconfont="icondelete2" class="iconfont" (click)="removeQuestion(i)"></i>
            </div>
          </div>
        </ng-container>
      </div>
      <div (click)="addQuestion()" class="quickQuestion-action-add" *ngIf="manual_questions.length < 10">
        <i adIcon [iconfont]="'iconxinzeng2'" class="icon-add"></i>
        {{ 'dj-添加问句' | translate }}
      </div>
    </ng-container>
    <ng-container *ngIf="questionForm.get('quickQuestion').value === 'AI'">
      <ng-container *ngIf="!isFailed; else failedTpl">
        <nz-spin [nzSpinning]="loading">
          <div class="quickQuestion-content-ai">
            <div class="quickQuestion-content-ai-action" (click)="changeABatch()">
              <img src="assets/img/agile/changed.svg" alt="" />
              {{ 'dj-换一批' | translate }}
            </div>
            <ng-container *ngFor="let question of AI_questions">
              <div class="quickQuestion-content-ai-item">
                {{ question?.lang?.['question']?.[('dj-LANG' | translate)] ?? question.question }}
              </div>
            </ng-container>
          </div>
        </nz-spin>
      </ng-container>
      <ng-template #failedTpl>
        <div class="quickQuestion-content-ai-fail">
          <img class="fail-img" src="assets/img/empty.png" alt="" />
          <div class="fail-msg">
            <span class="fail-msg-text">{{ 'dj-推荐失败' | translate }}</span>
            <a nz-button nzType="link" (click)="changeABatch()">{{ 'dj-重新推荐' | translate }}</a>
          </div>
        </div>
      </ng-template>
    </ng-container>
  </div>
</form>
