import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';

import { TranslateModule } from '@ngx-translate/core';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzButtonModule } from 'ng-zorro-antd/button';

import { InputModule } from 'components/form-components/input/input.module';
import { TextareaModule } from 'components/form-components/textarea-agile/textarea.module';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';

import { DataSetQuestionComponent } from './data-set-question.component';

@NgModule({
  declarations: [DataSetQuestionComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropModule,
    TranslateModule,
    NzFormModule,
    NzRadioModule,
    NzToolTipModule,
    NzGridModule,
    NzIconModule,
    NzSpinModule,
    NzButtonModule,
    InputModule,
    TextareaModule,
    AdIconModule,
  ],
  exports: [DataSetQuestionComponent],
})
export class DataSetQuestionModule {}
