import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { CommonValidators } from 'common/utils/common.validators';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ApiService } from 'pages/app-agile-questions/api/api.service';

@Component({
  selector: 'app-data-set-question',
  templateUrl: './data-set-question.component.html',
  styleUrls: ['./data-set-question.component.less'],
  providers: [ApiService],
})
export class DataSetQuestionComponent implements OnInit {
  @Input() data!: any;
  @Output() dataChanged: EventEmitter<any> = new EventEmitter();

  questionForm: FormGroup;
  lang: any;
  langLoading: boolean = false;
  readOnly: boolean = false;
  questions: any[] = [];
  loading: boolean = false;
  isFailed: boolean = false;
  manual_questions: FormArray;
  manual_lang: any[] = [];
  AI_questions: any[] = [];

  constructor(
    private fb: FormBuilder,
    private api: ApiService,
    private message: NzMessageService,
    private t: TranslateService,
  ) {
    this.questionForm = this.fb.group({
      name: ['', [Validators.required, CommonValidators.trim]],
      description: ['', [Validators.required, CommonValidators.trim]],
      quickQuestion: ['manual'],
    });
    this.manual_questions = this.fb.array([]);
  }

  ngOnInit(): void {
    if (this.data) {
      this.questionForm.patchValue(this.data);
      this.lang = this.data?.lang;
      if (this.data.quickQuestion === 'manual') {
        if (this.data?.questions?.length > 0) {
          this.data.questions.forEach((question) => {
            const group = this.fb.group({
              question: [question.question, [Validators.required, CommonValidators.trim]],
            });
            this.manual_questions.push(group);
            this.manual_lang.push(question.lang);
          });
        }
      }
      if (this.data.quickQuestion === 'AI') {
        this.AI_questions = this.data?.questions;
      }
      this.dataChangedEmit();
    }
  }

  handlePatchLang(key: any, data: any): void {
    // this.questionForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang?.value || data.lang!,
      };
    }
    this.dataChangedEmit();
  }

  handleQuestionPatchLang(key: any, data: any, index: number): void {
    if (data.needLang) {
      this.manual_lang[index] = {
        [key]: data.lang?.value || data.lang!,
      };
    }
    this.dataChangedEmit();
  }

  handleLoadingLang(bol): void {
    this.langLoading = bol;
  }

  handleQuickQuestionChanged(value: any): void {
    if (value === 'AI') {
      if (this.AI_questions.length === 0) {
        this.changeABatch();
      } else {
        this.dataChangedEmit();
      }
    }
    if (value === 'manual') {
      if (this.manual_questions.length === 0) {
        this.addQuestion();
      }
    }
  }

  drop(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.manual_questions.controls, event.previousIndex, event.currentIndex);
    moveItemInArray(this.manual_lang, event.previousIndex, event.currentIndex);
    this.dataChangedEmit();
  }

  removeQuestion(index: number): void {
    this.manual_questions.removeAt(index);
    this.manual_lang.splice(index, 1);
    this.dataChangedEmit();
  }

  addQuestion(): void {
    // 检查是否有空值
    const hasEmptyQuestion = this.manual_questions.controls.some((control) => {
      const questionValue = control.get('question')?.value?.trim();
      return !questionValue;
    });

    if (hasEmptyQuestion) {
      this.message.error(this.t.instant('dj-请输入内容后再继续添加快捷提问'));
      return;
    }
    const group = this.fb.group({
      question: ['', [Validators.required, CommonValidators.trim]],
    });
    this.manual_questions.push(group);
    this.manual_lang.push({ question: '' });
    this.dataChangedEmit();
  }

  changeABatch(): void {
    this.loading = true;
    this.isFailed = false;
    const datasetName = this.questionForm.get('name')?.value;
    const description = this.questionForm.get('description').value;
    if (!datasetName) {
      this.loading = false;
      this.message.error(this.t.instant('dj-请先输入数据集名称'));
      return;
    }
    if (!description) {
      this.loading = false;
      this.message.error(this.t.instant('dj-请先输入数据集描述'));
      return;
    }
    const fields = [].concat(this.data.dimensions).concat(this.data.measures);
    const p = { datasetName, description, fields };
    this.api.getRecommended(p).subscribe(
      (res) => {
        this.loading = false;
        const { code, data } = res;
        if (code === 0) {
          this.isFailed = false;
          this.AI_questions = data;
          this.dataChangedEmit();
        }
      },
      () => {
        this.loading = false;
        this.isFailed = true;
      },
      () => {},
    );
  }

  dataChangedEmit(): void {
    const rowData = this.questionForm.getRawValue();
    const manual_questions = [];
    this.manual_questions.controls.forEach((control, index) => {
      manual_questions.push({
        lang: this.manual_lang[index],
        ...control.value,
      });
    });
    const data = {
      ...rowData,
      lang: this.lang,
      manual_questions,
      AI_questions: this.AI_questions,
    };
    this.dataChanged.emit(data);
  }

  verified(): boolean {
    for (const i of Object.keys(this.questionForm?.controls)) {
      this.questionForm.controls[i].markAsDirty();
      this.questionForm.controls[i].updateValueAndValidity();
    }
    if (this.questionForm.get('quickQuestion').value === 'manual') {
      let valid = true;
      this.manual_questions?.controls.forEach((manual_question: any) => {
        for (const i of Object.keys(manual_question?.controls)) {
          manual_question.controls[i].markAsDirty();
          manual_question.controls[i].updateValueAndValidity();
        }
        if (!manual_question.valid) {
          valid = false;
        }
      });
      if (!valid) {
        this.message.error(this.t.instant('dj-请输入快捷提问内容'));
      }
      return this.questionForm.valid && valid;
    }
    return this.questionForm.valid;
  }
}
