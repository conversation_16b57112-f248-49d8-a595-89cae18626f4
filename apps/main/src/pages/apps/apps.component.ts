import { After<PERSON>iew<PERSON>nit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AppService } from './app.service';
import { FormControl, FormGroup } from '@angular/forms';
import { AuthManageService } from 'components/bussiness-components/layout/menu-bar/auth-manage/auth-manage.service';

import { ExcelAppService } from './create-app/excel-app/excel-app.service';
import { debounceTime } from 'rxjs/operators';
import { fromEvent, Subscription } from 'rxjs';
import { delay, throttle } from 'lodash';
import { isInternalTenant, validatorForm } from 'common/utils/core.utils';
import { AdUserService } from 'pages/login/service/user.service';
import { AppStatus, AppTypes } from '../app/typings';
import { AuthService } from '../../common/service/auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import dayjs from 'dayjs';
import { TenantService } from 'pages/login/service/tenant.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { GlobalService } from 'common/service/global.service';
import { AppsService } from './apps.service';
import { LocaleService } from 'common/service/locale.service';
import { AppCardComponent } from './app-card/app-card.component';
import { IRecentAppModel } from 'pages/home/<USER>/type';
import { cloneDeep } from 'lodash';
import { SolutionBaseInfoFormComponent } from 'components/bussiness-components/solution-base-info-form/solution-base-info-form.component';
import { AuthPanelTypeEnum } from 'common/config/auth.config';
import { IndividualService } from 'pages/individual/individual.service';
import { SolutionCardService } from 'app/service/solution-card.service';

@Component({
  selector: 'app-apps',
  templateUrl: './apps.component.html',
  styleUrls: ['./apps.component.less'],
  providers: [ExcelAppService],
})
export class AppsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('appCard') appCard: AppCardComponent;
  @ViewChild('solutionInfoForm') solutionInfoForm: SolutionBaseInfoFormComponent;
  applyNameArr: Array<string>; // app多语言名称集合
  appName: string; // app名称
  searchValue: string;
  searchAppType: string = '';
  searchAppAuth: string = '';
  pageNum: number = 1;
  pageSize: number = 40;
  totalPageNum: number = 0;
  appList: any[] = [];
  searchControl: FormControl = new FormControl();
  branchChangeSubject: Subscription;

  addAppVisible: boolean = false;
  addAgileDataAppVisible: boolean = false; // 敏捷解决方案
  addNanaAssistantAppVisible: boolean = false; // 娜娜
  type: 'empty' | 'excel';
  params: any = {};
  createItems: any[];
  switchValue: string = 'card';
  // 新建解决方案开窗
  createSolutionVisible: boolean = false;
  // 新建解决方案loading
  createSolutionLoading: boolean = false;
  currentSolutionCardData: any; // 点击热门卡片的数据
  /**
   * app过期的map
   */
  private appOvertimeMap: Map<string, string | null> = new Map();

  isInternalTenant = isInternalTenant;
  // 内部isv
  teamId;
  private subscription: Subscription;

  isTenantActive = false; // 租户级 开发平台 是否激活
  updateCooperateEnterDataSubject$;
  listContainer: any; // 执行者

  isHighApp = AppTypes.HIGH_CODE; // 是否为高级应用

  // 浮层的位置
  position: { x: number; y: number } | undefined = undefined;
  // 默认的可创建的类型的解决方案
  defaultItems: any[] = [
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-数据驱动1.0解决方案',
      description: 'dj-通过数据驱动来完成业务流',
      type: 'empty',
      category: 1,
      appType: 1,
      detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/Ojx7wiJtqiVfmQkzzsucp6FGnzd?from=from_copylink',
    },
    // {
    //   icon: 'assets/img/home/<USER>',
    //   name: 'dj-数据驱动2.0解决方案',
    //   description: 'dj-通过数据驱动来完成业务流',
    //   type: 'empty',
    //   category: 1,
    //   appType: 5,
    //   detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/Wu08wIGsjiztb3kr1XwccppxnKg?from=from_copylink',
    // },
    // {
    //   img: 'assets/img/home/<USER>',
    //   title: 'dj-创建收集数据解决方案',
    //   description: 'dj-快速创建表单来收集数据、进行分析',
    //   type: 'empty',
    //   params: { appType: 3 },
    // },
    // {
    //   img: 'assets/img/home/<USER>',
    //   title: 'dj-根据Excel来创建解决方案',
    //   description: 'dj-通过已有的excel来创建表单并导入数据',
    //   type: 'excel',
    //   params: {},
    // },
    // {
    //   img: 'assets/img/home/<USER>',
    //   title: 'dj-业务过程随心控',
    //   description: 'dj-通过简易配置即可生成任务表单',
    //   type: 'empty',
    //   params: { appType: 4 },
    //   detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/DlY3wmOBQiL1AQkz7sqc1BZrnQd',
    // },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-敏捷问数1.0',
      description: 'dj-数据获取更加敏捷，言出数至',
      type: 'empty',
      category: 1,
      appType: 6,
      detailUrl: 'https://ksd.apps.digiwincloud.com.cn/public/1OKtXWx54',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-敏捷问数2.0',
      description: 'dj-AI助力，零基础数据分析助手',
      type: 'empty',
      category: 1,
      appType: 12,
      detailUrl: 'https://ksd.apps.digiwincloud.com.cn/public/1OKtXWx54',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-娜娜助理',
      description: 'dj-通过助理设计器创建娜娜助理',
      type: 'empty',
      category: 2,
      appType: 7,
      detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/IvdVwI9xniUcQ0kDCyuchncLnOh',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-AI模型服务',
      description: 'dj-面向开发者提供丰富且标准化的AI能力接口',
      type: 'empty',
      category: 2,
      appType: 8,
      detailUrl: 'https://z0lxpczot6u.feishu.cn/wiki/J9h8wvqifiCdBrk00NJc5SojnYg?from=from_copylink',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-知识中台技能中心',
      description: 'dj-通过知识中台技能中心创建AI技能',
      type: 'empty',
      category: 2,
      appType: -2,
      detailUrl: 'https://kai-skc-test.apps.digiwincloud.com.cn/sso-login',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: 'dj-数据中台',
      description: 'dj-一站式数据开发治理',
      type: 'empty',
      category: 3,
      appType: -1,
      detailUrl: 'https://hw-test-dmp-dmf.digiwincloud.com.cn/sso-login',
    },
    {
      icon: 'assets/img/home/<USER>',
      name: this.translate.instant('dj-高代码解决方案'),
      description: this.translate.instant('dj-通过编写代码开发前后端应用'),
      type: 'empty',
      category: 4,
      appType: 9,
      detailUrl: 'https://dev.digiwincloud.com.cn/help-center/content?fileId=DevConsoleDesc',
    },
  ];
  // 默认的可创建的类型的解决方案
  enterItems: any[] = [
    {
      img: 'assets/img/home/<USER>',
      title: 'dj-智驱解决方案开发',
      category: 1,
    },
    {
      img: 'assets/img/home/<USER>',
      title: 'dj-AI开发',
      category: 2,
    },
    {
      img: 'assets/img/home/<USER>',
      title: 'dj-大数据开发',
      category: 3,
    },
  ];
  activeCategory = 1; //激活的分类 1/2/3
  dataCenterUrl: any;
  platformCategory: any;

  // 卡片操作的状态
  public statusMap: Map<string, boolean> = new Map();
  // 正在编辑的数据
  public editingApp: IRecentAppModel | undefined = undefined;
  // 其他的解决方案修改弹框
  public appInfoVisible: boolean = false;
  // 删除解决方案的确认框
  public deleteAppVisible: boolean = false;

  appsLoading: boolean = false;

  get createItemsFilter() {
    return this.createItems?.filter((item) => {
      return item.category === this.activeCategory;
    });
  }

  get appLoading(): boolean {
    return this.app.appLoading;
  }

  hiddenMenuByEnv: boolean = false; //某些环境需要隐藏菜单，读取配置项
  showHighCodeByAppTypes: number[] = []; //某些制定的应用展示高代码入口
  solutionCardLoading = {}; // 创建入口的卡片，点击时需请求权限验证接口，这里记录loading状态

  constructor(
    private router: Router,
    private app: AppService,
    private userService: AdUserService,
    private authService: AuthService,
    private message: NzMessageService,
    private translate: TranslateService,
    private tenantService: TenantService,
    private globalService: GlobalService,
    private configService: SystemConfigService,
    private appsService: AppsService,
    private languageService: LocaleService,
    private authManageService: AuthManageService,
    public individualService: IndividualService,
    private solutionCardService: SolutionCardService,
    public elementRef: ElementRef,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      this.dataCenterUrl = config.dataCenterUrl;
      this.platformCategory = config.platformCategory;
      this.hiddenMenuByEnv = config.hiddenMenuByEnv === 'true';
      this.showHighCodeByAppTypes = config.showHighCodeByAppTypes?.split('_')?.map(Number);
      this.handleEnterItems();
    });
  }

  ngOnInit(): void {
    this.updateTenant();
    this.teamId = this.userService.getUser('teamId');
    this.handleInit();
    // search防抖优化
    this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
      this.handleChangeSearchValue();
    });
    this.updateCooperateEnterDataSubject$ = this.authService.updateCooperateEnterDataSubject$.subscribe((res) => {
      this.handleSearch();
    });
  }

  handleEnterItems() {
    if (!this.hiddenMenuByEnv) {
      this.enterItems.push({
        img: 'assets/img/home/<USER>',
        title: 'dj-高代码开发',
        category: 4,
      });
    }
    // 业务中心
    this.enterItems.push({
      img: 'assets/img/home/<USER>',
      title: 'dj-业务中台建模',
      category: 5,
    });
  }

  ngOnDestroy(): void {
    this.branchChangeSubject?.unsubscribe();
    this.subscription?.unsubscribe();
    this.updateCooperateEnterDataSubject$.unsubscribe();
  }

  async ngAfterViewInit() {
    this.addViewScroll();
    try {
      // TODO:暂时注掉，后面可能需要(热门卡片可能需要走接口获取，本期TBB暂时隐藏)
      await this.getCards();
    } catch (error) {}

    if (this.tenantService.isEduAndisExperience()) {
      await this.queryPermission();
    } else {
      this.createItems = this.defaultItems;
    }
  }

  private addViewScroll(): void {
    // 监听指定节点（元素） listContainer
    this.subscription = fromEvent(this.elementRef.nativeElement, 'scroll', {
      capture: true,
      passive: true,
    }).subscribe(
      throttle(() => {
        if (this.switchValue === 'list') {
          return;
        }
        // 租户级的解决方案列表不做分页
        if (this.userService.getUser('isTenantActive')) {
          return;
        }
        this.handleScroll();
      }, 100),
    );
  }

  /**
   * 查询可以使用的解决方案类型
   */
  private async queryPermission(): Promise<void> {
    const param = {
      userId: this.userService.getUser('userId'),
      sysId: this.platformCategory === 'TENANT' ? 'athena_tenant_designer' : 'athena-lcdp',
    };
    const types = await this.appsService.queryIamPermission(param);
    // 体验用户正常显示
    types?.concat([-1, -2]);
    this.createItems = this.defaultItems.filter((item) => {
      return types.includes(item.params.appType);
    });
  }

  /**
   * 当是体验用户的时候，查询解决方案的过期时间
   */
  private async queryAppOvertimeIfNeed(): Promise<void> {
    if (!this.tenantService.isEduAndisExperience()) return;
    const codes = this.appList.map((app) => app.code).filter((code) => !this.appOvertimeMap.has(code));
    if (!codes.length) return;
    const result = await this.app.queryExperienceOverTime(codes).toPromise();
    if (result.code === 0) {
      result.data.forEach((item) => {
        this.appOvertimeMap.set(item.code, item.expiredTime || null);
      });
    }
  }

  handleScroll() {
    const clientHeight = this.elementRef.nativeElement.clientHeight;
    const scrollTop = this.elementRef.nativeElement.scrollTop;
    const scrollHeight = this.elementRef.nativeElement.scrollHeight;
    if (clientHeight + scrollTop + 20 >= scrollHeight && this.app.appLoading === false) {
      // 滚动到底部
      if (this.pageNum < this.totalPageNum) {
        // 未加载完
        this.handlePagedSearch();
      }
    }
  }

  // 初始化
  handleInit(): void {
    this.handleSearch();
  }

  // 解决方案类型查询
  handleAppTypeSearch() {
    this.handleSearch();
  }
  //解决方案权限查询
  handleAppAuthSearch() {
    this.handleSearch();
  }

  // 分页搜索 搜索下一页数据
  handlePagedSearch(): void {
    this.handleSearch(this.pageNum + 1);
  }

  // 搜索 直接调用是从第一页开始重新搜索
  handleSearch(pageNum: number = 1): void {
    this.pageNum = pageNum;
    if (this.switchValue === 'list') {
      this.appsLoading = true;
    } else {
      this.app.appLoading = true;
    }
    if (this.userService.getUser('isTenantActive')) {
      // 租户级的解决方案列表单独查询
      this.getTenantAppList({
        condition: this.searchValue || '',
      });
      return;
    }
    this.app
      .loadAppList({
        condition: this.searchValue || '',
        pageNum,
        pageSize: this.pageSize,
        appType: this.searchAppType,
        auth: this.searchAppAuth,
      })
      .subscribe(
        (res) => {
          this.totalPageNum = res?.data?.totalPageNum ?? 0;
          const start = (pageNum - 1) * this.pageSize;
          const deleteCount = this.appList?.length ?? 0 + pageNum - start;
          this.appList.splice(start, deleteCount, ...(res?.data?.data ?? []));

          if (this.switchValue === 'list') {
            this.appsLoading = false;
          } else {
            this.app.appLoading = false;
          }

          // appType不存在时 做兼容
          this.appList.forEach((item) => {
            item.appType ??= 1;
          });
          this.queryAppOvertimeIfNeed();
          this.appList = [...this.appList];
        },
        () => {
          if (this.switchValue === 'list') {
            this.appsLoading = false;
          } else {
            this.app.appLoading = false;
          }
          this.appList = [];
        },
      );
  }

  getTenantAppList(params) {
    this.app.loadTenantAppList(params).subscribe(
      (res) => {
        this.appList = res?.data ?? [];
        this.app.appLoading = false;
        // appType不存在时 做兼容
        this.appList.forEach((item) => {
          item.appType ??= 1;
        });
        this.appList = [...this.appList];
      },
      () => {
        this.app.appLoading = false;
        this.appList = [];
      },
    );
  }

  // 清空搜索
  handleClearSearch(): void {
    this.searchValue = '';
    this.handleSearch();
  }

  // 新增
  async handleAddApp({ event, data }) {
    const { appType, detailUrl, type } = data;
    // 权限检测：是否购买或授权
    const isContiue = await this.checkCardAuth(data);
    if (!isContiue) return;

    if (appType < 0) {
      this.handleLinkDetail({ event, data });
      return;
    }
    this.type = type ?? 'empty';
    this.params = { appType };
    this.currentSolutionCardData = data;
    if (this.tenantService.isEduAndisExperience()) {
      const target = event.currentTarget as HTMLDivElement;
      const { x, y, height, width } = target.getBoundingClientRect();
      this.position = {
        x: ((x + width / 2) << 0) + 10,
        y: ((y + height / 2) << 0) - 20,
      };
    } else {
      if (data.appType === AppTypes.DATA_VIEW) {
        // 新建解决方案
        this.createSolutionVisible = true;
      } else if ( [AppTypes.AGILE_DATA, AppTypes.AGILE_QUESTIONS].includes(appType)) {
        this.addAgileDataAppVisible = true;
      } else if (appType === AppTypes.NANA_ASSISTANT) {
        this.addNanaAssistantAppVisible = true;
      } else {
        this.editingApp = undefined;
        this.addAppVisible = true;
      }
    }
  }

  // 选择APP
  handleSelectApp(app: any, data?: any): void {
    const { appType, code } = app;
    if (this.checkCodeIsOvertime(code)) {
      this.message.error(this.translate.instant('dj-当前解决方案体验已到期'));
      return;
    }
    this.app.setAppAuthUser({ code: app.code }).subscribe(
      () => {},
      () => {},
    );
    // 新开页面
    switch (appType) {
      case AppTypes.COLLECT_DATA_V2: {
        // 数据收集型解决方案暂不维护，跳404
        this.router.navigateByUrl('errors/404');
        break;
      }
      case AppTypes.SCENARIO_KIT: {
        let urlString = data?.hightCode ? 'app/app-publish' : 'app/kit/task-control';
        const url = this.router.serializeUrl(
          this.router.createUrlTree([urlString], { queryParams: { appCode: app.code } }),
        );
        window.open(url, '_blank');
        break;
      }
      case AppTypes.NANA_ASSISTANT: {
        let urlString = 'asa-designer-web';
        let params: any = { appCode: app.code };
        if (data?.hightCode) {
          params.extensionApp = 'highCode';
          urlString = 'app';
        }
        const url = this.router.serializeUrl(this.router.createUrlTree([urlString], { queryParams: params }));
        window.open(url, '_blank');
        break;
      }

      case AppTypes.DATA_VIEW: {
        // tbb解决方案,获取注册信息->拿到第一个menu后再跳转
        const designerInfo = this.globalService.standaloneDesigners.find((s) => s.appType === appType);
        // 处理hash路由
        const indexUrl = designerInfo.designer.routeMode === 'hash' ? '#/' : '';
        const url = decodeURIComponent(
          this.router.serializeUrl(
            this.router.createUrlTree([`standalone-solution${indexUrl}`], { queryParams: { appCode: app.code } }),
          ),
        );
        window.open(url, '_blank');
        break;
      }
      case AppTypes.BUSINESS_DOMAIN_CENTER: {
        let urlString = 'app/business-domain-center/model-design';
        let params: any = { appCode: app.code };
        if (data?.hightCode) {
          params.extensionApp = 'highCode';
          urlString = 'app';
        }
        const url = this.router.serializeUrl(this.router.createUrlTree([urlString], { queryParams: params }));
        window.open(url, '_blank');
        break;
      }
      case AppTypes.HIGH_CODE: {
        const url = this.router.serializeUrl(
          this.router.createUrlTree(['app'], { queryParams: { appCode: app.code } }),
        );
        window.open(url, '_blank');
        break;
      }
      default: {
        if (this.tenantService.hideMenuBar()) {
          const urlString = 'app/integrated-automation/data-execution-new';
          const url = this.router.serializeUrl(
            this.router.createUrlTree([urlString], { queryParams: { appCode: app.code } }),
          );
          location.href = url;
        } else {
          const urlString = data?.hightCode ? 'app/app-publish' : 'app';
          const url = this.router.serializeUrl(
            this.router.createUrlTree([urlString], { queryParams: { appCode: app.code } }),
          );
          window.open(url, '_blank');
        }
        break;
      }
    }
  }

  handleChangeSearchValue(): void {
    if (this.searchValue?.length <= 50) {
      this.handleSearch();
    }
  }

  /**
   * 解决方案保存成功回调
   */
  onAfterCreated(app) {
    if (this.editingApp) {
      // 编辑应用信息的
      this.handleAppEdit(app);
    } else {
      this.handleSearch();
      const t = setTimeout(() => {
        clearTimeout(t);
        this.handleSelectApp(app, {});
      }, 1500);
    }
  }

  onVisibleChange(visible) {
    if (this.editingApp) {
      // 编辑应用的
      this.onVisibleChangeSet(visible);
    } else {
      this.addAppVisible = visible;
    }
  }

  updateTenant() {
    this.isTenantActive = this.userService.getUser('isTenantActive') || false;
    if (this.isTenantActive) this.searchAppType = '5';
  }

  /**
   * 检测解决方案有没有过期
   * @param code
   * @returns
   */
  private checkCodeIsOvertime(code: string) {
    if (this.appOvertimeMap.has(code)) {
      const overtime = this.appOvertimeMap.get(code);
      return overtime !== null && dayjs(overtime).isBefore(new Date());
    }
  }

  enterCategory(item) {
    this.activeCategory = item.category;
  }

  handleLinkDetail({ event, data }) {
    event.stopPropagation();
    // 知识中台入口，支持sso特殊处理
    const token = this.userService.getUser('iamToken');
    const { appType, detailUrl } = data;
    let url = detailUrl;

    if (appType === -2) {
      const language = this.languageService?.currentLanguage || 'zh_CN';
      url = `${detailUrl}?userToken=${token}&customLang=${language}`;
    }
    if (appType === -1) {
      url = `${this.dataCenterUrl}?userToken=${token}`;
    }
    window.open(url, '_blank');
  }

  handleSwitch(type) {
    this.switchValue = type ? 'card' : 'list';
  }

  public checkCanLoadMore = (): boolean => {
    return !this.userService.getUser('isTenantActive');
  };

  public handleQueryPermission = (type: 'Delete' | 'Setting', app: IRecentAppModel): boolean => {
    if (type === 'Setting') {
      return !this.isNana(app.appType);
    }
    if (type === 'Delete') {
      return this.isManageDelete(app.roles);
    }
  };

  public handleLoadMore(): void {
    if (this.pageNum < this.totalPageNum) {
      // 未加载完
      this.handlePagedSearch();
    }
  }

  //#region html method
  public isNana(appType: AppTypes): boolean {
    return appType === AppTypes.NANA_ASSISTANT;
  }

  public isManageDelete(roles) {
    return this.authManageService.getDeleteAuth(roles);
  }
  //#endregion

  //#region 打开解决方案
  async handleAppClick(app: IRecentAppModel): Promise<void> {
    if (await this.checkAppBeforeOpen(app)) {
      this.handleSelectApp(app);
    }
  }

  /**
   * 检测解决方案是否可以打开
   * @param app
   * @returns true: 可以打开 false:不可以打开
   */
  private async checkAppBeforeOpen(app: IRecentAppModel): Promise<boolean> {
    if (this.statusMap.get(app.objectId)) return false;
    if (app.deleteStatus === AppStatus.DELETING) return false;
    if (app.deleteStatus === AppStatus.DELETE_FAIL) {
      this.message.warning(this.translate.instant('dj-解决方案删除失败，请重新删除'));
      return false;
    }
    try {
      const res = await this.app.queryAppInfo([app.code]);
      if (res.code === 0) {
        const info = res.data?.[0];
        if (info) {
          if (!info.deleteStatus || info.deleteStatus === AppStatus.SUCCESS) {
            return true;
          }
        }
      }
      return false;
    } catch {
      return false;
    }
  }
  //#endregion

  //#region 设置解决方案
  /**
   * 设置解决方案信息
   * @param event
   * @param app
   */
  public async handleSetApp(event: MouseEvent | undefined, app: IRecentAppModel): Promise<void> {
    event?.stopPropagation();
    event?.preventDefault();

    this.editingApp = cloneDeep(app);
    if (!(await this.checkAppBeforeOpen(app))) {
      return;
    }
    if (this.editingApp.appType === AppTypes.MODEL_DRIVEN || this.editingApp.appType === AppTypes.HIGH_CODE) {
      this.params = {
        appType: this.editingApp?.appType,
      };
      this.type = 'empty';
      this.addAppVisible = true;
    } else {
      this.appInfoVisible = true;
    }
  }

  public onVisibleChangeSet(visible: boolean): void {
    this.addAppVisible = visible;
    if (!visible) {
      this.editingApp = undefined;
    }
  }

  public handleAppEdit(app: Partial<IRecentAppModel>): void {
    if (app) {
      const index = this.appList.findIndex((item) => item.code === app.code);
      if (index > -1) {
        this.appList = this.appList.map((e, i) => {
          return i === index ? Object.assign(this.appList[index], app, { lang: app.application?.lang || app.lang }) : e;
        });
        if (this.app.selectedApp?.code && this.app.selectedApp?.code === app.code) {
          this.app.selectedApp = this.appList[index];
        }
      }
    }
    this.appInfoVisible = false;
    this.addAppVisible = false;
    this.editingApp = undefined;
  }

  handleGoApp(appCode) {
    if (this.editingApp) {
      const url = this.router.serializeUrl(this.router.createUrlTree(['app'], { queryParams: { appCode } }));
      window.open(url, '_blank');
    }
  }
  //#endregion

  //#region 删除解决方案
  /**
   * 确认删除解决方案
   * @param event
   * @param app
   */
  public handleDeleteApp(event: MouseEvent | undefined, app: IRecentAppModel): void {
    event?.stopPropagation();
    event?.preventDefault();
    this.editingApp = app;
    const nameObj = app?.lang?.name;
    this.applyNameArr = [];
    this.appName = nameObj[this.languageService.currentLanguage];
    Object.entries(nameObj).forEach(([, value]) => {
      this.applyNameArr.push(value);
    });
    this.deleteAppVisible = true;
  }

  public async handleAppOk(value?: string): Promise<void> {
    this.handleDeleteCancel();
    this.statusMap.set(this.editingApp!.objectId, true);
    try {
      const res = await this.app.deleteApp(this.editingApp!.code).toPromise();
      this.updateAppList(this.editingApp!.code, 'success');
      if (res.code === 0) {
        if (this.app.selectedApp?.code && this.app.selectedApp?.code === this.editingApp?.code) {
          this.app.selectedApp = null;
        }
        this.message.success(this.translate.instant('dj-home-删除成功'));
      }
    } catch {
      this.message.error(this.translate.instant('dj-home-请重试'));
    } finally {
      this.statusMap.set(this.editingApp!.objectId, false);
      this.handleDeleteCancel();
    }
  }

  public handleDeleteNewOk(status: string): void {
    if (status === 'success') {
      if (this.app.selectedApp?.code && this.app.selectedApp?.code === this.editingApp?.code) {
        this.app.selectedApp = null;
      }
    }
    this.updateAppList(this.editingApp!.code, status);
  }

  private updateAppList(code: string, status: string): void {
    const ind = this.appList.findIndex((item) => item.code === code);
    if (ind > -1) {
      if (status === 'success') {
        this.appList = [...this.appList.slice(0, ind), ...this.appList.slice(ind + 1)];
      } else {
        // 删除失败
        this.appList[ind]['deleteStatus'] = AppStatus.DELETE_FAIL;
      }
    }
  }

  public handleDeleteCancel(): void {
    this.deleteAppVisible = false;
  }
  //#endregion

  public handleGridAppAction(event: { type: 'Edit' | 'HighCode' | 'Setting' | 'Delete'; app: IRecentAppModel }): void {
    const { type, app } = event;
    switch (type) {
      case 'Edit':
        this.handleAppClick(app);
        break;
      case 'HighCode':
        // todo 进入高代码
        break;
      case 'Delete':
        this.handleDeleteApp(undefined, app);
        break;
      case 'Setting':
        this.handleSetApp(undefined, app);
        break;
    }
  }

  /**
   * 获取所有卡片信息
   * @returns
   */
  async getCards() {
    const res = await this.solutionCardService.queryAllSolutionCards().toPromise();
    if (res.code !== 0) return;
    res.data.forEach((item) => {
      const { codeRule, enter, auth } = item;
      this.defaultItems.push({
        ...codeRule,
        ...enter,
        auth,
      });
    });
  }

  /**
   * 创建解决方案-保存
   */
  async handleOk() {
    const form: FormGroup = this.solutionInfoForm.getForm();
    const formValue = this.solutionInfoForm.getFormValue();

    // 触发验证
    validatorForm(form);

    if (form.valid) {
      try {
        this.createSolutionLoading = true;
        const res = await this.appsService.createSolution(formValue).toPromise();
        if (res.code !== 0) {
          return;
        }
        this.message.success(this.translate.instant('dj-创建成功'));
        this.createSolutionLoading = false;
        this.onAfterCreated(formValue);
        this.handleSelectApp(formValue);
        this.createSolutionVisible = false;
      } catch (error) {
        this.createSolutionLoading = false;
      }
    }
  }

  isManage(roles) {
    return (
      this.authManageService.getAuthPanelType(roles) &&
      this.authManageService.getAuthPanelType(roles) !== AuthPanelTypeEnum.CooperateCommon
    );
  }

  /**
   * 点击进入高代码
   */
  async handleEnterHighApp(data) {
    if (await this.checkAppBeforeOpen(data)) {
      this.handleSelectApp(data, {
        hightCode: true,
      });
    }
  }

  /**
   * 权限检测：是否购买或授权
   */
  async checkCardAuth(data): Promise<boolean> {
    const { appType } = data;

    if (appType === AppTypes.MODEL_DRIVEN) {
      try {
        this.solutionCardLoading[appType] = true;
        await this.appsService.verifySolutionPermission({ appType }).toPromise();
        // 请求接口获取权限
        return true;
      } catch (error) {
        return false;
      } finally {
        this.solutionCardLoading[appType] = false;
      }
    }
    return true;
  }
}
