import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { DatePipe } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ComponentsService } from 'common/service/components.service';
import { isInternalTenant, S4 } from 'common/utils/core.utils';
import { AdUserService } from 'pages/login/service/user.service';
import { AppService } from '../app.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { TenantService } from 'pages/login/service/tenant.service';
import { omit } from 'lodash';

@Component({
  selector: 'app-create-agile-data-app',
  templateUrl: './create-agile-data-app.component.html',
  styleUrls: ['./create-agile-data-app.component.less'],
})
export class CreateAgileDataAppComponent implements OnInit, OnChanges {
  @Input() type: string;
  @Input() visible: boolean = false;
  @Input() params: any = {}; // 提交时params也一起提交
  @Output() afterCreated: EventEmitter<any> = new EventEmitter();
  @Output() readonly visibleChange = new EventEmitter<boolean>();

  currentLanguage: string;
  repeatVisible: boolean = false;
  tenantId: string;
  teamId: string;
  appForm: FormGroup;
  repeatForm: FormGroup;
  appLang: any;
  saveAppLoading: boolean;
  repeatAppData: any;
  value: any;
  emptyVisible: boolean;
  applicationCode: string = '';
  uploadFile: any = null;
  progressShow: boolean = false;
  progressApp: any = null;
  progressInterval: any; // 轮询进度
  appCodeEnv: string;

  isInternalTenant = isInternalTenant;

  // 敏捷解决方案导入权限用户列表（已弃用，不维护用户账号）
  agileDataImportUsers: string[] = [];
  agileDataImportAuth: boolean = false;

  get eduEnv() {
    return this.tenantService.isEduEnv();
  }

  constructor(
    private datePipe: DatePipe,
    private userService: AdUserService,
    private app: AppService,
    private fb: FormBuilder,
    public componentService: ComponentsService,
    private message: NzMessageService,
    private translateService: TranslateService,
    private languageService: LocaleService,
    private configService: SystemConfigService,
    private tenantService: TenantService,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      this.appCodeEnv = config.appCodeEnv;
    });
    this.currentLanguage = this.languageService.currentLanguage;
    this.tenantId = this.userService.getUser('tenantId');
    this.teamId = this.userService.getUser('teamId');

    const currentUserName = this.userService.getUser('userName') ?? this.userService.getUser('name');
    this.agileDataImportAuth = this.agileDataImportUsers.includes(currentUserName);

    this.applicationCode = this.getDefaultCode();
    this.appForm = this.fb.group({
      way: ['default', [Validators.required]],
      code: [
        this.getDefaultCode(),
        [Validators.required, Validators.minLength(2), Validators.maxLength(10)],
        [this.app.codeValidator],
      ],
      name: [null, [Validators.required, Validators.pattern('^.{0,40}$')]],
      description: [null],
      iconName: [null, Validators.required],
      iconBgcolor: [null, Validators.required],
      source: [null, [Validators.required]],
      introduction: [null, [Validators.pattern('^.{0,38}$')]],
      prompt: [null, [Validators.pattern('^.{0,20}$')]],
      appSystem: [null, [Validators.required]],
      iconUrl: [null],
    });

    this.repeatForm = this.fb.group({
      appToken: [null, [Validators.required]],
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('visible')) {
      this.emptyVisible = changes.visible.currentValue;
      if (this.emptyVisible) {
        this.handleInitApp();
      }
    }
  }

  // eslint-disable-next-line @angular-eslint/no-empty-lifecycle-method
  ngOnInit() {
    const nameControl = this.appForm.get('name');
    if (nameControl) {
      nameControl.setValidators([
        Validators.required,
        Validators.pattern(this.params?.appType === 12 ? '^.{0,8}$' : '^.{0,40}$'),
      ]);
      nameControl.updateValueAndValidity();
    }
    const descriptionControl = this.appForm.get('description');
    if (descriptionControl) {
      if (this.params.appType === 12) {
        nameControl.setValidators([Validators.required]);
        nameControl.updateValueAndValidity();
      }
    }
  }

  getDefaultCode(): string {
    return `M${S4()}${S4()[0]}${S4()}`;
  }

  // 新增
  handleInitApp(): void {
    this.applicationCode = this.getDefaultCode();
    this.appForm.patchValue({
      code: this.applicationCode,
      name: null,
      description: null,
      iconName: this.params?.appType === 12 ? 'robot.svg' : '1.png',
      iconBgcolor: '#0189FF',
      source: 'dcp',
      introduction: null,
      prompt: null,
      appSystem: 'dataset',
    });

    this.value = { image: '1.png', color: '#0189FF' };

    this.handleInitLang();

    // 体验环境
    if (this.eduEnv) {
      // 连接系统类型，默认连接地端系统，且不可更改
      this.appForm.get('source')?.disable();
    }
  }

  // 处理国际化
  handleInitLang(): void {
    this.appLang = {
      name: this.componentService.formatLang({}, 'name'),
      description: this.componentService.formatLang({}, 'description'),
      introduction: this.componentService.formatLang({}, 'introduction'),
      prompt: this.componentService.formatLang({}, 'prompt'),
    };
  }

  // 机制表单赋值
  handlePatchApp(key: any, data: any): void {
    this.appForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.appLang = {
        ...(this.appLang || {}),
        [key]: data.lang?.value || data.lang,
      };
    }
  }
  // 机制表单赋值
  handlePatchLangApp(key: any, data: any): void {
    if (data.needLang) {
      this.appLang = {
        ...(this.appLang || {}),
        [key]: data.lang?.value || data.lang,
      };
    }
  }

  // 选择图标
  onAppIconChange(data) {
    const { image, color } = data;
    this.appForm.patchValue({
      iconName: image,
      iconBgcolor: color,
    });
    this.value = { image: image, color: color };
  }
  // 选择图片
  onAppImageChange(data) {
    if (typeof data === 'string') {
      this.appForm.patchValue({
        iconUrl: data,
      });
    }
  }

  // 创建方式变化
  handleWayChanged($event): void {
    if ($event === 'default') {
      this.appForm.addControl('description', this.fb.control(null, [Validators.required]));
      this.appForm.addControl('iconName', this.fb.control(null, Validators.required));
      this.appForm.addControl('iconBgcolor', this.fb.control(null, Validators.required));
      this.appForm.addControl('source', this.fb.control(null, Validators.required));
      this.appForm.addControl('appSystem', this.fb.control(null, Validators.required));
      this.appForm.patchValue({
        description: null,
        iconName: '1.png',
        iconBgcolor: '#0189FF',
        source: 'dcp',
        appSystem: 'metric',
      });
      this.value = { image: '1.png', color: '#0189FF' };
      this.appLang.description = this.componentService.formatLang({}, 'description');
    } else {
      this.appForm.removeControl('description');
      this.appForm.removeControl('iconName');
      this.appForm.removeControl('iconBgcolor');
      this.appForm.removeControl('source');
      this.appForm.removeControl('appSystem');
      this.uploadFile = null;
    }
  }

  beforeUpload = (file: any): boolean => {
    if (!['zip'].includes(file?.name?.split('.')?.slice(-1)?.[0])) {
      this.message.warning(this.translateService.instant('dj-文件格式有误，请重新选择'));
      return false;
    }
    const isLt5M = file.size! / 1024 / 1024 < 5;
    if (!isLt5M) {
      this.message.warning(this.translateService.instant('dj-文件大小超过限制，请重新选择'));
      return false;
    }

    this.handleUpload(file);
    return false;
  };

  // 上传文件
  handleUpload(file: any): void {
    this.uploadFile = file;
  }

  // 移除文件
  removeFile() {
    this.uploadFile = null;
  }

  // 保存解决方案
  handleSaveApp(): void {
    for (const i of Object.keys(this.appForm?.controls)) {
      this.appForm.controls[i].markAsDirty();
      this.appForm.controls[i].updateValueAndValidity();
    }
    if (this.appForm.valid) {
      const { way, ...values } = this.appForm.getRawValue();
      // 拼接CN、TW
      values.code = values.code + this.appCodeEnv;
      if (way === 'default') {
        this.saveAppLoading = true;
        let param = {
          ...values,
          ...this.params,
          lang: this.appLang,
        };
        if (this.params?.appType === 12) {
          param = omit(param, ['appSystem']);
        }
        this.app.addApp(param).subscribe(
          (res) => {
            this.successCallback(res, param);
          },
          (e) => {
            this.errorCallback(e, param);
          },
        );
      }

      if (way === 'zip') {
        this.saveAppLoading = true;
        const formData = new FormData();
        formData.append('file', this.uploadFile);
        formData.append('appName', values?.name);
        formData.append('appCode', values?.code);
        this.app.importAgileDataApp(formData).subscribe(
          (res) => {
            if (res?.code === 0) {
              this.saveAppLoading = false;
              this.progressShow = true;
              this.handleModalProcess(res?.data);
              this.progressInterval = setInterval(() => {
                this.handleModalProcess(res?.data);
              }, 3000);
            } else {
              this.saveAppLoading = false;
            }
          },
          () => {
            this.saveAppLoading = false;
          },
        );
      }
    }
  }

  successCallback(res, param) {
    this.saveAppLoading = false;
    if (res.code === 0) {
      this.message.success(this.translateService.instant('dj-保存成功！'));
      this.handleCancelApp();
      this.afterCreated.emit(param);
    }
  }

  errorCallback(e, param) {
    this.saveAppLoading = false;
    if (e?.error?.errorCode === -1) {
      this.emptyVisible = false;
      this.repeatForm.reset();
      this.repeatAppData = param;
      this.repeatVisible = true;
    }
  }

  // 展示解决方案导入进度
  handleModalProcess(params): void {
    this.app.queryImportProcessAgileDataApp(params).subscribe(
      (res) => {
        if (res?.code === 0) {
          this.progressApp = res?.data
            ? {
                ...(res?.data || {}),
                progress: Math.floor((res?.data?.progress || 0) * 100),
              }
            : null;
          // 如果进度100%，结束轮询
          if (this.progressApp?.progress === 100 || this.progressApp?.progress === -100) {
            clearInterval(this.progressInterval);
          }
        }
      },
      () => {
        this.progressApp = null;
        clearInterval(this.progressInterval);
      },
    );
  }

  // 关闭导入进度
  handleCloseProcess(): void {
    this.progressShow = false;
    this.handleCancelApp();
  }

  // 进入解决方案
  handleEnterApp(): void {
    this.progressShow = false;
    this.handleCancelApp();
    this.afterCreated.emit({ code: this.progressApp?.application });
  }

  handleSaveRepeat(): void {
    for (const i of Object.keys(this.repeatForm?.controls)) {
      this.repeatForm.controls[i].markAsDirty();
      this.repeatForm.controls[i].updateValueAndValidity();
    }
    if (this.repeatForm.valid) {
      this.saveAppLoading = true;
      const { appToken } = this.repeatForm.getRawValue();
      const param = {
        ...this.repeatAppData,
        appToken,
      };

      this.app.addApp(param).subscribe(
        (res) => {
          this.saveAppLoading = false;
          if (res.code === 0) {
            this.message.success(this.translateService.instant('dj-保存成功！'));
            this.repeatVisible = false;
            this.afterCreated.emit(param);
            this.visibleChange.emit(false);
          }
        },
        () => {
          this.saveAppLoading = false;
        },
      );
    }
  }

  handleCancelRepeat() {
    this.repeatVisible = false;
    this.visibleChange.emit(false);
  }

  handleCancelApp() {
    this.emptyVisible = false;
    this.visibleChange.emit(false);
  }

  handleModalClose() {
    this.appForm.reset();
    this.uploadFile = null;
  }
}
