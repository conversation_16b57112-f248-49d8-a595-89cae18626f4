/**
 * 解决方案类型：
 * DTD：数据驱动型解决方案
 * COLLECT_DATA：数据收集型解决方案
 * COLLECT_DATA_V2：数据收集型v2解决方案
 * SCENARIO_KIT：场景化套件型解决方案
 * MODEL_DRIVEN：模型驱动型解决方案
 * AGILE_DATA：敏捷数据解决方案
 * DATA_VIEW:数据可视化
 * BUSINESS_DOMAIN_CENTER: 业务领域中心
 */
export const enum AppTypes {
  DTD = 1,
  COLLECT_DATA = 2,
  COLLECT_DATA_V2 = 3,
  SCENARIO_KIT = 4,
  MODEL_DRIVEN = 5,
  AGILE_DATA = 6,
  NANA_ASSISTANT = 7,
  AI_MODEL = 8,
  HIGH_CODE = 9,
  DATA_VIEW = 10,
  BUSINESS_DOMAIN_CENTER = 11,
  AGILE_QUESTIONS = 12,
  AI_AGENT_CREATE_APP = 13,
}

/**
 * 解决方案状态
 * DELETE_FAIL:删除失败
 * DELETING:删除中
 * SUCCESS:创建成功
 * CREATING:创建中
 * CREATIONFAILED:创建失败
 * UPDATING:更新中
 * UPDATEFAILED:更新失败
 */
export enum AppStatus {
  DELETE_FAIL = 'deleteFailed',
  DELETING = 'deleting',
  SUCCESS = 'success',
  CREATING = 'creating',
  CREATE_FAIL = 'creationFailed',
  UPDATING = 'updating',
  UPDATE_FAIL = 'updateFailed',
}

export const enum ExtensionAppTypes {
  HIGHTCODE = 'highCode',
}
