import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormBuilder, Validators } from '@angular/forms';
import { LocaleService } from 'common/service/locale.service';
import { AppTypes } from 'pages/app/typings';
import { AppService } from '../../../apps/app.service';
import { AppInfoService } from '../app-info.service';
import { TenantService } from 'pages/login/service/tenant.service';
import { SolutionCardService } from 'app/service/solution-card.service';
import { omit } from 'lodash';

@Component({
  selector: 'app-info-edit',
  templateUrl: './app-info-edit.component.html',
  styleUrls: ['./app-info-edit.component.less'],
  providers: [AppInfoService],
})
export class AppInfoEditComponent implements OnInit {
  @Input() visible: boolean = false;
  @Input() formData: any;
  @Output() callback: EventEmitter<any> = new EventEmitter();

  submitLoading: boolean = false;
  appForm: any;
  lang: any;
  value: any;

  targetList: any[] = [];
  serviceVisible: boolean = false;

  dynamicsAttributes: any[];

  // 是否展示公共配置
  get isShowCommomApp() {
    return this.formData?.appType === AppTypes.DTD;
  }

  // 是否模型驱动解决方案
  get isMdApp() {
    return this.formData?.appType === AppTypes.MODEL_DRIVEN;
  }

  // 是否敏捷数据解决方案
  get isAgApp() {
    return [AppTypes.AGILE_DATA, AppTypes.AGILE_QUESTIONS].includes(this.formData?.appType);
  }
  // 敏数2.0
  get isAgQuestion() {
    return this.formData?.appType === AppTypes.AGILE_QUESTIONS;
  }

  // 敏数1.0
  get isAgData() {
    return this.formData?.appType === AppTypes.AGILE_DATA;
  }

  get eduEnv() {
    return this.tenantService.isEduEnv();
  }

  constructor(
    private appService: AppService,
    public translateService: TranslateService,
    private message: NzMessageService,
    private fb: FormBuilder,
    private languageService: LocaleService,
    private appInfoService: AppInfoService,
    public tenantService: TenantService,
    public solutionCardService: SolutionCardService,
  ) {}

  ngOnInit(): void {
    this.appForm = this.fb.group({
      code: [null, [Validators.required]],
      name: [null, [Validators.required, Validators.pattern(this.isAgQuestion ? '^.{0,8}$' : '^.{0,40}$')]],
      commonApp: [false],
      description: [null, [Validators.required]],
      iconName: [null, Validators.required],
      iconBgcolor: [null, Validators.required],
      introduction: [null, [Validators.pattern('^.{0,38}$')]],
      prompt: [null, [Validators.pattern('^.{0,20}$')]],
      iconUrl: [null],
    });
    if (!!this.formData?.code) {
      this.appForm.get('code').disable();
      // this.appForm.get('commonApp').disable();
    }

    const iconName = this.formData?.iconName ?? '1.png';
    const iconBgcolor = this.formData?.iconBgcolor ?? '#0189FF';
    const iconUrl = this.formData?.iconUrl;

    this.appForm.patchValue({
      code: this.formData?.code,
      name: this.formData?.name,
      commonApp: !!this.formData?.commonApp,
      description: this.formData?.description,
      iconName,
      iconBgcolor,
      prompt: this.formData?.prompt,
      introduction: this.formData?.introduction,
      iconUrl,
    });
    this.handleLang(this.formData);
    this.value = { image: iconName, color: iconBgcolor, iconUrl };

    // 调用处理动态表单
    this.handleSetDynamicsAttribute();

    // 模型驱动解决方案
    if (this.isMdApp) {
      this.getTargetList(false);
    }

    if (this.isAgApp) {
      this.appForm.addControl('source', this.fb.control(this.formData?.source, Validators.required));
      this.appForm.addControl('appSystem', this.fb.control(this.formData?.appSystem ?? 'metric', Validators.required));
      this.appForm.addControl('payType', this.fb.control(this.formData?.payType ?? 'none', Validators.required));
      if (this.appForm.get('payType').value !== 'none') {
        this.appForm.addControl(
          'billingGoodsId',
          this.fb.control(this.formData?.billingGoodsId ?? '', Validators.required),
        );
      }

      // 体验环境
      if (this.eduEnv) {
        // 连接系统类型，默认连接地端系统，且不可更改
        this.appForm.get('source')?.disable();
        // 计费方式默认按次收费，且不可更改
        this.appForm.get('payType')?.disable();
        // 计费商品ID不可更改
        this.appForm.get('billingGoodsId')?.disable();
      }

      // 连接应用体系不可更改
      this.appForm.get('appSystem')?.disable();
    }
    if (this.isAgData) {
      this.appForm.get('description').removeValidators(Validators.required);
    }
  }

  /**
   * 处理动态表单
   * @returns
   */
  handleSetDynamicsAttribute(): void {
    this.dynamicsAttributes = this.solutionCardService.getDynamicsAttributes(this.formData?.appType);
    if (this.dynamicsAttributes.length === 0) {
      return;
    }
    // 处理表单
    this.dynamicsAttributes.forEach((attr) => {
      switch (attr.componentType) {
        case 'CHECKBOX':
          if (!this.appForm.get(attr.formControlName)) {
            this.appForm.addControl(attr.formControlName, this.fb.control(this.formData?.[attr.formControlName]));
          } else {
            this.appForm.patchValue({
              [attr.formControlName]: attr.defaultValue,
            });
          }
          break;
      }
    });
  }

  handlePayType(type: string): void {
    if (type === 'none') {
      this.appForm.removeControl('billingGoodsId');
    } else {
      this.appForm.addControl(
        'billingGoodsId',
        this.fb.control(this.formData?.billingGoodsId ?? '', Validators.required),
      );
    }
  }

  // 处理国际化
  handleLang(data: any): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const format = (key: any): any => {
      const lang = {
        zh_CN: data?.lang?.[key]?.zh_CN || '',
        zh_TW: data?.lang?.[key]?.zh_TW || '',
        en_US: data?.lang?.[key]?.en_US || '',
      };
      if (!lang[language]) {
        lang[language] = data[key] || '';
      }
      return lang;
    };
    this.lang = {
      ...(data?.lang || {}),
      name: format('name'),
      description: format('description'),
      introduction: format('introduction'),
      prompt: format('prompt'),
    };
  }

  // 赋值
  handlePatchApp(key: any, data: any): void {
    this.appForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang?.value,
      };
    }
  }
   // 机制表单赋值
   handlePatchLangApp(key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang?.value || data.lang,
      };
    }
  }

  // 取消
  handleCancel() {
    this.appForm.reset();
    this.callback.emit();
  }

  // 保存
  handleSaveApp(): void {
    for (const i of Object.keys(this.appForm?.controls)) {
      this.appForm.controls[i].markAsDirty();
      this.appForm.controls[i].updateValueAndValidity();
    }
    if (this.appForm.valid) {
      this.submitLoading = true;
      let param = {
        ...(this.appForm.getRawValue() || {}),
        lang: this.lang,
        commonApp: !!this.formData?.commonApp,
      };
      if (this.isAgQuestion) {
        param = omit(param, ['appSystem']);
      }
      (this.solutionCardService.isDynamicsApp(this.formData?.appType)
        ? this.solutionCardService.saveDynamicsApp(
            param,
            this.solutionCardService.getDynamicsAppUpdateUrl(this.formData?.appType),
          )
        : this.appService.saveApp(param)
      ).subscribe(
        (res) => {
          this.submitLoading = false;
          if (res.code === 0) {
            const selectedApp = {
              ...(this.formData || {}),
              ...param,
            };
            this.message.success(this.translateService.instant('dj-保存成功'));
            // 关闭
            this.appForm.reset();
            this.callback.emit(selectedApp);
          }
        },
        () => {
          this.submitLoading = false;
        },
      );
    }
  }

  // 打开解决方案后端开窗
  handleOpenModel() {
    this.getTargetList(true);
  }

  // 获取解决方案后端
  getTargetList(show) {
    this.appInfoService.postTargetList({ application: this.formData?.code }).subscribe((res) => {
      if (res.code === 0) {
        this.targetList = res?.data || [];
        if (show) this.serviceVisible = true;
      }
    });
  }

  // 关闭解决方案后端开窗
  handleCloseModel(e) {
    this.serviceVisible = false;
    this.getTargetList(false);
  }

  // 选择图标
  onAppIconChange(data) {
    const { image, color } = data;
    this.appForm.patchValue({
      iconName: image,
      iconBgcolor: color,
    });
    this.value = { image: image, color: color };
  }
  // 选择图片
  onAppImageChange(data) {
    if (typeof data === 'string') {
      this.appForm.patchValue({
        iconUrl: data,
      });
    }
  }
}
