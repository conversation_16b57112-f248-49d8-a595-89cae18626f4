.form-info {
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;

  ::ng-deep .ant-form-item-control-input-content {
    align-items: baseline;
    display: flex;
  }
  ::ng-deep .center-control .ant-form-item-control-input-content {
    align-items: center;
  }
  ::ng-deep .question-icon {
    padding-left: 4px;
    font-size: 14px;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    margin-top: 2px;
  }
  ::ng-deep .row-wrap {
    .ant-form-item {
      font-size: 13px;
      color: #333333;
      margin-bottom: 16px;
    }
    .ant-form-item-label {
      font-weight: Normal;
      height: 32px;
      line-height: 1;
      > label {
        font-size: 13px;
        color: #333333;
        height: 32px;
        line-height: 32px;
        width: 160px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.ant-form-item-required::before {
          display: contents;
        }
      }
    }
    .lang-input {
      width: 100%;
      .input-container,
      .ant-input-affix-wrapper,
      .ant-input {
        width: 100%;
      }
    }
  }

  .serviceCode-set {
    // padding-left: 8px;
    width: 100%;
  }
  .data-form-setting {
    height: 32px;
    line-height: 32px;
    color: #6a4cff;
    font-size: 13px;
    cursor: pointer;
  }

  ::ng-deep .serviceCode-control .ant-form-item-control-input-content {
    align-items: flex-start;
  }
  ::ng-deep .question-icon-serviceCode {
    padding-left: 4px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 10px;
  }
  .target-list-container {
    max-height: 150px;
    overflow-y: auto;
  }
  .target-list {
    height: 28px;
    background: #f7f8ff;
    border-radius: 2px;
    line-height: 28px;
    padding-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    margin-bottom: 8px;
  }

  .agile-source-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .agile-source-more {
      color: #b1b1b1;
      cursor: pointer;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 16px;
}
