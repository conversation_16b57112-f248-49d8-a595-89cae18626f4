<ad-modal
  [(nzVisible)]="visible"
  [nzTitle]="null"
  nzMaskClosable="false"
  [nzWidth]="800"
  (nzOnCancel)="handleCancel()"
  (nzOnOk)="handleModalOk()"
>
  <ng-container *adModalContent>
    <form nz-form [formGroup]="tableForm" class="add-business-key-modal">
      <!-- 表单项 -->
      <div nz-row class="form-item-up">
        <nz-form-item class="form-item" nz-col nzSpan="12">
          <div class="item-title">
            {{ 'dj-数据推送变量' | translate }}
            <span class="item-required">*</span>
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="'dj-默认为_in变量，支持用户自定义修改' | translate"
            >
            </i>
          </div>
          <nz-form-control [nzAutoTips]="errorTips">
            <input nz-input formControlName="entity_name" [maxLength]="60" placeholder="字段名称" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item class="form-item" nz-col nzSpan="12">
          <div class="item-title">
            {{ 'dj-查询标签' | translate }}
            <i
              adIcon
              iconfont="iconshuomingwenzi"
              aria-hidden="true"
              class="question-icon"
              nzTooltipTrigger="hover"
              nz-tooltip
              [nzTooltipTitle]="
                'dj-该配置为快捷查询标签，可设定一个实体名称用于快速查询，建议使用类似BKentity的命名，可自定义设置'
                  | translate
              "
            >
            </i>
          </div>
          <nz-form-control [nzAutoTips]="errorTips">
            <input nz-input formControlName="data_name" [maxLength]="60" placeholder="字段名称" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <!-- 新增按钮 -->
      <div class="add-field">
        <button ad-button adType="text" nzSize="small" (click)="addFieldItem($event)">
          <i adIcon iconfont="iconxinzeng2" class="plus-icon"></i>
          {{ 'dj-新增' | translate }}
        </button>
      </div>
      <!-- 表格 -->
      <nz-table
        nzBordered
        [nzData]="fieldRows"
        [nzShowPagination]="false"
        [nzScroll]="{ y: '400px' }"
        class="business-key-table"
        [nzFrontPagination]="false"
      >
        <ng-container formArrayName="field">
          <thead>
            <tr>
              <th nzWidth="120px">{{ 'dj-字段名称' | translate }}</th>
              <th nzWidth="120px">{{ 'dj-描述' | translate }}</th>
              <th nzWidth="120px">{{ 'dj-数据类型' | translate }}</th>
              <th nzWidth="60px">{{ 'dj-是否数组' | translate }}</th>
              <th nzWidth="60px">{{ 'dj-操作' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let group of field.controls; let i = index" [formGroupName]="i">
              <!-- 字段名称 -->
              <td>
                <nz-form-item>
                  <nz-form-control [nzAutoTips]="errorTips">
                    <nz-input-group style="width: unset" size="small">
                      <input nz-input formControlName="data_name" [maxLength]="60" placeholder="字段名称" />
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </td>

              <!-- 描述 -->
              <td>
                <nz-form-item>
                  <nz-form-control [nzAutoTips]="errorTips">
                    <app-modal-input
                      class="language-input"
                      [attr]="{
                        name: '字段描述',
                        required: true,
                        lang: group.get('lang').value?.description,
                        needLang: true
                      }"
                      [value]="group.get('lang').value?.description?.[('dj-LANG' | translate)]"
                      (callBack)="handlePatchLang('description', $event, i)"
                      [formControlName]="'description'"
                      ngDefaultControl
                    >
                    </app-modal-input>
                  </nz-form-control>
                </nz-form-item>
              </td>

              <!-- 数据类型 -->
              <td>
                <nz-form-item>
                  <nz-form-control [nzAutoTips]="errorTips">
                    <ad-select
                      style="width: 100%"
                      [nzAllowClear]="false"
                      formControlName="data_type"
                      [nzOptions]="dataTypeOptions"
                      [nzShowSearch]="true"
                      nzPlaceHolder="请选择类型"
                    >
                    </ad-select>
                  </nz-form-control>
                </nz-form-item>
              </td>

              <td style="text-align: center">
                <nz-form-item>
                  <nz-form-control>
                    <label nz-checkbox formControlName="is_array"></label>
                  </nz-form-control>
                </nz-form-item>
              </td>

              <td style="text-align: center" class="operation-btn">
                <i adIcon (click)="removeField(i)" adIcon iconfont="icondelete3" aria-hidden="true"> </i>
              </td>
            </tr>
          </tbody>
        </ng-container>
      </nz-table>

      <!-- <div *ngIf="!field.length" class="no-data">
        <img src="/assets/img/designer/no_data.png" alt="" />
        {{ 'dj-暂无数据' | translate }}
      </div> -->
    </form>
  </ng-container>
</ad-modal>
