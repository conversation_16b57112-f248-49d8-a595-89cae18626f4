import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
// import { validatorForm } from 'common/utils/core.utils';

export function validatorForm(form: FormGroup | FormArray): void {
  Object.keys(form.controls).forEach((key) => {
    const control = form.get(key);
    if (control instanceof FormGroup || control instanceof FormArray) {
      validatorForm(control); // 递归
    } else {
      control?.markAsTouched();
      control?.updateValueAndValidity();
    }
  });
}

@Component({
  selector: 'app-add-business-key-modal',
  templateUrl: './add-business-key-modal.component.html',
  styleUrls: ['./add-business-key-modal.component.less'],
})
export class AddBusinessKeyModalComponent implements OnInit {
  @Input() visible: boolean = false;
  @Input() inputData: any;
  @Output() closeModal: EventEmitter<any> = new EventEmitter();
  @Output() submit: EventEmitter<any> = new EventEmitter();
  tableForm: FormGroup;

  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      maxlength: this.translate.instant('dj-长度不能超过{{count}}', { count: 60 }),
    },
  };

  // 表单字段
  get field(): FormArray {
    return this.tableForm.get('field') as FormArray;
  }

  fieldRows: FormGroup[] = []; // 表格显示用的数据

  dataTypeOptions = [
    { label: 'string', value: 'string' },
    { label: 'number', value: 'number' },
    { label: 'boolean', value: 'boolean' },
    { label: 'data', value: 'data' },
    { label: 'datatime', value: 'datatime' },
    { label: 'object', value: 'object' },
  ];

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private athMessageService: NzMessageService,
  ) {}

  ngOnInit(): void {
    this.tableForm = this.fb.group({
      entity_name: ['_in', [Validators.required, Validators.maxLength(60)]],
      data_name: ['', [Validators.maxLength(60)]],
      field: this.fb.array([]),
    });
    this.handleInit();
  }

  handleInit() {
    this.tableForm.patchValue({
      entity_name: this.inputData?.entity_name || '_in',
      data_name: this.inputData?.data_name || '',
    });
    this.inputData?.field?.forEach((item: any) => {
      const group = this.fb.group({
        data_name: [item?.data_name || '', [Validators.required, Validators.maxLength(60)]],
        description: [item?.description || '', [Validators.required]],
        data_type: [item?.data_type || '', [Validators.required]],
        is_array: [item?.is_array || false],
        is_businesskey: [item?.is_businesskey ?? true],
        required: [item?.required ?? true],
        lang: [item?.lang || {}],
      });
      this.field.push(group);
    });
    this.updateFieldRows();
  }

  updateFieldRows(): void {
    this.fieldRows = [...this.field.controls] as FormGroup[]; // 触发变更检测
  }

  addFieldItem(e: Event): void {
    e.stopPropagation();
    e.preventDefault();
    const group = this.fb.group({
      data_name: ['', [Validators.required, Validators.maxLength(60)]],
      description: ['', [Validators.required]],
      data_type: ['', [Validators.required]],
      is_array: [false],
      lang: [{}],
      is_businesskey: [true],
      required: [true],
    });
    this.field.push(group);
    this.updateFieldRows();
  }

  removeField(index: number): void {
    this.field.removeAt(index);
    this.updateFieldRows();
  }

  /**
   * 多语言组件回调
   * @param key
   * @param data
   * @param index
   */
  handlePatchLang(key: any, data: any, index?: number): void {
    this.field.at(index).patchValue({
      lang: { description: data?.lang },
      description: data?.value,
    });
  }

  handleModalOk() {
    validatorForm(this.tableForm);
    const formData = this.tableForm.getRawValue();
    // if (this.field?.length === 0) {
    //   this.athMessageService.error(this.translate.instant('dj-请添加字段'));
    //   return;
    // }
    const backData = {
      ...formData,
      is_businesskey: false,
      required: true,
      is_array: true,
    };
    if (this.tableForm.valid) {
      this.submit.emit(backData);
    }
  }

  handleCancel() {
    this.closeModal.emit();
  }
}
