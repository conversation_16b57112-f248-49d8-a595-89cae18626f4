import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { JSONEditor, toJSONContent, Mode } from 'vanilla-jsoneditor';

@Component({
  selector: 'app-json-editor',
  templateUrl: './json-editor.component.html',
  styleUrls: ['./json-editor.component.less', './jsoneditor.css'],
  encapsulation: ViewEncapsulation.None,
})
export class JsonEditorComponent implements OnInit {
  loading: boolean; // 页面加载状态
  editor: any; // 编辑器
  @Input() className: string = 'JSON-Editor';
  @Input() jsonEditorStyle: string;
  @Input() attr?: any; // 组件传递, 可忽略
  @Input() data: any;
  @Input() jsonEditorProps?: any;
  @Output() callBack = new EventEmitter();
  // 为了不避免之前的引用组件，在这里添加一个只暴露错误事件
  @Output() errorEvent = new EventEmitter();
  constructor() {}

  ngOnInit(): void {
    this.handleInitJSONEditor();
  }

  // 初始化json编辑器
  handleInitJSONEditor(): void {
    setTimeout(() => {
      const dom = document.querySelector(`.${this.className}`);
      if (this.editor && this.editor.destroy) {
        this.editor.destroy();
      }
      this.editor = new JSONEditor({
        target: dom,
        props: {
          mode: this.attr?.mode ? Mode[this.attr?.mode] : Mode.tree,
          content: {
            text: undefined,
            json: this.data ?? {},
          },
          onChange: (updatedContent: any, previousContent, { contentErrors }) => {
            if ((updatedContent?.json || updatedContent?.text) && 'parseError' in contentErrors) {
              this.errorEvent.emit({ contentErrors });
            }
            const dataTem = !!(updatedContent?.json || updatedContent?.text) ? toJSONContent(updatedContent).json : '';
            // 原来逻辑
            const param = {
              data: dataTem,
              contentErrors,
            };
            this.callBack.emit(param);
          },
          ...this.jsonEditorProps,
        },
      });
    }, 10);
  }
}
