import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { RouterModule } from '@angular/router';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { InputModule } from '../../form-components/input/input.module';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';

import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { NzInputModule } from 'ng-zorro-antd/input';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';

import { ReleaseModalModule } from '../release-modal/release-modal.module';
import { DefaultLayoutComponent } from './default-layout/default-layout.component';
import { MenuBarComponent } from './menu-bar/menu-bar.component';
import { PageHeaderComponent } from './page-header/page-header.component';
import { DirectiveModule } from '../../../common/directive/directive.module';
import { GPTModule } from '../gpt/gpt.module';
import { LayoutService } from './layout.service';
import { AppInfoEditModule } from 'pages/app/app-info/app-info-edit/app-info-edit.module';
import { DeployService } from '../../../pages/deploy/deploy.service';
import { BranchManageComponent } from './menu-bar/branch-manage/branch-manage.component';
import { AddBranchModalComponent } from './menu-bar/branch-manage/add-branch-modal/add-branch-modal.component';
import { TextareaModule } from '../../../components/form-components/textarea/textarea.module';
import { AuthManageModule } from './menu-bar/auth-manage/auth-manage.module';
import { MqttNotificationModule } from '../mqtt-notification/mqtt-notification.module';
import { NotificationModule } from '../notification/notification.module';
import { IntroStepsModule } from '../intro-steps/intro-steps.module';
import { ModelDrivenIntroService } from 'common/service/intro/model-driven-intro/model-driven-intro.service';
import { CollaborateTipModule } from '../collaborate-tip/collaborate-tip.module';
import { PipeModule } from 'common/pipe/pipe.module';
import { IndividualService } from 'pages/individual/individual.service';
import { IndividualGuard } from 'pages/individual/individual.guard';
import { NonIndividualGuard } from 'pages/individual/nonIndividual.guard';
import { NzNotificationModule } from 'ng-zorro-antd/notification';
import { BusinessDomainCenterService } from 'pages/business-domain-center/business-domain-center.service';
import { SolutionCardService } from 'app/service/solution-card.service';
import { IsPrivatizationService } from 'common/service/is-privatization.service';
// import { BusinessShareConsumerPreloadModule } from 'components/bussiness-components/business-share-consumer-preload/business-share-consumer-preload.module';
import { AiAgentManageModule } from 'components/ai-agent-manage/ai-agent-manage.module';
@NgModule({
  imports: [
    CommonModule,
    NzSpinModule,
    NzLayoutModule,
    RouterModule,
    NzDropDownModule,
    NzDividerModule,
    InputModule,
    TranslateModule,
    ReleaseModalModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    AdModalModule,
    AdIconModule,
    NzToolTipModule,
    NzInputModule,
    AdButtonModule,
    DirectiveModule,
    CollaborateTipModule,
    AppInfoEditModule,
    TextareaModule,
    AuthManageModule,
    MqttNotificationModule,
    NotificationModule,
    IntroStepsModule,
    PipeModule,
    NzNotificationModule,
    // BusinessShareConsumerPreloadModule,
    AiAgentManageModule,
  ],
  declarations: [
    DefaultLayoutComponent,
    MenuBarComponent,
    PageHeaderComponent,
    BranchManageComponent,
    AddBranchModalComponent,
  ],
  providers: [
    LayoutService,
    DeployService,
    ModelDrivenIntroService,
    IndividualService,
    IndividualGuard,
    NonIndividualGuard,
    BusinessDomainCenterService,
    SolutionCardService,
    IsPrivatizationService,
  ],
  exports: [DefaultLayoutComponent, MenuBarComponent, PageHeaderComponent],
})
export class LayoutModule {}
