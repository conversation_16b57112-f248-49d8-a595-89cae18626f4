import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';
import { Observable } from 'rxjs';

@Injectable()
export class ApiService {
  adesignerUrl: string;
  apiMgmtUrl: string;

  constructor(private http: HttpClient, private configService: SystemConfigService, private appService: AppService) {
    this.configService.getConfig().subscribe((config) => {
      this.adesignerUrl = config.adesignerUrl;
      this.apiMgmtUrl = config.apiMgmtUrl;
    });
  }

  /**
   * 批量查询模型的详情
   * @param params
   * @returns
   */
  queryModelByCodes(params: { modelCode: string; serviceCode: string }[]): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/modelDriver/queryModelByCodes`;
    return this.http.post(url, params);
  }
}
