import { ChangeDetectorRef, Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { OnChange } from 'vanilla-jsoneditor';

@Component({
  selector: 'app-table-node',
  templateUrl: './table-node.component.html',
  styleUrls: ['./table-node.component.less'],
})
export class TableNodeComponent implements OnInit {
  @Input() model: any;
  @Input() valid: any;
  @Input() handleProperty: (arg: any) => void;
  @Input() handleField: (arg: any) => void;

  public expand: boolean = false;

  get fields() {
    if (this.expand) {
      return this.model.fields || [];
    }
    return this.model.fields.slice(0, 5);
  }

  constructor(private ref: ChangeDetectorRef) {}

  ngOnInit() {}

  public toggle(e: MouseEvent) {
    e.stopPropagation();
    this.expand = !this.expand;
    this.ref.detectChanges();
  }

  public handleSetting() {
    if (this.model._rel) return;
    this.handleProperty?.(this.model);
  }

  public handleFieldSetting(field: any) {
    if (this.model._rel) return;
    this.handleField?.({
      model: this.model,
      field,
    });
  }
}
