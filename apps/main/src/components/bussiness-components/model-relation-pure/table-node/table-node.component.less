.table-node {
  position: relative;
  border-radius: 8px;
  border: 1px solid #e6e6eb;
  overflow: hidden;
  &.main {
    background: linear-gradient(180deg, #fffbf7 0%, #ffffff 100%);
  }
  &.rel {
    background: linear-gradient(180deg, #f4fff8 0%, #ffffff 100%);
  }
  &.sub {
    background: linear-gradient(180deg, #efeffe 0%, #ffffff 100%);
  }
  .table-header {
    padding: 16px 16px 8px 16px;
    display: flex;
    align-items: center;
    width: 256px;

    .table-type {
      width: 40px;
      line-height: 40px;
      border-radius: 4px;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
    }
    .table-desc {
      margin-left: 8px;
      display: flex;
      flex-direction: column;
      max-width: 180px;
      .line-1 {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .table-title {
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: #3d3d3d;
        max-width: 200px;
        .line-1;
      }
      .table-sub {
        font-size: 12px;
        line-height: 18px;
        color: #868a9c;
        .line-1;
      }
    }
  }
  .table-body {
    .field {
      .disable {
        cursor: not-allowed !important;
      }
      cursor: pointer;
      padding: 0 16px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: all ease 0.2s;
      &:hover {
        background-color: #ffffff;
      }

      > span {
        font-size: 12px;
        color: #8c8b99;
        max-width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:nth-child(2n + 2) {
          color: #1d1c33;
          font-size: 12px;
        }
      }
      &.field-error {
        background-color: #fbe6e5;
      }
    }
  }
  .expand {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    .expand-icon {
      transition: all ease 0.2s;
      font-size: 12px;
      color: #333;
      transform: rotate(90deg);
      &.open {
        transform: rotate(270deg);
      }
    }
  }
  .setting {
    position: absolute;
    z-index: 10;
    right: 16px;
    top: 16px;
    font-size: 14px;
    color: #1d1c33;
    .disable {
      ::ng-deep svg {
        background-color: red;
        cursor: not-allowed !important;
      }
    }
  }

  .disable {
    cursor: not-allowed !important;
  }
}
