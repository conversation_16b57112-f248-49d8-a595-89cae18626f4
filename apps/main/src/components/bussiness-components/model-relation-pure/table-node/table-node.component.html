<div class="table-node" [class]="model._rel ? 'rel' : model._pid ? 'sub' : 'main'">
  <div class="table-header">
    <div
      class="table-type"
      [ngStyle]="{
        'background-color': model._rel ? '#00B042' : model._pid ? '#605CE5' : '#FF7F00'
      }"
    >
      {{ (model._rel ? 'dj-关联' : model._pid ? 'dj-子' : 'dj-主') | translate }}
    </div>
    <div class="table-desc">
      <div class="table-title">{{ model.name }}</div>
      <div class="table-sub">{{model.lang?.comment?.[('dj-LANG' | translate)] || model.comment}}</div>
    </div>
  </div>
  <div class="table-body">
    <div
      class="field"
      [ngClass]="{
        'field-error': valid[field._uuid] === false,
        disable: !!model._rel
      }"
      *ngFor="let field of fields"
      (click)="handleFieldSetting(field)"
    >
      <span>{{ field.fieldId }}</span>
      <span>{{ field.fieldType }}</span>
    </div>
  </div>
  <div class="expand" v-if="(model.fields?.length || 0) > 5">
    <i
      adIcon
      iconfont="iconicon_shuangjiantou-you"
      class="expand-icon"
      (click)="toggle($event)"
      [ngClass]="{
        open: expand
      }"
    ></i>
  </div>
  <i adIcon iconfont="iconsetting-o-svgrepo-com" class="setting" *ngIf="!model._rel" (click)="handleSetting()"></i>
</div>
