.canvas-container {
  height: 100%;
  width: 100%;
  position: relative;
  background-color: #f2f2f5;

  .tools {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    .group1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 8px;
      height: 36px;
      border-radius: 4px;
      background-color: #fff;
      .btn {
        flex-direction: row;
        align-items: center;
        margin-right: 8px;
        width: 20px;
        height: 20px;
        justify-content: center;
        display: flex;
        &:hover {
          background-color: #f7f7fa;
        }
        & > i {
          color: #605ce5;
        }
        &.disabled {
          cursor: not-allowed !important;
          & > i {
            color: #ddd !important;
            ::ng-deep {
              svg {
                color: #ddd !important;
                cursor: not-allowed !important;
              }
            }
          }

          &:hover {
            background-color: transparent;
          }
        }
      }
      .split {
        width: 1px;
        height: 14px;
        margin-right: 8px;
        background-color: #5050e3;
      }
      .scale {
        display: flex;
        align-items: center;
        flex-direction: row;
        background-color: #f7f7fa;
        padding: 3px 5px;
        border-radius: 4px;
        margin-right: 8px;
      }
      .scale-100 {
        margin-right: 8px !important;
      }
      .min {
        margin-right: 8px;
        font-size: 14px;
        color: #605ce5;
      }
      .scale-span {
        font-size: 12px;
        margin-right: 8px;
        color: #605ce5;
      }
      .max {
        font-size: 14px;
        color: #605ce5;
      }
      .button {
        background-color: #f7f7fa;
        font-size: 14px;
        width: 23px;
        height: 23px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        margin-right: 0;
        & > i {
          font-size: 12px;
          color: #605ce5;
        }
      }
    }
  }

  .handle-full-screen {
    position: absolute;
    display: inline-block;
    top: 18px;
    right: 14px;
    width: 28px;
    height: 28px;
    background: #f2f2f5;
    border-radius: 14px;
    font-size: 14px;
    color: #6868ae;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }

  & > .canvas-panel {
    height: 100%;
    width: 100%;
  }

  &.full-screen {
    position: fixed;
    background-color: #f2f2f5;
    width: 100%;
    height: 100%;
    left: 0%;
    top: 0%;
    z-index: 9999;
  }
}
::ng-deep {
  svg {
    #iconweibiaoti545,
    #iconweibiaoti546 {
      cursor: inherit !important;
      path {
        fill: inherit !important;
      }
    }
  }
}
