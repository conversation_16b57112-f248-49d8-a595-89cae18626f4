import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableNodeComponent } from './table-node/table-node.component';
import { ModelRelationPureComponent } from './model-relation-pure.component';
import { TranslateModule } from '@ngx-translate/core';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';

@NgModule({
  imports: [CommonModule, TranslateModule, AdIconModule],
  declarations: [ModelRelationPureComponent, TableNodeComponent],
  exports: [ModelRelationPureComponent],
})
export class ModelRelationPureModule {}
