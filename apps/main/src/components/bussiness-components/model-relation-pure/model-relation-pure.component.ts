import {
  AfterViewInit,
  Component,
  ComponentFactoryResolver,
  ElementRef,
  Injector,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { History } from '@antv/x6-plugin-history';
import { Cell, Graph, Point, Shape } from '@antv/x6';
import { UUID } from 'angular2-uuid';
import { FieldTypes } from './types';
import { TranslateService } from '@ngx-translate/core';
import { register, unRegister } from 'common/utils/x6-angular-shape';
import { TableNodeComponent } from './table-node/table-node.component';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { isEqual } from 'lodash';
import { ApiService } from './api.service';

export interface PositionGenerateObject {
  baseX: number; // 基础坐标x
  baseY: number; // 基础坐标y
  max: number; // 同级对象数量
  index: number; // 当前对象序号
  direction: 'left' | 'right'; // 放置在 基础左边 的 位置
}

export interface ChangeModelCodeObject {
  sourceModelCode: string; // 原始code
  targetModelCode: string; // 修改code
}

@Component({
  selector: 'app-model-relation-pure',
  templateUrl: './model-relation-pure.component.html',
  styleUrls: ['./model-relation-pure.component.less'],
  providers: [ApiService],
})
export class ModelRelationPureComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() modelList: any[] = [];

  @Input() serviceCode: string;

  @ViewChild('componentSetting') componentSetting: ElementRef;
  public graph: Graph = null;
  isFullScreen: boolean = false; // 是否是全屏模式

  private readonly destroy$ = new Subject();

  public loading: boolean = false;

  get graphZoom(): string {
    if (!this.graph) return '';
    return Math.round((this.graph?.zoom() || 0) * 100) + '%';
  }

  private relationModels: any[] = [];

  private modelRelateShipMap: Map<string, any[]> = new Map();

  constructor(private apiService: ApiService, private injector: Injector, private cfr: ComponentFactoryResolver) {}

  ngOnInit() {}

  ngAfterViewInit() {
    try {
      this.loading = true;
      this.initGraph();
      this.initGraphData();
    } finally {
      this.loading = false;
    }
  }

  ngOnDestroy(): void {
    this.graph.clearCells();
    unRegister({
      shape: 'model-relation',
      content: TableNodeComponent,
      injector: this.injector,
      cfr: this.cfr,
    });
    // Graph.unregisterRouter('straightLine');
    this.graph.dispose(true);
    this.graph = undefined;
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleFullScreen() {
    this.isFullScreen = !this.isFullScreen;
  }

  resize100() {
    this.graph.zoomTo(1);
  }

  public scale(type: 'in' | 'out') {
    const currentZoom = this.graph.zoom();
    if (type === 'in') {
      const next = Math.max(0.1, currentZoom - 0.2);
      this.graph.zoomTo(next);
    } else {
      const next = Math.min(2, currentZoom + 0.2);
      this.graph.zoomTo(next);
    }
  }

  public handleDo(type: 'undo' | 'redo') {
    if (type === 'undo') {
      this.graph.undo();
    } else {
      this.graph.redo();
    }
    this.syncStore();
  }

  //#region 初始化画布和创建数据

  private syncStore() {
    const nodes = this.graph.getNodes() || [];
    if (!nodes.length) return;
    const map = nodes.reduce((pre, curr) => {
      const model = curr.data.ngArguments?.model;
      pre[model.name] = model;
      return pre;
    }, {});
    this.modelList.forEach((model, i) => {
      if (!isEqual(model, map[model.name])) {
        this.modelList[i] = map[model.name];
      }
    });
  }

  private loadData(data) {
    const cells: Cell[] = [];
    data.forEach((item: any) => {
      if (item.shape === 'edge') {
        cells.push(this.graph.createEdge(item));
      } else {
        cells.push(this.graph.createNode(item));
      }
    });
    this.graph.resetCells(cells);
    this.graph.zoomToFit({ padding: 10, maxScale: 1 });
    this.graph.center();
    this.setPortVisible();
  }

  private setPortVisible() {
    const nodes = this.graph.getNodes();
    nodes.forEach((node) => {
      const hasIncoming = this.graph.getIncomingEdges(node);
      if (!hasIncoming) {
        node.setPortProp(`${node.id}port_l`, 'attrs/circle/display', 'none');
      }
      const hasOut = this.graph.getOutgoingEdges(node);
      if (!hasOut) {
        node.setPortProp(`${node.id}port_r`, 'attrs/circle/display', 'none');
      }
    });
  }

  private registerNodeAndEdge() {
    register({
      shape: 'model-relation',
      content: TableNodeComponent,
      injector: this.injector,
      cfr: this.cfr,
    });
    // Graph.registerRouter(
    //   'straightLine',
    //   (vertices, args, view) => {
    //     const BOUNCES = args.bounces || 1;
    //     const points = vertices.map((p) => Point.create(p));
    //     const sourceCorner = view.sourceBBox.getCenter();
    //     const targetCorner = view.targetBBox.getCenter();

    //     const stepx = (targetCorner.x - sourceCorner.x) / BOUNCES;
    //     const stepy = (targetCorner.y - sourceCorner.y) / BOUNCES;
    //     points.push(Point.create(sourceCorner.x, sourceCorner.y + stepy - 20));
    //     points.push(Point.create(sourceCorner.x + stepx, sourceCorner.y + stepy - 20));

    //     return points;
    //   },
    //   true,
    // );
  }

  private initGraph() {
    this.registerNodeAndEdge();
    this.graph = new Graph({
      container: this.componentSetting.nativeElement,
      grid: true,
      interacting: {
        magnetConnectable: false, // 禁止边连接
        arrowheadMovable: false, // 禁止箭头移动
        vertexMovable: false, // 禁止顶点移动
      },
      panning: {
        enabled: true,
        eventTypes: ['mouseWheel', 'leftMouseDown'],
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      autoResize: true,
    });
    this.graph.use(new History());
  }

  private async initGraphData() {
    this.graph.disableHistory();
    const cells = await this.getData();
    this.loadData(cells);
    this.graph.centerContent();
    this.graph.zoomTo(1);
    this.graph.enableHistory();
  }

  /**
   * 获取子模型的关系
   * @param modelList
   * @param modelMap
   * @returns
   */
  private getChildShips(modelList: any[], modelMap: any) {
    const childships = [];
    modelList.forEach((model) => {
      if (model.children?.length) {
        model.children.forEach((subModel) => {
          const field = model.fields.find((f) => f.fieldId === subModel.name && f.type === FieldTypes.COLLECTION);
          childships.push({
            relationType: 'sub',
            sourceFieldId: field.fieldId,
            sourceModelId: model.name,
            targetFieldId: modelMap[field.fieldId].fields?.find((f) => f.isPk)?.fieldId,
            targetModelId: field.fieldId,
          });
        });
      }
    });
    return childships;
  }

  /**
   * 获取关联模型的关系
   * @param modelList
   * @param modelMap
   * @returns
   */
  private getRelationShips(modelList: any[], modelMap: any) {
    const relationships = [];
    modelList.forEach((model) => {
      model.fields.forEach((field) => {
        if (field.associatedInfo?.tableName && field.associatedInfo?.joinField) {
          const item = {
            relationType: 'relate',
            sourceFieldId: field.fieldId,
            sourceModelId: model.name,
            targetFieldId: field.associatedInfo.joinField.fieldId,
            targetModelId: field.associatedInfo?.tableName,
          };
          if (
            !relationships.some((e) => e.sourceModelId === item.sourceModelId && e.targetModelId === item.targetModelId)
          ) {
            relationships.push(item);
          }
        }
      });
    });
    return relationships;
  }

  private flatTree(data: any[], result: any[] = []) {
    data.forEach((item) => {
      result.push(item);
      if (item.children?.length) {
        this.flatTree(item.children, result);
      }
    });
    return result;
  }

  /**
   * 查询关联的模型的信息
   * @param modelCodes
   */
  private async queryRelationModel(modelCodes: string[]) {
    const result = await this.apiService
      .queryModelByCodes(
        modelCodes.map((e) => ({
          modelCode: e,
          serviceCode: this.serviceCode,
        })),
      )
      .toPromise();
    if (result.code === 0) {
      const list = this.flatTree((result.data || []).map((e) => e.model));
      const search = modelCodes.map((item) => list.find((e) => e.name === item));
      this.relationModels = this.treeToArray(
        search.map((e) => ({
          ...e,
          children: [],
        })),
      ).map((e) => ({
        ...e,
        _rel: true,
      }));
    }
  }

  /**
   * 树转扁平数组
   * @param tree
   * @returns
   */
  private treeToArray(tree) {
    return tree.reduce((res, item) => {
      const { children, ...i } = item;

      i.children = [];
      children.forEach((c) => i.children.push({ name: c.name }));
      return res.concat(i, children && children.length ? this.treeToArray(children) : []);
    }, []);
  }

  /**
   * 设置模型与关联表的关系
   * @param relationships
   */
  private updateModelAndRelation(relationships: any[]) {
    relationships.forEach((ship) => {
      const sourceModelName = ship.sourceModelId;
      if (this.modelRelateShipMap.has(sourceModelName)) {
        this.modelRelateShipMap.get(sourceModelName).push(ship);
      } else {
        this.modelRelateShipMap.set(sourceModelName, [ship]);
      }
    });
  }

  private async getData() {
    const modelList = this.modelList || [];

    const mainTableList = modelList.filter((model) => !model._pid);

    const mainTableItemList = mainTableList.map((mainTable, index) => {
      return {
        mainTable,
        mainCell: this.getCell(mainTable, {
          baseX: -200,
          baseY: 150,
          max: mainTableList.length,
          index,
          direction: 'right',
        }),
      };
    });

    if (mainTableItemList.length === 0) return [];
    const modelMap = modelList.reduce((pre, curr) => {
      pre[curr.name] = curr;
      return pre;
    }, {});

    const childships = this.getChildShips(modelList, modelMap);
    const relationships = this.getRelationShips(modelList, modelMap);

    if (relationships.length) {
      await this.queryRelationModel(relationships.map((e) => e.targetModelId));
      this.updateModelAndRelation(relationships);
    }

    let childCells = mainTableItemList.reduce((pre, mainTableItem) => {
      let mainRelation = [];
      const { mainTable, mainCell } = mainTableItem;
      if (this.modelRelateShipMap.has(mainTable.name)) {
        mainRelation = this.modelRelateShipMap.get(mainTable.name).map((e) => ({ name: e.targetModelId }));
      }
      // 子表
      const tables = mainTable?.children || [];
      let childCells = this.getChildCells(this.getChildren([...tables, ...mainRelation]), mainCell, 'right', []); // 渲染子表节点
      return [...pre, ...childCells];
    }, []);

    return [
      ...mainTableItemList.map((mainTableItem) => mainTableItem.mainCell),
      ...childCells,
      ...this.getEdges([...childships, ...relationships]),
    ];
  }

  //#endregion

  //#region 生成表
  private getCell(model: any, positionGenerateObject?: PositionGenerateObject) {
    const returnCell = {
      id: model.name,
      shape: 'model-relation',
      width: 272,
      height: 56,
      data: {
        ngArguments: {
          model,
          valid: true,
        },
      },
      position: positionGenerateObject
        ? this.getCellPosition(positionGenerateObject)
        : {
            x: 24,
            y: 150,
          },
      ports: {
        groups: {
          group1: {
            position: 'right',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#ffffff',
                fill: '#605CE5',
              },
            },
          },
          group2: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#ffffff',
                fill: '#605CE5',
              },
            },
          },
        },
        items: [
          { id: model.name + 'port_r', group: 'group1', args: { dx: 0, dy: -10 } },
          { id: model.name + 'port_l', group: 'group2', args: { dx: 0, dy: -10 } },
        ],
      },
    };
    return returnCell;
  }

  private getCellPosition(positionGenerateObject: PositionGenerateObject): { x: number; y: number } {
    const stepX = 470;
    const stepY = 300;
    const offsetX = (positionGenerateObject.direction === 'left' ? -1 : 1) * stepX;
    const offsetY = (positionGenerateObject.max / 2 + 0.5 - (positionGenerateObject.index + 1)) * stepY;

    return {
      x: positionGenerateObject.baseX + offsetX,
      y: positionGenerateObject.baseY + offsetY,
    };
  }

  private getChildren(children: { name: string }[]) {
    const modelList = this.modelList || [];
    const modelMap = [...modelList, ...this.relationModels].reduce((pre, curr) => {
      pre[curr.name] = curr;
      return pre;
    }, {});
    return children?.map((child) => modelMap[child.name]) || [];
  }

  private getChildCells(
    modelList: any[],
    parentCell: any,
    direction: 'left' | 'right',
    changeModelCodeList: ChangeModelCodeObject[] = [],
  ) {
    let cells = [];
    modelList.forEach((model: any, index: number) => {
      let positionGenerateObject = {
        baseX: parentCell.position.x,
        baseY: parentCell.position.y,
        max: modelList.length,
        index: index,
        direction,
      };
      const cell = this.getCell(model, positionGenerateObject);
      cells.push(cell);
      const list = [...(model.children || [])];
      if (this.modelRelateShipMap.has(model.name)) {
        list.push(...this.modelRelateShipMap.get(model.name).map((e) => ({ name: e.targetModelId })));
      }
      if (list.length > 0) {
        const next = this.getChildCells(this.getChildren(list), cell, direction, changeModelCodeList);
        cells = [...cells, ...next];
      }
    });

    return cells;
  }

  private getEdges(relationships: any[] = []) {
    const paramsList: [string, string][] = relationships.map((relation) => {
      return [relation.sourceModelId, relation.targetModelId];
    });

    const tempSet = new Set();
    const uniqueArray = paramsList.filter((item) => {
      const serialized = JSON.stringify(item);
      if (tempSet.has(serialized)) {
        return false;
      }
      tempSet.add(serialized);
      return true;
    });

    return uniqueArray.map((item: [string, string]) => {
      return this.getEdge(...item);
    });
  }

  private getEdge(sourceCell: string, targetCell: string) {
    const returnEdge = {
      id: UUID.UUID(),
      shape: 'edge',
      source: {
        cell: sourceCell,
        port: sourceCell + 'port_r',
      },
      target: {
        cell: targetCell,
        port: targetCell + 'port_l',
      },
      attrs: {
        line: {
          stroke: '#A2B1C3',
          strokeWidth: 1,
        },
      },
      connector: { name: 'smooth' },
      zIndex: 1,
    };

    return returnEdge;
  }

  //#endregion
}
