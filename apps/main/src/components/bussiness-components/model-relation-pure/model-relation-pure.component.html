<div class="canvas-container" [ngClass]="{ 'full-screen': isFullScreen }">
  <div class="tools">
    <div class="group1">
      <span
        class="btn"
        [ngClass]="{
          disabled: !graph?.canUndo()
        }"
        (click)="handleDo('undo')"
      >
        <i adIcon iconfont="iconweibiaoti545" class="iconfont"></i>
      </span>
      <span
        class="btn"
        [ngClass]="{
          disabled: !graph?.canRedo()
        }"
        (click)="handleDo('redo')"
      >
        <i adIcon iconfont="iconweibiaoti546"></i>
      </span>
      <div class="split"></div>
      <span class="scale">
        <i nz-icon nzType="minus" nzTheme="outline" class="min" (click)="scale('in')"></i>
        <span class="scale-span">{{ graphZoom }}</span>
        <i nz-icon nzType="plus" nzTheme="outline" class="max" (click)="scale('out')"></i>
      </span>
      <span class="btn button scale-100">
        <i adIcon iconfont="icona-100-xian" style="transform: scale(1.5)" (click)="resize100()"></i>
      </span>
      <span class="btn button">
        <i adIcon [iconfont]="isFullScreen ? 'iconsuoxiao1' : 'iconzuidahua'" (click)="toggleFullScreen()"></i>
      </span>
    </div>
  </div>
  <div #componentSetting class="canvas-panel"></div>
</div>
