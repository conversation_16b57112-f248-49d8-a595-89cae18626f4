import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { isEmpty } from '../../../common/utils/core.utils';
import { ExtendedInfoService } from './extended-info.service';
import { trim } from 'lodash';
import { AuthService } from 'common/service/auth.service';
import { AuthOperate } from 'common/types/auth.types';

@Component({
  selector: 'app-extended-info',
  templateUrl: './extended-info.component.html',
  styleUrls: ['./extended-info.component.less'],
})
export class ExtendedInfoComponent implements OnInit {
  title: string;
  isVisible: boolean;
  datas: any[] = [];
  @Input() sceneCode: string;
  @Input() code: string;
  @Input() secondKey?: string;
  @Input() appCode: string;
  @Input() notShowIcon: boolean = false;
  @Input() isTenantProcessId: boolean = false; // 是否是租户级流程
  @Input() editable: boolean = true;
  @Output() isVisibleChange = new EventEmitter();
  realDataPath: any[] = [];
  selectedIndex: number = 0;
  listOfTabs: any[] = [];
  activedItem: any;
  example: string;
  isEmpty = isEmpty;
  @Input() showName: boolean = false;
  @Input() textStyle: string = '';
  @Input() extendHeader: any = null;
  @Input() isFromDtdReference: boolean = false; // 是否是引用dtd进入该组件

  constructor(
    private translate: TranslateService,
    private service: ExtendedInfoService,
    private message: NzMessageService,
    private modal: AdModalService,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    this.secondKey = this.secondKey ?? null;
    this.title = this.translate.instant('dj-扩展信息') + this.translate.instant('dj-配置');
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.extendHeader?.currentValue) {
      this.service.extendHeader = this.extendHeader;
    }
  }

  loadDatas(): void {
    const param = {
      primaryKey: this.code,
      sceneCode: this.sceneCode,
      secondKey: this.secondKey,
    };
    this.service.search(param).subscribe(
      (res) => {
        this.datas = res.data.extendFieldList ?? [];
        // this.realDataPath = res.data.realDataPath ?? [];
        this.example = res.data.example;
        this.listOfTabs = (this.datas ?? []).map((d) => {
          const item = {
            ...d,
            // _visibleFields: d.fileNameValue.filter(
            //   (f) => f.isVisible === true && f.type === 'LIST'
            // ),
            _isChange: false,
          };
          return item;
        });
        this.activedItem = this.listOfTabs[this.selectedIndex];
      },
      () => {},
    );
  }
  handleOpenExtendedInfoModal($event: any): void {
    $event.stopPropagation();
    this.isVisible = true;
    this.isVisibleChange.emit(this.isVisible);
    this.datas = [];
    setTimeout(() => {
      this.loadDatas();
    });
  }
  handleChangeSelectedIndex(item: any, ind: number): void {
    this.selectedIndex = ind;
    this.activedItem = item;
  }
  handleAddExtraInfo(): void {
    if (this.listOfTabs.length > 0 && !!this.listOfTabs[this.selectedIndex]._isChange) {
      this.message.info(this.translate.instant('dj-请保存完当前行数据后再点击新增'));
      return;
    }
    this.listOfTabs.push({
      name: this.translate.instant('dj-扩展信息'),
      // sceneCode:
      //   this.realDataPath.length > 0 ? this.realDataPath[0].value : null,
      // sourceFieldType: 'OTHER',
      jsonPath: '',
      // fieldCondition: '',
      value: '',
      valueType: 'String',
      example: this.example,
      // fileNameValue: [],
      // _visibleFields: [],
      _isChange: true,
    });
    this.selectedIndex = this.listOfTabs.length - 1;
    this.activedItem = this.listOfTabs[this.selectedIndex];
    // if (this.activedItem.sceneCode) {
    //   this.handleSceneCodeChange(this.activedItem.sceneCode);
    // }
  }
  handleCancel(): void {
    if (this.validateIsChange()) {
      this.modal.confirm({
        nzTitle: this.translate.instant('dj-存在未保存'),
        nzOkText: this.translate.instant('dj-忽略'),
        nzOnOk: () => {
          this.isVisible = false;
          this.isVisibleChange.emit(this.isVisible);
        },
        nzCancelText: this.authService.getAuth(AuthOperate.UPDATE) ? this.translate.instant('dj-去保存') : null,
        nzOnCancel: () => {
          this.isVisible = true;
          this.isVisibleChange.emit(this.isVisible);
        },
      });
    } else {
      this.isVisible = false;
      this.isVisibleChange.emit(this.isVisible);
    }
  }

  validateIsChange(): boolean {
    return this.listOfTabs?.some((tab) => tab._isChange === true);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleValueTypeChange($event: any): void {
    this.validate();
  }

  inputChange(event, key) {
    setTimeout(() => {
      this.activedItem[key] = trim(event);
    });
  }

  validate(): boolean {
    const data = this.activedItem;
    let typeValid = true;
    const value = data.value;
    // if (data.valueType === 'String') {
    //   if (!isEmpty(value)) {
    //     if (value.startsWith('\\"') || value.startsWith("\\'")) {
    //       value = '"' + value.substring(2);
    //     } else if (value.startsWith('"') || value.startsWith("'")) {
    //       value = '"' + value.substring(1);
    //     } else {
    //       value = '"' + value;
    //     }

    //     if (value.endsWith('\\"') || value.endsWith("\\'")) {
    //       value = value.substring(0, value.length - 2) + '"';
    //     } else if (value.endsWith('"') || value.endsWith("'")) {
    //       value = value.substring(0, value.length - 1) + '"';
    //     } else {
    //       value = value + '"';
    //     }
    //   }
    // }
    try {
      // const tmp = new Function('return ' + value)();
      if (data.valueType === 'Number') {
        // const testReg1 = new RegExp(`^[-]?[0-9]+\.?[0-9]+$`, 'g');
        const testReg2 = new RegExp(`^\-{0,1}[0-9]{1,}$`, 'g');
        typeValid = testReg2.test(value);
      } else if (data.valueType === 'Boolean') {
        typeValid = typeof value === 'boolean' || value === 'true' || value === 'false';
      } else if (data.valueType === 'Map') {
        typeValid =
          typeof value === 'object' ||
          (value.startsWith('[') && value.endsWith(']')) ||
          (value.startsWith('{') && value.endsWith('}'));
      } else if (data.valueType === 'String') {
        typeValid = value ? true : false;
      }
      this.activedItem._errorMsg = !typeValid ? this.translate.instant('dj-不符合数据类型') : '';
      return (
        typeValid &&
        !isEmpty(data.name) &&
        // !isEmpty(data.sceneCode) &&
        !isEmpty(data.valueType) &&
        // (data.sourceFieldType !== 'LIST' || !isEmpty(data.fieldCondition)) &&
        !isEmpty(data.jsonPath) &&
        !isEmpty(data.value)
      );
    } catch (error) {
      this.activedItem._errorMsg = this.translate.instant('dj-不符合数据类型');
      return false;
    }
  }
  handleSaveExtraInfo(): void {
    if (this.listOfTabs.length === 0) {
      this.message.info(this.translate.instant('dj-至少添加一条数据'));
      return;
    }
    this.activedItem._formDirty = true;
    if (this.activedItem.valueType === 'Object') {
      const param = {
        value: this.activedItem.value,
      };
      this.service.translateCustom(param).subscribe(
        (res) => {
          if (res.code === 0) {
            const { value } = res.data;
            this.handleSave({ ...this.activedItem, value });
          }
        },
        () => {},
      );
    } else {
      this.handleSave(this.activedItem);
    }
  }
  handleSave(activedItem) {
    if (this.validate()) {
      this.service
        .save({
          ...activedItem,
          sceneCode: this.sceneCode,
          primaryKey: this.code,
          secondKey: this.secondKey,
          application: this.appCode,
        })
        .subscribe(
          (res) => {
            activedItem._formDirty = false;
            activedItem._isChange = false;
            activedItem.code = activedItem.code ?? res.data.code;
            this.listOfTabs[this.selectedIndex]._isChange = false;
            this.message.success(this.translate.instant('dj-保存成功'));
          },
          () => {
            this.message.error(this.translate.instant('dj-保存失败'));
          },
        );
    }
  }
  handleDeleteExtendedInfo($event: string): void {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzOnOk: () => {
        if (!$event) {
          this.listOfTabs = this.listOfTabs.filter((tab) => tab.code !== $event);
          this.activedItem = null;
        } else {
          this.service.delete($event).subscribe(
            () => {
              this.listOfTabs = this.listOfTabs.filter((tab) => tab.code !== $event);
              this.message.success(this.translate.instant('dj-删除成功'));
              this.activedItem = null;
            },
            () => {
              this.message.error(this.translate.instant('dj-删除失败'));
            },
          );
        }
      },
      nzOnCancel: () => {},
    });
  }
  // handleVisibleFieldValueChange($event: any, field: any): void {
  //   this.listOfTabs[this.selectedIndex].fileNameValue.forEach((f) => {
  //     if (f.from === field.from) {
  //       f.value = field.value;
  //     }
  //   });
  //   this.listOfTabs[this.selectedIndex]._isChange = true;
  // }
  // handleSceneCodeChange($event: any): void {
  //   const param = {
  //     code: this.code,
  //     sceneCode: $event,
  //   };
  //   this.service.searchFields(param).subscribe(
  //     (res) => {
  //       this.activedItem.fileNameValue = res.data;
  //       this.activedItem._visibleFields = this.activedItem.fileNameValue.filter(
  //         (f) => f.isVisible === true && f.type === 'LIST'
  //       );
  //       this.activedItem._isChange = true;
  //       this.listOfTabs[this.selectedIndex] = this.activedItem;
  //     },
  //     (err) => {}
  //   );
  // }
  // handleSourceFieldTypeChange($event: any): void {
  //   this.activedItem.fieldCondition = null;
  //   this.activedItem._isChange = true;
  //   this.listOfTabs[this.selectedIndex] = this.activedItem;
  // }
}
