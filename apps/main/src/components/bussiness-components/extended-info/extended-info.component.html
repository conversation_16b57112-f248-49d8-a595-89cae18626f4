<div (click)="handleOpenExtendedInfoModal($event)" class="extended-warp">
  <i
    *ngIf="!notShowIcon"
    adIcon
    iconfont="icondaliebiaokuozhanxinxi"
    aria-hidden="true"
    class="extend-info-icon iconfont"
    nz-tooltip
    [nzTooltipTitle]="'dj-扩展信息' | translate"
  >
  </i>
  <span [style]="textStyle" *ngIf="showName" class="extended-name">{{ 'dj-扩展信息' | translate }}</span>
</div>
<ad-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="title"
  [nzFooter]="null"
  [nzWidth]="1030"
  nzClassName="extended-info-modal"
  (nzOnCancel)="handleCancel()"
>
  <ng-container *adModalContent>
    <div class="extended-info-panel">
      <div class="extended-info-left">
        <ng-container *operateAuth="{ prefix: 'create', tenantPaas: isTenantProcessId, guards: [editable] }">
          <a ad-button adType="link" (click)="handleAddExtraInfo()">
            <i adIcon type="plus" theme="outline"></i>{{ 'dj-添加' | translate }}
          </a>
        </ng-container>
        <ul class="extended-info-list">
          <li
            class="extended-info-item"
            (click)="handleChangeSelectedIndex(tab, ind)"
            *ngFor="let tab of listOfTabs; let ind = index"
            [class.extended-info-selected]="selectedIndex === ind"
          >
            <span class="extend-name" nz-tooltip [nzTooltipTitle]="tab.name">{{ tab.name }}</span>
            <span class="btn-del">
              <ng-container *operateAuth="{ prefix: 'delete', tenantPaas: isTenantProcessId, guards: [editable] }">
                <i
                  adIcon
                  iconfont="iconxiaoliebiaoshanchu"
                  aria-hidden="true"
                  class="icon"
                  nz-tooltip
                  (click)="handleDeleteExtendedInfo(tab.code)"
                  [nzTooltipTitle]="'dj-删除' | translate"
                >
                </i>
              </ng-container>
            </span>
          </li>
        </ul>
      </div>
      <div class="extended-info-center">
        <div nz-row *ngIf="activedItem" class="extended-info-content">
          <div nz-col class="info-col">
            <span class="required">*</span>
            {{ 'dj-配置名称' | translate }}
          </div>
          <div nz-col class="info-col" [nzFlex]="'0 0 630px'">
            <input nz-input [(ngModel)]="activedItem.name" [disabled]="!editable" (ngModelChange)="inputChange($event, 'name')" />
            <div class="error-tip" *ngIf="activedItem._formDirty && isEmpty(activedItem.name)">
              {{ 'dj-配置名称' | translate }}{{ 'dj-必填' | translate }}
            </div>
          </div>
          <!-- <div nz-col class="info-col">
            <span class="required">*</span>
            {{ 'dj-存储真实表路径' | translate }}
          </div>
          <div nz-col class="info-col" [nzFlex]="'0 0 240px'">
            <ad-select
              [(ngModel)]="activedItem.sceneCode"
              (ngModelChange)="handleSceneCodeChange($event)"
            >
              <ad-option
                *ngFor="let path of realDataPath"
                [nzValue]="path.value"
                [nzLabel]="path.label"
              ></ad-option>
            </ad-select>
          </div> -->
          <!-- <div nz-col class="info-col">
            <span class="required">*</span>
            {{ 'dj-字段上级节点类型' | translate }}
          </div>
          <div nz-col class="info-col" [nzFlex]="'0 0 240px'">
            <ad-select
              [(ngModel)]="activedItem.sourceFieldType"
              (ngModelChange)="handleSourceFieldTypeChange($event)"
            >
              <ad-option
                nzValue="LIST"
                [nzLabel]="'dj-数组' | translate"
              ></ad-option>
              <ad-option
                nzValue="OTHER"
                [nzLabel]="'dj-其他' | translate"
              ></ad-option>
            </ad-select>
          </div> -->
          <div nz-col class="info-col">
            <span class="required">*</span>
            {{ 'dj-字段存储路径' | translate }}
          </div>
          <div nz-col class="info-col" [nzFlex]="'0 0 630px'">
            <input nz-input [(ngModel)]="activedItem.jsonPath" [disabled]="!editable" (ngModelChange)="inputChange($event, 'jsonPath')" />
            <div class="error-tip" *ngIf="activedItem._formDirty && isEmpty(activedItem.jsonPath)">
              {{ 'dj-字段存储路径' | translate }}{{ 'dj-必填' | translate }}
            </div>
          </div>
          <div nz-col class="info-col">
            <span class="required">*</span>
            {{ 'dj-扩展字段数据类型' | translate }}
          </div>
          <div nz-col [nzFlex]="'0 0 630px'">
            <ad-select
              style="width: 100px"
              [nzDisabled]="!editable"
              [(ngModel)]="activedItem.valueType"
              (ngModelChange)="handleValueTypeChange($event)"
            >
              <ad-option nzValue="Number" nzLabel="Number"></ad-option>
              <ad-option nzValue="String" nzLabel="String"></ad-option>
              <ad-option nzValue="Map" nzLabel="Object"></ad-option>
              <ad-option nzValue="Boolean" nzLabel="Boolean"></ad-option>
            </ad-select>
            <div class="error-tip" *ngIf="activedItem._formDirty && isEmpty(activedItem.valueType)">
              {{ 'dj-扩展字段数据类型' | translate }}{{ 'dj-必填' | translate }}
            </div>
          </div>
          <!-- <ng-container *ngIf="activedItem.sourceFieldType === 'LIST'">
            <div nz-col class="info-col">
              <span class="required">*</span>
              {{ 'dj-唯一指定数组条件' | translate }}
            </div>
            <div nz-col class="info-col" [nzFlex]="'0 0 630px'">
              <textarea
                nz-input
                [(ngModel)]="activedItem.fieldCondition"
                [nzAutosize]="{ minRows: 1, maxRows: 3 }"
              ></textarea>
            </div>
          </ng-container> -->
          <div nz-col class="info-col">
            <span class="required">*</span>
            {{ 'dj-扩展内容' | translate }}
          </div>
          <div nz-col [nzFlex]="'0 0 630px'">
            <textarea
              nz-input
              [disabled]="!editable"
              [(ngModel)]="activedItem.value"
              [autosize]="{ minRows: 1, maxRows: 3 }"
              (ngModelChange)="inputChange($event, 'value')"
            ></textarea>
            <div class="error-tip" *ngIf="activedItem._formDirty && isEmpty(activedItem.value)">
              {{ 'dj-扩展内容' | translate }}{{ 'dj-必填' | translate }}
            </div>
            <div
              class="error-tip"
              *ngIf="activedItem._formDirty && !isEmpty(activedItem.value) && !isEmpty(activedItem._errorMsg)"
            >
              {{ activedItem._errorMsg }}
            </div>
          </div>
          <!-- 动态出现部分 -->
          <!-- <ng-container *ngIf="activedItem._visibleFields?.length > 0">
            <ng-container *ngFor="let field of activedItem._visibleFields">
              <div nz-col class="info-col">
                <span class="required">*</span>
                {{ field.name }}
              </div>
              <div nz-col class="info-col" [nzFlex]="'0 0 240px'">
                <ad-select
                  [(ngModel)]="field.value"
                  (ngModelChange)="handleVisibleFieldValueChange($event, field)"
                >
                  <ad-option
                    *ngFor="let option of field.options"
                    [nzValue]="option.value"
                    [nzLabel]="option.label"
                  ></ad-option>
                </ad-select>
              </div>
            </ng-container>
          </ng-container> -->
          <div nz-col class="info-col">
            {{ 'dj-参考示例' | translate }}
          </div>
          <i
            adIcon
            iconfont="iconexplain"
            aria-hidden="true"
            class="question-icon"
            nzTooltipTrigger="hover"
            nz-tooltip
            [nzTooltipTitle]="exampleTmpl"
          >
          </i>
          <div nz-col class="info-col" [nzFlex]="'0 0 240px'">
            <ng-template #exampleTmpl>
              <div class="pre-tip">{{ activedItem.example || example }}</div>
            </ng-template>
          </div>
        </div>
        <div class="extended-info-footer">
          <ng-container
            *operateAuth="{ prefix: 'update', tenantPaas: isTenantProcessId, guards: [!isFromDtdReference, editable] }"
          >
            <button ad-button adType="primary" (click)="handleSaveExtraInfo()">
              {{ 'dj-保存' | translate }}
            </button>
          </ng-container>
        </div>
      </div>
    </div>
  </ng-container>
</ad-modal>
