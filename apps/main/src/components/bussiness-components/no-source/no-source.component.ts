import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-no-source',
  templateUrl: './no-source.component.html',
  styleUrls: ['./no-source.component.less'],
})
export class NoSourceComponent {
  @Input() tip: any;
  @Input() isCustom: any; // 是否定制
  @Input() customTip: any; // 定制标题
  @Input() canCustom: any; // 是否可设为定制
  @Output() setSource: EventEmitter<any> = new EventEmitter();
  @Output() setCustom: EventEmitter<any> = new EventEmitter();

  constructor() {}

  // 设定数据源
  handleSetSource(): void {
    this.setSource.emit();
  }

  // 设置定制
  handleSetCustom(): void {
    this.setCustom.emit();
  }
}
