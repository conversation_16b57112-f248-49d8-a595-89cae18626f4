<div class="no-source">
  <ad-empty [nzNotFoundContent]="contentTpl1" *ngIf="!isCustom" class="empty-source">
    <ng-template #contentTpl1>
      <span>{{ tip }}</span>
      <span class="set-source" (click)="handleSetSource()">
        {{ 'dj-前往设定' | translate }}
      </span>
      <span *ngIf="canCustom" class="set-custom" (click)="handleSetCustom()">
        ，{{ 'dj-或转为定制' | translate }}
      </span>
    </ng-template>
  </ad-empty>
  <app-custom-widget *ngIf="!!isCustom" [codeTip]="customTip" [disableCustom]="true"> </app-custom-widget>
  <ng-content></ng-content>
</div>
