<nz-spin [nzSpinning]="!form">
  <form nz-form [formGroup]="form" *ngIf="form">
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>{{ nameLabel }}</nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-请输入' | translate">
        <app-component-input
          class="lang-input"
          formControlName="name"
          [attr]="{
            code: 'name',
            needLang: true,
            lang: { value: form.get('lang').value?.name }
          }"
          [value]="form.get('lang').value?.name?.[('dj-LANG' | translate)] || form.get('name').value"
          ngDefaultControl
          (callBack)="handlePatchLang('name', $event)"
        >
        </app-component-input>
      </nz-form-control>
    </nz-form-item>

    <!-- 目前只有通用打印模板有actionId -->
    <nz-form-item *ngIf="isInForm('actionId')">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>{{ 'dj-数据源' | translate }}</nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-请选择API' | translate">
        <nz-input-group [nzSuffix]="suffixIcon">
          <input readonly nz-input formControlName="actionId" [placeholder]="'dj-请选择' | translate" />
        </nz-input-group>
        <ng-template #suffixIcon>
          <i
            adIcon
            iconfont="iconkaichuang"
            aria-hidden="true"
            class="window-icon iconfont"
            (click)="handleOpenAction()"
          >
          </i>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <!-- tenantId -->
    <nz-form-item *ngIf="isInForm('tenantId') && form.get('tenantId').value !== 'SYSTEM'">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>{{ 'dj-租户' | translate }}</nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-租户' | translate">
        <input nz-input formControlName="tenantId" type="text" />
      </nz-form-control>
    </nz-form-item>

    <!-- code -->
    <nz-form-item *ngIf="isInForm('code') && status !== 'add'">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>code</nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-请输入' | translate">
        <input nz-input formControlName="code" />
      </nz-form-control>
    </nz-form-item>

    <!-- resCode -->
    <nz-form-item *ngIf="isInForm('resCode')">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>resCode</nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-请输入' | translate">
        <input nz-input formControlName="resCode" />
      </nz-form-control>
    </nz-form-item>

    <!-- category -->
    <nz-form-item *ngIf="type === 'custom-statement' || status === 'edit'">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>{{ 'dj-类别' | translate }} </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-请输入' | translate">
        <ad-select *ngIf="type === 'custom-statement'; else categoryTpl" formControlName="category" style="width: 100%">
          <ad-option [nzValue]="'CUSTOM-STATEMENT'" [nzLabel]="'CUSTOM-STATEMENT'"></ad-option>
          <ad-option [nzValue]="'CUSTOM-STATEMENT-DETAIL'" [nzLabel]="'CUSTOM-STATEMENT-DETAIL'"></ad-option>
        </ad-select>
        <ng-template #categoryTpl>
          <input *ngIf="status === 'edit'" nz-input formControlName="category" type="text" />
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <!-- categoryAbi -->
    <nz-form-item *ngIf="isInForm('categoryAbi')">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>{{ 'dj-abi报表类型' | translate }} </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="'dj-请输入' | translate">
        <ad-select formControlName="categoryAbi" style="width: 100%">
          <ad-option *ngFor="let item of categoryApiOptions" [nzValue]="item.value" [nzLabel]="item.label"></ad-option>
        </ad-select>
      </nz-form-control>
    </nz-form-item>

    <!-- pattern -->
    <nz-form-item *ngIf="status === 'edit' || type === 'tbb-statement'">
      <nz-form-label [nzSm]="6" [nzXs]="24">{{ 'dj-模式' | translate }} </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24">
        <ad-select formControlName="pattern" style="width: 100%">
          <ad-option [nzValue]="'STATEMENT'" [nzLabel]="'STATEMENT'"></ad-option>
          <ad-option [nzValue]="'MOBI-STATEMENT'" [nzLabel]="'MOBI-STATEMENT'"></ad-option>
        </ad-select>
      </nz-form-control>
    </nz-form-item>

    <!-- 更新布局为横排 -->
    <div nz-row class="register-area">
      <div nz-col [nzSpan]="6"></div>
      <div nz-col [nzSpan]="18">
        <div class="report-grid report-grid-auto-fill-48">
          <!-- dependOnGroundEnd 是否依赖地端 -->
          <nz-form-item class="register-area">
            <nz-form-control>
              <label nz-checkbox formControlName="dependOnGroundEnd">
                {{ 'dj-是否依赖地端' | translate }}
              </label>
            </nz-form-control>
          </nz-form-item>

          <!-- subReport 是否为隐藏报表 -->
          <nz-form-item *ngIf="isInForm('subReport')" class="register-area">
            <nz-form-control>
              <label
                nz-checkbox
                formControlName="subReport"
                [nzDisabled]="form.get('subReport').disabled"
                (ngModelChange)="subReportChange()"
              >
                {{ 'dj-是否为隐藏报表' | translate }}
              </label>
            </nz-form-control>
          </nz-form-item>

          <!-- authorityPrefix 权限控制 -->
          <nz-form-item class="register-area">
            <nz-form-control>
              <label nz-checkbox formControlName="authorityPrefix" [nzDisabled]="form.get('authorityPrefix').disabled">
                {{ 'dj-权限控制' | translate }}
              </label>
            </nz-form-control>
          </nz-form-item>

          <!-- pageFlag 开启后端分页 -->
          <nz-form-item *ngIf="isInForm('pageFlag')" class="register-area">
            <nz-form-control>
              <label nz-checkbox formControlName="pageFlag">
                {{ 'dj-开启后端分页' | translate }}
              </label>
            </nz-form-control>
          </nz-form-item>

          <!-- 禁用导出 -->
          <nz-form-item *ngIf="shouldShowPrintExportControls()" class="register-area">
            <nz-form-control>
              <label nz-checkbox formControlName="disableExportFlag">
                <span>{{ 'dj-禁用导出' | translate }}</span>
              </label>
            </nz-form-control>
          </nz-form-item>

          <!-- 禁用打印 -->
          <nz-form-item *ngIf="shouldShowPrintExportControls()" class="register-area">
            <nz-form-control>
              <label nz-checkbox formControlName="disablePrintFlag">
                {{ 'dj-禁用打印' | translate }}<span nz-icon nzType="close-circle" nzTheme="outline"></span>
              </label>
              <i
                adIcon
                iconfont="iconqipaotishi-wenhao"
                class="icon"
                aria-hidden="true"
                nz-tooltip
                [nzTooltipTitle]="'dj-报表中包含指标树组件需要同时禁用打印和导出' | translate"
              >
              </i>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </div>
  </form>
</nz-spin>

<!--action开窗组件-->
<app-action-modal
  *ngIf="actionModalVisible"
  [transferModal]="actionModalVisible"
  [transferData]="actionData"
  labelType="EspAction"
  (callBack)="handleConfirmAction($event)"
  (closeModal)="actionModalVisible = false"
>
</app-action-modal>
