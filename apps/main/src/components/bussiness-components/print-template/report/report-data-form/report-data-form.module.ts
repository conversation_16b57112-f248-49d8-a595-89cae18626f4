import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { ReportDataFormComponent } from 'components/bussiness-components/print-template/report/report-data-form/report-data-form.component';
import { TranslateModule } from '@ngx-translate/core';
import { ReportService } from 'components/bussiness-components/print-template/report/report-service/report-service.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { InputModule } from '../../../../form-components/input/input.module';
import { AdSelectModule } from '../../../../ad-ui-components/ad-select/ad-select.module';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { ActionModalModule } from 'components/bussiness-components/action-modal/action-modal.module';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@NgModule({
  declarations: [ReportDataFormComponent],
  imports: [
    CommonModule,
    NzInputModule,
    TranslateModule,
    FormsModule,
    NzFormModule,
    InputModule,
    ReactiveFormsModule,
    AdSelectModule,
    NzCheckboxModule,
    ActionModalModule,
    AdIconModule,
    NzSpinModule,
    NzToolTipModule
  ],
  providers: [ReportService],
  exports: [ReportDataFormComponent],
})
export class ReportDataFormModule {}
