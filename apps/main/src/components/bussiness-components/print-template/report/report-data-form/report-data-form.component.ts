import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AppService } from 'pages/apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, delay } from 'lodash';
import { ReportTypeEnum } from '../tools/enum';
import { ReportTypeFormConfig } from '../tools/const-variables';
import { ReportService } from '../report-service/report-service.service';

@Component({
  selector: 'app-report-data-form',
  templateUrl: './report-data-form.component.html',
  styleUrls: ['./report-data-form.component.less'],
})
export class ReportDataFormComponent implements OnInit, OnChanges {
  @Input() status: 'add' | 'edit' = 'add';
  @Input() editData: any;
  @Input() type:
    | ReportTypeEnum.ABI_PRPTTPL /* 凭证类 */
    | ReportTypeEnum.ABI_EBIBASE /* 数据查询报表 */
    | ReportTypeEnum.TBB_STATEMENT /* 数据分析 */
    | ReportTypeEnum.CUSTOM_STATEMENT /* 定制报表 */
    | ReportTypeEnum.PRINT_TEMPLATE /* 通用打印模板 */ = ReportTypeEnum.ABI_PRPTTPL;

  form: FormGroup;
  baseFormItems: any; // 所有的表单项，根据该属性筛选不同类型的报表表单
  actionModalVisible: boolean = false;
  categoryApiOptions = [
    {
      label: this.translate.instant('打印表'),
      value: 'prpttpl',
    },
    {
      label: this.translate.instant('固定表'),
      value: 'ebibase',
    },
  ];

  isInForm(controlName) {
    return controlName in this.form.controls;
  }

  constructor(
    private fb: FormBuilder,
    private appService: AppService,
    private reportService: ReportService,
    private translate: TranslateService,
  ) {
    const appCode = this.appService?.selectedApp?.code ?? '';

    this.baseFormItems = {
      name: ['', Validators.required],
      category: ['', Validators.required],
      pattern: ['STATEMENT'],
      dependOnGroundEnd: [false], // 是否依赖地端
      authorityPrefix: [false], // 权限控制
      lang: [{}],
      application: [appCode],
      disablePrintFlag: [false], // 禁用打印
      disableExportFlag: [false] // 禁用导出
    };
  }

  get nameLabel() {
    return this.type === ReportTypeEnum.PRINT_TEMPLATE
      ? this.translate.instant('dj-模板名称')
      : this.translate.instant('dj-名称');
  }

  get actionData() {
    return {
      actionId: this.form.get('actionId')?.value ?? '',
      actionName: '',
      useApp: 'true',
    };
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.status || changes.type || changes.editData) {
      if (this.type === ReportTypeEnum.PRINT_TEMPLATE) {
        this.categoryApiOptions = [
          {
            label: this.translate.instant('通用打印模板'),
            value: 'prpttpl',
          },
        ];
      } else {
        this.categoryApiOptions = [
          {
            label: this.translate.instant('打印表'),
            value: 'prpttpl',
          },
          {
            label: this.translate.instant('固定表'),
            value: 'ebibase',
          },
        ];
      }
      // 根据新增或编辑状态、不同报表类型生成对应的表单
      this.formFilter(this.type);
    }
  }

  ngOnInit() {}

  /**
   * 根据不同类型的报表生成对应的表单
   * @param type
   */
  formFilter(type): void {
    const whiteItem = ['name', 'dependOnGroundEnd', 'authorityPrefix', 'pageFlag', 'subReport', 'tenantId', 'disablePrintFlag', 'disableExportFlag'];
    const formItems = cloneDeep(this.baseFormItems);
    let addItems = ReportTypeFormConfig[type][this.status];

    addItems.forEach((item) => {
      const newItem: any[] = [{ value: null, disabled: item.disabled }];
      if (item.required) {
        newItem.push(Validators.required);
      }
      formItems[item.name] = newItem;
    });

    this.form = null;
    delay(() => {
      this.form = this.fb.group(formItems);

      if (this.status === 'add') {
        this.form.patchValue(ReportTypeFormConfig[type].defalutValue);
      } else {
        // 默认值赋值
        const data = this.reportService.transformInOut('in', this.editData);
        this.form.patchValue(data);

        // 编辑状态下，disabled某些formItem
        for (const name in this.form.controls) {
          if (!whiteItem.includes(name)) {
            this.form.get(name).disable();
          }
        }
        // 是否为隐藏报表与权限控制的交互
        this.subReportChange();
      }
    }, 1);
  }

  /**
   * 多语言回调 表单赋值
   * @param key
   * @param data
   */
  handlePatchLang(key, data) {
    this.form.patchValue({
      [key]: data?.value,
      lang: {
        ...(this.form.get('lang').value || {}),
        [key]: data.lang?.value,
      },
    });
  }

  /**
   * 是否为隐藏报表与权限控制的交互
   */
  subReportChange() {
    if ([ReportTypeEnum.ABI_EBIBASE, ReportTypeEnum.ABI_PRPTTPL, ReportTypeEnum.TBB_STATEMENT].includes(this.type)) {
      const val = this.form.get('subReport').value;
      const control = this.form.get('authorityPrefix');

      if (val) {
        control.setValue(false);
        control.disable();
      } else {
        control.enable();
      }
    }
  }

  handleOpenAction() {
    if (this.form.get('actionId').disabled) return;
    this.actionModalVisible = true;
  }

  handleConfirmAction(data) {
    this.form.patchValue({
      actionId: data?.actionId ?? '',
    });
    this.actionModalVisible = false;
  }
  
  /**
   * 判断是否显示“禁用打印”和“禁用导出”配置项
   * 条件：数据查询明细表（abi-ebibase） + 固定表（ebibase） + 分类为ABI-STATEMENT
   */
  shouldShowPrintExportControls(): boolean {
    return (
      this.type === ReportTypeEnum.ABI_EBIBASE &&
      this.form?.get('categoryAbi')?.value === 'ebibase' &&
      this.form?.get('category')?.value === 'ABI-STATEMENT'
    );
  }
}
