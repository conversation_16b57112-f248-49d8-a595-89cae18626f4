import { ABIReportEnum, ReportCategoryEnum } from './enum';

/* 根据基础表单项配置，不同类型的报表有不同的添加、编辑字段以及表单默认值 */
export const ReportTypeFormConfig = {
  /* 凭证类 */
  'abi-prpttpl': {
    add: [{ name: 'categoryAbi', disabled: true }, { name: 'pageFlag' }, { name: 'subReport' }],
    edit: [
      { name: 'code', disabled: true },
      { name: 'categoryAbi', disabled: true },
      { name: 'pageFlag' },
      { name: 'tenantId' },
      { name: 'subReport' },
    ],
    defalutValue: {
      category: 'ABI-STATEMENT',
      categoryAbi: 'prpttpl',
      pageFlag: false,
      subReport: false,
    },
  },
  /* 数据查询报表 */
  'abi-ebibase': {
    add: [{ name: 'categoryAbi', disabled: true }, { name: 'pageFlag' }, { name: 'subReport' },{ name: 'disablePrintFlag' }, { name: 'disableExportFlag' }],
    edit: [
      { name: 'code', disabled: true },
      { name: 'categoryAbi', disabled: true },
      { name: 'pageFlag' },
      { name: 'tenantId' },
      { name: 'subReport' },
      { name: 'disablePrintFlag' },  // 新增：禁用打印
      { name: 'disableExportFlag' }  // 新增：禁用导出
    ],
    defalutValue: {
      category: 'ABI-STATEMENT',
      categoryAbi: 'ebibase',
      pageFlag: false,
      subReport: false,
      disablePrintFlag: false,  // 默认不禁用打印
      disableExportFlag: false  // 默认不禁用导出
    },
  },
  /* 数据分析 */
  'tbb-statement': {
    add: [{ name: 'subReport' }],
    edit: [
      { name: 'code', disabled: true },
      { name: 'resCode', disabled: true },
      { name: 'subReport' },
      { name: 'tenantId' },
    ],
    defalutValue: {
      category: 'STATEMENT',
      subReport: false,
    },
  },
  /* 定制报表 */
  'custom-statement': {
    add: [{ name: 'code' }],
    edit: [{ name: 'code', disabled: true }, { name: 'tenantId' }],
    defalutValue: {
      code: '',
      category: 'CUSTOM-STATEMENT-DETAIL',
    },
  },
  'print-template': {
    add: [{ name: 'categoryAbi', disabled: true }, { name: 'actionId', required: true }, { name: 'pageFlag' }, { name: 'subReport', disabled: true }, { name: 'adpType' }],
    edit: [
      { name: 'actionId', disabled: true, required: true },
      { name: 'code', disabled: true },
      { name: 'categoryAbi', disabled: true },
      { name: 'pageFlag' },
      { name: 'tenantId' },
      { name: 'subReport', disabled: true },
      { name: 'adpType' },
    ],
    defalutValue: {
      category: 'ABI-STATEMENT',
      categoryAbi: 'prpttpl',
      adpType: 'generalPrint',
      pageFlag: false,
      subReport: true,
    },
  },
};

export const ReportAddInfoConfig = {
  // 凭证类
  'abi-prpttpl': {
    category: ReportCategoryEnum.ABI_STATEMENT,
    categoryAbi: ABIReportEnum.PRPTTPL,
    title: 'dj-凭证类报表',
  },
  // 数据查询报表
  'abi-ebibase': {
    category: ReportCategoryEnum.ABI_STATEMENT,
    categoryAbi: ABIReportEnum.EBIBASE,
    title: 'dj-数据查询报表',
  },
  // 数据分析
  'tbb-statement': { category: ReportCategoryEnum.STATEMENT, title: 'dj-数据分析' },
  // 定制报表
  'custom-statement': {
    category: ReportCategoryEnum.CUSTOM_STATEMENT_DETAIL,
    title: 'dj-定制报表',
  },
  // 打印模板
  'print-template': {
    category: ReportCategoryEnum.ABI_STATEMENT,
    categoryAbi: ABIReportEnum.PRPTTPL,
    adpType: 'generalPrint',
    title: 'dj-打印模板',
  },
};

/**
 * 报表类型选择信息配置
 */
// 展示为card的列表
export const ReportCardTypeListConfig = [
  {
    // 展示在卡片列表中
    type: 'abi-prpttpl',
    scene: 'dj-侧重打印与导出效果的报表',
    title: 'dj-凭证打印报表',
    tip: 'dj-凭证打印报表-tip',
    logo: '/assets/img/abi-prpttpl.png',
  },
  {
    type: 'abi-ebibase',
    scene: 'dj-侧重明细数据展示与分析的报表',
    title: 'dj-数据查询明细表',
    tip: 'dj-数据查询明细表-tip',
    logo: '/assets/img/abi-ebibase.png',
  },
  {
    type: 'tbb-statement',
    scene: 'dj-侧重数据分析与图标呈现的报表',
    title: 'dj-图表分析报表',
    tip: 'dj-图表分析报表-tip',
    logo: '/assets/img/tbb-statement.png',
  },
  {
    type: 'print-template',
    scene: 'dj-侧重于可打印模版的设计与输出',
    title: 'dj-通用打印模板',
    tip: 'dj-通用打印模板-tip',
    logo: '/assets/img/print-template.png',
  },
]

// 展示在button中
export const ReportButtonTypeListConfig = [
  {
    type: 'custom-statement',
    icon: 'iconzidingyi',
    text: 'dj-自定义定制报表',
  },
]
