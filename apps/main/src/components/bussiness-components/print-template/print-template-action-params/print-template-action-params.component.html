<div class="print-template-action-params-setter" *ngIf="!modalOnly">
  <div *ngIf="!hideLabel">{{ 'dj-参数' | translate }}</div>
  <button ad-button adType="link" (click)="handleShowModal()">{{ 'dj-设置' | translate }}</button>
</div>
<!--action列表开窗-->
<ad-modal
  *ngIf="visible"
  nzClassName="view-action-params"
  [nzWidth]="'666px'"
  [nzTitle]="'dj-参数设置' | translate"
  [(nzVisible)]="visible"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="handleCloseSet()"
>
  <ng-container *adModalContent>
    <nz-spin [nzSpinning]="loading">
      <div class="action-params">
        <div class="field-list">
          <div class="title" (click)="handleAdd()">+ {{ 'dj-新增字段' | translate }}</div>
          <div *ngIf="currentActionParams?.length > 0" class="param-content">
            <div
              *ngFor="let param of currentActionParams; let i = index"
              class="field-data"
              [ngClass]="{ 'active-param': activeIndex === i }"
              (click)="handleSelectParam(i)"
            >
              <div class="field-title">{{ 'dj-字段' | translate }}{{ i + 1 }}</div>
              <div class="field-icon">
                <i
                  adIcon
                  nz-tooltip
                  [nzTooltipTitle]="'dj-删除' | translate"
                  iconfont="iconxiaoliebiaoshanchu"
                  aria-hidden="true"
                  (click)="handleDeleteParam(i)"
                >
                </i>
              </div>
            </div>
          </div>
        </div>
        <div class="field-detail" *ngIf="!!selectItem && currentActionParams?.length > 0">
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-提交目标字段' | translate }}
            </div>
            <!--<ad-tree-select-->
            <!--  nzShowSearch-->
            <!--  nzDefaultExpandAll-->
            <!--  [nzNodes]="nameList"-->
            <!--  [ngModel]="selectItem['name']"-->
            <!--  (ngModelChange)="handleModifyData('name', $event)"-->
            <!--></ad-tree-select>-->
            <input
              nz-dropdown
              [nzDropdownMenu]="nameMenu"
              nz-input
              type="text"
              [(ngModel)]="selectItem['name']"
              [nzTrigger]="'click'"
              [(nzVisible)]="nameVisible"
            />
            <nz-dropdown-menu #nameMenu="nzDropdownMenu">
              <div class="select-value-drop">
                <nz-tree
                  [nzSelectedKeys]="[selectItem['name']]"
                  [nzData]="nameList"
                  (nzClick)="handleClickNameList($event)"
                  nzExpandAll
                >
                </nz-tree>
              </div>
            </nz-dropdown-menu>
          </div>
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-类型' | translate }}
            </div>
            <ad-select
              style="width: 100%"
              [(ngModel)]="selectItem['type']"
              (ngModelChange)="handleModifyData('type', $event)"
            >
              <ad-option *ngFor="let option of sourceType" [nzValue]="option.value" [nzLabel]="option.label">
              </ad-option>
            </ad-select>
          </div>
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-值' | translate }}
            </div>
            <!--<ad-tree-select-->
            <!--  nzShowSearch-->
            <!--  nzDefaultExpandAll-->
            <!--  [nzNodes]="valueList"-->
            <!--  [ngModel]="selectItem['value']"-->
            <!--  (ngModelChange)="handleModifyData('value', $event)"-->
            <!--></ad-tree-select>-->

            <input
              nz-dropdown
              [nzDropdownMenu]="valueMenu"
              nz-input
              type="text"
              [(ngModel)]="selectItem['value']"
              [nzTrigger]="'click'"
              [(nzVisible)]="valueVisible"
            />
            <nz-dropdown-menu #valueMenu="nzDropdownMenu">
              <div class="select-value-drop">
                <nz-tree
                  [nzSelectedKeys]="[selectItem['value']]"
                  [nzData]="valueList"
                  (nzClick)="handleClickValueList($event)"
                  nzExpandAll
                >
                </nz-tree>
              </div>
            </nz-dropdown-menu>
          </div>
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-转换器' | translate }}
            </div>
            <ad-select
              style="width: 100%"
              [(ngModel)]="selectItem['typeConverter']"
              (ngModelChange)="handleModifyData('typeConverter', $event)"
            >
              <ad-option *ngFor="let option of converterList" [nzValue]="option.value" [nzLabel]="option.label">
              </ad-option>
            </ad-select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button ad-button adType="default" (click)="handleCloseSet()">
          {{ 'dj-取消' | translate }}
        </button>
        <button ad-button adType="primary" (click)="handleConfirm()">
          {{ 'dj-确定' | translate }}
        </button>
      </div>
    </nz-spin>
  </ng-container>
</ad-modal>
