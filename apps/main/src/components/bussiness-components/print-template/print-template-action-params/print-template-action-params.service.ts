import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/internal/Observable';
import { ICommonResponse } from '../types';
import { SystemConfigService } from 'common/service/system-config.service';

@Injectable()
export class PrintTemplateActionParamsService {
  private adesignerUrl: string;

  constructor(private http: HttpClient, private configService: SystemConfigService) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  /**
   * 查询actionParams参数
   * @param {string} actionId - esp actionId
   * @returns
   */
  queryActionParams(actionId: string): Observable<ICommonResponse<any>> {
    const url = `${this.adesignerUrl}/athena-designer/action/findActionByActionId`;
    return this.http.get(url, { params: { label: 'EspAction', actionId } }) as Observable<ICommonResponse<any>>;
  }
}
