import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PrintTemplateActionParamsService } from './print-template-action-params.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { IOptionItem, IActionParam, IFieldData } from '../types';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-print-template-action-params',
  templateUrl: './print-template-action-params.component.html',
  styleUrls: ['./print-template-action-params.component.less'],
})
export class PrintTemplateActionParamsComponent implements OnInit, OnChanges {
  /**
   * 是否只使用modal
   */
  @Input() modalOnly: boolean = false;
  /**
   * 初始化值
   */
  @Input() actionParams: IActionParam[] = [];
  /**
   * 模版打印绑定的esp actionId
   */
  @Input() actionId: string = '';
  /**
   * 当前作业绑定的数据源fields, 传就使用这个作为值的下拉值，不传就是使用response_parameters
   */
  @Input() fields: IFieldData[] = [];
  /**
   * 表单中有时候需要隐藏标题
   */
  @Input() hideLabel: boolean = false;
  /**
   * actionParams值变更
   */
  @Output() change: EventEmitter<IActionParam[]> = new EventEmitter<IActionParam[]>();

  /**
   * 开窗是否显示
   */
  visible: boolean = false;
  /**
   * 开窗loading
   */
  loading: boolean = false;

  /**
   * 当前选择的参数index
   */
  activeIndex: number = 0;
  /**
   * 来源类型
   */
  sourceType: IOptionItem[];
  /**
   * 转换器
   */
  converterList: IOptionItem[];
  /**
   * typeConverter 当前选中的参数
   */
  selectItem?: IActionParam;
  /**
   * 目标字段树数据
   */
  nameList: IFieldData[] = [];
  /**
   * 值字段树数据
   */
  valueList: IFieldData[] = [];
  nameVisible: boolean = false;
  valueVisible: boolean = false;

  currentActionParams: IActionParam[] = [];

  constructor(
    private trans: TranslateService,
    private service: PrintTemplateActionParamsService,
    private message: NzMessageService,
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (
      'actionId' in changes &&
      changes.actionId.currentValue &&
      changes.actionId.currentValue !== changes.actionId.previousValue
    ) {
      this.handleLoadName();
    }
    if ('actionParams' in changes) {
      this.currentActionParams = cloneDeep(changes.actionParams.currentValue || []);
      if (this.currentActionParams?.length > 0) {
        this.handleSelectParam(0);
      }
    }
    if ('fields' in changes && changes.fields.currentValue?.length > 0) {
      this.valueList = this.formatTreeNodes(this.fields);
    }
  }

  ngOnInit(): void {
    this.sourceType = [
      { value: 'ACTIVE_ROW', label: this.trans.instant('dj-ACTIVE_ROW:当前行动态值') },
      { value: 'ACTIVE_ROW_CONSTANT', label: this.trans.instant('dj-ACTIVE_ROW_CONSTANT:静态值') },
      {
        value: 'GET_ACTION_RESPONSE',
        label: this.trans.instant('dj-GET_ACTION_RESPONSE:提交的值，或上一个action返回的值'),
      },
      { value: 'CONSTANT', label: this.trans.instant('dj-CONSTANT:静态值，空数组可以用[]') },
      { value: 'SYSTEM', label: this.trans.instant('dj-SYSTEM:取系统参数，value可以为当前登录id') },
      { value: 'PROCESS_VARIABLE', label: this.trans.instant('dj-PROCESS_VARIABLE:任务引擎推送来的变量') },
      { value: 'TM_VARIABLE', label: this.trans.instant('dj-TM_VARIABLE:交付设计器变量') },
    ];
    this.converterList = [
      { value: '', label: this.trans.instant('dj-空') },
      { value: 'stringToBooleanConverter', label: 'stringToBooleanConverter' },
      { value: 'stringToNumberConverter', label: 'stringToNumberConverter' },
    ];
  }

  /**
   * 过滤附件字段
   */
  private filterFunc = (field: IFieldData) => {
    return field.data_type !== 'file' && field.data_type !== 'json';
  };

  /**
   * 数组字段不让选
   */
  private isFieldIsArray(field: IFieldData) {
    const sbool = [true, 'true'];
    return sbool.includes(field.is_array) && field.data_type === 'object';
  }

  formatTreeNodes(fields: IFieldData[] = []): IFieldData[] {
    const appData = cloneDeep(fields || []);
    const format = (origin: IFieldData[]) => {
      const restData = (origin || []).filter(this.filterFunc);
      return restData.map((d) => {
        const returnVal = {
          ...d,
          key: d.fullPath,
          title: d.data_name,
          selectable: !this.isFieldIsArray(d),
        };
        if (d['field']?.length > 0) {
          returnVal['children'] = format(d['field']);
          returnVal['expanded'] = true;
        } else {
          returnVal['isLeaf'] = true;
        }
        return returnVal;
      });
    };
    const restData = appData.filter(this.filterFunc);
    restData.forEach((d) => {
      d.key = d.fullPath;
      d.title = d.data_name;
      d.expanded = true;
      d.selectable = !this.isFieldIsArray(d);
      d.children = format(d['field']);
    });
    return restData;
  }

  handleLoadName(): void {
    if (this.actionId) {
      this.loading = true;
      this.service.queryActionParams(this.actionId).subscribe(
        (res) => {
          if (res.code === 0) {
            this.nameList = this.formatTreeNodes(res.data.request_parameters);
            if (!this.fields || this.fields?.length === 0) {
              this.valueList = this.formatTreeNodes(res.data.request_parameters);
            }
          }
          this.loading = false;
        },
        () => {
          this.loading = false;
        },
      );
    }
  }

  // 回调修改的数据
  handleModifyData(key: any, data: any): void {
    this.selectItem = {
      ...this.selectItem,
      [key]: data,
    };
    if (this.activeIndex > -1) {
      this.currentActionParams[this.activeIndex] = {
        ...this.currentActionParams[this.activeIndex],
        [key]: data,
      };
    }
  }

  // 新增
  handleAdd(): void {
    if (!this.currentActionParams) {
      this.currentActionParams = [];
    }
    this.currentActionParams.push({
      name: '',
      type: 'ACTIVE_ROW',
      value: '',
      typeConverter: '',
    });
    this.handleSelectParam(this.currentActionParams.length - 1);
  }

  // 选中
  handleSelectParam(index: any): void {
    this.activeIndex = index;
    this.selectItem = this.currentActionParams[index];
  }

  // 删除
  handleDeleteParam(index: any): void {
    this.currentActionParams.splice(index, 1);
  }

  handleShowModal() {
    if (!this.actionId) {
      this.message.error(this.trans.instant('dj-请先选择打印模板'));
      return;
    }
    if (this.currentActionParams?.length > 0) {
      this.handleSelectParam(0);
    }
    this.visible = true;
  }

  // 关闭action
  handleCloseSet(): void {
    this.visible = false;
    this.selectItem = null;
  }

  // 确认
  handleConfirm(): void {
    this.visible = false;
    this.selectItem = null;
    this.change.emit(this.currentActionParams);
  }

  // 设置name
  handleClickNameList(event: NzFormatEmitEvent): void {
    this.selectItem = {
      ...this.selectItem,
      name: event.node.key,
    };
    if (this.activeIndex > -1) {
      this.currentActionParams[this.activeIndex] = {
        ...this.currentActionParams[this.activeIndex],
        name: event.node.key,
      };
    }
    this.nameVisible = false;
  }

  // 设置value
  handleClickValueList(event: NzFormatEmitEvent): void {
    this.selectItem = {
      ...this.selectItem,
      value: event.node.key,
    };
    if (this.activeIndex > -1) {
      this.currentActionParams[this.activeIndex] = {
        ...this.currentActionParams[this.activeIndex],
        value: event.node.key,
      };
    }
    this.valueVisible = false;
  }
}
