import { Component, OnInit, Input, ViewChild, Output, EventEmitter } from '@angular/core';
import { PrintTemplatePreviewService } from 'components/bussiness-components/print-template/print-template-preview/print-template-preview.service';
import { DomSanitizer } from '@angular/platform-browser';
import { AppService } from 'pages/apps/app.service';
import { PrintTemplateActionParamsComponent } from 'components/bussiness-components/print-template/print-template-action-params/print-template-action-params.component';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';

@Component({
  selector: 'app-print-template-preview',
  templateUrl: './print-template-preview.component.html',
  styleUrls: ['./print-template-preview.component.less'],
})
export class PrintTemplatePreviewComponent implements OnInit {
  @Input() reportData: any;
  @ViewChild('actionParams') actionParamsRef: PrintTemplateActionParamsComponent;
  @Output() refresh: EventEmitter<void> = new EventEmitter();

  loading: boolean = false;
  reportPrintTemplateVisible: boolean = false;
  actionModalVisible: boolean = false;
  actionModalData = {
    actionId: '',
    useApp: 'true',
  };
  iframeUrl: any = '';

  constructor(
    private service: PrintTemplatePreviewService,
    private appService: AppService,
    private languageService: LocaleService,
    private modal: AdModalService,
    private translate: TranslateService,
    private domSanitizer: DomSanitizer,
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.createIframeUrl();
  }

  createIframeUrl() {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const param = `application=${this.appService?.selectedApp?.code}&code=${this.reportData.code}&locale=${language}`;
    this.service.getReportResid(param).subscribe((res) => {
      if (res?.code === 0) {
        const resId = res.data?.resid;
        this.iframeUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(
          `${this.service.abiReportUrl}/abi/web/prpttpl/showpreport.do?resid=${resId}&showmenu=false`,
        );
      }
    });
  }

  handleShowDesign() {
    this.reportPrintTemplateVisible = true;
  }

  handleCloseReportPrintTemplate() {
    this.reportPrintTemplateVisible = false;
  }

  handleSetActionParams() {
    this.actionParamsRef.handleShowModal();
  }

  handleSwitchDataSource() {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-更换数据源将导致现有配置失效确认需要更换吗'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzOnOk: () => {
        this.actionModalData = {
          actionId: this.reportData?.actionId,
          useApp: 'true',
        };
        this.actionModalVisible = true;
      },
      nzOnCancel: () => {},
    });
  }

  handlePrintTemplateActionParamsChange(actionParams: any[]) {
    this.service
      .updatePrintTemplate({
        application: this.appService?.selectedApp?.code,
        code: this.reportData?.code,
        actionId: this.reportData?.actionId,
        actionParams: actionParams,
      })
      .subscribe(() => {
        this.refresh.emit();
      });
  }

  handleActionModalSelect(data: any) {
    this.service
      .updatePrintTemplate({
        application: this.appService?.selectedApp?.code,
        code: this.reportData?.code,
        actionId: data?.actionId,
        actionParams: null,
      })
      .subscribe(() => {
        this.refresh.emit();
        this.actionModalVisible = false;
      });
  }

  handleActionModalClose() {
    this.actionModalVisible = false;
    this.refresh.emit();
  }
}
