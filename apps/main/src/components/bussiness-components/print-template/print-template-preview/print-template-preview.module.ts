import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { PrintTemplatePreviewComponent } from 'components/bussiness-components/print-template/print-template-preview/print-template-preview.component';
import { TranslateModule } from '@ngx-translate/core';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { PrintTemplatePreviewService } from 'components/bussiness-components/print-template/print-template-preview/print-template-preview.service';
import { FormsModule } from '@angular/forms';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { ReportWorkDesignPrintTemplateModule } from 'components/page-design/entries/report-work-design-print-template/report-work-design-print-template.module';
import { PrintTemplateActionParamsModule } from 'components/bussiness-components/print-template/print-template-action-params/print-template-action-params.module';
import { ActionModalModule } from 'components/bussiness-components/action-modal/action-modal.module';
import { PublishButtonModule } from 'components/bussiness-components/module-publish-button/module-publish-button.module';
import { DirectiveModule } from 'common/directive/directive.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@NgModule({
  declarations: [PrintTemplatePreviewComponent],
  imports: [
    CommonModule,
    NzInputModule,
    TranslateModule,
    AdIconModule,
    AdModalModule,
    NzSpinModule,
    FormsModule,
    AdButtonModule,
    ReportWorkDesignPrintTemplateModule,
    PrintTemplateActionParamsModule,
    ActionModalModule,
    PublishButtonModule,
    DirectiveModule,
    NzToolTipModule,
  ],
  providers: [PrintTemplatePreviewService],
  exports: [PrintTemplatePreviewComponent],
})
export class PrintTemplatePreviewModule {}
