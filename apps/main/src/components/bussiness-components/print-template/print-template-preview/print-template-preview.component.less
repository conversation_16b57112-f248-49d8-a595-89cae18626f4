.print-template-preview-container {
  width: 100%;
  height: 100%;
  ::ng-deep .ant-spin-container {
    height: 100%;
  }
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 16px;
    border-bottom: 1px solid #ddd;
    .left {
      font-size: 14px;
      color: #333333;
    }
  }
  .header-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 15px 25px;
    gap: 20px;
    .ant-btn {
      font-size: 13px;
      color: #6868AE;
      padding: 0;
    }
    .header-btn {
      span {
        margin-left: 5px;
      }
    }
  }
  .content {
    position: relative;
    width: 100%;
    height: calc(100% - 117px);
    padding: 3px 25px 20px 25px;
    .edit-icon {
      position: absolute;
      right: 34px;
      top: 11px;
    }
    iframe {
      height: 100%;
      width: 100%;
      border-radius: 2px;
      border: 2px solid rgb(101, 77, 246);
    }
  }
}
