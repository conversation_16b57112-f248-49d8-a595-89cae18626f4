<div class="print-template-preview-container">
  <nz-spin [nzSpinning]="loading" style="height: 100%">
    <div class="title">
      <div class="left">
        {{ reportData?.name }}
      </div>
      <div class="right">
        <app-module-publish-button
          [size]="'default'"
          [module]="'Statement'"
          btnType="primary"
          [pkValue]="reportData?.code"
          [needTenant]="true"
          [needSave]="false"
        ></app-module-publish-button>
      </div>
    </div>
    <div class="header-buttons">
      <!--<ad-button adType="link">-->
      <!--  <i adIcon iconfont="iconjiemiansheji"></i>-->
      <!--  {{ 'dj-使用界面' | translate }}-->
      <!--</ad-button>-->
      <button class="header-btn" ad-button adType="link" (click)="handleSetActionParams()">
        <i adIcon iconfont="iconcanshupeizhi1"></i>
        {{ 'dj-参数设置' | translate }}
      </button>
      <button class="header-btn" ad-button adType="link" (click)="handleSwitchDataSource()">
        <i adIcon iconfont="iconcanshupeizhi1"></i>
        {{ 'dj-切换数据源' | translate }}
      </button>
    </div>
    <div class="content">
      <div class="edit-icon" nz-tooltip [nzTooltipTitle]="'dj-模板设计' | translate">
        <button ad-button adType="primary">
          <i adIcon iconfont="iconjizhikabianji1" (click)="handleShowDesign()"></i>
        </button>
      </div>
      <iframe [src]="iframeUrl" (load)="loading = false"></iframe>
    </div>
  </nz-spin>
</div>

<!-- 通用打印模板设计开窗 -->
<app-report-work-design-print-template
  *ngIf="reportPrintTemplateVisible"
  [reportVisible]="reportPrintTemplateVisible"
  [reportData]="reportData"
  (closeReport)="handleCloseReportPrintTemplate()"
></app-report-work-design-print-template>

<app-print-template-action-params
  #actionParams
  [actionParams]="reportData?.actionParams ?? []"
  [modalOnly]="true"
  [actionId]="reportData?.actionId"
  (change)="handlePrintTemplateActionParamsChange($event)"
></app-print-template-action-params>

<app-action-modal
  *ngIf="actionModalVisible"
  [transferModal]="actionModalVisible"
  [transferData]="actionModalData"
  labelType="EspAction"
  (callBack)="handleActionModalSelect($event)"
  (closeModal)="handleActionModalClose()"
>
</app-action-modal>
