import { Component, OnInit, OnChanges, OnDestroy, SimpleChanges, AfterViewInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { AiAgentManageService } from './service/ai-agent-manage.service';
import { AiAgentGobalManageService } from './service/ai-agent-manage-gobal.service';
import { AiAgentManageRequestService } from './service/ai-agent-manage-request.service';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AiChatSseService } from './service/ai-chat-sse.service';

@Component({
  selector: 'app-ai-agent-manage',
  templateUrl: './ai-agent-manage.component.html',
  styleUrls: ['./ai-agent-manage.component.less'],
  providers: [AiAgentManageService, AiAgentManageRequestService, AiChatSseService],
})
export class AiAgentManageComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  loading = false;
  isDragging = false;

  // 路由相关属性
  private destroy$ = new Subject<void>();
  private currentRoute: string = '';
  private previousRoute: string = '';

  constructor(
    public aiAgentManageService: AiAgentManageService,
    private aiAgentManageRequestService: AiAgentManageRequestService,
    private translateService: TranslateService,
    private modal: AdModalService,
    private athMessageService: NzMessageService,
    private aiChatSseService: AiChatSseService,
    public aiAgentGobalManageService: AiAgentGobalManageService,
    private router: Router,
  ) {
    // 初始化当前路由
    this.currentRoute = this.router.url;
  }

  ngOnInit(): void {
    this.handleInit();
    this.setupRouteListener();
  }

  ngAfterViewInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngOnDestroy(): void {
    // 确保在组件销毁时关闭 SSE 连接
    this.aiChatSseService.closeConnection();

    // 清理路由监听
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ======= 接口请求 =======
  // TODO 请求智能体列表信息等

  // ======= 接口处理 =======
  // TODO 请求智能体列表信息等

  // ======= 其他业务逻辑 =======
  private handleInit(): void {
    console.log('handleInit');
  }

  // 设置路由监听器
  private setupRouteListener(): void {
    // 需要先先初始化路由信息
    this.currentRoute = this.router.url;
    this.previousRoute = this.currentRoute;
    this.handleRouteChange();

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      )
      .subscribe((event: NavigationEnd) => {
        this.previousRoute = this.currentRoute;
        this.currentRoute = event.urlAfterRedirects;
        this.handleRouteChange();
      });
  }

  // 处理路由变化
  handleRouteChange(): void {
    console.log('路由发生变化:', {
      previous: this.previousRoute,
      current: this.currentRoute,
    });

    console.log('=======');
    console.log(this.currentRoute.includes('/app/business-constructor/model-design/'));
  }

  handleClickAiAgentManage(): void {
    if (!this.isDragging) this.aiAgentGobalManageService.setIsShowChat(true);
    this.isDragging = false;
  }
}
