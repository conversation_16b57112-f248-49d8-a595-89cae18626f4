export const getFlatModelList = (modelList, parentModel?) => {
  let result = [];
  modelList.forEach((model) => {
    if (parentModel) model._pid = parentModel.name;
    result.push(model);
    if (model.children) {
      result = [...result, ...getFlatModelList(model.children, model)];
      model.children = model.children.map((child) => {
        return { name: child.name };
      });
    }
  });
  return result;
};

export const renameModel = (model, applicationCode) => {
  model.name = `${applicationCode}_${model.name}`;
  if (model.children) {
    model.children.forEach(renameModel);
  }
};
