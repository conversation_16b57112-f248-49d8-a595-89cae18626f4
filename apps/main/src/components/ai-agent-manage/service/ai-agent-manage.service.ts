import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject } from 'rxjs';
import { IAgentConfig } from '../config/ai-agent-manage.type';
import { aiAgentList } from '../config/ai-agent-manage.config';

@Injectable()
export class AiAgentManageService {
  // ========================= 模块级属性 =========================
  private _sseloading: boolean = false; // sse请求loading
  private _aiAgentList: IAgentConfig[] = [...aiAgentList]; // TODO aiAgent列表(之后是从后端动态取值)
  private _currentAiAgent: IAgentConfig = this._aiAgentList[1]; // TODO 当前aiAgent(之后动态设置，现在默认是ai生成应用的智能体)
  private _isShowChat: boolean = false; // 是否显示聊天窗口
  private _isMaximized: boolean = false; // 是否最大化
  private _isExecuting: boolean = false; // 是否正在执行
  private _changeMessageInput$: Subject<any> = new Subject<any>(); // 主动修改消息输入框内容

  get sseloading(): boolean {
    return this._sseloading;
  }

  get aiAgentList(): IAgentConfig[] {
    return this._aiAgentList;
  }

  get currentAiAgent(): IAgentConfig {
    return this._currentAiAgent;
  }

  get isShowChat(): boolean {
    return this._isShowChat;
  }

  get isMaximized(): boolean {
    return this._isMaximized;
  }

  get isExecuting(): boolean {
    return this._isExecuting;
  }

  get changeMessageInput$(): Observable<any> {
    return this._changeMessageInput$.asObservable();
  }

  // ========================= 模块级属性的操作 =========================
  setSseloading(value: boolean): void {
    this._sseloading = value;
  }

  setAiAgentList(value: IAgentConfig[]): void {
    this._aiAgentList = value;
  }

  setCurrentAiAgent(value: IAgentConfig): void {
    this._currentAiAgent = value;
  }

  setIsShowChat(value: boolean): void {
    this._isShowChat = value;
  }

  toggleIsMaximized(): void {
    this._isMaximized = !this._isMaximized;
  }

  setIsExecuting(value: boolean): void {
    this._isExecuting = value;
  }

  setChangeMessageInput(value: any): void {
    this._changeMessageInput$.next(value);
  }

  // 重置所有数据
  resetAll(): void {
    this._sseloading = false;
    this._aiAgentList = [];
    this._currentAiAgent = null;
  }
}
