import { Injectable, NgZone } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { SystemConfigService } from 'common/service/system-config.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { fetchEventSource } from '../utils/fetch-event-source';
import { ChatMessage, ChatResponse, IAgentConfig, ChatMessageUser } from '../config/ai-agent-manage.type';

@Injectable()
export class AiChatSseService {
  private serviceUrl: string;
  private controller: AbortController | null = null;
  private chatResponseSubject = new Subject<ChatResponse>();

  constructor(protected configService: SystemConfigService, protected http: HttpClient, private ngZone: NgZone) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
  }

  /**
   * 发送消息并通过 SSE 接收流式响应
   * @param messages 聊天历史消息
   * @param aiAgent 当前的智能体
   * @param iamToken IAM token
   * @param conversationId 可选的会话ID
   */
  sendMessage(
    userMessage: string,
    aiAgent: IAgentConfig,
    iamToken: string,
    conversationId?: string,
  ): Observable<ChatResponse> {
    // 构建 SSE 请求 UR
    const url = `${this.serviceUrl}/athena-designer/agent/chat/${aiAgent.agentId}`;
    // const url = `${this.serviceUrl}/athena-designer/agent/chat/2`;
    // const url = `${this.serviceUrl}/athena-designer/agent/chat/4`;

    // 关闭之前的连接
    this.closeConnection();

    // 重置响应Subject
    this.chatResponseSubject = new Subject<ChatResponse>();

    // 创建 AbortController 用于中止请求
    this.controller = new AbortController();

    // 使用自定义的 fetchEventSource 函数建立 SSE 连接
    fetchEventSource(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'digi-middleware-auth-user': iamToken,
      },
      body: JSON.stringify({
        sessionId: conversationId || '',
        question: userMessage,
        userToken: '7d9d147b-9a5f-44e4-9aa8-d7c1298f9845',
      }),
      signal: this.controller.signal,
      openWhenHidden: true,
      onmessage: (event) => {
        this.ngZone.run(() => {
          try {
            const data = JSON.parse(event.data);
            this.chatResponseSubject.next(data);

            // 如果响应完成，关闭连接
            if (data.isComplete) {
              this.closeConnection();
            }
          } catch (error) {
            console.error('解析 SSE 消息失败:', error);
            this.chatResponseSubject.error(error);
            this.closeConnection();
          }
        });
      },
      onopen: async (response) => {
        this.chatResponseSubject.next({
          otherResponseType: 'onopen',
        });
        if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
          console.log('SSE 连接已建立');
        } else {
          console.error('SSE 连接失败:', response.status, response.statusText);
          this.chatResponseSubject.error(`SSE 连接失败: ${response.status} ${response.statusText}`);
          this.closeConnection();
        }
      },
      onerror: (error) => {
        this.ngZone.run(() => {
          console.error('SSE 连接错误:', error);
          this.chatResponseSubject.error(error);
          this.closeConnection();
        });

        throw new Error();
      },
      onclose: () => {
        this.ngZone.run(() => {
          console.log('SSE 连接已关闭');
          this.chatResponseSubject.next({
            otherResponseType: 'onclose',
          });
          this.closeConnection();
        });
      },
    }).catch((error) => {
      this.ngZone.run(() => {
        console.error('建立 SSE 连接失败:', error);
        this.chatResponseSubject.error(error);
        this.closeConnection();
      });
    });

    return this.chatResponseSubject.asObservable();
  }

  /**
   * 关闭 SSE 连接
   */
  closeConnection(): void {
    if (this.controller) {
      this.controller.abort();
      this.controller = null;
    }
  }

  /**
   * 中止当前对话
   * @param conversationId 会话ID
   */
  // abortConversation(conversationId: string): Observable<any> {
  //   const url = `${this.serviceUrl}/athena-designer/ai-chat/abort`;
  //   const headers = new HttpHeaders({
  //     'Content-Type': 'application/json',
  //     'digi-middleware-auth-user': this.AUTH_USER_ID,
  //   });

  //   return this.http.post(url, { sessionId: conversationId }, { headers });
  // }
}
