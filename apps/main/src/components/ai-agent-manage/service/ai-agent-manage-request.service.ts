import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
@Injectable()
export class AiAgentManageRequestService {
  serviceUrl: string;

  constructor(protected configService: SystemConfigService, protected http: HttpClient) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
  }

  // 资产中心资产列表
  assetCenterList(params: unknown): Observable<unknown> {
    const url = `${this.serviceUrl}/athena-designer/assetCenter/assetCenterList`;
    return this.http.post(url, params);
  }

  // 下架资产（应用中心）
  removedAssetOpenTenant(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/assetCenter/removedAssetOpenTenant`;
    return this.http.get(url, {
      params,
    });
  }

  // 上传图片
  shareUploadFile(params): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dmc/shareUploadFile`;
    return this.http.post(url, params);
  }
}
