<div
  [ngClass]="{ isShowChat: aiAgentGobalManageService.isShowChat }"
  class="ai-agent-manage"
  *ngIf="aiAgentGobalManageService.isShow"
>
  <div class="ai-chat-wrapper" [ngClass]="{ maximized: aiAgentManageService.isMaximized }">
    <app-ai-chat></app-ai-chat>
  </div>
  <div
    class="ai-agent-logo-wrapper"
    cdkDrag
    cdkDragBoundary=".gpt-icon-drag-container"
    (cdkDragStarted)="isDragging = true"
    (click)="handleClickAiAgentManage()"
  >
    <div class="ai-agent-logo"></div>
  </div>
</div>
