<div class="message-system" [ngClass]="msg.systemType">
  <div class="message-content" *ngIf="msg.systemType === 'error'">
    {{ msg.content }}
  </div>
  <div class="message-content" *ngIf="msg.systemType === 'welcome'">
    <div class="welcome-message-content">{{msg.aiAgent?.lang?.welcomeMessage?.[currentLanguage] ?? ''}}</div>
    <div class="welcome-desc-content">{{msg.aiAgent?.lang?.agentDesc?.[currentLanguage] ?? ''}}</div>
  </div>
  <div class="message-content" *ngIf="msg.systemType === 'suggestion'">
    <div class="suggestion-title">{{ 'dj-我可以帮您：' | translate }}</div>
    <ul class="suggestion-list">
      <li *ngFor="let suggestion of msg.suggestionsList" (click)="handleSelectSuggestion(suggestion)">
        {{ suggestion.lang?.description?.[currentLanguage] ?? suggestion.description ?? '' }}
      </li>
    </ul>
  </div>
</div>
