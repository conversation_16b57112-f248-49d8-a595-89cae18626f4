.message-system{
  padding: 12px 16px;
  font-size: 14px;
  font-weight: normal;

  .message-content {
    white-space: pre-wrap;
    word-break: break-word;
  }

  &.error{
    border-radius: 12px;
    line-height: 20px;
    letter-spacing: normal;
    margin: 0 auto;
    max-width: 100%;
    text-align: center;
    background-color: #FEF1E9;
  }

  &.welcome{
    padding: 12px 16px;
    border-radius: 12px;
    line-height: 20px;
    letter-spacing: normal;
    margin: 0 auto;
    background: linear-gradient(106deg, #F1F3FF 0%, #F6F9FF 53%, #F7F3FF 100%);
    max-width: 100%;
    .message-content {
      & > .welcome-message-content{
        font-size: 20px;
        font-weight: 600;
        line-height: 30px;
        letter-spacing: normal;
        color: #1D1C33;
      }
  
      & > .welcome-desc-content{
        margin-top: 4px;
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: normal;
        color: #1D1C33;
      }

    
    }
  }


  &.suggestion{
    padding: 0 0 12px 0;
    background: #fff;

    .message-content {
      & > .suggestion-title {
        line-height: 20px;
        color: #8C8B99;
      }
  
      & > .suggestion-list {
        & > li {
          margin-top: 12px;
          padding: 10px 16px;
          border: 1px solid #F0F0F5;
          border-radius: 12px;
          white-space: normal;
          cursor: pointer;
          &:hover{
            color: #605CE5;
            border: 1px solid #8783EF;
            background: #EFEFFE;
          }
        }
      }
    }
  }
}