import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
} from '@angular/core';
import { ChatMessageSystem, Suggestion } from '../../config/ai-agent-manage.type';
import Vditor from 'vditor';
import { LocaleService } from '../../../../common/service/locale.service';
import { AiAgentManageService } from 'components/ai-agent-manage/service/ai-agent-manage.service';

@Component({
  selector: 'app-message-system',
  templateUrl: './message-system.component.html',
  styleUrls: ['./message-system.component.less'],
})
export class MessageSystemComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() msg: ChatMessageSystem;
  @Input() loading: boolean = false;
  currentLanguage: string;

  constructor(private localeService: LocaleService, private aiAgentManageService: AiAgentManageService) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngAfterViewInit(): void {}

  handleSelectSuggestion(value: Suggestion) {
    console.log('handleSelectSuggestion:', value);
    this.aiAgentManageService.setChangeMessageInput(value.lang?.value?.[this.currentLanguage] ?? value.value ?? '');
  }
}
