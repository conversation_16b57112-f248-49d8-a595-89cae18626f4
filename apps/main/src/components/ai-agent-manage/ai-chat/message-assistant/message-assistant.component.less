.message-assistant{
  // padding: 12px 16px;
  padding: 0px;
  border-radius: 12px;
  // box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: normal;
  margin-right: auto;
  // background-color: #fff;
  color: #1D1C33;
  // background-color: aqua;

  & > .message-content {
    // white-space: pre-wrap;
    // word-break: break-word;

    .think-state {
      padding: 12px 16px;
      border-radius: 12px;
      background: linear-gradient(104deg, #F1F3FF 0%, #F6F9FF 53%, #F7F3FF 100%);
      margin-bottom: 12px;
      display: inline-flex;
      .think-state-img {
        margin-right: 12px;
        display: inline-block;
        background-size: 100%;
        &.loading {
          width: 38px;
          height: 26px;
          background-image: url('/assets/img/ai-agent-nana-loading.gif');
        }
        &.finish {
          margin-top: 5px;
          width: 16px;
          height: 16px;
          background-image: url('/assets/img/ai-agent-nana-finish.svg');
        }
      }
      .think-state-text {
        font-size: 14px;
        font-weight: normal;
        line-height: 26px;
        letter-spacing: normal;
        color: #8C8B99;
        & > .anticon {
          font-size: 10px;
          margin-left: 12px;
          transition: all 0.3s ease-out;
          &.opened {
            transform: rotate(180deg);
          }
        }
      }
    }

    .think {
      padding: 0px 16px;
      border-left: 4px solid #F7F7FA;
      color: #8C8B99;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      transition: all 0.35s ease-out;
      overflow: scroll;
      max-height: 2000px;
      &::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }
      &.closed{
        max-height: 0;
      }
    }

    ::ng-deep .markdown {
      pre code.hljs {
        max-height: none !important;
      }
    }

    .typing-indicator {
      display: inline-flex;
      align-items: center;
      margin-left: 4px;

      span {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin: 0 2px;
        background-color: #8c8c8c;
        border-radius: 50%;
        opacity: 0.6;
        animation: typing-animation 1.4s infinite ease-in-out both;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }
      }
    }

    @keyframes typing-animation {
      0%, 80%, 100% {
        transform: scale(0.6);
      }
      40% {
        transform: scale(1);
      }
    }
  }

}
