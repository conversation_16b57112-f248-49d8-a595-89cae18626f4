<div class="message-assistant">
  <div class="message-content">
    <div class="think-state" *ngIf="msg?.thinkState !== 'idle'">
      <span
        class="think-state-img"
        [class]="{ loading: msg?.thinkState === 'start', finish: msg?.thinkState === 'end' }"
      ></span>
      <span class="think-state-text"
        >{{ (msg?.thinkState === 'start' ? 'dj-娜娜正在思考中' : 'dj-娜娜思考完成') | translate }}
        <i
          [class]="{ opened: isThinkDetailOpen }"
          adIcon
          iconfont="iconxiajiantou"
          aria-hidden="true"
          (click)="isThinkDetailOpen = !isThinkDetailOpen"
        ></i>
      </span>
    </div>
    <div class="think" [class]="{ closed: !isThinkDetailOpen }" *ngIf="msg?.thinkContent">{{ msg?.thinkContent }}</div>
    <div class="markdown" #messageContainer></div>
    <div *ngIf="loading" class="typing-indicator">
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div>
</div>
