import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
} from '@angular/core';
import { ChatMessageAssistant } from '../../config/ai-agent-manage.type';
import Vditor from 'vditor';
import { LocaleService } from '../../../../common/service/locale.service';

@Component({
  selector: 'app-message-assistant',
  templateUrl: './message-assistant.component.html',
  styleUrls: ['./message-assistant.component.less'],
})
export class MessageAssistantComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() msg: ChatMessageAssistant;
  @Input() loading: boolean = false;
  currentLanguage: string;

  isThinkDetailOpen = true;
  assistantContent = '';

  @ViewChild('messageContainer') messageContainer: ElementRef;

  constructor(private localeService: LocaleService) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.msg) {
    }
  }

  ngAfterViewChecked(): void {
    this.handleMessage();
  }

  handleMessage() {
    if (this.messageContainer && this.assistantContent !== this.msg?.content) {
      // console.log('=======');
      // console.log(this.msg);
      // console.log('=======');
      this.assistantContent = this.msg?.content ?? '';
      Vditor.preview(this.messageContainer?.nativeElement, this.assistantContent, {
        mode: 'light',
      });
    }
  }
}
