<div
  class="ai-chat-container"
  [ngClass]="{ 'no-message': messages.length === 0 }"
  appResizeWidth
  [minWidth]="400"
  [maxWidth]="800"
  [initialWidth]="400"
>
  <div class="chat-header">
    <div class="chat-header-col">
      <!-- <div class="header-icon-button font-size-24">
        <i adIcon iconfont="iconzhankaishouqi"></i>
      </div>
      <div class="new-chat" (click)="clearConversation()">
        <span>{{ 'dj-娜娜新对话' | translate }}</span>
      </div> -->
    </div>
    <div class="chat-header-col">
      <img [src]="aiAgentManageService.currentAiAgent?.logo ?? '/assets/img/ai-agent-icon.svg'" />
      {{ aiAgentManageService.currentAiAgent?.lang?.[currentLanguage]?? aiAgentManageService?.currentAiAgent?.agentName }}
    </div>
    <div class="chat-header-col">
      <div class="header-icon-button" (click)="aiAgentGobalManageService.setIsShowChat(false)">
        <i adIcon iconfont="iconguanbi-xian1" nz-tooltip [nzTooltipTitle]="'dj-关闭' | translate"></i>
      </div>
      <div class="header-icon-button" (click)="aiAgentManageService.toggleIsMaximized()">
        <i
          adIcon
          *ngIf="aiAgentManageService.isMaximized"
          iconfont="iconxiaoping-xian"
          nz-tooltip
          [nzTooltipTitle]="'dj-缩小' | translate"
        ></i>
        <i
          adIcon
          *ngIf="!aiAgentManageService.isMaximized"
          iconfont="iconquanping-xian"
          nz-tooltip
          [nzTooltipTitle]="'dj-放大' | translate"
        ></i>
      </div>
      <div
        class="header-icon-button"
        (click)="clearConversation()"
        nz-tooltip
        [nzTooltipTitle]="'dj-清空对话' | translate"
      >
        <i adIcon iconfont="iconxinduihua-xian"></i>
      </div>
    </div>
  </div>

  <div class="chat-body" #chatContainer autoScroll [autoScrollEnabled]="autoScrollEnabled">
    <div class="chat-message-container" *ngFor="let msg of messages">
      <app-message-user *ngIf="msg.role === 'user'" [msg]="msg"></app-message-user>
      <app-message-assistant
        *ngIf="msg.role === 'assistant'"
        [msg]="msg"
        [loading]="aiAgentManageService.sseloading && msg === messages[messages.length - 1] && msg.role === 'assistant'"
      ></app-message-assistant>
      <app-message-system *ngIf="msg.role === 'system'" [msg]="msg"></app-message-system>
      <app-message-component *ngIf="msg.role === 'component'" [msg]="msg"></app-message-component>
    </div>
  </div>

  <div class="chat-footer">
    <nz-spin [nzSpinning]="isUploading">
      <form [formGroup]="chatForm">
        <div class="input-container">
          <!-- 现在只有图片 -->
          <div class="attachment-wrap" *ngIf="uploadImageUrlList.length > 0">
            <div class="image-item" *ngFor="let url of uploadImageUrlList; let i = index">
              <img [src]="url" alt="Uploaded Image" />
              <i adIcon iconfont="iconshanchu4" (click)="removeImage(i)"></i>
            </div>
          </div>
          <div class="textarea-wrap">
            <textarea
              class="textarea-auto-height"
              nz-input
              formControlName="message"
              [placeholder]="'dj-可以问我任何问题或输入/使用技能' | translate"
              [disabled]="aiAgentManageService.sseloading"
              (keydown.enter)="!$event.shiftKey && sendMessage($event)"
              rows="1"
              autosize
            ></textarea>
            <div class="textarea-mirror">{{ chatForm.value.message }}</div>
          </div>

          <div class="button-group">
            <div class="tool-bar">
              <input #fileInput type="file" accept="image/*" style="display: none" (change)="onFileSelected($event)" />
              <i
                adIcon
                iconfont="icondaochutupian"
                (click)="selectImage()"
                style="cursor: pointer"
                nz-tooltip
                [nzTooltipTitle]="'dj-选择图片' | translate"
              ></i>
            </div>
            <div class="send-message-button" [ngClass]="{ disabled: isDisabled }" (click)="sendMessage()">
              <i adIcon iconfont="iconfasong1"></i>
            </div>
          </div>
        </div>
      </form>
    </nz-spin>
  </div>
</div>
