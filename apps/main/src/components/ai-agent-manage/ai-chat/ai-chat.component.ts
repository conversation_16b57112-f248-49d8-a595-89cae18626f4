import { <PERSON>mpo<PERSON>, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  ChatMessage,
  ChatResponse,
  isOtherResponseData,
  isChatMessageAssistant,
  resultActionMessage,
} from '../config/ai-agent-manage.type';
import { AiChatSseService } from '../service/ai-chat-sse.service';

import { AiAgentManageService } from '../service/ai-agent-manage.service';
import { AiAgentGobalManageService } from '../service/ai-agent-manage-gobal.service';
import { AiAgentManageRequestService } from '../service/ai-agent-manage-request.service';

import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';
import { AdUserService } from 'pages/login/service/user.service';
import { UUID } from 'angular2-uuid';
import { appCreateMockData, modelCreateMockData } from '../config/ai-agent-manage.config';

@Component({
  selector: 'app-ai-chat',
  templateUrl: './ai-chat.component.html',
  styleUrls: ['./ai-chat.component.less'],
  providers: [AiChatSseService],
})
export class AiChatComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('chatContainer') chatContainer: ElementRef;
  @ViewChild('fileInput') fileInput: ElementRef;

  chatForm: FormGroup;
  messages: ChatMessage[] = [];
  isThink = false; // 当前的回复是否是思考
  conversationId: string | null = null;
  currentLanguage: string;
  iamToken: string;
  _currentAiAgent: any;

  isUploading = false;
  uploadImageTypeList = ['image/pdf', 'image/png', 'image/jpg', 'image/bmp', 'image/jpeg'];
  uploadImageUrlList = [];

  private _changeMessageInput$: Subscription; // 改变消息输入框内容
  private _chatSse$: Subscription = new Subscription();

  get autoScrollEnabled(): boolean {
    return this.aiAgentManageService?.sseloading || this.aiAgentManageService.isExecuting;
  }

  get isDisabled(): boolean {
    return (
      this.aiAgentManageService.sseloading || this.aiAgentManageService.isExecuting || !this.chatForm.value.message
    );
  }

  constructor(
    private fb: FormBuilder,
    private aiChatSseService: AiChatSseService,
    private message: NzMessageService,
    public aiAgentManageService: AiAgentManageService,
    public translateService: TranslateService,
    private localeService: LocaleService,
    private userService: AdUserService,
    public aiAgentGobalManageService: AiAgentGobalManageService,
    private aiAgentManageRequestService: AiAgentManageRequestService,
  ) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
    this.iamToken = this.userService.getUser('iamToken');
  }

  ngOnInit(): void {
    this._changeMessageInput$ = this.aiAgentManageService.changeMessageInput$.subscribe((message: string) => {
      this.chatForm?.patchValue({ message });
    });
    this.handleInit();
  }

  ngAfterViewChecked(): void {}

  ngOnDestroy(): void {
    this._chatSse$.unsubscribe();
    this.aiChatSseService.closeConnection();
    this._changeMessageInput$.unsubscribe();
  }

  // ======= 接口请求 =======
  // 上传文件
  shareUploadFileRequest(params: unknown): Promise<unknown> {
    return this.aiAgentManageRequestService.shareUploadFile(params).toPromise();
  }

  // ======= 接口处理 =======
  // 上传文件逻辑
  async handleShareUploadFileRequest(formData: FormData): Promise<void> {
    try {
      this.isUploading = true;
      this.aiAgentManageService.setIsExecuting(true);
      let shareUploadFileRequestRes: any = await this.shareUploadFileRequest(formData);
      this.uploadImageUrlList.push(shareUploadFileRequestRes.data);
      this.message.success(this.translateService.instant('dj-操作成功'));
    } catch (error) {
      console.log('handleConfirm error:', error);
    } finally {
      this.isUploading = false;
      this.aiAgentManageService.setIsExecuting(false);
    }
  }

  // ======= 其他业务逻辑 =======

  handleInit() {
    this.chatForm = this.fb.group({
      message: ['', [Validators.required]],
    });

    // 初始化系统消息
    if (this.aiAgentManageService.currentAiAgent) {
      this.addMessage({
        role: 'system',
        systemType: 'welcome',
        aiAgent: this.aiAgentManageService.currentAiAgent,
        timestamp: Date.now(),
        id: UUID.UUID(),
      });

      if (this.aiAgentManageService.currentAiAgent.suggestionsList.length > 0) {
        this.addMessage({
          role: 'system',
          systemType: 'suggestion',
          suggestionsList: this.aiAgentManageService.currentAiAgent.suggestionsList,
          timestamp: Date.now(),
          id: UUID.UUID(),
        });
      }

      setTimeout(() => {
        this.addMessage({
          role: 'component',
          content: {
            componetType: 'modelCreate',
            componetContent: { ...modelCreateMockData },
          },
          timestamp: Date.now(),
          id: UUID.UUID(),
        });
      }, 20000);
    }
  }

  // 这里的滚动到最新位置的逻辑比较复杂
  // 1是被动逻辑，autoScroll 指令，主要处理message 的update
  // 当正在接收 sse 推流时，由于是一个字一个字 吐的，update到当前message，所以只要 当前 列表所属位置 在 阈值 50 以内，就自动 到最新位置
  // 2是用户新增消息，那么直接跳到最新位置
  // 3是收到其他消息（系统，智能体等），正常来说，由于autoScroll 指令的存在，也会自动到最新位置
  // 但是 如果 某个智能体的 message 是一大块，或者 这个 message 解析为一个 比较大的 组件，那么 滚动位置 瞬间就会超越阈值 50
  // 这样 autoScroll 就会失效
  // 所以 我们在 addMessage时会去判断，当前是否在阈值 50 以内，如果在那就在 addMessage 后滚动到最新位置
  addMessage(chatMessage: ChatMessage) {
    const { scrollHeight, scrollTop, clientHeight } = this.chatContainer?.nativeElement || {};
    const isPostionNeedScrollToBottom = scrollHeight - scrollTop - clientHeight < 50;

    // 增加用户发送的消息一定跳到最新位置
    const isScrollToBottom = chatMessage.role === 'user' || isPostionNeedScrollToBottom;
    this.messages.push(chatMessage);

    if (isScrollToBottom) {
      setTimeout(() => {
        this.scrollToBottom();
      }, 0);
    }
  }

  /**
   * 发送消息
   */
  sendMessage($event?: Event): void {
    if ($event) $event.preventDefault();
    if (this.chatForm.invalid || this.isDisabled) {
      return;
    }

    const userMessage = this.chatForm.value.message.trim();

    // 添加用户消息到消息列表
    this.addMessage({
      role: 'user',
      content: userMessage,
      timestamp: Date.now(),
      id: UUID.UUID(),
    });

    this.resetCurrentChatContent();

    // 准备助手回复的占位
    this.aiAgentManageService.setSseloading(true);
    this.addMessage({
      role: 'assistant',
      content: '',
      thinkState: 'idle',
      timestamp: Date.now(),
      id: UUID.UUID(),
      nodeId: '',
    });
    this.isThink = false; // 当前的回复是否是思考

    const sub = this.aiChatSseService
      .sendMessage(userMessage, this.aiAgentManageService.currentAiAgent, this.iamToken, this.conversationId)
      .subscribe(this.handleChatResponse());

    this._chatSse$.add(sub);
  }

  // 检查消息列表，清理无用的占位消息
  private checkMessages() {
    let currentMessage = this.messages[this.messages.length - 1];
    if (currentMessage.role === 'assistant' && currentMessage.nodeId === '') {
      // 如果只是一个空的占位消息，则删除它
      this.messages.pop();
    }
  }

  /**
   * 处理聊天响应
   */
  private handleChatResponse() {
    return {
      next: (response: ChatResponse) => {
        console.log('Chat response:==============', response);
        if (isOtherResponseData(response)) {
          const { otherResponseType } = response;
          if (otherResponseType === 'onclose') {
            this.aiAgentManageService.setSseloading(false);
            this.checkMessages();
            return;
          }
          return;
        }

        let { action, message, nodeId, sessionId } = response;
        if (action === 'error' || action === 'failed') {
          this.message.error(`error:${message}`);
          this.aiAgentManageService.setSseloading(false);
          this.checkMessages();
          this.addMessage({
            role: 'system',
            systemType: 'error',
            content: message,
            timestamp: Date.now(),
            id: UUID.UUID(),
          });
          return;
        }

        if (action === 'result') {
          const resultData: resultActionMessage = JSON.parse(message);
          console.log('result:', resultData);
          const dataList = resultData.data || [];

          dataList.forEach((data) => {
            const { type, value } = data;
            if (type === 'appCreate') {
              this.addMessage({
                role: 'component',
                content: {
                  componetType: 'appCreate',
                  componetContent: value,
                },
                timestamp: Date.now(),
                id: UUID.UUID(),
              });
            }
          });

          return;
        }

        let currentMessage = this.messages[this.messages.length - 1];
        if (!isChatMessageAssistant(currentMessage) || !message || action !== 'answer') return;
        const isStartThinking = message === '<think>';
        const isEndThinking = message === '</think>';
        if (isStartThinking || isEndThinking) message = '';

        // 更新会话ID
        if (sessionId) this.conversationId = response.sessionId;

        // 更新当前响应
        if (nodeId !== currentMessage.nodeId) {
          this.checkMessages();
          currentMessage = {
            role: 'assistant',
            content: '',
            thinkContent: '',
            thinkState: 'idle',
            id: UUID.UUID(),
            timestamp: Date.now(),
            nodeId,
          };
          this.addMessage(currentMessage);
        }

        if (isStartThinking) currentMessage.thinkState = 'start';
        if (isEndThinking) currentMessage.thinkState = 'end';
        if (currentMessage.thinkState === 'start') {
          currentMessage.thinkContent += message;
        } else {
          currentMessage.content += message;
        }

        // TODO
        // 检查是否完成
        // if (status === 'done') {
        //   this.loading = false;
        // }
      },
      error: (error: any) => {
        console.error('Chat SSE error:', error);
        this.message.error('连接出错，请稍后重试');
        this.aiAgentManageService.setSseloading(false);
        this.checkMessages();
        this.addMessage({
          role: 'system',
          systemType: 'error',
          content: error.message ?? error ?? '连接出错，请稍后重试',
          timestamp: Date.now(),
          id: UUID.UUID(),
        });
      },
    };
  }

  /**
   * 中止当前对话
   */
  abortConversation(): void {
    if (!this.aiAgentManageService.sseloading || !this.conversationId) {
      return;
    }
    this.aiChatSseService.closeConnection();
    this.aiAgentManageService.setSseloading(false);
    this.message.info('已停止生成回复');
    this.checkMessages();
  }

  resetCurrentChatContent() {
    this.chatForm.reset();
    this.uploadImageUrlList = [];
  }

  /**
   * 清空对话
   */
  clearConversation(): void {
    this.abortConversation();
    this.messages = [
      {
        role: 'system',
        systemType: 'welcome',
        aiAgent: this.aiAgentManageService.currentAiAgent,
        timestamp: Date.now(),
        id: UUID.UUID(),
      },
    ];
    this.isThink = false;
    this.conversationId = null;
    this.aiAgentManageService.setSseloading(false);
    this.aiChatSseService.closeConnection();
    this.resetCurrentChatContent();
  }

  private scrollToBottom(oldScroll?: number): void {
    try {
      this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  /**
   * 选择图片
   */
  selectImage(): void {
    this.fileInput.nativeElement.click();
  }

  /**
   * 文件选择
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // 验证文件类型
      if (!this.uploadImageTypeList.includes(file.type)) {
        this.message.error(this.translateService.instant('dj-请选择符合标准的图片格式'));
        return;
      }

      // 验证文件大小 (限制为4MB)
      const maxSize = 4 * 1024 * 1024; // 4MB
      if (file.size > maxSize) {
        this.message.error('dj-图片大小不能超过4MB');
        return;
      }

      this.uploadImage(file);
    }
  }

  /**
   * 上传图片
   */
  uploadImage(file: File): void {
    const formData = new FormData();
    formData.append('file', file);
    this.handleShareUploadFileRequest(formData);
  }

  removeImage(index: number) {
    this.uploadImageUrlList.splice(index, 1);
  }
}
