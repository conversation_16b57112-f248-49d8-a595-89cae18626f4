import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
} from '@angular/core';
import { ChatMessageComponent, ChatComponent } from '../../config/ai-agent-manage.type';
import { LocaleService } from '../../../../common/service/locale.service';
import { AiAgentManageService } from '../../service/ai-agent-manage.service';
import { StateType } from './components/message-component-app-create/message-component-app-create.type';

@Component({
  selector: 'app-message-component',
  templateUrl: './message-component.component.html',
  styleUrls: ['./message-component.component.less'],
})
export class MessageComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() msg: ChatMessageComponent;
  @Input() loading: boolean = false;
  currentLanguage: string;

  get chatComponent(): ChatComponent {
    return this.msg.content;
  }

  constructor(private localeService: LocaleService, private aiAgentManageService: AiAgentManageService) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngAfterViewInit(): void {}

  handleChangeState(state: StateType) {
    console.log('handleChangeState:', state);
    this.aiAgentManageService.setIsExecuting(state === StateType.EXECUTING);
  }
}
