import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class MessageComponentAppCreateRequestService {
  adesignerUrl: string;

  constructor(protected configService: SystemConfigService, protected http: HttpClient) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }
  /**
   * 创建业务对象
   * @param params
   * @returns
   */
  businessDirAdd(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/businessDir/add`;
    return this.http.post(url, params);
  }

  /** 生成查询方案接口 */
  generateQueryPlan(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/generateQueryPlan`;
    return this.http.post(url, params);
  }
}
