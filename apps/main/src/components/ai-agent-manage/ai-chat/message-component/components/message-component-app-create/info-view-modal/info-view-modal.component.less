@import url(./jse-theme.css);

::ng-deep .info-view-modal {
  .ant-modal-content{
    .ant-modal-header{
      padding: 0 !important;
      .ant-modal-title {
        height: 60px;
        width: 100%;
        padding: 12px 24px;
        border-bottom: 1px solid #DFDFE5;
        .title-tabs{
          display: flex;
          .title-tab-item{
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 0 16px;
            line-height: 34px;
            box-sizing: content-box;
            color: #1D1C33;
            border: 1px solid #D0D0D9;
            cursor: pointer;
            font-size: 14px;
            font-weight: normal;
            &.active{
              color: #605CE5;
              border-color: #605CE5;
              background: #EFEFFE;
            }
      
            &:nth-of-type(1){
              border-radius: 4px 0px 0px 4px;
            }
      
            &:nth-of-type(2){
              border-radius: 0px 4px 4px 0px;
            }
          }  
        } 
      }
    }
  
  
    .ant-modal-body {
      padding: 20px 24px;
      .JSON-Editor {
        height: 500px;
        overflow-y: auto;
      }
  
      .preview-content{
        height: 500px;
        width: 100%;
        background-color: #605CE5;
      }
  
    }
    
  
    & > .ant-modal-footer {
      padding: 16px 24px;
      border-top: 1px solid #DFDFE5;
      display: flex;
      flex-wrap: nowrap;
      justify-content: flex-end;
      align-items: center;
    }

  }

}

