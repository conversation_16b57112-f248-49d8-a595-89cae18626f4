import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { AppCreateChatComponent, ModelCreateChatComponent } from '../../../../config/ai-agent-manage.type';
import { LocaleService } from '../../../../../../common/service/locale.service';
import {} from './message-component-model-create.type';
import { MessageComponentModelCreateRequestService } from './message-component-model-create-request.service';
import { AiAgentGobalManageService } from 'components/ai-agent-manage/service/ai-agent-manage-gobal.service';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { getFlatModelList, renameModel } from '../../../../utils/tools';
import { cloneDeep } from 'lodash';
import { StateType } from '../message-component-app-create/message-component-app-create.type';

@Component({
  selector: 'app-message-component-model-create',
  templateUrl: './message-component-model-create.component.html',
  styleUrls: ['./message-component-model-create.component.less'],
  providers: [MessageComponentModelCreateRequestService],
})
export class MessageComponentModelCreate implements OnInit, OnChanges, AfterViewInit {
  @Input() value: ModelCreateChatComponent;
  @Input() loading: boolean = false;

  @Output() changeState = new EventEmitter<StateType>();

  currentLanguage: string;

  status?: 'generating' | 'success' | 'retry';

  // 组件信息
  get componetContent() {
    return this.value.componetContent;
  }

  constructor(
    private localeService: LocaleService,
    private messageComponentModelCreateRequestService: MessageComponentModelCreateRequestService,
    private aiAgentGobalManageService: AiAgentGobalManageService,
    private translateService: TranslateService,
    private router: Router,
  ) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngAfterViewInit(): void {
    console.log('ngAfterViewInit:', this.value);
  }

  businessDirAddRequest(params: unknown): Promise<unknown> {
    return this.messageComponentModelCreateRequestService.businessDirAdd(params).toPromise();
  }

  handleCallback(value) {
    console.log('handleCallback:', value);
    setTimeout(() => {
      this.status = 'generating';
      this.changeState.emit(StateType.EXECUTING);
    }, 0);
    setTimeout(() => {
      this.status = 'success';
      this.changeState.emit(StateType.SUCCESS);
      if (this.componetContent.scene === 'business') {
        this.aiAgentGobalManageService.setGenerateBusinessModel(value);
      } else {
        this.aiAgentGobalManageService.setGenerateFieldModel(value);
      }
    }, 5000);
    setTimeout(() => {
      this.status = 'retry';
      this.changeState.emit(StateType.ERROR);
    }, 10000);
  }
}
