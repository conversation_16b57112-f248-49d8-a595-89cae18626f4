import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { AppCreateChatComponent } from '../../../../config/ai-agent-manage.type';
import { LocaleService } from '../../../../../../common/service/locale.service';
import {
  AppCreateProcess,
  StateType,
  ProcessStepType,
  AppCreateProcessStepBase,
} from './message-component-app-create.type';
import { MessageComponentAppCreateRequestService } from './message-component-app-create-request.service';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { getFlatModelList, renameModel } from '../../../../utils/tools';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-message-component-app-create',
  templateUrl: './message-component-app-create.component.html',
  styleUrls: ['./message-component-app-create.component.less'],
  providers: [MessageComponentAppCreateRequestService],
})
export class MessageComponentAppCreate implements OnInit, OnChanges, AfterViewInit {
  @Input() value: AppCreateChatComponent;
  @Input() loading: boolean = false;

  @Output() changeState = new EventEmitter<StateType>();

  StateType = StateType;
  currentLanguage: string;
  addAppVisible = false; // 是否显示添加应用弹窗

  // 当前这个创建应用的过程只有三个步骤，创建应用，创建对象，创建查询方案和作业，且这三个步骤是前后依赖的
  // TODO 之后为了解耦，我们会调用统一的后端的一个接口，通过sse获得最详细的创建过程
  appCreateProcess: AppCreateProcess;

  createAppData = {};

  viewData = {
    // 代码界面的代码数据
    codeData: {},
    modelList: [],
  };
  showCodeModal: boolean = false;

  get appCreatePercent() {
    if (!this.appCreateProcess) return 0;
    const finishSteps = this.appCreateProcess?.appCreateProcessStepList?.filter((processItem) =>
      [StateType.SUCCESS, StateType.ERROR].includes(processItem.state),
    );
    return Math.round((finishSteps.length / this.appCreateProcess.appCreateProcessStepList.length) * 100);
  }

  get resultText() {
    if (this.appCreateProcess?.gobalState === StateType.SUCCESS) return this.translateService?.instant('dj-成功');
    if (this.appCreateProcess?.gobalState === StateType.ERROR) return this.translateService?.instant('dj-失败');
    return `${this.appCreatePercent}%`;
  }

  get progressColor() {
    if (this.appCreateProcess?.gobalState === StateType.EXECUTING) return '#605CE5';
    return this.appCreateProcess?.gobalState === StateType.ERROR ? '#EB2F2F' : '#00B042';
  }

  // 组件信息
  get componetContent() {
    return this.value.componetContent;
  }

  constructor(
    private localeService: LocaleService,
    private messageComponentAppCreateRequestService: MessageComponentAppCreateRequestService,
    private translateService: TranslateService,
    private router: Router,
  ) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngAfterViewInit(): void {
    console.log('ngAfterViewInit:', this.value);
    this.initAppCreateProcess();
  }

  businessDirAddRequest(params: unknown): Promise<unknown> {
    return this.messageComponentAppCreateRequestService.businessDirAdd(params).toPromise();
  }

  generateQueryPlanRequest(params: unknown): Promise<unknown> {
    return this.messageComponentAppCreateRequestService.generateQueryPlan(params).toPromise();
  }

  addProcessStep(appCreateProcessStepBase: AppCreateProcessStepBase) {
    this.appCreateProcess.appCreateProcessStepList.push({
      ...appCreateProcessStepBase,
      index: this.appCreateProcess.appCreateProcessStepList.length,
    });
  }

  initAppCreateProcess() {
    this.appCreateProcess = {
      processShareData: {},
      gobalState: StateType.IDLE,
      currentStepIndex: -1,
      appCreateProcessStepList: [
        {
          index: 0,
          type: ProcessStepType.APP_INIT, // 初始化应用
          description: `${this.translateService.instant('dj-生成应用')}[${
            this.componetContent.appInfo.lang?.[this.currentLanguage] ?? this.componetContent.appInfo.name ?? ''
          }]`,
          state: StateType.IDLE,
          stepInfo: { appInfo: this.componetContent.appInfo },
        },
      ],
    };

    this.componetContent.businessInfo.forEach((businessInfo) => {
      this.addProcessStep({
        type: ProcessStepType.BUSINESS_ADD, // 创建业务对象
        description: `${this.translateService.instant('dj-生成业务对象')}[${
          businessInfo.lang?.name?.[this.currentLanguage] ?? businessInfo.name ?? ''
        }]`,
        state: StateType.IDLE,
        stepInfo: { businessInfo },
      });
      this.addProcessStep({
        type: ProcessStepType.GENERATE_QUERY_PLAN, // 创建查询方案和作业
        description: `${this.translateService.instant('dj-生成查询方案')}[${
          businessInfo.lang?.name?.[this.currentLanguage] ?? businessInfo.name ?? ''
        }]`,
        state: StateType.IDLE,
        stepInfo: { businessInfo },
      });
      this.addProcessStep({
        type: ProcessStepType.GENERATE_PAGE_DESIGN, // 创建作业
        description: `${this.translateService.instant('dj-生成作业')}[${
          businessInfo.lang?.name?.[this.currentLanguage] ?? businessInfo.name ?? ''
        }]`,
        state: StateType.IDLE,
        stepInfo: {},
      });
    });
  }

  handleSubmit() {
    this.createAppData = {
      appType: 5,
      name: this.componetContent.appInfo.name,
      description: this.componetContent.appInfo.description,
      lang: this.componetContent.appInfo.lang,
    };
    this.addAppVisible = true;
  }

  handleOpenView() {
    this.viewData = {
      codeData: cloneDeep(this.componetContent),
      modelList: getFlatModelList(
        cloneDeep(this.componetContent?.businessInfo?.map((businessInfo) => businessInfo.model) ?? []),
      ),
    };

    this.showCodeModal = true;
  }

  handleCloseView() {
    this.showCodeModal = false;
  }

  handleSubmitView() {
    console.log('handleCodeModal');
    this.handleCloseView();
    this.handleSubmit();
  }

  onAfterCreated(app) {
    console.log('success');
    console.log('app:', app);
    this.handleNextStep(app.application);
  }

  // 执行下一步
  // 如果是非异步逻辑，可以直接传入下一步的value，切换到下一步时，直接赋值，同时改变状态，跳到之后的步骤
  // 当前创建应用就是这个逻辑，会用到nextStepValue这个入参
  // 因为创建应用弹窗点击确定后，并没有事件返回（由于前端触发逻辑仅是暂时逻辑，所以不想改造历史组件逻辑）
  // 所以触发创建应用弹窗的onAfterCreated时，其实应用已经创建成功了
  async handleNextStep(nextStepValue?) {
    // appCreateProcess 队列开始执行
    if (this.appCreateProcess.gobalState === StateType.IDLE) this.appCreateProcess.gobalState = StateType.EXECUTING;

    // process非执行状态
    if (this.appCreateProcess.gobalState !== StateType.EXECUTING) return;

    const currentStep = this.appCreateProcess.appCreateProcessStepList[this.appCreateProcess.currentStepIndex];
    // 当前步骤未执行完毕
    if (currentStep && currentStep.state === StateType.EXECUTING) return;

    // 队列执行完毕
    if (this.appCreateProcess.currentStepIndex >= this.appCreateProcess.appCreateProcessStepList.length - 1) {
      this.appCreateProcess.gobalState = this.appCreateProcess.appCreateProcessStepList.every(
        (processItem) => processItem.state === StateType.SUCCESS,
      )
        ? StateType.SUCCESS
        : StateType.ERROR;
      return;
    }

    // TODO
    this.changeState.emit(this.appCreateProcess.gobalState);

    this.appCreateProcess.currentStepIndex++;
    const nextStep = this.appCreateProcess.appCreateProcessStepList[this.appCreateProcess.currentStepIndex];

    nextStep.state = StateType.EXECUTING;

    switch (nextStep.type) {
      case ProcessStepType.APP_INIT:
        this.appCreateProcess.processShareData[ProcessStepType.APP_INIT] = nextStepValue;
        nextStep.resultInfo = nextStepValue;
        nextStep.state = StateType.SUCCESS;
        break;
      case ProcessStepType.BUSINESS_ADD:
        try {
          const application = this.appCreateProcess.processShareData[ProcessStepType.APP_INIT];
          const businessInfo = nextStep.stepInfo.businessInfo;
          const aiModel = businessInfo.model;
          aiModel.name = `${application?.code}_${aiModel.name}`;
          renameModel(aiModel, application?.code);

          const businessDirAddRequestRes: any = await this.businessDirAddRequest({
            addSourceType: 'object_perspective',
            aiModel: aiModel,
            application: application?.code,
            code: `${application?.code}_${businessInfo.code}`,
            createType: 'ai',
            customProperties: {},
            description: businessInfo.description,
            lang: businessInfo.lang,
            modelType: 'basic',
            name: businessInfo.name,
            serviceCode: application.serviceCode[0],
          });

          nextStep.state = StateType.SUCCESS;
          console.log('businessDirAddRequest:', businessDirAddRequestRes);
          this.appCreateProcess.processShareData[ProcessStepType.BUSINESS_ADD] =
            this.appCreateProcess.processShareData[ProcessStepType.BUSINESS_ADD] ?? [];
          this.appCreateProcess.processShareData[ProcessStepType.BUSINESS_ADD].push(businessDirAddRequestRes.data);
        } catch (error) {
          console.log('handleConfirm error:', error);
          nextStep.state = StateType.ERROR;
        } finally {
        }

        break;
      case ProcessStepType.GENERATE_QUERY_PLAN:
        const application = this.appCreateProcess.processShareData[ProcessStepType.APP_INIT];
        const pathString =
          this.appCreateProcess.processShareData[ProcessStepType.BUSINESS_ADD][
            this.appCreateProcess.processShareData[ProcessStepType.BUSINESS_ADD].length - 1
          ]?.modelPath;
        const pathList = pathString?.split('/');
        try {
          const generateQueryPlanRequestRes: any = await this.generateQueryPlanRequest({
            addSourceType: 'object_perspective',
            application: application?.code,
            businessCode: pathList[pathList.length - 3],
            modelId: pathList[pathList.length - 2],
            operateType: 'first',
            serviceCode: application.serviceCode[0],
          });

          nextStep.state = StateType.SUCCESS;
          console.log('generateQueryPlanRequestRes:', generateQueryPlanRequestRes);

          this.appCreateProcess.processShareData[ProcessStepType.GENERATE_QUERY_PLAN] =
            this.appCreateProcess.processShareData[ProcessStepType.GENERATE_QUERY_PLAN] ?? [];
          this.appCreateProcess.processShareData[ProcessStepType.GENERATE_QUERY_PLAN].push(
            generateQueryPlanRequestRes.data,
          );
        } catch (error) {
          console.log('handleConfirm error:', error);
          nextStep.state = StateType.ERROR;
        } finally {
        }
        break;
      case ProcessStepType.GENERATE_PAGE_DESIGN:
        // 首先必须注意的是，这里所有逻辑都是 暂时的，最后都应该是 后端逻辑，然后 通过sse 获取状态
        // 然后这里，其实在上一个 generateQueryPlanRequest 接口中 就 同时创建了 查询方案 和 作业
        // 但是在这里 为了 demo的 体验，人为增加了 生成 作业 这一步
        await new Promise((resolve) => setTimeout(resolve, 1000));

        this.appCreateProcess.processShareData[ProcessStepType.GENERATE_PAGE_DESIGN] =
          this.appCreateProcess.processShareData[ProcessStepType.GENERATE_PAGE_DESIGN] ?? [];
        this.appCreateProcess.processShareData[ProcessStepType.GENERATE_PAGE_DESIGN].push({});
        nextStep.resultInfo = nextStepValue;
        nextStep.state = StateType.SUCCESS;
        break;

      default:
        break;
    }

    this.handleNextStep();
  }

  onVisibleChange(visible) {
    this.addAppVisible = visible;
  }

  handleJumpApp() {
    console.log('handleJumpApp');
    const application = this.appCreateProcess.processShareData[ProcessStepType.APP_INIT];
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['app'], { queryParams: { appCode: application?.code } }),
    );
    window.open(url, '_blank');
  }

  handleRebuild() {
    console.log('handleRebuild');
  }
}
