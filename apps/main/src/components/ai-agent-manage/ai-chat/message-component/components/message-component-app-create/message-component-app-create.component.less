.message-component-app-create {
  width: 100%;
  .app-create-card {
    width: 100%;
    border-radius: 12px;
    padding: 24px;
    background: linear-gradient(180deg, #bab8ffe2 0%,#bab8ff55 30%, rgb(255, 255, 255) 100%);
    position: relative;
    overflow: hidden;
    border: 1px solid #E0DFFD;

    .background-elements {
      position: absolute;
      background: linear-gradient(138deg, #5A55FF 37%, rgba(246, 238, 255, 0) 100%);
  
      &:nth-of-type(1) {
        width: 66px;
        height: 66px;
        left: -15px;
        top:-10px;
        border-radius: 50%;
        opacity: 0.3;
      }
  
      &:nth-of-type(2) {
        width: 66px;
        height: 66px;
        right: -12px;
        top:-20px;
        border-radius: 50%;
        opacity: 0.3;
      }
  
      &:nth-of-type(3) {
        width: 22px;
        height: 22px;
        right: 57px;
        top:52px;
        border-radius: 50%;
        opacity: 0.3;
      }
  
      &:nth-of-type(4) {
        width: 100%;
        height: 100%;
        left: 0;
        top:0;
        background: linear-gradient(252deg, #EBEDFF -1%, #FAFBFF 100%);
      }
      
    }
    
    .title {
      width: 100%;
      display: flex;
      span {
        display: inline-block;
        max-width: 80%;
        word-wrap: break-word;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      span:nth-of-type(1){
        font-size: 20px;
        font-weight: 600;
        line-height: 30px;
        text-align: left;
        letter-spacing: normal;
        color: #1D1C33;
      }

      span:nth-of-type(2){
        height: 18px;
        line-height: 16px;
        padding: 0px 4px;
        margin-left: 4px;
        border-radius: 4px;
        align-self: center;
        justify-self: center;
        background: #ECFEEC;
        box-sizing: border-box;
        border: 1px solid #33CF5E;
        font-size: 12px;
        color: #00B042;
      }
     
    }
  
    .toolbar{
      margin-top: 20px;
      display: flex;
      gap: 4px;
    }
  }

  .app-create-process {
    margin-top: 16px;

    .progress {
      padding: 12px 0;
      display: flex;
      gap: 4px;

      .progress-bar{
        flex:  1;
      }

      .result{
        width: 56px;
        flex-basis: 56px;
        flex-shrink: 0;
        flex-grow: 0;
        color: #1D1C33;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        letter-spacing: normal;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding-left: 4px;

        &.success {
          color: #00B042;
          .result-text{
            color: #1D1C33;
          }
        }

        &.error {
          color: #EB2F2F;
        }

        .result-text{
        
        }
        
      }
    }

    .process-step-list {
      .process-step {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        gap: 8px;
        border-radius: 20px;
        background: #F7F7FA;
        font-size: 13px;
        line-height: 20px;
        color: #1D1C33;
        margin-bottom: 4px;

        & > .step-state {
          flex-grow: 0;
          flex-shrink: 0;
          flex-basis: 16px;
          width: 16px;
          font-size: 16px;
          &.success {
            color: #605CE5;
          }
          &.error {
            color: #EB2F2F;
          }
          &.executing {
            color: #605CE5;
          }
        }
        
        & > .step-description {
          flex:1;
          word-wrap: break-word;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          &.error{
            color: #C2C2CC;
          }
        }

        
      }
    }

    .process-result {
      
    }
  }
}