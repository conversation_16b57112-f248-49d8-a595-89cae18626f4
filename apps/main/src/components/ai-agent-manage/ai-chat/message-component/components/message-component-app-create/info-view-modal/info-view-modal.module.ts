import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { InfoViewModalComponent } from './info-view-modal.component';
import { JsonEditorModule } from 'components/bussiness-components/json-editor/json-editor.module';
import { DirectiveModule } from 'common/directive/directive.module';
import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { ModelRelationPureModule } from 'components/bussiness-components/model-relation-pure/model-relation-pure.module';
import { NzIconModule } from 'ng-zorro-antd/icon';

@NgModule({
  declarations: [InfoViewModalComponent],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    AdModalModule,
    AdButtonModule,
    JsonEditorModule,
    DirectiveModule,
    AdTabsModule,
    NzSpinModule,
    AdIconModule,
    ModelRelationPureModule,
    NzIconModule,
  ],
  exports: [InfoViewModalComponent],
  providers: [],
})
export class InfoViewModalModule {}
