interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

export enum StateType {
  'IDLE' = 'IDLE',
  'EXECUTING' = 'EXECUTING',
  'SUCCESS' = 'SUCCESS',
  'ERROR' = 'ERROR',
}

export enum ProcessStepType {
  'APP_INIT' = 'APP_INIT', // 初始化应用
  'BUSINESS_ADD' = 'BUSINESS_ADD', // 创建业务对象
  'GENERATE_QUERY_PLAN' = 'GENERATE_QUERY_PLAN', // 生成查询方案
  'GENERATE_PAGE_DESIGN' = 'GENERATE_PAGE_DESIGN', // 生成作业
}

export interface AppCreateProcess {
  processShareData: any; // 步骤间共享的数据
  gobalState: StateType;
  currentStepIndex: number;
  appCreateProcessStepList: AppCreateProcessStep[];
}

export interface AppCreateProcessStepBase {
  description: string;
  type: ProcessStepType;
  state: StateType;
  stepInfo: any; // 步骤的其他信息
  resultInfo?: any; // 步骤完成后的赋值
}

export interface AppCreateProcessStep extends AppCreateProcessStepBase {
  index: number;
}
