<div class="message-component-app-create">
  <div class="app-create-card">
    <div class="background-elements"></div>
    <div class="background-elements"></div>
    <div class="background-elements"></div>

    <div class="title">
      <span>{{ componetContent?.appInfo?.name }}</span>
      <span *ngIf="appCreateProcess?.gobalState !== StateType.IDLE">{{ 'dj-已采纳' | translate }}</span>
    </div>
    <div class="toolbar">
      <button
        ad-button
        adType="primary"
        *ngIf="appCreateProcess?.gobalState === StateType.IDLE"
        (click)="handleSubmit()"
      >
        {{ 'dj-采纳' | translate }}
      </button>
      <button
        ad-button
        [adType]="appCreateProcess?.gobalState !== StateType.IDLE ? 'primary' : 'default'"
        (click)="handleOpenView()"
      >
        {{ 'dj-查看' | translate }}
      </button>
    </div>
  </div>
  <div class="app-create-process" *ngIf="appCreateProcess?.gobalState !== StateType.IDLE">
    <div class="progress">
      <nz-progress
        class="progress-bar"
        [nzPercent]="appCreatePercent"
        [nzShowInfo]="false"
        [nzStrokeColor]="progressColor"
      ></nz-progress>
      <span
        class="result"
        [ngClass]="{
          success: appCreateProcess?.gobalState === StateType.SUCCESS,
          error: appCreateProcess?.gobalState === StateType.ERROR
        }"
      >
        <i
          nz-icon
          class="result-icon"
          *ngIf="appCreateProcess?.gobalState === StateType.SUCCESS"
          nzType="check-circle"
          nzTheme="fill"
        ></i>
        <i
          nz-icon
          class="result-icon"
          *ngIf="appCreateProcess?.gobalState === StateType.ERROR"
          nzType="close-circle"
          nzTheme="fill"
        ></i>
        <span class="result-text">{{ resultText }}</span>
      </span>
    </div>
    <div class="process-step-list">
      <ng-container *ngFor="let item of appCreateProcess?.appCreateProcessStepList">
        <div class="process-step" *ngIf="item.state !== StateType.IDLE">
          <i
            nz-icon
            class="step-state success"
            *ngIf="item.state === StateType.SUCCESS"
            nzType="check-circle"
            nzTheme="fill"
          ></i>
          <i
            nz-icon
            class="step-state error"
            *ngIf="item.state === StateType.ERROR"
            nzType="close-circle"
            nzTheme="fill"
          ></i>
          <i
            nz-icon
            class="step-state executing"
            *ngIf="item.state === StateType.EXECUTING"
            nzType="loading"
            nzTheme="outline"
          ></i>
          <span class="step-description">{{ item.description }}</span>
        </div>
      </ng-container>
    </div>

    <div class="process-result" *ngIf="[StateType.SUCCESS, StateType.ERROR].includes(appCreateProcess?.gobalState)">
      <button
        ad-button
        adType="primary"
        *ngIf="appCreateProcess?.gobalState === StateType.SUCCESS"
        (click)="handleJumpApp()"
      >
        {{ 'dj-前往查看' | translate }}
      </button>
      <button
        ad-button
        adType="primary"
        *ngIf="appCreateProcess?.gobalState === StateType.ERROR"
        (click)="handleRebuild()"
      >
        {{ 'dj-重新生成' | translate }}
      </button>
    </div>
  </div>
</div>

<app-create-app
  *ngIf="addAppVisible"
  [visible]="addAppVisible"
  [type]="'empty'"
  [params]="createAppData"
  (visibleChange)="onVisibleChange($event)"
  (afterCreated)="onAfterCreated($event)"
></app-create-app>

<!-- 代码 -->
<app-info-view-modal
  [visible]="showCodeModal"
  [viewData]="viewData"
  [title]="'dj-代码' | translate"
  (ok)="handleSubmitView()"
  (close)="handleCloseView()"
>
</app-info-view-modal>
