<!--设置扩展开窗-->
<ad-modal
  nzClassName="info-view-modal"
  [nzWidth]="'980px'"
  [nzVisible]="visible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="footerTpl"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle>
    <div class="title-tabs">
      <div
        class="title-tab-item"
        [ngClass]="{ active: currentTab === tab.code }"
        (click)="currentTab = tab.code"
        *ngFor="let tab of tabs"
      >
        <i adIcon [iconfont]="tab.icon"></i>
        <span>{{ tab.title | translate }}</span>
      </div>
    </div>
  </ng-template>
  <ng-template #modalContent>
    <nz-spin [nzSpinning]="isLoading">
      <app-json-editor
        class="jse-theme-lcdp"
        *ngIf="currentTab === 'code'"
        [data]="codeData"
        [attr]="attr"
        [jsonEditorProps]="jsonEditorProps"
      ></app-json-editor>
      <div class="preview-content" *ngIf="currentTab === 'preview'">
        <app-model-relation-pure [modelList]="modelList" serviceCode=""></app-model-relation-pure>
      </div>
    </nz-spin>
  </ng-template>
  <ng-template #footerTpl>
    <div class="info-view-modal-footer">
      <button ad-button adType="default" (click)="handleCancel()">
        {{ 'dj-退出' | translate }}
      </button>
      <!-- <ng-container *operateAuth="{ prefix: 'update' }"> -->
      <button ad-button adType="primary" (click)="handleOK()">
        {{ 'dj-采纳' | translate }}
      </button>
      <!-- </ng-container> -->
    </div>
  </ng-template>
</ad-modal>
