import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { GlobalService } from 'common/service/global.service';

@Component({
  selector: 'app-info-view-modal',
  templateUrl: './info-view-modal.component.html',
  styleUrls: ['./info-view-modal.component.less'],
})
export class InfoViewModalComponent implements OnInit, OnChanges {
  isLoading: boolean = false;
  parseError: any;

  @Input() visible: boolean = false;
  @Input() viewData: any; // 扩展JSON数据

  @Output() ok: EventEmitter<any> = new EventEmitter();
  @Output() close: EventEmitter<any> = new EventEmitter();

  get codeData() {
    return this.viewData?.codeData;
  }

  get modelList() {
    return this.viewData?.modelList;
  }

  jsonEditorProps = {
    mode: 'text',
    readOnly: true,
    mainMenuBar: false,
    navigationBar: false,
    statusBar: false,
  };

  tabs = [
    {
      title: 'dj-预览',
      icon: 'iconkejianxing-kejian',
      code: 'preview',
    },
    {
      title: 'dj-代码',
      icon: 'iconkaifasheji',
      code: 'code',
    },
  ];
  currentTab = 'preview';

  constructor(private message: NzMessageService, public globalService: GlobalService) {}

  ngOnInit(): void {
    // this.handleInit();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('visible') && changes.visible.currentValue) {
      this.handleInit();
    }
  }

  // 初始化
  handleInit(): void {
    // this.visible = true;
  }

  handleOK() {
    this.ok.emit();
  }

  handleCancel() {
    this.close.emit();
  }
}
