import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { MessageComponentModelCreate } from './message-component-model-create.component';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { CreateAppModule } from 'pages/apps/create-app/create-app.module';
import { AiModelModule } from 'components/page-design/entries/model-designer-new/components/ai-model/ai-model.module';

@NgModule({
  declarations: [MessageComponentModelCreate],
  imports: [
    CommonModule,
    TranslateModule,
    NzIconModule,
    AdButtonModule,
    AdIconModule,
    AdModalModule,
    CreateAppModule,
    AiModelModule,
  ],
  providers: [],
  exports: [MessageComponentModelCreate],
})
export class MessageComponentModelCreateModule {}
