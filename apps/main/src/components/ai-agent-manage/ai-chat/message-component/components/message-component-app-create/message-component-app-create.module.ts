import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { MessageComponentAppCreate } from './message-component-app-create.component';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { CreateAppModule } from 'pages/apps/create-app/create-app.module';
import { InfoViewModalModule } from './info-view-modal/info-view-modal.module';

@NgModule({
  declarations: [MessageComponentAppCreate],
  imports: [
    CommonModule,
    TranslateModule,
    NzIconModule,
    AdButtonModule,
    NzProgressModule,
    AdIconModule,
    AdModalModule,
    CreateAppModule,
    InfoViewModalModule,
  ],
  providers: [],
  exports: [MessageComponentAppCreate],
})
export class MessageComponentAppCreateModule {}
