import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
} from '@angular/core';
import { ChatMessageUser } from '../../config/ai-agent-manage.type';
import Vditor from 'vditor';
import { LocaleService } from '../../../../common/service/locale.service';

@Component({
  selector: 'app-message-user',
  templateUrl: './message-user.component.html',
  styleUrls: ['./message-user.component.less'],
})
export class MessageUserComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() msg: ChatMessageUser;
  @Input() loading: boolean = false;
  currentLanguage: string;

  constructor(private localeService: LocaleService) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngAfterViewInit(): void {}
}
