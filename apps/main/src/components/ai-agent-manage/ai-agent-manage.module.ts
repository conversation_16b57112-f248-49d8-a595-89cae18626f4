import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AiAgentManageComponent } from './ai-agent-manage.component';
import { TranslateModule } from '@ngx-translate/core';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { AiChatComponent } from './ai-chat/ai-chat.component';
import { AiAgentManageService } from './service/ai-agent-manage.service';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { MessageUserComponent } from './ai-chat/message-user/message-user.component';
import { MessageSystemComponent } from './ai-chat/message-system/message-system.component';
import { MessageAssistantComponent } from './ai-chat/message-assistant/message-assistant.component';
import { MessageComponent } from './ai-chat/message-component/message-component.component';
import { MessageComponentAppCreateModule } from './ai-chat/message-component/components/message-component-app-create/message-component-app-create.module';
import { AutoScrollDirective } from './directives/auto-scroll.directive';
import { ResizeWidthDirective } from './directives/resize-width.directive';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { MessageComponentModelCreateModule } from './ai-chat/message-component/components/message-component-model-create/message-component-model-create.module';
import { NzSpinModule } from 'ng-zorro-antd/spin';

@NgModule({
  declarations: [
    AiAgentManageComponent,
    AiChatComponent,
    MessageUserComponent,
    MessageSystemComponent,
    MessageAssistantComponent,
    MessageComponent,
    AutoScrollDirective,
    ResizeWidthDirective,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    AdModalModule,
    NzMessageModule,
    DragDropModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzInputModule,
    NzIconModule,
    AdIconModule,
    MessageComponentAppCreateModule,
    NzToolTipModule,
    MessageComponentModelCreateModule,
    NzSpinModule,
  ],
  providers: [AiAgentManageService],
  exports: [AiAgentManageComponent],
})
export class AiAgentManageModule {}
