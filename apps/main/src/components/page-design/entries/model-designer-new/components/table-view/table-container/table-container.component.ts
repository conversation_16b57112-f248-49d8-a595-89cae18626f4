import { Component, Input, OnInit, ViewChild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { FieldTypes } from '../../../types/types';
import { UUID } from 'angular2-uuid';
import { ModelDesignerService } from '../../../service/model-designer.service';
import { StoreService } from '../../../service/store.service';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { to } from 'common/utils/core.utils';
import { AiModelComponent } from '../../ai-model/ai-model.component';
import { cloneDeep, remove } from 'lodash';
import { LocaleService } from 'common/service/locale.service';
import dayjs from 'dayjs';
import { delay } from 'lodash';
import { AiAgentGobalManageService } from 'components/ai-agent-manage/service/ai-agent-manage-gobal.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-table-container',
  templateUrl: './table-container.component.html',
  styleUrls: ['./table-container.component.less'],
})
export class TableContainerComponent implements OnInit, OnDestroy {
  constructor(
    public service: ModelDesignerService,
    private store: StoreService,
    private translate: TranslateService,
    private modal: AdModalService,
    private message: NzMessageService,
    private language: LocaleService,
    private aiAgentGobalManageService: AiAgentGobalManageService,
  ) {}

  @ViewChild('aiModelComponent') aiModelComponent: AiModelComponent;
  // 添加SQL字段
  public sqlVisible: boolean = false;
  // ai生成
  public aiGenerateModal: boolean = false;
  public deleteing: boolean = false;

  @Input() model: any;

  generateModelFieldChange$: Subscription;

  get state() {
    return this.store.state;
  }
  get isApi() {
    return this.service.originModelData?.modelType === 'api';
  }

  get modelType() {
    return this.service.originModelData?.modelType;
  }

  get isMainModel() {
    return this.model?.name === this.service.modelList[0]?.name;
  }

  get deleteDisable() {
    if (this.model?.fields?.some((field) => field.checked)) return false;
    return true;
  }

  ngOnInit() {
    this.generateModelFieldChange$ = this.aiAgentGobalManageService.generateModelFieldChange$.subscribe(
      (value: any) => {
        // 只有当前激活的模型才处理事件，避免多个组件实例重复处理
        if (this.model?.name === this.store.state.currentTabName) {
          console.log('generateModelFieldChange$', value);
          this.callbackAiGenerate(value);
        }
      },
    );
  }

  ngOnDestroy(): void {
    this.generateModelFieldChange$?.unsubscribe();
  }

  //#region 字段
  public handleDelete(e: MouseEvent): void {
    e.stopPropagation();
    if (this.deleteDisable) return;
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-确认删除吗？'),
      nzOnOk: async () => {
        try {
          this.deleteing = true;
          await this.handleDeleteField();
        } finally {
          this.deleteing = false;
        }
      },
      nzOnCancel: () => {},
    });
  }

  /**
   * 添加基础字段/逻辑字段
   */
  public handleAddField(type: 'BASIC' | 'LOGIC'): void {
    const { propPanpelSetting, currentTabName } = this.state;
    const fieldUUID = propPanpelSetting?.[currentTabName]?.fieldUUID;
    const model = (this.service.modelList ?? []).find((model) => model.name === this.state?.currentTabName);
    const index = model?.fields?.findIndex((field) => field._uuid === fieldUUID);
    const len = model.fields.filter((field) => !field.isSystem)?.length || 0;
    const fieldId = `field${len + 1}`;
    const fieldNameLang = {
      en_US: `field${len + 1}`,
      zh_CN: `字段${len + 1}`,
      zh_TW: `欄位${len + 1}`,
    };
    const rowData = this.addSimple({
      type: type === 'BASIC' ? FieldTypes.SIMPLE : FieldTypes.LOGIC,
      fieldType: 'VARCHAR',
      size: 100,
      fieldId: fieldId,
      fieldName: fieldNameLang[this.translate.instant('dj-LANG')],
      lang: {
        fieldName: fieldNameLang,
      },
    });
    this.handleAddRowDataList([rowData], index === -1 ? undefined : index + 1);
    delay(() => {
      const inputElem = document.getElementById('edit-field-input') as HTMLInputElement;
      inputElem?.focus();
      inputElem?.select();
    }, 50);
  }

  public handleAddBusinessField(type: 'addNode' | 'addManagementUnit' | 'addDataManage'): void {
    if (type === 'addNode') {
      if (this.checkClassifyDisable()) return;
    }
    if (type === 'addManagementUnit') {
      if (this.checkUnitDisable()) return;
    }
    if (type === 'addDataManage') {
      if (this.checkDataPermissionDisable()) return;
    }
    const { propPanpelSetting, currentTabName } = this.state;
    const fieldUUID = propPanpelSetting?.[currentTabName]?.fieldUUID;
    const model = (this.service.modelList ?? []).find((model) => model.name === this.state?.currentTabName);
    const index = model?.fields?.findIndex((field) => field._uuid === fieldUUID);
    const rowDataList = this[`${type}`]();
    this.handleAddRowDataList(rowDataList, index === -1 ? undefined : index + 1);
  }

  public handleUpdateValue(type: 'fields' | 'indexs' | 'services', value: string) {
    this.service.keywords[type] = value;
  }

  // 添加引用关联
  public handleAddQuoteQuery(): void {
    this.store.setState({ associationModalVisible: true, associationFromPanel: true });
  }

  // 添加集合
  public handleAddCollectField(): void {
    this.store.setState({ addModelVisible: true });
  }

  public handleImport(): void {
    this.sqlVisible = true;
  }

  public handleAi(): void {
    this.aiGenerateModal = true;
  }

  public handleDownloadTemplate() {
    // todo
  }

  public handleImportTemplate() {
    // todo
  }

  public checkClassifyDisable(): boolean {
    const shouldFields = ['node_id', 'node_no', 'node_name', 'parent_node_id', 'node_type'];
    const fields = this.model.fields || [];
    return shouldFields.every((field) => fields.some((f) => f.fieldId === field));
  }

  public checkUnitDisable(): boolean {
    const shouldFields = ['eoc_company_id', 'eoc_site_id', 'eoc_region_id'];
    const fields = this.model.fields || [];
    return shouldFields.every((field) => fields.some((f) => f.fieldId === field));
  }

  public checkDataPermissionDisable(): boolean {
    const shouldFields = ['owner_dept_id', 'owner_dept_name', 'owner_emp_id', 'owner_emp_name'];
    const fields = this.model.fields || [];
    return shouldFields.every((field) => fields.some((f) => f.fieldId === field));
  }

  /**
   * SQL脚本添加简单类型字段开窗确认
   */
  public onConfirmModal(data): void {
    const sqlFields = data?.tableModel?.fields || [];
    sqlFields.forEach((fieldData, index) => {
      sqlFields[index] = {
        ...this.addSimple(),
        ...fieldData,
        type: FieldTypes.SIMPLE,
        fieldType: fieldData.fieldType,
      };
      sqlFields[index] = this.fieldTypeChange(sqlFields[index]);
    });

    // 验证批量生成的字段是否通过
    if (sqlFields.length > 1) {
      const form = this.service.createFieldPropertiesForm();
      sqlFields.forEach((fieldData, i) => {
        // 过滤第一个
        if (i === 0) return;
        const valid = this.service.checkFieldValid(fieldData, form);
        this.store.setState((state) => {
          state.fieldFormsValid[state.currentTabName][fieldData._uuid] = valid;
        });
      });
    }

    this.handleAddRowDataList(sqlFields);
    this.onCancelModal();
  }

  /**
   * SQL脚本添加简单类型字段开窗关闭
   */
  public onCancelModal() {
    this.sqlVisible = false;
  }

  /**
   * 取消对话框
   */
  public handleCloseAIDrawer(): void {
    this.aiGenerateModal = false;
  }

  public callbackAiGenerate({ data, msgId }: any) {
    if (!data?.length) {
      this.aiGenerateModal = false;
      return;
    }

    this.aiModelComponent?.updateMessageStatus?.(msgId, 'success');

    const { simpleField, collectionModel } = this.handleAiData(data);

    // 判断新增的子表是否已存在
    const collectionTablesName = collectionModel?.map((item) => item.name) || [];
    const repeatTableName = [];
    this.service.modelList.forEach((item) => {
      if (collectionTablesName.includes(item.name)) {
        repeatTableName.push(item.name);
      }
    });

    if (!repeatTableName?.length) {
      // 验证批量生成的字段是否通过
      if (simpleField.length > 1) {
        const form = this.service.createFieldPropertiesForm();
        simpleField.forEach((fieldData, i) => {
          // 过滤第一个
          if (i === 0) return;
          const valid = this.service.checkFieldValid(fieldData, form);
          this.store.setState((state) => {
            state.fieldFormsValid[state.currentTabName][fieldData._uuid] = valid;
          });
        });
      }

      // 添加简单字段
      this.handleAddRowDataList(simpleField);

      // 添加集合模型
      if (collectionModel?.length) {
        collectionModel.forEach((model) => {
          this.service.handleCollectField(this.state.currentTabName, model);
          this.store.setState((state) => {
            state.propPanpelSetting[model.name] = { visible: false, fixed: false, type: null, fieldUUID: null };
          });

          // 验证添加模型中的字段
          model.fields
            .filter((f) => !f.isSystem)
            .forEach((fieldData) => {
              const form = this.service.createFieldPropertiesForm();
              // 过滤第一个
              // if (i === 0) return;
              const t = setTimeout(() => {
                // 子表组件渲染完成后验证子表字段
                clearTimeout(t);
                const valid = this.service.checkFieldValid(fieldData, form, model.name);
                this.store.setState((state) => {
                  state.fieldFormsValid[model.name][fieldData._uuid] = valid;
                });
              }, 0);
            });
        });
      }
      this.message.success(this.translate.instant('dj-新增成功！'));
      return;
    }

    this.modal.confirm({
      nzTitle: this.translate.instant('dj-模型名称重复，确认替换原模型吗？', { name: repeatTableName.join('、') }),
      nzOnOk: () => {
        // 添加简单字段
        this.handleAddRowDataList(simpleField);

        if (!collectionModel?.length) {
          this.message.success(this.translate.instant('dj-新增成功！'));
          this.aiGenerateModal = false;
          return;
        }

        // 添加集合模型
        collectionModel.forEach(async (model) => {
          if (repeatTableName.includes(model.name)) {
            // 删除重复集合模型
            const originModel = this.service.modelList.find((i) => i.name === model.name);
            const { isPass, message }: any = await this.handleDeleteModelVaild(originModel);
            if (isPass) {
              this.handleDeleteModel(model);
              this.store.setState((state) => {
                state.propPanpelSetting[model.name] = {};
              });

              const t = setTimeout(() => {
                clearTimeout(t);
                this.service.handleCollectField(this.state.currentTabName, model);
                this.store.setState((state) => {
                  state.propPanpelSetting[model.name] = { visible: false, fixed: false, type: null, fieldUUID: null };
                });

                // 验证添加模型中的字段
                model.fields
                  .filter((f) => !f.isSystem)
                  .forEach((fieldData) => {
                    const form = this.service.createFieldPropertiesForm();
                    // 过滤第一个
                    // if (i === 0) return;
                    const t = setTimeout(() => {
                      // 子表组件渲染完成后验证子表字段
                      clearTimeout(t);
                      const valid = this.service.checkFieldValid(fieldData, form, model.name);
                      this.store.setState((state) => {
                        state.fieldFormsValid[model.name][fieldData._uuid] = valid;
                      });
                    }, 0);
                  });
              });
            } else {
              // this.message.error(message);
            }
          } else {
            this.service.handleCollectField(this.state.currentTabName, model);
            this.store.setState((state) => {
              state.propPanpelSetting[model.name] = { visible: false, fixed: false, type: null, fieldUUID: null };
            });
            // 验证添加模型中的字段
            model.fields
              .filter((f) => !f.isSystem)
              .forEach((fieldData) => {
                const form = this.service.createFieldPropertiesForm();
                // 过滤第一个
                // if (i === 0) return;
                const t = setTimeout(() => {
                  // 子表组件渲染完成后验证子表字段
                  clearTimeout(t);
                  const valid = this.service.checkFieldValid(fieldData, form, model.name);
                  this.store.setState((state) => {
                    state.fieldFormsValid[model.name][fieldData._uuid] = valid;
                  });
                }, 0);
              });
          }
        });
        this.message.success(this.translate.instant('dj-新增成功！'));
        this.aiGenerateModal = false;
      },
    });
  }

  // 添加索引
  public handleAddIndex() {
    const model = (this.service.modelList ?? []).find((model) => model.name === this.state?.currentTabName);
    // 兼容model.index不存在的情况
    if (!model.index) {
      model.index = [];
    }
    // index: 包含一般索引和业务主键
    const index = model.index.filter((m) => m.type === 'index');
    let indexId = index.length === 0 ? 0 : Number(index[index.length - 1]?.id?.slice(-3));
    const id = 'I' + (++indexId).toString().padStart(3, '0');
    const fieldId = model.fields?.find((field) => !field.isPk)?.fieldId;
    const item = {
      idName: id,
      type: 'index',
      id,
      member: fieldId ? [fieldId] : [],
    };
    // 往模型索引里添加item
    model.index.push(item);

    this.store.setState((state) => {
      state.propPanpelSetting[state.currentTabName].fieldUUID = (index.length || 0).toString();
      state.propPanpelSetting[state.currentTabName].visible = true;
      state.propPanpelSetting[state.currentTabName].type = 'index';
    });
    this.service.updateModelReference(this.state.currentTabName);
  }
  //#endregion

  //#region 私有方法
  /**
   * 删除模型校验
   */
  private async handleDeleteModelVaild(model) {
    // 拦截校验：条件1、主模型不能删除 条件2、子模型中存在子模型，不能删除 条件3、子模型被其他模型关联，不能删除
    let isPass = true;
    let message = '';
    if (!model._pid) {
      message = this.translate.instant('dj-主模型不能删除');
      isPass = false;
    } else if ((model?.children ?? [])?.length > 0) {
      message = this.translate.instant('dj-子模型中存在子模型，不能删除');
      isPass = false;
    } else {
      const data = (await this.service.checkModelRelated(model.name)) ?? [];
      if (data?.length > 0) {
        message = this.translate.instant('dj-子模型被其他模型关联，不能删除');
        isPass = false;
      }
    }

    return { isPass, message };
  }

  /**
   * 删除模型
   */
  private handleDeleteModel(model): void {
    // 删除模型
    if (model._pid) {
      const findPIndex = this.service.modelList.findIndex((m) => m.name === model._pid);
      // 删除主模型中的children
      const findCIndex = this.service.modelList[findPIndex].children.findIndex((m) => m.name === model.name);
      this.service.modelList[findPIndex].children.splice(findCIndex, 1);
      // 删除主模型中的集合字段
      const findCollectionIndex = (this.service.modelList[findPIndex].fields ?? []).findIndex(
        (f) => f.type === 'COLLECTION' && f.fieldId === model.name,
      );
      this.service.modelList[findPIndex].fields.splice(findCollectionIndex, 1);
      // 更新父表引用，用于重渲染
      this.service.modelList[findPIndex] = { ...this.service.modelList[findPIndex] };
    }
    const findIndex = this.service.modelList.findIndex((m) => m.name === model.name);
    this.service.modelList.splice(findIndex, 1);
  }

  /**
   * AI生成回调数据处理
   */
  private handleAiData(data) {
    const cloneData = cloneDeep(data);
    const _currentLanguage = this.language.currentLanguage || 'zh_CN';

    const simpleField = cloneData?.filter((item) => item.fieldType !== 'COLLECTION') || [];
    const collectionModel = cloneData?.filter((item) => item.fieldType === 'COLLECTION') || [];

    // 简单字段
    simpleField.forEach((fieldData, index) => {
      simpleField[index] = {
        ...this.addSimple(),
        type: FieldTypes.SIMPLE,
        fieldId: fieldData.fieldName,
        fieldName: fieldData.lang?.fieldDesc?.[_currentLanguage] || fieldData.fieldDesc,
        fieldType: fieldData.fieldType,
        lang: {
          fieldName: {
            en_US: '',
            ...(fieldData.lang?.fieldDesc || {}),
          },
        },
        notNull: fieldData.notNull,
        size: fieldData.isSystem ? fieldData?.size : fieldData?.fieldLength,
        scale: fieldData.fieldPrecision,
      };
      simpleField[index] = this.fieldTypeChange(simpleField[index]);
    });

    // 集合模型
    collectionModel.forEach((fieldData, index) => {
      const fields = fieldData?.tableFields || [];
      fields.forEach((fieldChildData, childIndex) => {
        fields[childIndex] = {
          ...this.addSimple(),
          type: FieldTypes.SIMPLE,
          fieldId: fieldChildData.fieldName,
          fieldName: fieldChildData.lang?.fieldDesc?.[_currentLanguage] || fieldChildData.fieldDesc,
          fieldType: fieldChildData.fieldType,
          lang: {
            fieldName: {
              en_US: '',
              ...(fieldChildData.lang?.fieldDesc || {}),
            },
          },
          isPk: !!fieldChildData.isPk,
          autoIncrement: fieldChildData.autoIncrement,
          enumValue: fieldChildData.enumValue,
          notNull: fieldChildData.notNull,
          size: fieldChildData.isSystem ? fieldChildData?.size : fieldChildData?.fieldLength,
          scale: fieldChildData.fieldPrecision,
          isSystem: fieldChildData.isSystem,
          unique: fieldChildData.unique,
        };
        fields[childIndex] = this.fieldTypeChange(fields[childIndex]);
      });

      collectionModel[index] = {
        name: fieldData.fieldName,
        pk: `${fieldData.fieldName}_id`,
        lang: {
          comment: {
            en_US: '',
            ...(fieldData.lang?.fieldDesc || {}),
          },
        },
        isSystem: fieldData.isSystem,
        comment: fieldData.lang?.fieldDesc?.[_currentLanguage] || fieldData.fieldDesc,
        createDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        fields: fields,
      };
    });

    return { simpleField, collectionModel };
  }

  private fieldTypeChange(rowData: any): any {
    return {
      ...rowData,
      size: rowData.fieldType === 'BIT' ? 1 : rowData.size,
      unique: rowData.fieldType === 'FILE' ? false : rowData.unique,
    };
  }

  private async handleDeleteField() {
    const waitDelete = this.model.fields.filter((field) => field.checked);
    while (waitDelete.length) {
      const first = waitDelete.shift();
      await this.deleteOneField(first);
    }
    this.message.success(this.translate.instant('dj-删除成功'));
    const findIndex = (this.service.modelList ?? []).findIndex((model) => model.name === this.state?.currentTabName);
    if (findIndex > -1) {
      this.service.modelList[findIndex] = { ...this.service.modelList[findIndex] };
    }
    this.service.refreshApiConfig$.next(true);
  }

  private async deleteOneField(fieldData: any) {
    const modelBKs = Object.values(this.state.modelIndexes[this.model.name] || {});
    if (
      modelBKs.includes(fieldData._uuid) ||
      this.isFK(fieldData) ||
      this.service?.originModelData?.modelType === 'api'
    ) {
      return;
    }

    if (this.service.isIndexField(fieldData, this.model)) {
      this.message.error(this.translate.instant('dj-该字段是索引，请先删除索引'));
      return;
    }

    if (fieldData.type === FieldTypes.COLLECTION) {
      const [err, res] = await to(this.service.removeCollectionField(fieldData, false));
      if (res) {
        this.updateAfterRemoveField(fieldData);
        // 更新被删表的主表children
        remove(
          this.service.modelList.find((m) => m.name === this.model.name).children,
          (child: any) => child.name === fieldData.fieldId,
        );
        // 如果被删除的字段在fieldFormsValid里，则同步删除
        this.service.deleteFieldFromFieldFormsValid(fieldData._uuid);
        this.service.refreshApiConfig$.next(true);
      }
      return;
    }

    // 删除组
    if (fieldData.type === 'GROUP') {
      const [err, res] = await to(this.service.removeGroup(fieldData, false));
      if (res) {
        this.updateAfterRemoveField(fieldData);
        this.service.refreshApiConfig$.next(true);
      }
      return;
    }

    const [err, res] = await to(this.service.removeField(fieldData, false));
    if (res) {
      this.updateAfterRemoveField(fieldData);
      // 如果被删除的字段在fieldFormsValid里，则同步删除
      this.service.deleteFieldFromFieldFormsValid(fieldData._uuid);
    }
  }

  private updateAfterRemoveField(fieldData) {
    const { propPanpelSetting, currentTabName } = this.state;
    const selectedFieldUUID = propPanpelSetting?.[currentTabName]?.selectedFieldUUID;
    // 如果被删除了被选中的字段，需要关闭属性面板
    if (fieldData._uuid === selectedFieldUUID) {
      this.store.setState((state) => {
        const tabSet = state.propPanpelSetting[state.currentTabName];
        const newSet = { fieldUUID: '', visible: false, type: '' };
        state.propPanpelSetting[state.currentTabName] = { ...tabSet, ...newSet };
      });
    }
  }

  private isFK(field): boolean {
    const parentModel = this.service.modelList.find((item) => item.name === this.model._pid);
    if (!parentModel) return false;
    return !!parentModel.fields.find((item) => item.fieldId === field.fieldId && item.isSystem);
  }

  private addSimple(defaultParam: any = {}): any {
    return {
      fieldId: '',
      fieldName: '', // 字段说明
      businessCode: '', // 业务类型code
      fieldType: null, // 字段数据类型
      size: null, // 长度
      scale: null, // 精度
      notNull: false, // 是否为空
      unique: false, // 是否唯一
      autoIncrement: false,
      enumValue: null, // 下拉辞汇code
      dictionaryContent: null, // 下拉辞汇内容
      defaultValue: null,
      associatedInfo: null, // 关联
      lang: {}, //
      quoteGroup: '', // 关联分组(对应一次join),生成规则：group-{别名1}-{别名2}
      wordDictionaryId: '', // 辞汇字典id
      businessTypeId: '', // 业务类型id
      dictionaryId: '', // 下拉辞汇id
      _uuid: UUID.UUID(), // 用来标识这条数据的唯一性，方便查找
      type: FieldTypes.SIMPLE,
      ...defaultParam,
    };
  }

  private handleAddRowDataList(rowDataList: any[], systemSecondIndex: number = -1) {
    const findIndex = (this.service.modelList ?? []).findIndex((model) => model.name === this.state?.currentTabName);
    if (findIndex > -1) {
      const model = this.service.modelList[findIndex];
      const fields = model.fields;
      systemSecondIndex =
        systemSecondIndex === -1 ? fields.length - fields.filter((f) => f.isSystem).length + 1 : systemSecondIndex;
      this.service.modelList[findIndex].fields.splice(systemSecondIndex, 0, ...rowDataList);
      // 更新父表引用，用于重渲染
      this.service.modelList[findIndex] = { ...this.service.modelList[findIndex] };
      // 更新fieldUUID
      setTimeout(() => {
        this.store.setState((state) => {
          state.propPanpelSetting[state.currentTabName].visible = true;
          state.propPanpelSetting[state.currentTabName].type = 'field';
          state.propPanpelSetting[state.currentTabName].fieldUUID = rowDataList[0]._uuid;
        });
        // 未配置的字段直接显红报错
        rowDataList.forEach((field) => {
          if (!field.fieldId) {
            this.store.setState((state) => {
              state.fieldFormsValid[state.currentTabName][field._uuid] = false;
            });
          }
        });
      }, 0);
      // 检测重复字段
      this.service.checkFieldRepeat();
    }
  }

  /**
   * 业务字段 分类导航
   */
  private addNode(): any[] {
    const fieldsIds = (this.model.fields || []).map((field) => field.fieldId);
    const managementUnits = [
      {
        fieldId: 'node_id',
        fieldName: this.translate.instant('dj-节点id'),
        size: '64',
        lang: {
          fieldName: {
            zh_CN: '节点id',
            zh_TW: '節點id',
            en_US: 'node id',
          },
        },
      },
      {
        fieldId: 'node_no',
        fieldName: this.translate.instant('dj-节点编号'),
        size: '20',
        lang: {
          fieldName: {
            zh_CN: '节点编号',
            zh_TW: '節點編號',
            en_US: 'node number',
          },
        },
      },
      {
        fieldId: 'node_name',
        fieldName: this.translate.instant('dj-节点名称'),
        lang: {
          fieldName: {
            zh_CN: '节点名称',
            zh_TW: '節點名稱',
            en_US: 'node name',
          },
        },
      },
      {
        fieldId: 'parent_node_id',
        fieldName: this.translate.instant('dj-父节点id'),
        size: '64',
        lang: {
          fieldName: {
            zh_CN: '父节点id',
            zh_TW: '父節點id',
            en_US: 'parent node ID',
          },
        },
      },
      {
        fieldId: 'node_type',
        fieldName: this.translate.instant('dj-节点类型'),
        fieldType: 'INT',
        size: '2',
        lang: {
          fieldName: {
            zh_CN: '节点类型',
            zh_TW: '節點類型',
            en_US: 'Employee Id',
          },
        },
      },
    ].filter((e) => !fieldsIds.includes(e.fieldId));
    managementUnits.forEach((fieldData, index) => {
      managementUnits[index] = {
        ...this.addSimple(),
        type: FieldTypes.SIMPLE,
        fieldType: fieldData.fieldType || 'VARCHAR',
        size: fieldData.size || '50',
        ...fieldData,
      };
    });
    return managementUnits;
  }

  /**
   * 业务字段：运营单元
   */
  private addManagementUnit(): any[] {
    const fieldsIds = (this.model.fields || []).map((field) => field.fieldId);
    const managementUnits = [
      {
        fieldId: 'eoc_company_id',
        fieldName: this.translate.instant('dj-营运公司编号'),
        lang: {
          fieldName: {
            zh_CN: '营运公司编号',
            zh_TW: '營運公司編號',
            en_US: 'Operating Company Number',
          },
        },
      },
      {
        fieldId: 'eoc_site_id',
        fieldName: this.translate.instant('dj-营运据点编号'),
        lang: {
          fieldName: {
            zh_CN: '营运据点编号',
            zh_TW: '營運據點編號',
            en_US: 'Operation Site Number',
          },
        },
      },
      {
        fieldId: 'eoc_region_id',
        fieldName: this.translate.instant('dj-营运域编号'),
        lang: {
          fieldName: {
            zh_CN: '营运域编号',
            zh_TW: '營運域編號',
            en_US: 'Operation Domain Number',
          },
        },
      },
    ].filter((e) => !fieldsIds.includes(e.fieldId));
    managementUnits.forEach((fieldData, index) => {
      managementUnits[index] = {
        ...this.addSimple(),
        type: FieldTypes.SIMPLE,
        fieldType: 'VARCHAR',
        size: '50',
        ...fieldData,
      };
    });
    return managementUnits;
  }

  /**
   * 业务字段：数据权限
   * @param emitType
   */
  private addDataManage(): any[] {
    const fieldsIds = (this.model.fields || []).map((field) => field.fieldId);
    const managementUnits = [
      {
        fieldId: 'owner_dept_id',
        fieldName: this.translate.instant('dj-部门id'),
        lang: {
          fieldName: {
            zh_CN: '部门id',
            zh_TW: '部門id',
            en_US: 'Department id',
          },
        },
        fieldType: 'VARCHAR',
        size: '60',
        notNull: false,
        autoIncrement: false,
      },
      {
        fieldId: 'owner_dept_name',
        fieldName: this.translate.instant('dj-部门名称'),
        lang: {
          fieldName: {
            zh_CN: '部门名称',
            zh_TW: '部門名稱',
            en_US: 'Department name',
          },
        },
        fieldType: 'VARCHAR',
        size: '60',
        notNull: false,
        autoIncrement: false,
      },
      {
        fieldId: 'owner_emp_id',
        fieldName: this.translate.instant('dj-员工id'),
        lang: {
          fieldName: {
            zh_CN: '员工id',
            zh_TW: '員工id',
            en_US: 'Employee Id',
          },
        },
        fieldType: 'VARCHAR',
        size: '60',
        notNull: false,
        autoIncrement: false,
      },
      {
        fieldId: 'owner_emp_name',
        fieldName: this.translate.instant('dj-员工名称'),
        lang: {
          fieldName: {
            zh_CN: '员工名称',
            zh_TW: '員工名稱',
            en_US: 'Employee name',
          },
        },
        fieldType: 'VARCHAR',
        size: '60',
        notNull: false,
        autoIncrement: false,
      },
    ].filter((e) => !fieldsIds.includes(e.fieldId));
    managementUnits.forEach((fieldData, index) => {
      managementUnits[index] = {
        ...this.addSimple(),
        type: FieldTypes.SIMPLE,
        fieldType: fieldData.fieldType || 'VARCHAR',
        size: fieldData.size || '50',
        ...fieldData,
      };
    });
    return managementUnits;
  }
  //#endregion
}
