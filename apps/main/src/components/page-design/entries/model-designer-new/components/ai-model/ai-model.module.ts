import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AiModelComponent } from './ai-model.component';
import { FormsModule } from '@angular/forms';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { AdInputModule } from 'components/ad-ui-components/ad-input/ad-input.module';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { InputModule } from 'components/form-components/input/input.module';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { TranslateModule } from '@ngx-translate/core';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { ViewExampleModule } from '../view-example/view-example.module';
import { AiMessageLoadingComponent } from './ai-message-loading/ai-message-loading.component';
import { AiMessageTextComponent } from './ai-message-text/ai-message-text.component';
import { AiMessageModelTableComponent } from './ai-message-model-table/ai-message-model-table.component';
import { AiMessageComponent } from './ai-message/ai-message.component';
import { AiDetaultComponent } from './ai-detault/ai-detault.component';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { AiModelImageComponent } from './ai-model-image/ai-model-image.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { AiModelService } from './ai-model.service';

@NgModule({
  imports: [
    CommonModule,
    NzDrawerModule,
    AdIconModule,
    AdInputModule,
    NzInputModule,
    FormsModule,
    NzTabsModule,
    AdButtonModule,
    NzIconModule,
    TranslateModule,
    NzUploadModule,
    NzSpinModule,
    NzModalModule,
    ViewExampleModule,
    NzTableModule,
    NzGridModule,
    AdSelectModule,
    NzCheckboxModule,
    InputModule,
    NzToolTipModule,
  ],
  declarations: [
    AiModelComponent,
    AiMessageComponent,
    AiMessageLoadingComponent,
    AiMessageTextComponent,
    AiMessageModelTableComponent,
    AiModelImageComponent,
    AiDetaultComponent,
  ],
  exports: [AiModelComponent, AiMessageModelTableComponent],
  providers: [AiModelService],
})
export class AiModelModule {}
