import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { getServiceName } from 'components/page-design/components/data-source/data-source.component.util';
import { cloneDeep } from 'lodash';
import { Observable } from 'rxjs';

@Injectable()
export class ReportWorkDesignService {
  serviceUrl: string;
  workData: any;
  _netWorkData: any;
  cloneNetWorkData: any;
  pageView: any; //界面设计数据
  clonePageView: any;
  defaultSubmitActions: any[] = [];

  set netWorkData(data: any) {
    this._netWorkData = cloneDeep(data ?? {});
    this.cloneNetWorkData = cloneDeep(data ?? {});
    this.pageView = cloneDeep(data?.activityConfig ?? {});
    this.clonePageView = cloneDeep(data?.activityConfig ?? {});
  }
  get netWorkData(): any {
    return this._netWorkData;
  }
  get masterFromDataSourceName(): any {
    return this.pageView?.masterFromDataSourceName ?? false;
  }

  _pageCode: string;
  set pageCode(code: string) {
    this._pageCode = code;
  }
  get pageCode(): string {
    return this._pageCode;
  }
  activeMenu: string;

  // 界面设计数据
  set page(data: any) {
    this.pageView.pages[this.pageCode] = data;
  }
  get page(): any {
    return this.pageView?.pages?.[this.pageCode];
  }

  get actionId(): string {
    return this.page?.submitActions?.[0]?.actionId;
  }

  get UIBOT__appendEocFields(): any {
    return this.page?.extendedFields?.UIBOT__appendEocFields;
  }

  constructor(
    protected http: HttpClient,
    private translate: TranslateService,
    protected configService: SystemConfigService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
  }

  /**
   * 重置
   */
  handleReset(actionId: string) {
    this.page = {
      ...this.page,
      operations: [],
      layout: [],
      submitActions: this.defaultSubmitActions.map((submitAction) => {
        return {
          ...submitAction,
          actionId: actionId,
          serviceName: getServiceName(actionId),
        };
      }),
      submitType: {
        isBatch: false,
      },
    };
  }

  /**
   * 保留
   * @param actionId
   */
  handleReserve(actionId: string) {
    this.page = {
      ...(this.page ?? {}),
      submitActions: this.page.submitActions.map((submitAction) => {
        return {
          ...(submitAction ?? {}),
          actionId: actionId,
        };
      }),
    };
  }

  checkDefaultData() {
    const defaultData = ['layout', 'submitActions', 'operations'];
    for (let i of defaultData) {
      if (!Reflect.has(this.page, i)) {
        this.page[i] = [];
      }
    }
  }

  // 获取ABI报表条件界面设计
  loadPageView(param: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/activityConfigs/${param}`;
    return this.http.get(url);
  }

  // 保存ABI报表条件界面设计
  savePageView(param: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/activityConfigs/upsertActivityConfigs`;
    return this.http.post(url, param);
  }

  // 获取abi报表resid
  getReportResid(param: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/activityConfigs/getResidByCodeAndLocale?${param}`;
    return this.http.get(url);
  }

  getAbiInnerToken(): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/activityConfigs/getAbiInnerToken`;
    return this.http.get(url);
  }

  getTbbInnerToken(): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/activityConfigs/getTbbInnerToken`;
    return this.http.get(url);
  }
}
