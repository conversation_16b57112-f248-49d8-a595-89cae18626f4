import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AppService } from 'pages/apps/app.service';
import { getServiceName } from 'components/page-design/components/data-source/data-source.component.util';
import { DslWorkDesignComponent } from 'components/page-design/components/dsl-work-design/dsl-work-design.component';
import { CONDITION_DETAIL_CONFIG } from 'components/page-design/components/dsl-work-design/shared/config/dsl-work-design.config';
import { WorkDesignConfig } from 'components/page-design/components/dsl-work-design/shared/interface/dsl-work-design.interface';
import { isEmpty } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ReportWorkDesignService } from '../report-work-design.service';
import { DslWorkFormService } from 'components/page-design/components/dsl-work-design/dsl-work-form/dsl-work-form.service';
import { isNone } from 'common/utils/core.utils';
import { IsvPackageDataAllInfo } from '@webdpt/form-editor-components';
import { IsvCustomPackageService } from 'common/service/isv-custom-package/isv-custom-package.service';

@Component({
  selector: 'app-conditional-design',
  templateUrl: './conditional-design.component.html',
  styleUrls: ['./conditional-design.component.less'],
})
export class ConditionalDesignComponent implements OnInit {
  config: WorkDesignConfig = CONDITION_DETAIL_CONFIG;

  loading: boolean;

  isLoadingIsvPackageDataList: boolean = true; // 是否在进行自定义组件包信息加载
  isvPackageDataList: IsvPackageDataAllInfo[] = [];

  formInstance: any = null; // 渲染的formio 实例

  @Output() refreshPageView: EventEmitter<any> = new EventEmitter();

  @ViewChild('dslWorkDesign') dslWorkDesign: DslWorkDesignComponent;

  constructor(
    public service: ReportWorkDesignService,
    public appService: AppService,
    private message: NzMessageService,
    private translate: TranslateService,
    private dslWorkFormService: DslWorkFormService,
    private cd: ChangeDetectorRef,
    private isvCustomPackageService: IsvCustomPackageService,
  ) {
    this.service.pageCode = this.config.pageCode;
  }

  ngOnInit(): void {
    this.handleInit();
  }

  // 初始化
  async handleInit(refreshDesign: boolean = false): Promise<void> {
    this.loading = true;
    this.config.formSetConfig.uploadCategory = this.appService?.selectedApp?.code;

    // 自定义组件信息请求完成之后再进行 app-dsl-work-design渲染
    this.isLoadingIsvPackageDataList = true;
    this.isvPackageDataList = await this.isvCustomPackageService.getIsvPackageDataList(this.appService.selectedApp);
    this.isLoadingIsvPackageDataList = false;

    this.handleLoadConditionDetail(refreshDesign);
    this.loading = false;
  }

  handleLoadConditionDetail(refreshDesign): void {
    // (1) 重置submit相关数据
    if (isEmpty(this.service.page.submitActions)) {
      const { submitActions, submitType } = this.service.page || {};
      const { isBatch } = submitType || {};
      const actionId = this.service.actionId;
      this.service.page = {
        ...this.service.page,
        submitActions: isEmpty(submitActions)
          ? this.service.defaultSubmitActions.map((submitAction) => {
              return {
                ...submitAction,
                actionId,
                serviceName: getServiceName(actionId),
              };
            })
          : submitActions,
        submitType: {
          ...submitType,
          isBatch: isNone(isBatch) ? false : isBatch,
        },
      };
    }

    this.service.checkDefaultData();

    if (refreshDesign) {
      this.cd.detectChanges();
      this.dslWorkFormService.UpdatePageByCode();
    }
  }

  handleChangeFieldSource(e: any): void {
    if (isNone(this.service.actionId)) {
      this.service.handleReset(e);
      this.cd.detectChanges();
      this.handleInit(true);
    }
  }

  handleResetPageUIElement(e: any): void {
    this.service.handleReset(e.to);
    this.cd.detectChanges();
    this.handleInit(true);
  }

  handleChangePageUIElementCode(e: any): void {
    this.service.handleReserve(e.to);
  }

  handleChangePage(e: any): void {
    this.service.page = e;
  }

  handleAcceptFieldData(e: any): void {
    const { fieldTree } = e;
    // 设置schema
    const { submitType } = this.service.page || {};
    const { schema } = submitType || {};
    this.service.page = {
      ...this.service.page,
      submitType: {
        ...submitType,
        schema: isEmpty(schema) ? fieldTree?.[0]?.children?.[0]?.data_name : schema,
      },
    };
  }

  // 提交数据校验
  handleValidate(): boolean {
    return this.dslWorkDesign.handleValidateSubmit();
  }

  public handleRefreshPageView() {
    this.refreshPageView.emit();
  }

  handleChangeFormInstance(data) {
    this.formInstance = data;
  }
}
