import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';

@Injectable()
export class DataEntryWorkDesignRequestService {
  apiUrl = '';
  constructor(
    private httpClient: HttpClient,
    private systemConfig: SystemConfigService,
    private appService: AppService,
  ) {
    this.systemConfig.get('adesignerUrl').subscribe((url) => {
      this.apiUrl = url;
    });
  }

  // 加载驱动模型的界面数据
  loadModelPageView(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/pageDesign/queryByCode`;
    return this.httpClient.get(url, {
      params,
    });
  }

  // 保存
  saveCrudForm(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/pageDesign/update`;
    return this.httpClient.post(url, params);
  }

  getSimpleModelCodeDatas(): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/modelDriver/modelAssign/queryList`;
    return this.httpClient.get(url, {
      params: { application: this.appService.selectedApp.code, queryType: 'association' },
    });
  }

  // 根据数据源生成默认的element
  generateDefaultDslByCode(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/pageDesign/generateDefaultDslByCode`;
    return this.httpClient.post(url, params);
  }
}
