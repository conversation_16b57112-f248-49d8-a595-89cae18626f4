<nz-spin
  [nzSpinning]="
    !gobalErrorMessage &&
    ((!appDslWorkDesign?.isNoSource && dataEntryWorkDesignService?.renderLoading) ||
      dataEntryWorkDesignService.saveLoading ||
      dataEntryWorkDesignService.initInfoLoading)
  "
  [nzTip]="'dj-请勿关闭当前页面' | translate"
>
  <ad-empty class="work-design-empty" [nzNotFoundContent]="contentTpl" *ngIf="!!gobalErrorMessage">
    <ng-template #contentTpl>
      <span>{{ gobalErrorMessage }}</span>
    </ng-template>
  </ad-empty>
  <div class="work-design" *ngIf="!gobalErrorMessage">
    <div class="work-header">
      <div [class]="modelPageType !== 'notModelDriven' ? 'header-title-tpl' : 'header-title'">
        <div
          *ngIf="modelPageType === 'notModelDriven'; else headerCustom"
          nz-tooltip
          [nzTooltipTitle]="dataEntryWorkDesignService.workDesignInfo?.name"
        >
          {{ dataEntryWorkDesignService.workDesignInfo?.name }}
        </div>
        <ng-template #headerCustom [ngTemplateOutlet]="headerCustomTemplate"></ng-template>
      </div>
      <div class="header-menu">
        <a
          class="header-menu-show"
          nz-dropdown
          [nzDropdownMenu]="menuTab"
          [nzOverlayClassName]="'work-design'"
          [nzTrigger]="'click'"
          (nzVisibleChange)="handlePageSelectVisibleChange($event)"
        >
          {{ dataEntryWorkDesignService?.activeMenu?.name }}
          <i [class]="{ open: isPageSelectVisible }" adIcon iconfont="iconzhankai1" aria-hidden="true"></i>
        </a>

        <nz-dropdown-menu #menuTab="nzDropdownMenu">
          <ul class="header-menu-list" nz-menu>
            <li
              nz-menu-item
              class="header-menu-list-item"
              *ngFor="let menu of menus"
              [class]="{
                active: dataEntryWorkDesignService.activeMenu?.code === menu.code
              }"
              (click)="handleToggleMenu(menu)"
            >
              <span class="text-overflow" (click)="handleToggleMenu(menu)"> {{ menu.name }}</span>
            </li>
          </ul>
        </nz-dropdown-menu>
      </div>
      <div class="menu-divider"></div>
      <div class="menu-toolbar">
        <div class="menu-toolbar-item" (click)="handleToggleMenu({code: 'pageView'})">
          <i adIcon iconfont="icondaima123" aria-hidden="true"></i>
          <span>{{ 'dj-代码' | translate }}</span>
        </div>
        <ng-container *ngIf="modelPageType !== 'browse'">
          <div class="menu-toolbar-item" (click)="handleToggleMenu({code: 'ruleCode'})">
            <i adIcon iconfont="iconxiangmuka" aria-hidden="true"></i>
            <span>{{ 'dj-规则代码' | translate }}</span>
          </div>
        </ng-container>
        <ng-container *operateAuth="{ prefix: 'update' }">
          <div class="menu-toolbar-item" *ngIf="showPreviewButton" (click)="handlePreview()">
            <i adIcon iconfont="iconyulan" aria-hidden="true"></i>
            <span>{{ 'dj-预览' | translate }}</span>
          </div>
        </ng-container>
      </div>
      <div class="header-btn" [ngStyle]="{ 'padding-right': modelPageType !== 'notModelDriven' ? '20px' : '60px' }">
        <ng-container *operateAuth="{ prefix: 'update' }">
          <app-module-publish-button
            #publishButton
            [size]="'large'"
            [module]="'DataEntry'"
            [pkValue]="dataEntryWorkDesignService.workData.code"
            [needTenant]="true"
            [needSave]="true"
            (clickPublicAction)="handleClickPublicAction()"
            (publicAction)="dataEntryWorkDesignService.setSaveLoading($event)"
          ></app-module-publish-button>
        </ng-container>

        <ng-container *operateAuth="{ prefix: 'update' }">
          <button ad-button adType="primary" (click)="handleSave()">
            {{ 'dj-保存' | translate }}
          </button>
        </ng-container>

        <a nz-dropdown [nzDropdownMenu]="menu">
          <i class="more" adIcon type="ellipsis"></i>
        </a>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <ul nz-menu nzSelectable>
            <li nz-menu-item (click)="openModifyHistoryModal()">{{ 'dj-修改历史' | translate }}</li>
          </ul>
        </nz-dropdown-menu>
      </div>
    </div>
    <div
      class="work-body"
      *ngIf="
        !dataEntryWorkDesignService.initInfoLoading &&
        ['design', 'listSetting', 'editPage'].includes(dataEntryWorkDesignService.activeMenu?.code)
      "
    >
      <app-dsl-work-design
        appCollaborate
        [collaborateInfo]="{
          type: designerType + modelPageType,
          sourceId: dataEntryWorkDesignService.workDesignInfo?.code
        }"
        [dataSources]="renderDataSources"
        [dataSourceName]="renderDataSourceName"
        [workData]="renderWorkData"
        [pageData]="renderPage"
        [workServiceData]="workServiceData"
        [renderStateManagementData]="renderExtendedFields"
        [config]="dataEntryWorkDesignService.config"
        [isCustom]="dataEntryWorkDesignService.isCustom"
        [customTip]="dataEntryWorkDesignService.customTip"
        [activeMenu]="dataEntryWorkDesignService.activeMenu?.code"
        [descriptionLang]="dataEntryWorkDesignService.descriptionLang"
        [submitActionsBase]="dataEntryWorkDesignService.submitActionsBase"
        [isvPackageDataList]="dataEntryWorkDesignService.isvPackageDataList"
        [masterFromDataSourceName]="dataEntryWorkDesignService.masterFromDataSourceName"
        (changeDataSource)="handleChangeDataSource($event)"
        (changeFieldSource)="handleChangeFieldSource($event)"
        (syncTab)="handleSyncTab($event)"
        (resetPageUIElement)="handleResetPageUIElement($event)"
        (changePageUIElementCode)="handleChangePageUIElementCode($event)"
        (changePage)="handleChangePage($event)"
        (changeCustom)="handleChangeCustom($event)"
        (changeFormInstance)="handleChangeFormInstance($event)"
        (changeWorkDetailData)="handleChangeWorkDetailData($event)"
        #appDslWorkDesign
      ></app-dsl-work-design>
    </div>

    <div class="state-management" *ngIf="dataEntryWorkDesignService.activeMenu?.code === 'stateManagement'">
      <app-state-management
        #stateManagement
        [extendedFields]="dataEntryWorkDesignService.workDesignInfo?.extendedFields"
        (valueChange)="handleStateChanged($event)"
      ></app-state-management>
    </div>
  </div>
</nz-spin>

<!--确认关闭开窗-->
<!-- <ad-modal
  nzClassName="close-page-modal"
  [nzWidth]="'400px'"
  [nzVisible]="showConfirm"
  [nzFooter]="null"
  [nzClosable]="false"
>
  <ng-container *adModalContent>
    <div class="confirm-tips">
      {{ 'dj-请确认是否已保存所有数据' | translate }}
    </div>
    <div class="modal-footer">
      <button ad-button adType="default" (click)="handleContinue()">
        {{ 'dj-继续编辑' | translate }}
      </button>
      <ng-container *operateAuth="{ prefix: 'update' }">
        <button ad-button adType="primary" (click)="handleSureSave()">
          {{ 'dj-保存并退出' | translate }}
        </button>
      </ng-container>
      <button ad-button adType="default" (click)="handleSureClose()">
        {{ 'dj-直接关闭' | translate }}
      </button>
    </div>
  </ng-container>
</ad-modal> -->

<!-- 预览 -->
<app-component-preview-modal
  *ngIf="previewVisible"
  [(visible)]="previewVisible"
  [params]="previewParam"
></app-component-preview-modal>

<!-- 代码 -->
<app-extend-editor-modal
  *ngIf="showPageView"
  [data]="showPageViewData"
  [title]="'dj-代码' | translate"
  (ok)="handlePageView($event)"
  (close)="showPageView = false"
>
</app-extend-editor-modal>

<!-- 规则代码 -->
<app-extend-editor-modal
  *ngIf="showRuleList"
  [data]="showRuleListData"
  [title]="'dj-规则代码' | translate"
  (ok)="showRuleList = false"
  (close)="showRuleList = false"
>
</app-extend-editor-modal>

<!-- 历史 -->
<app-modify-history-modal
  *ngIf="historyModalProps.transferModal"
  [historyModalProps]="historyModalProps"
  (closeModal)="historyModalProps.transferModal = false"
>
</app-modify-history-modal>

<app-openwindow-work-design
  *ngIf="openwindowVisible"
  [workVisible]="openwindowVisible"
  [workData]="openwindowData"
  (close)="handleCloseOpenwindow()"
  (onOk)="handleSaveOpenwindow($event)"
>
</app-openwindow-work-design>
