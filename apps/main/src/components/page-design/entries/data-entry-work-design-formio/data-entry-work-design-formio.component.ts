import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
  TemplateRef,
} from '@angular/core';
import { DataEntryWorkDesignService } from './service/data-entry-work-design.service';
import { DataEntryWorkDesignRequestService } from './service/data-entry-work-design-request.service';
import { TranslateService } from '@ngx-translate/core';
import {
  Menu,
  MenuCode,
  PageUIElementGenerateOrigin,
  ShowPageViewData,
  WorkData,
} from './config/data-entry-work-design.type';
import { historyModalProps, signDocumentConfigCategoryList } from './config/data-entry-work-design.config';

import { cloneDeep, isEqual, isNone } from 'common/utils/core.utils';

import {
  SIGN_DOCUMENT_CONFIG,
  DOUBLE_DOCUMENT_CONFIG,
} from 'components/page-design/components/dsl-work-design/shared/config/dsl-work-design.config';

import { <PERSON><PERSON><PERSON>ey, PageDesignCommonService } from 'common/service/page-design-common.service';
import { DslWorkFormService } from 'components/page-design/components/dsl-work-design/dsl-work-form/dsl-work-form.service';

import {
  DataSourceModeEnum,
  SubModuleEnum,
} from 'components/page-design/components/dsl-work-design/shared/config/dsl-work-sidebar.config';

import { NzMessageService } from 'ng-zorro-antd/message';
import { StateManagementComponent } from './components/state-management/state-management.component';
import { SESSIONSTORAGE_KEY_CONSTANT } from 'common/config/SessionStorageKeyConstant';
import { Subscription, Subject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';

import { DslWorkDesignComponent } from 'components/page-design/components/dsl-work-design/dsl-work-design.component';
import { AppService } from '../../../../pages/apps/app.service';
import { EActivitiyType } from 'components/bussiness-components/preview-modal';

import { IsvCustomPackageService } from 'common/service/isv-custom-package/isv-custom-package.service';
import { DslFormRenderShareService } from 'components/page-design/components/dsl-form-render/service/dsl-form-render-share.service';
import { AppTypes } from 'pages/app/typings';

import { getDataSourceNameList } from 'components/page-design/components/data-source/data-source.component.util';
import { DesignerTypes } from 'app/types';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-data-entry-work-design-formio',
  templateUrl: './data-entry-work-design-formio.component.html',
  styleUrls: ['./data-entry-work-design-formio.component.less'],
  providers: [DataEntryWorkDesignService, DataEntryWorkDesignRequestService],
})
export class DataEntryWorkDesignFormioComponent implements OnInit, OnChanges, OnDestroy {
  @Input() modelPageType: 'design' | 'browse' | 'edit' | 'notModelDriven' = 'notModelDriven'; // 模型驱动解决方案页面类型，默认是notModelDriven，走非模型驱动逻辑
  @Input() workData: WorkData; // 数据录入基础信息
  @Input() uiKey: string;
  @Input() dataViewQueryCode: string = ''; // isv组件管理跳转到界面设计时，用来定位当前数据源(当然也可作为初始化时，首次加载dsl的数据源dataViewQueryCode)
  @Input() isHideHeaderRight: boolean = false; // 是否隐藏头部右侧操作
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // 模型驱动解决方案自定义头部，之前处理这块的逻辑的开发者将headerCustomTemplate放在workData里面，参与了workData的更新，性能堪忧，现在将其优化拆分出去
  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() contentChangeWithoutSaveChange: EventEmitter<boolean> = new EventEmitter(); // 内容变更未保存状态的变化

  menus: Menu[] = []; // 菜单列表
  historyModalProps: HistoryModalProps = cloneDeep(historyModalProps); // 操作记录弹窗信息
  showPageView: boolean; // 代码界面是否打开
  showPageViewData: ShowPageViewData = {}; // 代码界面的代码数据

  showRuleList: boolean; // 规则代码是否打开
  showRuleListData: any[] = []; // 规则代码的数据

  // 用于dsl-work-design渲染的数据
  renderDataSources = {};
  renderPage = {};
  renderWorkData = {};
  renderDataSourceName = '';
  renderExtendedFields = null;

  // 是否展示预览按钮
  get showPreviewButton() {
    return [MenuCode.editPage, MenuCode.design].includes(this.dataEntryWorkDesignService.activeMenu?.code);
  }
  // 预览相关
  previewVisible = false;
  previewParam = null;

  workDataHasChanged: boolean = false; // 关闭时提醒

  destroy$ = new Subject();
  saveAndPublishLoadingChange$: Subscription; // 保存和发布loading状态变化
  contentChangeWithoutSaveChange$: Subscription; // 内容变更未保存状态的变化
  initPageData: {
    pageDsl: any;
    detailDsl: any;
    pageUIElement: any[];
  } = null; // 每个页面formio第一次完整渲染之后的数据对象（配合检测数据变更使用）

  workServiceData: any = {}; // 作业关于服务的信息

  openwindowVisible: boolean;
  openwindowData: any;

  designerType = `${DesignerTypes.WORK}@${this.userService.getUser('branch')}`;
  gobalErrorMessage: string = ''; // 数据录入错误信息，当不为空时，代表数据录入页面存在问题

  isPageSelectVisible: boolean = false; // 是否显示页面选择弹窗
  // 发布按钮
  @ViewChild('publishButton', { static: false }) publishButtonRef: any;
  @ViewChild('stateManagement') stateManagement: StateManagementComponent;
  @ViewChild('appDslWorkDesign') appDslWorkDesign: DslWorkDesignComponent;

  constructor(
    public dataEntryWorkDesignService: DataEntryWorkDesignService,
    private dataEntryWorkDesignRequestService: DataEntryWorkDesignRequestService,
    private pageDesignCommonService: PageDesignCommonService,
    private dslWorkFormService: DslWorkFormService,
    private cd: ChangeDetectorRef,
    private athMessageService: NzMessageService,
    private translateService: TranslateService,
    private appService: AppService,
    private isvCustomPackageService: IsvCustomPackageService,
    private dslFormRenderShareService: DslFormRenderShareService,
    private userService: AdUserService,
  ) {}

  ngOnInit(): void {
    this.dataEntryWorkDesignService.setRenderLoading(true);
    this.saveAndPublishLoadingChange$ = this.dataEntryWorkDesignService.saveLoadingChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        this.saveAndPublishLoadingChange.emit(isLoading);
      });

    this.dslWorkFormService.formIsReady$
      .pipe(takeUntil(this.destroy$))
      .pipe(debounceTime(500))
      .subscribe((val: any) => {
        this.dataEntryWorkDesignService.setRenderLoading(!val.status);
        this.cd.detectChanges();
      });

    this.dslFormRenderShareService.openwindowChangeEvent$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      this.openwindowVisible = true;
      this.openwindowData = data?.data;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {

    if (Object.keys(changes).includes('workData')) {
      this.handleInit();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 初始化
  private async handleInit(): Promise<void> {
    try {
      this.dataEntryWorkDesignService.setInitInfoLoading(true);
      this.dataEntryWorkDesignService.setWorkData(this.workData);

      // 必须先请求自定义组件信息，之后再请求界面数据 进行渲染
      // appType 为 4 是随心控解决方案，随心控解决方案 不需要加载 自定义组件
      if (this.appService.selectedApp.appType !== AppTypes.SCENARIO_KIT) {
        const isvPackageDataList = await this.isvCustomPackageService.getIsvPackageDataList(
          this.appService.selectedApp,
        );
        this.dataEntryWorkDesignService.setIsvPackageDataList(isvPackageDataList);
      }

      const initWorkDesignInfoRes = await this.initWorkDesignInfo();
      this.dataEntryWorkDesignService.setWorkDesignInfo(initWorkDesignInfoRes.data);

      sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.serviceCode, initWorkDesignInfoRes.data.serviceCode);
      this.dataEntryWorkDesignService.setInitInfoLoading(false);

      this.initMenus();
      this.updateRenderInfo();
      this.getServiceCode();
    } catch (error) {
      this.gobalErrorMessage = error?.error?.errorMessage ?? error?.message ?? 'init error';
    }
  }

  // 初始化菜单
  initMenus(): void {
    this.menus = [];
    if (signDocumentConfigCategoryList.includes(this.dataEntryWorkDesignService.workDesignInfo.category)) {
      this.menus.push({
        name: this.translateService.instant('dj-界面设计'),
        code: MenuCode.design,
        icon: 'icontable',
      });
    } else {
      if (this.modelPageType !== 'edit') {
        this.menus.push({
          name: this.translateService.instant('dj-浏览界面'),
          code: MenuCode.listSetting,
          icon: 'iconbars',
        });
      }

      if (this.modelPageType !== 'browse') {
        this.menus.push({
          name: this.translateService.instant('dj-编辑界面'),
          code: MenuCode.editPage,
          icon: 'icontable',
        });
      }
    }
    if (this.modelPageType === 'notModelDriven') {
      this.menus.push({
        name: this.translateService.instant('dj-状态管理'),
        code: MenuCode.stateManagement,
        icon: 'iconsync',
      });
    }

    const { defaultCategory } = this.workData;
    let code = this.menus[0].code;

    if (defaultCategory === 'SIGN-DOCUMENT') {
      if (this.uiKey === 'pageDsl') {
        code = MenuCode.design;
      }
    } else {
      if (this.uiKey === 'pageDsl') {
        code = MenuCode.listSetting;
      } else if (this.uiKey === 'detailDsl') {
        code = MenuCode.editPage;
      }
    }

    const menu = this.menus.find(s => s.code === code);
    this.dataEntryWorkDesignService.setActiveMenu(menu);

    if (this.dataViewQueryCode) {
      this.dataEntryWorkDesignService.resetDataSourceName(this.dataViewQueryCode);
    }
  }

  // 更新渲染数据
  updateRenderInfo(): void {
    if (
      ![MenuCode.editPage, MenuCode.listSetting, MenuCode.design].includes(
        this.dataEntryWorkDesignService.activeMenu?.code,
      )
    )
      return;

    this.handleQueryButtons(this.dataEntryWorkDesignService.activeMenu?.code);

    // 获取基础config
    const config = cloneDeep(
      signDocumentConfigCategoryList.includes(this.dataEntryWorkDesignService.workDesignInfo.category)
        ? SIGN_DOCUMENT_CONFIG
        : DOUBLE_DOCUMENT_CONFIG,
    );

    config.formCenterConfig.dataEntryDefaultOptionsCode = this.dataEntryWorkDesignService.activeMenu?.code as
      | 'design'
      | 'listSetting'
      | 'editPage';

    config.formCenterConfig.isShowCustomButton = [MenuCode.design, MenuCode.editPage].includes(
      this.dataEntryWorkDesignService.activeMenu?.code,
    );
    config.canCustom = config.formCenterConfig.isShowCustomButton;

    config.formSetConfig.uploadCategory = this.appService?.selectedApp?.code;
    config.formSetConfig.productName = this.dataEntryWorkDesignService.workDesignInfo?.serviceCode;

    config.formCenterConfig.applyToField = ['design', 'listSetting'].includes(
      this.dataEntryWorkDesignService.activeMenu?.code,
    )
      ? 'BUTTON_GROUP'
      : 'UIBOT_BUTTON_GROUP';

    // 浏览界面 不展示 规则列表
    if (this.dataEntryWorkDesignService.activeMenu?.code === MenuCode.listSetting) {
      config.sidebarConfig.ModuleCodeList = config.sidebarConfig.ModuleCodeList.filter(
        (code) => code !== SubModuleEnum.Rule,
      );
    }

    // 模型驱动解决方案展示数据查询视图
    if (this.modelPageType === 'edit') {
      config.formCenterConfig.serachViewDisplay = true;
    }

    // 模型驱动解决方案的浏览界面 允许切换查询方案
    if (this.modelPageType === 'browse') {
      config.sidebarConfig.dataSourceOptions.isShowQueryPlan = true; // 开启查询方案数据源
      config.sidebarConfig.queryPlanDataSourceMode = DataSourceModeEnum.Multiple;
    }

    this.dataEntryWorkDesignService.setConfig(config);

    // 处理渲染数据
    this.renderWorkData = cloneDeep(this.dataEntryWorkDesignService.workData);
    this.renderDataSources = cloneDeep(this.dataEntryWorkDesignService.dataSources);
    this.renderDataSourceName = cloneDeep(this.dataEntryWorkDesignService.dataSourceName);
    this.renderPage = cloneDeep(this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData()?.elements);

    // 浏览界面和界面设计 状态列数据
    this.renderExtendedFields = null;
    if ([MenuCode.listSetting, MenuCode.design].includes(this.dataEntryWorkDesignService.activeMenu?.code)) {
      this.renderExtendedFields = cloneDeep(this.dataEntryWorkDesignService.workDesignInfo.extendedFields ?? null);
    }

    this.cd.detectChanges();

    // 沿用之前的逻辑，触发界面的数据刷新
    this.dslWorkFormService.UpdatePageByCode();
  }

  /**
   * 获取作业的serviceCode
   */
  getServiceCode() {
    this.dataEntryWorkDesignRequestService.getSimpleModelCodeDatas().subscribe((data) => {
      if (data.data) {
        const serviceData =
          data.data?.find(
            (item) =>
              item.code === this.workData.simpleModelCode && item.serviceCode === this.workData.simpleModelServiceCode,
          ) || {};
        this.workServiceData = {
          serviceCode: serviceData?.serviceCode || '',
          targetProd: serviceData?.customProperties?.targetProd || '',
        };
      }
    });
  }

  // 获取按钮配置
  handleQueryButtons(pageType: string): void {
    let key: ButtonsKey = null;
    switch (pageType) {
      case MenuCode.design: // 单档多栏界面设计
        key = 'BASIC-DATA_SINGLE';
        break;
      case MenuCode.listSetting: // 浏览界面
        key = 'BASIC-DATA_DOUBLE_BROWSE';
        break;
      case MenuCode.editPage: // 编辑界面
        key = 'BASIC-DATA_DOUBLE_EDIE';
        break;
    }
    if (!isNone(key)) {
      this.pageDesignCommonService.handleQueryButtonsByKey(key).subscribe((data) => {
        this.dataEntryWorkDesignService.setSubmitActionsBase(data);
      });
    }
  }

  // 初始化界面设计数据信息
  initWorkDesignInfo(): Promise<any> {
    const params = {
      code: this.dataEntryWorkDesignService.workData.code,
      type: 'dsl',
    };
    return this.dataEntryWorkDesignRequestService.loadModelPageView(params).toPromise();
  }

  // 切换菜单
  handleToggleMenu(menu: any): void {
    if (this.dataEntryWorkDesignService.activeMenu?.code === menu.code) return;
    this.dataEntryWorkDesignService.setRenderLoading(true);
    if (menu.code === MenuCode.pageView) {
      // 代码
      this.showPageViewData = cloneDeep(
        this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData()?.elements,
      );
      if (!this.showPageViewData) {
        this.athMessageService.error(this.translateService.instant('dj-没有设定数据源'));
        this.dataEntryWorkDesignService.setRenderLoading(false);
        return;
      }
      this.showPageView = true;
      this.dataEntryWorkDesignService.setRenderLoading(false);
      return;
    }
    if (menu.code === MenuCode.ruleCode) {
      // 规则代码
      this.showRuleListData = cloneDeep(this.dataEntryWorkDesignService.ruleList);
      this.showRuleList = true;
      this.dataEntryWorkDesignService.setRenderLoading(false);
      return;
    }
    if (menu.code === MenuCode.stateManagement) {
      this.dataEntryWorkDesignService.setRenderLoading(false);
    }
    // 作业设计切换页面时，需要手动将sidebar中的showHooks重置
    if (this.appDslWorkDesign?.dslWorkSidebar?.dslWorkSidebarService?.showHooks) {
      this.appDslWorkDesign.dslWorkSidebar.dslWorkSidebarService.showHooks = false;
    }
    this.dataEntryWorkDesignService.setActiveMenu({
      ...this.dataEntryWorkDesignService.activeMenu,
      ...menu,
    });
    this.updateRenderInfo();
  }

  handlePageSelectVisibleChange(value) {
    this.isPageSelectVisible = value;
  }

  // 保存
  async handleSave(isAfterSaveNeedPublish = false): Promise<void> {
    if (
      this.dataEntryWorkDesignService.formInstance?.editForm &&
      this.dataEntryWorkDesignService.formInstance?.editForm?.initialized &&
      !this.dataEntryWorkDesignService.formInstance?.editForm.checkValidity()
    ) {
      this.athMessageService.error(this.translateService.instant('dj-字段配置中存在必填项未填写'));
      return;
    }

    if (
      ['design', 'listSetting', 'editPage'].includes(this.dataEntryWorkDesignService.activeMenu?.code) &&
      !this.appDslWorkDesign?.handleValidateSubmit()
    ) {
      this.athMessageService.error(this.translateService.instant('dj-字段配置中存在必填项未填写'));
      return;
    }

    if (this.stateManagement && !this.stateManagement.saveStateManagement()) return;

    this.dataEntryWorkDesignService.setSaveLoading(true);
    await this.saveCrudForm();

    if (isAfterSaveNeedPublish) {
      this.publishButtonRef.startPublish();
    } else {
      this.athMessageService.success(this.translateService.instant('dj-保存成功'));
    }

    this.initPageData = this.getInitPageData();
    this.judgeDataChange();

    this.dataEntryWorkDesignService.setSaveLoading(false);
  }

  async saveCrudForm(): Promise<any> {
    const workDesignInfo = cloneDeep(this.dataEntryWorkDesignService.workDesignInfo);
    // 固定false 给后台区分模型设计|界面设计（false）
    workDesignInfo.generateDslAction = true;
    workDesignInfo['masterFromDataSourceName'] = this.dataEntryWorkDesignService.masterFromDataSourceName;
    return this.dataEntryWorkDesignRequestService.saveCrudForm(workDesignInfo).toPromise();
  }

  openModifyHistoryModal(): void {
    this.historyModalProps.code = this.dataEntryWorkDesignService.workData.code;
    this.historyModalProps.transferModal = true;
  }

  handlePageView(data: any): void {
    this.showPageView = false;
    this.dataEntryWorkDesignService.setCurrentDataSourcePageUIElementData({
      ...this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData(),
      elements: data,
    });
    this.updateRenderInfo();
  }

  // 数据源发生变化
  async handleChangeDataSource(data: any) {
    this.dataEntryWorkDesignService.setWorkDesignInfoByKeyPath(
      [this.dataEntryWorkDesignService.pageType, 'dataSources'],
      data.dataSources,
    );
    this.updateDataSourceNames();

    await this.updataCurrentPageUIElementByDataSources();
    this.updateRenderInfo();
    this.judgeDataChange();
  }

  // 切换数据源
  handleChangeFieldSource(dataSourceName: string): void {
    this.dataEntryWorkDesignService.setDataSourceName(dataSourceName);
    this.updateRenderInfo();
  }

  // 重置当前页指定数据源下数据
  async handleResetPageUIElement({ from: dataViewQueryCode }: { from: string }) {
    try {
      const dataSource = this.dataEntryWorkDesignService.getDataSourceByDataViewQueryCode(dataViewQueryCode);
      const dataSourceName = this.dataEntryWorkDesignService.getDataSourceNameByDataViewQueryCode(dataViewQueryCode);
      if (!dataSource) return;
      const pageUIElementSearchInfo =
        this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByDataSource(dataSource);

      const defaultPageUIElements = this.dataEntryWorkDesignService.masterFromDataSourceName
        ? [
            {
              ...pageUIElementSearchInfo,
              actionId: dataSource.actionId,
              type: dataSource.type,
              dataSourceName,
            },
          ]
        : [
            {
              ...pageUIElementSearchInfo,
              actionId: dataSource.actionId,
              type: dataSource.type,
            },
          ];

      this.dataEntryWorkDesignService.setSaveLoading(true);
      const defaultPageUIElementRes = await this.getDefaultPageUIElement(defaultPageUIElements);
      if (defaultPageUIElementRes?.data?.[0]) {
        this.dataEntryWorkDesignService.setPageUIElementData(pageUIElementSearchInfo, defaultPageUIElementRes.data[0]);
        this.updateRenderInfo();
      }
    } catch (error) {
      console.error(error);
    }

    this.dataEntryWorkDesignService.setSaveLoading(false);
  }

  // 更改dataViewQueryCode
  async handleChangePageUIElementCode({ from, to }: { from: string; to: string }) {
    const fromPageUIElementSearchInfo =
      this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByCode(from);
    // 原始的pageUIElementData
    const fromPageUIElementData = this.dataEntryWorkDesignService.getPageUIElementData(fromPageUIElementSearchInfo);

    if (this.modelPageType !== 'browse') {
      // 非 模型驱动 解决方案的 浏览界面 的 特殊逻辑
      // 因为只有 模型驱动2.0的浏览界面才支持  tab 模式
      // 但其他 情况下 既有 pageUIElement 结构，又不支持 tab 模式，所以需要特殊处理
      // 将选择保留时，将编辑界面所有的 pageUIElement 都更新为 之前的 pageUIElement
      Object.values(this.dataEntryWorkDesignService.dataSources).forEach((dataSource: any) => {
        const pageUIElementSearchInfo =
          this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByDataSource(dataSource);
        const pageUIElementData = { ...pageUIElementSearchInfo, elements: { ...fromPageUIElementData.elements } };
        this.dataEntryWorkDesignService.setPageUIElementData(pageUIElementSearchInfo, pageUIElementData);
      });
    }

    const toPageUIElementSearchInfo = this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByCode(to);
    const toPageUIElementData = { ...toPageUIElementSearchInfo, elements: fromPageUIElementData.elements };
    this.dataEntryWorkDesignService.setPageUIElementData(toPageUIElementSearchInfo, toPageUIElementData);
  }

  // close时判断页面数据是否变化
  judgeDataChange() {
    if (!!this.initPageData) {
      const {
        pageDsl: sourcePageDsl,
        detailDsl: sourceDetailDsl,
        pageUIElement: sourcePageUIElement,
      } = this.initPageData;
      const {
        pageDsl: targetPageDsl,
        detailDsl: targetDetailDsl,
        pageUIElement: targetPageUIElement,
      } = this.getInitPageData();

      const isPageDslEqual = isEqual(sourcePageDsl, targetPageDsl);
      const isDetailDslEqual = isEqual(sourceDetailDsl, targetDetailDsl);
      const isPageUIElementEqual =
        sourcePageUIElement.length === targetPageUIElement.length &&
        sourcePageUIElement.every((sourceItem) =>
          isEqual(
            sourceItem,
            targetPageUIElement.find(
              (targetItem) =>
                sourceItem.code === targetItem.code &&
                sourceItem.activityId === targetItem.activityId &&
                sourceItem.pageCode === targetItem.pageCode,
            ),
          ),
        );

      this.workDataHasChanged = !isPageDslEqual || !isDetailDslEqual || !isPageUIElementEqual;
      this.contentChangeWithoutSaveChange.emit(this.workDataHasChanged);
    }
  }

  // 渲染数据发生变化
  handleChangePage(pageData: any): void {
    const currentDataSourcePageUIElementData = this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData();
    if (!currentDataSourcePageUIElementData) return;

    const { layout, operations, submitActions, hooks, gridSettings, componentIdDataMap } = pageData;
    const { elements, ...otherInfo } = currentDataSourcePageUIElementData;
    this.dataEntryWorkDesignService.setCurrentDataSourcePageUIElementData({
      ...otherInfo,
      elements: {
        ...elements,
        layout,
        operations,
        submitActions,
        hooks,
        gridSettings,
        componentIdDataMap,
      },
    });

    // if (!this.initPageData && !this.dataEntryWorkDesignService.formInstance.isLoading) {
    //   this.initPageData = this.getInitPageData();
    // }
    if (!this.initPageData) this.initPageData = this.getInitPageData();
    this.judgeDataChange();
  }

  // 切换自定义界面
  handleChangeCustom(data: any): void {
    this.dataEntryWorkDesignService.setWorkDesignInfoByKeyPath(
      [this.dataEntryWorkDesignService.pageType, 'isCustomize'],
      data,
    );

    this.dataEntryWorkDesignService.pageUIElement.forEach((pageUIElement) => {
      if (pageUIElement.pageCode === 'browse-page') {
        const { elements, ...otherInfo } = pageUIElement;
        elements?.operations?.forEach((operation) => {
          if (operation.openWindowDefine) operation.openWindowDefine.isCustomize = data;
        });
        this.dataEntryWorkDesignService.setPageUIElementData(otherInfo, { ...otherInfo, elements });
      }
    });

    this.updateRenderInfo();
  }

  // formio实例发生变化
  handleChangeFormInstance(data: any): void {
    this.dataEntryWorkDesignService.setFormInstance(data);
  }

  async handlePreview(): Promise<void> {
    let uibotPageCode = '';
    switch (this.dataEntryWorkDesignService.activeMenu?.code) {
      case MenuCode.design:
        uibotPageCode = 'basic-data';
        break;
      case MenuCode.listSetting:
        uibotPageCode = 'browse-page';
        break;
      case MenuCode.editPage:
        uibotPageCode = 'edit-page';
        break;
    }
    // 保存后打开预览
    await this.handleSave();

    this.previewParam = {
      taskCode: this.dataEntryWorkDesignService.workDesignInfo.code,
      pageCode: uibotPageCode,
      actionId: this.dataEntryWorkDesignService?.dataSources[this.dataEntryWorkDesignService.dataSourceName]?.actionId,
      type: 'performer',
      domain: 'DataEntry',
      activityType: EActivitiyType.BASE_DATA,
      category: this.workData.defaultCategory,
    };
    this.previewVisible = true;
  }

  handleStateChanged($event: any): void {
    this.dataEntryWorkDesignService.setWorkDesignInfoByKeyPath(['extendedFields'], $event);
  }

  handleChangeWorkDetailData(data: any): void {
    this.dataEntryWorkDesignService.setRuleList(data?.rule ?? []);
  }

  // 处理了用户点击了发布
  handleClickPublicAction(): void {
    this.handleSave(true);
  }

  // 同步所有数据源对应的PageUIElement
  async updataCurrentPageUIElementByDataSources() {
    try {
      if (!this.dataEntryWorkDesignService.getCurrentDataSource()) {
        this.dataEntryWorkDesignService.resetDataSourceName();
      }

      const currentPageCode = this.dataEntryWorkDesignService.getPageCode(this.dataEntryWorkDesignService.pageType);

      // 获取其他页pageUIElementItem数据
      let otherPageUIElements =
        this.dataEntryWorkDesignService?.pageUIElement?.filter((pageUIElementItem) => {
          return pageUIElementItem.pageCode !== currentPageCode;
        }) ?? [];

      // 如果执行resetDataSourceName之后依然没有currentDataSource，那么说明当前页面没有数据源，所以理应清空对应的pageUIElements
      if (!this.dataEntryWorkDesignService.getCurrentDataSource()) {
        this.dataEntryWorkDesignService.setWorkDesignInfoByKey('pageUIElement', [...otherPageUIElements]);
        return;
      }

      // 当前页面所有的pageUIElements
      let currentPageDslPageUIElements = [];

      // 需要新增的pageUIElements 从后台 请求 default 数据
      let needAddPageUIElements: (PageUIElementGenerateOrigin & { dataSourceName?: string })[] = []; // 缺少的pageUIElement

      // 根据当前数据源 确定 数据源模式
      let isMultiple = this.dataEntryWorkDesignService.getCurrentDataSourceIsMultiple();

      // 单数据源模式
      if (!isMultiple) {
        const currentDataSourcePageUIElementData =
          this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData();
        if (currentDataSourcePageUIElementData) {
          currentPageDslPageUIElements.push(currentDataSourcePageUIElementData);
        } else {
          const pageUIElementSearchInfo =
            this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByDataSource(
              this.dataEntryWorkDesignService.getCurrentDataSource(),
            );

          const { actionId: currentDataSourceActionId, type: currentDataSourceType } =
            this.dataEntryWorkDesignService.getCurrentDataSource();
          needAddPageUIElements.push(
            this.dataEntryWorkDesignService.masterFromDataSourceName
              ? {
                  ...pageUIElementSearchInfo,
                  actionId: currentDataSourceActionId,
                  type: currentDataSourceType,
                  dataSourceName: this.dataEntryWorkDesignService.dataSourceName,
                }
              : {
                  ...pageUIElementSearchInfo,
                  actionId: currentDataSourceActionId,
                  type: currentDataSourceType,
                },
          );
        }
      } else {
        // 多数据源模式
        Object.keys(this.dataEntryWorkDesignService.dataSources).forEach((dataSourceName: any) => {
          const dataSource = this.dataEntryWorkDesignService.dataSources[dataSourceName];
          const pageUIElementSearchInfo =
            this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByDataSource(dataSource);

          // 现在数据源 控制了 同 code 的情况，但该段逻辑先注释 不 删除
          // 由于已经添加的查询方案 依然可以作为 新的 数据源存在，所以 在这里 如果 对应的 pageUIElement 已经被插入 那么就 跳过
          // if (
          //   this.dataEntryWorkDesignService.getTargetPageUIElementData(
          //     currentPageDslPageUIElements,
          //     pageUIElementSearchInfo,
          //   )
          // ) {
          //   return;
          // }

          const pageUIElementData = this.dataEntryWorkDesignService.getPageUIElementData(pageUIElementSearchInfo);
          if (!pageUIElementData) {
            needAddPageUIElements.push(
              this.dataEntryWorkDesignService.masterFromDataSourceName
                ? {
                    ...pageUIElementSearchInfo,
                    actionId: dataSource.actionId,
                    type: dataSource.type,
                    dataSourceName,
                  }
                : { ...pageUIElementSearchInfo, actionId: dataSource.actionId, type: dataSource.type },
            );
          } else {
            currentPageDslPageUIElements.push(pageUIElementData);
          }
        });
      }

      let addPageUIElements = [];
      if (needAddPageUIElements.length > 0) {
        this.dataEntryWorkDesignService.setSaveLoading(true);
        const defaultPageUIElementRes = await this.getDefaultPageUIElement(needAddPageUIElements);
        addPageUIElements = defaultPageUIElementRes.data;
      }

      this.dataEntryWorkDesignService.setWorkDesignInfoByKey('pageUIElement', [
        ...otherPageUIElements,
        ...currentPageDslPageUIElements,
        ...addPageUIElements,
      ]);
    } catch (error) {
      this.athMessageService.error(error?.error.errorMessage ?? error.message);
    }
    this.dataEntryWorkDesignService.setSaveLoading(false);
  }

  // 更新当前页面的dataSourceNames
  updateDataSourceNames() {
    let dataSourceNames = getDataSourceNameList(this.dataEntryWorkDesignService.dataSources) ?? [];

    if (dataSourceNames.length > 1 && !this.dataEntryWorkDesignService.getCurrentDataSourceIsMultiple()) {
      dataSourceNames = [dataSourceNames[0]];
    }

    this.dataEntryWorkDesignService.setWorkDesignInfoByKeyPath(
      [this.dataEntryWorkDesignService.pageType, 'dataSourceNames'],
      dataSourceNames,
    );
  }

  // 获取默认PageUIElement数据
  getDefaultPageUIElement(pageUIElementSearchInfoList: PageUIElementGenerateOrigin[]) {
    const params = {
      application: this.appService.selectedApp?.code,
      category: this.dataEntryWorkDesignService.workDesignInfo.category,
      dataSourceList: pageUIElementSearchInfoList,
    };
    return this.dataEntryWorkDesignRequestService.generateDefaultDslByCode(params).toPromise();
  }

  // 获取用于对比数据变更的初始化作业数据
  getInitPageData() {
    return {
      pageDsl: cloneDeep(this.dataEntryWorkDesignService.workDesignInfo['pageDsl'] ?? {}),
      detailDsl: cloneDeep(this.dataEntryWorkDesignService.workDesignInfo['detailDsl'] ?? {}),
      pageUIElement: cloneDeep(this.dataEntryWorkDesignService.workDesignInfo['pageUIElement'] ?? {}),
    };
  }

  // 查询方案 同步页签
  handleSyncTab({ dataSourceCode, syncTabs }) {
    const sourcePageUIElement = this.dataEntryWorkDesignService.getPageUIElementData(
      this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByCode(dataSourceCode),
    );

    if (!sourcePageUIElement) {
      console.error('当前queryPlan没有pageUIElement');
      return;
    }

    syncTabs.forEach(({ code }) => {
      const pageUIElementSearchInfo = this.dataEntryWorkDesignService.getDataSourcePageUIElementSearchInfoByCode(code);
      this.dataEntryWorkDesignService.setPageUIElementData(pageUIElementSearchInfo, {
        ...cloneDeep(sourcePageUIElement),
        ...pageUIElementSearchInfo,
      });
    });

    this.updateRenderInfo();
  }

  handleCloseOpenwindow() {
    this.openwindowVisible = false;
    this.openwindowData = undefined;
  }

  handleSaveOpenwindow(data) {
    this.dslFormRenderShareService.openwindowInEvent(data);
    this.handleCloseOpenwindow();
  }
}
