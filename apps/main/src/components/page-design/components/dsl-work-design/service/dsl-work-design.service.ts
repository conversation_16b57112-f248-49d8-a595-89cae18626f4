import { Injectable, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FieldData, FieldSourceMode, ModuleCode } from '../shared/interface/dsl-work-sidebar.interface';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { WorkDesignConfig } from '../shared/interface/dsl-work-design.interface';
import { distinctUntilChanged, filter } from 'rxjs/operators';
import { transformDataSourceList } from '../../data-source/data-source.component.util';
import { IsvCustomComponentInfo, PackageInfo } from '@webdpt/form-editor-components';
import { TangentHooksService } from 'components/bussiness-components/tangent-hooks/service/tangent-hooks.service';

@Injectable()
export class DslWorkDesignService implements OnDestroy {
  // 自定义打印操作
  private _operateCustomTemplateRelates: any = [];
  get operateCustomTemplateRelates() {
    return this._operateCustomTemplateRelates;
  }

  // isv定制组件信息
  private _isvPackageDataList: {
    packageInfo: PackageInfo;
    isvCustomComponentInfoList: IsvCustomComponentInfo[];
  }[] = [];
  get isvPackageDataList() {
    return this._isvPackageDataList;
  }

  // 用于同步左右的 hooks
  hooksSubject$ = new Subject<any>();

  favouriteCode: string; // 收藏的页面code
  headerCofig = {}; // 用于保存请求头
  applicationCodeProxy = null; // 解决方案code

  extendHeader: any = {};

  isTenantProcessId: boolean = false; // 是否是租户流程id

  /**
   * 初始化自动生成表单的监听事件
   */
  public layoutSubject: BehaviorSubject<Element> = new BehaviorSubject<Element>(null);

  private _activeModule: ModuleCode;
  private _config: WorkDesignConfig;
  private _workData: any;
  private _originFieldData: FieldData;

  /**
   * 数据源: TagWorkSidebarComponent
   * 用途: 只读
   */
  set activeModule(code: ModuleCode) {
    this._activeModule = code;
    this.activeModuleChange$.next(code);
  }
  get activeModule(): ModuleCode {
    return this._activeModule;
  }
  activeModuleChange$: Subject<ModuleCode> = new Subject();

  /**
   * 数据源: TagWorkDesignComponent
   * 用途: 只读
   */
  public taskCode: string;
  set workData(data: any) {
    this._workData = data;
    this.taskCode = data.code;
  }
  get workData() {
    return this._workData;
  }
  private _dataSourceList: any;
  get dataSourceList(): any {
    return this._dataSourceList;
  }

  public dataSources: any;
  public dataSourceName: string;
  public actionId: string;

  public pageCode: string;
  public inquireWordLibrary: boolean;
  public fieldSourceMode: FieldSourceMode;
  set config(designConfig: WorkDesignConfig) {
    this._config = designConfig;
    this.pageCode = designConfig.pageCode;
    this.fieldSourceMode = designConfig?.sidebarConfig?.fieldSourceMode;
    this.inquireWordLibrary = designConfig?.sidebarConfig?.inquireWordLibrary;
  }
  get config(): WorkDesignConfig {
    return this._config;
  }

  public fieldDataSubject: BehaviorSubject<any[]> = new BehaviorSubject([]);
  public fieldTree: any[];
  set fieldData(data: any) {
    const { fieldData, fieldTree } = data;
    this._originFieldData = fieldData;
    this.fieldTree = fieldTree;
    this.fieldDataSubject.next(data);
    this.tangentHooksService.fieldData = fieldTree?.[0];
  }
  get fieldData(): any {
    return this._originFieldData;
  }

  /**
   * 数据源: TagWorkDesignComponent
   * 用途: 读、写（写作用域: TagWorkDesignComponent）
   */
  // public isMobile: boolean = false;

  // formio 渲染实例
  _formInstance$ = new BehaviorSubject<any>(null);
  get formInstance$(): Observable<any> {
    return this._formInstance$.asObservable().pipe(distinctUntilChanged());
  }

  /**
   * 当前选中的数据状态提升到外部service
   */
  _selectedDataState$ = new BehaviorSubject<any>(null);
  get selectedDataState$(): Observable<any> {
    return this._selectedDataState$.asObservable().pipe(filter((data) => data !== null));
  }
  setSelectedDataState(data) {
    this._selectedDataState$.next(data);
  }

  constructor(private translate: TranslateService, private tangentHooksService: TangentHooksService) {}

  ngOnDestroy() {
    this.activeModuleChange$.unsubscribe();
  }

  /**
   * 更新formio 渲染实例
   */
  updateFormInstance(data) {
    this._formInstance$.next(data);
  }

  // 设置数据源列表
  setSourceList(data: any): void {
    this._dataSourceList = transformDataSourceList(data);
  }

  // 设置 isv定制组件信息
  setIsvPackageDataList(
    isvPackageDataList: {
      packageInfo: PackageInfo;
      isvCustomComponentInfoList: IsvCustomComponentInfo[];
    }[],
  ) {
    this._isvPackageDataList = isvPackageDataList;
  }

  // 设置自定义打印
  setOperateCustomTemplateRelates(custom: any[]): void {
    this._operateCustomTemplateRelates = custom || [];
  }
}
