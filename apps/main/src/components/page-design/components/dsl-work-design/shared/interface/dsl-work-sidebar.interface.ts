import { DataSourceOptions } from '../../../data-source/data-source.interface';

/**
 * 子模块菜单
 */
type ModuleCode = 'dataSource' | 'field' | 'widget' | 'rule' | 'config' | 'hook' | 'null' | 'cardField';

/**
 * 选择字段的模式
 * multiple: 使用多个数据源，可选择一个；
 * single: 使用单个数据源，默认取第一个；
 * action: 不使用数据源，自行选择action。
 */
type DataSourceMode = 'single' | 'singleMultiple' | 'multiple'; // FieldSourceMode === 'dataSource' 时，才有DataSourceMode
type FieldSourceMode = 'dataSource' | 'action'; // FieldSourceMode === 'action' 时，没有DataSourceMode，但是展现形式默认是 'single'
type FieldDataMode = 'ActionResponse' | 'ActionRequest'; // 接口请求类型，目前组建只支持数据源使用'ActionResponse'，action使用'ActionRequest'
interface ModuleConfig {
  ModuleCodeList: ModuleCode[];
  dataSourceOptions: DataSourceOptions;
  basicDataSourceMode: DataSourceMode;
  queryPlanDataSourceMode: DataSourceMode;
  fieldSourceMode: FieldSourceMode;
  fieldDataMode: FieldDataMode;
  inquireWordLibrary: boolean;
  isQueryEspActionFieldsInModel: boolean; // 是否使用模型驱动界面设计的数据源接口
  isTenantProcessId: boolean; // 是否租户级
  offline: boolean; // 是否离线 随心控规则离线配置
  hideDataSource: boolean; // 是否隐藏数据源
  openWindowFlag: boolean; // 是否是开窗
  interceptDataSourceUpdateOperations?: boolean; // 数据源逻辑变更，增加标记是否需要拦截数据源更新操作
}
interface SubModule {
  title: string; // 子模块菜单标题
  icon: string; // 子模块菜单图标
  selected: boolean; // 子模块菜单是否可选中
  disabled: boolean; // 子模块菜单是否可禁用
  customNotDisabled: boolean; // 定制时子模块菜单是否不可禁用
}

type PanelStatus = 'block' | 'absolute';

type Mark = 'field' | 'subFields';
interface FieldData {
  data: any;
  mark: Mark;
}

/**
 * 接口定义: 查询字段必要参数接口
 * masterFromDataSourceName: 接口参数是否需要传递数据源名称
 * dataSourceName: 数据源名称
 */
interface FieldParams {
  masterFromDataSourceName: boolean;
  dataSourceName: string;
}
interface Action {
  actionId: string;
  actionName: string;
  actionType: string;
}
interface IntelligentData {
  intelligentFields: any[];
  intelligentWords: any[];
}

export {
  ModuleConfig,
  ModuleCode,
  SubModule,
  PanelStatus,
  DataSourceMode,
  FieldSourceMode,
  FieldDataMode,
  Mark,
  FieldData,
  FieldParams,
  Action,
  IntelligentData,
};
