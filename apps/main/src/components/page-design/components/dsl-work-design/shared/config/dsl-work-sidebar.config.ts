import { DataSourceTypeEnum } from 'components/page-design/components/data-source/data-source.config';
import { ModuleCode, ModuleConfig, SubModule } from '../interface/dsl-work-sidebar.interface';

enum SubModuleEnum {
  DataSource = 'dataSource',
  Field = 'field',
  Widget = 'widget',
  Rule = 'rule',
  Hook = 'hook',
  Config = 'config',
  Null = 'null',
  CardField = 'cardField',
}

const SUB_MODULES: Record<ModuleCode, SubModule> = {
  [SubModuleEnum.DataSource]: {
    title: '数据源',
    icon: 'iconshujuyuan123',
    selected: true,
    disabled: false,
    customNotDisabled: true,
  },
  [SubModuleEnum.Field]: {
    title: '字段',
    icon: 'iconziduan123',
    selected: true,
    disabled: true,
    customNotDisabled: false,
  },
  [SubModuleEnum.Widget]: {
    title: '组件',
    icon: 'iconzujianku123',
    selected: true,
    disabled: true,
    customNotDisabled: true,
  },
  [SubModuleEnum.Rule]: {
    title: '规则',
    icon: 'iconguize123',
    selected: true,
    disabled: true,
    customNotDisabled: false,
  },
  [SubModuleEnum.Hook]: {
    title: 'Hooks',
    icon: 'icondaima123',
    selected: true,
    disabled: true,
    customNotDisabled: false,
  },
  [SubModuleEnum.Config]: {
    title: '扩展',
    icon: 'iconkuozhan',
    selected: false,
    disabled: true,
    customNotDisabled: true,
  },
  [SubModuleEnum.Null]: {
    title: '未选择',
    icon: 'null',
    selected: false,
    disabled: true,
    customNotDisabled: true,
  },
  [SubModuleEnum.CardField]: {
    title: '字段',
    icon: 'iconziduan123',
    selected: true,
    disabled: true,
    customNotDisabled: false,
  },
};

enum DataSourceModeEnum {
  Multiple = 'multiple',
  Single = 'single',
  SingleMultiple = 'singleMultiple',
}

enum FieldSourceModeEnum {
  DataSource = 'dataSource',
  Action = 'action',
}

enum FieldDataModeEnum {
  ActionResponse = 'ActionResponse',
  ActionRequest = 'ActionRequest',
}

export { SubModuleEnum, SUB_MODULES, DataSourceModeEnum, FieldSourceModeEnum, FieldDataModeEnum };

// 配置参考
/**
 * 项目卡任务卡设置
 */
export const TASK_CARD_PROJECT_CARD_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'cardField'],
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: false,
    optimizeMetadata: false,
    isShowQueryPlan: false, // 壳子中修改：数据驱动解决方案: false, 模型驱动解决方案: true
  },
  basicDataSourceMode: DataSourceModeEnum.SingleMultiple,
  queryPlanDataSourceMode: DataSourceModeEnum.SingleMultiple,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: true,
  interceptDataSourceUpdateOperations: true,
};

/**
 * 随心控项目卡任务卡设置
 */
export const KIT_TASK_CARD_PROJECT_CARD_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource'],
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: false,
    optimizeMetadata: false,
    isShowQueryPlan: false,
  },
  offline: true,
  hideDataSource: true,
};

/**
 * 普通任务的执行者设置界面
 */
export const TASK_DETAIL_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'field', 'widget', 'rule', 'hook'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: false,
    optimizeMetadata: false,
    isShowQueryPlan: false, // 壳子中修改：数据驱动解决方案: false, 模型驱动解决方案: true
  },
  basicDataSourceMode: DataSourceModeEnum.SingleMultiple,
  queryPlanDataSourceMode: DataSourceModeEnum.SingleMultiple,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: true,
  interceptDataSourceUpdateOperations: true,
};

/**
 * 随心控任务的执行者设置界面
 */
export const KIT_TASK_DETAIL_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'field', 'widget', 'rule', 'hook'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: false,
    optimizeMetadata: false,
    isShowQueryPlan: false,
  },
  basicDataSourceMode: DataSourceModeEnum.Single,
  queryPlanDataSourceMode: DataSourceModeEnum.Single,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: true,
  offline: true,
  hideDataSource: true,
};

/**
 * 普通任务的当责者设置界面
 */
export const PROJECT_DETAIL_SIDEBAR_CONFIG: Partial<ModuleConfig> = TASK_DETAIL_SIDEBAR_CONFIG;

/**
 * 手动发起项目的任务详情界面
 */
export const TASK_DETAIL_SIDEBAR_CONFIG2: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'field', 'widget', 'rule', 'hook'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: false,
    optimizeMetadata: true,
    isShowQueryPlan: false, // 壳子中修改：数据驱动解决方案: false, 模型驱动解决方案: true
  },
  basicDataSourceMode: DataSourceModeEnum.Single,
  queryPlanDataSourceMode: DataSourceModeEnum.Single,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: false,
  interceptDataSourceUpdateOperations: true,
};

/**
 * ABI报表条件设计界面
 */
export const CONDITION_DETAIL_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['field', 'widget', 'rule', 'hook'],
  fieldSourceMode: FieldSourceModeEnum.Action,
  fieldDataMode: FieldDataModeEnum.ActionRequest,
  inquireWordLibrary: false,
  interceptDataSourceUpdateOperations: true,
};

/**
 * 数据录入单档统一配置
 */
export const SIGN_DOCUMENT_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'field', 'widget', 'rule', 'hook'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: true,
    optimizeMetadata: false,
    isShowQueryPlan: false, // 壳子中修改：数据驱动解决方案: false, 模型驱动解决方案: true
  },
  basicDataSourceMode: DataSourceModeEnum.Single,
  queryPlanDataSourceMode: DataSourceModeEnum.Single,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: false,
  isQueryEspActionFieldsInModel: true,
  interceptDataSourceUpdateOperations: true,
};

/**
 * 数据录入双档统一配置
 */
export const DOUBLE_DOCUMENT_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'field', 'widget', 'rule', 'hook'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: false,
    hideProcessing: true,
    optimizeMetadata: false,
    isShowQueryPlan: false, // 壳子中修改：数据驱动解决方案: false, 模型驱动解决方案: true
  },
  basicDataSourceMode: DataSourceModeEnum.Single,
  queryPlanDataSourceMode: DataSourceModeEnum.Single,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: false,
  isQueryEspActionFieldsInModel: true,
  interceptDataSourceUpdateOperations: true,
};

/**
 * 界面配置工具统一配置
 */
export const INTERFACE_CONFIGURATION_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'field', 'widget', 'rule', 'hook'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: true,
    hideProcessing: true,
    optimizeMetadata: false,
    isShowQueryPlan: false,
  },
  basicDataSourceMode: DataSourceModeEnum.Single,
  queryPlanDataSourceMode: DataSourceModeEnum.Single,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: false,
  isQueryEspActionFieldsInModel: true,
};

/**
 * 开窗配置
 */
export const OPENWINDOW_SIDEBAR_CONFIG: Partial<ModuleConfig> = {
  ModuleCodeList: ['dataSource', 'cardField'], // [界面设计切片]
  dataSourceOptions: {
    hideNotArray: true,
    hideProcessing: true,
    optimizeMetadata: true,
    isShowQueryPlan: false,
    customMenuList: [DataSourceTypeEnum.ESP],
  },
  basicDataSourceMode: DataSourceModeEnum.Single,
  queryPlanDataSourceMode: DataSourceModeEnum.Single,
  fieldSourceMode: FieldSourceModeEnum.DataSource,
  fieldDataMode: FieldDataModeEnum.ActionResponse,
  inquireWordLibrary: false,
  openWindowFlag: true,
  interceptDataSourceUpdateOperations: true,
};
