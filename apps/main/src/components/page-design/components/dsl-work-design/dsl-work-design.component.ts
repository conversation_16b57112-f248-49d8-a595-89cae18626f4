import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  NgZone,
  OnInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
} from '@angular/core';

import { DslWorkSidebarComponent } from './dsl-work-sidebar/dsl-work-sidebar.component';

import { DslWorkSetSubmitComponent } from './dsl-work-form/components/dsl-work-set/submit/dsl-work-set-submit.component';
import { WorkDesignConfig } from './shared/interface/dsl-work-design.interface';

import { DslWorkDesignService } from './service/dsl-work-design.service';
import { DslWorkFormComponent } from './dsl-work-form/dsl-work-form.component';
import { DslWorkFormService } from './dsl-work-form/dsl-work-form.service';

import { isEmpty } from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ValidateSubmit } from './shared/utils/dsl-work-design.component.util';
import { Subscription } from 'rxjs';
import { isNone } from 'common/utils/core.utils';
import { IsvPackageDataAllInfo } from '@webdpt/form-editor-components';
import { DSL_Route_Config } from './shared/config/dsl-route.config';
import { stringToRegexp } from './shared/utils/dsl-path-regexp';
import { FieldSourceModeEnum } from './shared/config/dsl-work-sidebar.config';
import { UUID } from 'angular2-uuid';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'app-dsl-work-design',
  templateUrl: './dsl-work-design.component.html',
  styleUrls: ['./dsl-work-design.component.less'],
})
export class DslWorkDesignComponent implements OnInit, OnDestroy, OnChanges {
  moduleData: any; // 字段或模块属性
  switchSourceModal: boolean;
  isNone = isNone;
  submitActions = [];

  get isNoSource(): boolean {
    if (this.service.fieldSourceMode === FieldSourceModeEnum.Action) {
      return isEmpty(this.actionId);
    }
    return Object.keys(this.dataSources ?? {})?.length === 0;
  }

  get dataSources(): any {
    return this.service.dataSources;
  }
  get dataSourceName(): string {
    return this.service.dataSourceName;
  }
  get actionId(): string {
    return this.service.actionId;
  }

  get fieldData() {
    return this.service.fieldData;
  }

  formInstanceSubScription: Subscription;
  workDetailDataSubScription: Subscription;
  submitActionsSubScription: Subscription;

  @Input() showOperateCustomTemplate: boolean = true;
  // 自定义的打印操作
  @Input() set operateCustomTemplateRelates(custom: any[]) {
    this.service.setOperateCustomTemplateRelates(custom);
  }
  @Input() set config(designConfig: WorkDesignConfig) {
    this.service.config = designConfig;
  }
  @Input() isCustom: boolean; // 是否定制
  @Input() customTip: string; // 定制作业名称
  // 非定制
  @Input() set actionId(id: string) {
    this.service.actionId = id;
  }
  @Input() set dataSources(data: any) {
    this.service.dataSources = data ?? {};
    this.service.setSourceList(data || {});
  }
  @Input() set dataSourceName(name: string) {
    this.service.dataSourceName = name;
  }
  @Input() set workData(data: any) {
    this.service.workData = data;
  }
  @Input() pageData: any;
  @Input() renderStateManagementData: any; // 用于渲染的状态数据
  @Input() activeMenu: string;
  @Input() UIBOT__appendEocFields: any;
  @Input() submitActionsBase: any[];
  @Input() descriptionLang?: any; // 项目描述 PROJECT定制字段

  // 场景化套件解决方案的返回字段
  @Input() allReturnFields_A: any[] = [];
  @Input() workServiceData;
  // isv定制组件信息
  @Input() isvPackageDataList: IsvPackageDataAllInfo[] = [];

  @Input() favouriteCode: string;
  @Input() applicationCodeProxy: string;

  @Input() extendHeader: any = null;

  @Input() masterFromDataSourceName: boolean;

  @ViewChild('dslWorkSidebar') dslWorkSidebar: DslWorkSidebarComponent;
  @ViewChild('dslWorkForm') dslWorkForm: DslWorkFormComponent;
  @ViewChild('dslWorkSetSubmit') dslWorkSetSubmit: DslWorkSetSubmitComponent;

  @Output() changeDataSource: EventEmitter<any> = new EventEmitter();
  @Output() changeFieldSource: EventEmitter<any> = new EventEmitter();
  @Output() changePage: EventEmitter<any> = new EventEmitter();
  @Output() sendFieldData: EventEmitter<any> = new EventEmitter();
  @Output() changeCustom: EventEmitter<any> = new EventEmitter();
  @Output() refreshPageView: EventEmitter<any> = new EventEmitter();
  @Output() changeFormInstance: EventEmitter<any> = new EventEmitter();
  @Output() changeWorkDetailData: EventEmitter<any> = new EventEmitter(); // 包含规则数据
  @Output() syncTab: EventEmitter<any> = new EventEmitter(); // 查询方案 同步页签
  @Output() resetPageUIElement: EventEmitter<any> = new EventEmitter(); // 重置数据
  @Output() changePageUIElementCode: EventEmitter<any> = new EventEmitter(); // 保留数据
  @Output() changeOperateCustomTemplates: EventEmitter<any> = new EventEmitter(); // 修改了打印操作

  get appCode(): string {
    return this.appService.selectedApp?.code;
  }

  constructor(
    public service: DslWorkDesignService,
    private translate: TranslateService,
    private message: NzMessageService,
    private zone: NgZone,
    public dslWorkFormService: DslWorkFormService,
    public appService: AppService,
  ) {}

  ngOnInit(): void {
    this.formInstanceSubScription = this.service.formInstance$.subscribe((formInstance) => {
      this.changeFormInstance.emit(formInstance);
    });
    this.workDetailDataSubScription = this.dslWorkFormService.workDetailData$.subscribe((data) => {
      this.changeWorkDetailData.emit(data);
    });
    this.submitActionsSubScription = this.dslWorkFormService.submitActions$.subscribe(
      ({ isReset = false, submitActionActions = [] }) => {
        this.submitActions = submitActionActions;
      },
    );
    this.checkDslRoutePath();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('isvPackageDataList')) {
      this.service.setIsvPackageDataList(changes.isvPackageDataList.currentValue);
    }
    if (Object.keys(changes).includes('pageData')) {
      this.handleAddOperationId(this.pageData);
      this.dslWorkFormService._formatPageData(this.pageData);
    }
    if (changes.favouriteCode?.currentValue) {
      this.service.favouriteCode = this.favouriteCode;
      this.service.headerCofig = { templateId: this.favouriteCode };
    }
    if (changes.applicationCodeProxy?.currentValue) {
      this.service.applicationCodeProxy = this.applicationCodeProxy;
    }
    if (changes.extendHeader?.currentValue) {
      this.service.extendHeader = this.extendHeader;
    }
  }

  ngOnDestroy(): void {
    this.formInstanceSubScription.unsubscribe();
    this.workDetailDataSubScription.unsubscribe();
    this.submitActionsSubScription.unsubscribe();
  }

  /**
   * 刷数据
   */
  handleAddOperationId(pageData: any): void {
    let updatePageData = pageData;
    let updateFlag = false;
    if (pageData?.hasOwnProperty('dataStates')) {
      updatePageData = {
        ...pageData,
        dataStates: (pageData?.dataStates ?? []).map((dataState) => {
          return {
            ...dataState,
            operations: (dataState?.operations ?? []).map((o) => {
              if (o.hasOwnProperty('id')) {
                return o;
              } else {
                updateFlag = true;
                return { ...o, id: UUID.UUID() };
              }
            }),
          };
        }),
      };
    } else {
      updatePageData = {
        ...pageData,
        operations: (pageData?.operations ?? []).map((o) => {
          if (o.hasOwnProperty('id')) {
            return o;
          } else {
            updateFlag = true;
            return { ...o, id: UUID.UUID() };
          }
        }),
      };
    }
    if (updateFlag) {
      this.pageData = updatePageData;
      this.handleChangePage(updatePageData);
    }
  }

  private checkDslRoutePath() {
    const pathname = window.location.pathname;
    const whiteListMatch = DSL_Route_Config.whiteList.some((e) => {
      if (e === pathname) return true;
      return stringToRegexp(e)?.test(pathname);
    });
    if (whiteListMatch) return;
    const verifyMatch = DSL_Route_Config.match.some((e) => {
      if (e === pathname) return true;
      return stringToRegexp(e)?.test(pathname);
    });
    if (verifyMatch) return;
    console.error('自定义控件路由跳转未配置，请到 dsl-route.config.ts 文件配置配置');
  }

  handleSetSource(): void {
    if (this.service.fieldSourceMode === FieldSourceModeEnum.Action) {
      this.switchSourceModal = true;
    } else {
      this.dslWorkSidebar?.handleSwitch('dataSource');
    }
  }

  handleSelectAction(action: any): void {
    this.switchSourceModal = false;
    const { actionId } = action;
    this.handleChangeFieldSource(actionId);
  }

  handleChangeDataSource(e: any): void {
    this.dataSources = e;
    this.changeDataSource.emit({
      dataSources: e,
      lastDataSourceName: this.dataSourceName,
    });
  }

  handleChangeFieldSource(e: any): void {
    this.service.dataSourceName = e;
    this.changeFieldSource.emit(e);
  }

  handleChangePage(e: any): void {
    this.changePage.emit(e);
  }

  handleSendFieldData(e: any): void {
    this.service.fieldData = e;
    this.sendFieldData.emit(e);
  }

  // 定制转非定制 false 非定制转定制 true
  handleSwitchCustom(flag: boolean) {
    this.changeCustom.emit(flag);
  }

  // 修改了打印操作之后同步数据
  handleChangeOperateCustomTemplate(custom: any[]): void {
    this.changeOperateCustomTemplates.emit(custom);
  }

  handleModuleData(data) {
    // this.zone.run(() => {
    //   this.moduleData = data;
    // });
  }

  // 提交数据校验
  public handleValidateSubmit(): boolean {
    return ValidateSubmit(this.submitActions, this.translate);
  }

  handleDeleteRuleCallback(rule: any) {}

  handleRefreshPageView() {
    this.refreshPageView.emit();
  }

  // 同步页签
  handleSyncTab(e: any) {
    this.syncTab.emit(e);
  }

  // 数据源更新影响界面重置更新
  handleResetPageUIElement(e: any) {
    this.resetPageUIElement.emit(e);
  }

  // 数据源更新影响界面重置保留
  handleChangePageUIElementCode(e: any) {
    this.changePageUIElementCode.emit(e);
  }

  // 刷新界面设计
  public handleRefresh(): void {
    this.dslWorkSidebar.handleRefresh();
  }
}
