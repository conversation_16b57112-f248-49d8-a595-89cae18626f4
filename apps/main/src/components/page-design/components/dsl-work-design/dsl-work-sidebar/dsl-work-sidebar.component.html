<!-- 左侧子菜单 -->
<app-dsl-work-submodule
  #workSubmodule
  [hideDataSource]="config?.hideDataSource"
  [componentIdDataMap]="componentIdDataMap"
  [fieldSourceMode]="config?.fieldSourceMode"
  [config]="config?.ModuleCodeList"
  [isCustom]="isCustom"
  [activeModule]="activeModule"
  (togglePanel)="handleTogglePanel($event)"
></app-dsl-work-submodule>

<!-- 数据源 -->
<app-data-source
  #dataSource
  *ngIf="activeModule === SubModuleEnum.DataSource"
  [options]="config?.dataSourceOptions"
  [basicDataSourceMode]="config?.basicDataSourceMode"
  [queryPlanDataSourceMode]="config?.queryPlanDataSourceMode"
  [openWindowFlag]="config?.openWindowFlag"
  [interceptDataSourceUpdateOperations]="config?.interceptDataSourceUpdateOperations"
  [fieldSourceMode]="config?.fieldSourceMode"
  [pageData]="pageData"
  [favouriteCode]="dslWorkDesignService.favouriteCode"
  [applicationCodeProxy]="dslWorkDesignService.applicationCodeProxy"
  [isTenantProcessId]="dslWorkDesignService.config?.isTenantProcessId ?? config?.isTenantProcessId"
  [dataSources]="dslWorkSidebarService.dataSources"
  (close)="handleClosePanel()"
  (changeDataSource)="handleChangeDataSource($event)"
  (updateDataSource)="handleUpdateFieldSource($event)"
  (syncTab)="handleSyncTab($event)"
  (resetPageUIElement)="handleResetPageUIElement($event)"
  (changePageUIElementCode)="handleChangePageUIElementCode($event)"
></app-data-source>

<!-- 字段 -->
<app-dsl-field-set
  #field
  *ngIf="activeModule === SubModuleEnum.Field"
  [isShowQueryPlan]="config?.dataSourceOptions?.isShowQueryPlan"
  [basicDataSourceMode]="config?.basicDataSourceMode"
  [queryPlanDataSourceMode]="config?.queryPlanDataSourceMode"
  [fieldSourceMode]="config?.fieldSourceMode"
  [interceptDataSourceUpdateOperations]="config?.interceptDataSourceUpdateOperations"
  (close)="handleClosePanel()"
  (resetPageUIElement)="handleFieldResetPageUIElement($event)"
  (changePageUIElementCode)="handleFieldChangePageUIElementCode($event)"
></app-dsl-field-set>

<app-dsl-card-field-set
  #field
  *ngIf="activeModule === SubModuleEnum.CardField"
  [workData]="workData"
  [isShowQueryPlan]="config?.dataSourceOptions?.isShowQueryPlan"
  [basicDataSourceMode]="config?.basicDataSourceMode"
  [queryPlanDataSourceMode]="config?.queryPlanDataSourceMode"
  [fieldSourceMode]="config?.fieldSourceMode"
  [interceptDataSourceUpdateOperations]="config?.interceptDataSourceUpdateOperations"
  (close)="handleClosePanel()"
  (resetPageUIElement)="handleFieldResetPageUIElement($event)"
  (changePageUIElementCode)="handleFieldChangePageUIElementCode($event)"
></app-dsl-card-field-set>

<!-- 组件 -->
<app-dsl-component-set
  #widget
  *ngIf="activeModule === SubModuleEnum.Widget"
  [pageCode]="pageCode"
  (close)="handleClosePanel()"
  (refreshPageView)="handleRefreshPageView()"
></app-dsl-component-set>

<!-- 规则 -->
<app-common-rule-set
  #rule
  *ngIf="config?.ModuleCodeList?.includes(SubModuleEnum.Rule)"
  [pageType]="'dsl'"
  [service]="dslWorkSidebarService"
  [offline]="config?.offline"
  [ngClass]="{ 'is-hide': activeModule !== SubModuleEnum.Rule }"
  [activeModule]="activeModule"
  [workData]="workData"
  [rule]="pageData?.rule ?? []"
  [fieldSourceMode]="config?.fieldSourceMode"
  (deleteRuleCallback)="handleDeleteRuleCallback($event)"
  (rulesChange)="handleChangeRule($event)"
  (close)="handleClosePanel()"
  [favouriteCode]="dslWorkDesignService.favouriteCode"
  [applicationCodeProxy]="dslWorkDesignService.applicationCodeProxy"
  [extendHeader]="dslWorkDesignService.extendHeader"
  [isTenantProcessId]="dslWorkDesignService.config?.isTenantProcessId ?? config?.isTenantProcessId"
  (changeWorkDetailData)="changeWorkDetailData($event)"
  [masterFromDataSourceName]="dslWorkSidebarService.masterFromDataSourceName"
>
</app-common-rule-set>

<!-- Hook -->
<ng-container *ngIf="dslWorkSidebarService.showHooks && isNotNone(componentIdDataMap)">
  <app-hooks-set
    #hook
    [ngStyle]="{ display: activeModule === SubModuleEnum.Hook ? 'block' : 'none' }"
    [hooks]="hooks"
    [formSchemaList]="formSchemaList"
    [submitActions]="submitActions"
    [operations]="operations"
    [componentIdDataMap]="componentIdDataMap"
    [isTenantProcessId]="dslWorkDesignService.config?.isTenantProcessId ?? config?.isTenantProcessId"
    (hooksChange)="handleHooksChange($event)"
    (close)="handleClosePanel()"
  >
  </app-hooks-set>
</ng-container>

<!-- 扩展 -->
<app-extend-editor-modal
  *ngIf="extendModalVisible"
  [isTenantProcessId]="dslWorkDesignService.config?.isTenantProcessId ?? config?.isTenantProcessId"
  [data]="pageData"
  [width]="'640px'"
  (ok)="handleChangePage($event)"
  (close)="extendModalVisible = false"
></app-extend-editor-modal>
