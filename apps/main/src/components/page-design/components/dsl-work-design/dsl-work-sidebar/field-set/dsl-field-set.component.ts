import { Component, EventEmitter, Input, OnInit, Output, OnD<PERSON>roy, ElementRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SourceMergeFieldsService } from 'common/service/source-merge-fields.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { DslFieldSetService } from '../service/dsl-field-set.service';
import {
  Action,
  DataSourceMode,
  FieldSourceMode,
  PanelStatus,
} from '../../shared/interface/dsl-work-sidebar.interface';
import { DslWorkSidebarService } from '../service/dsl-work-sidebar.service';
import { DslFormRenderShareService } from '../../../dsl-form-render/service/dsl-form-render-share.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DslWorkDesignService } from '../../service/dsl-work-design.service';
import { DataSourceModeEnum, FieldSourceModeEnum } from '../../shared/config/dsl-work-sidebar.config';
import { DataSourceTypeEnum } from 'components/page-design/components/data-source/data-source.config';
import { getDataSourceNameList } from 'components/page-design/components/data-source/data-source.component.util';
import { LocaleService } from 'common/service/locale.service';
import { ExtendEvent } from 'components/page-design/components/data-source/data-source.interface';

@Component({
  selector: 'app-dsl-field-set',
  templateUrl: './dsl-field-set.component.html',
  styleUrls: ['./dsl-field-set.component.less'],
})
export class DslFieldSetComponent implements OnInit, OnDestroy {
  loading: boolean;
  showPanel: boolean; // 是否显示
  searchValue: string; // 搜索字段
  switchSourceModal: boolean;
  switchActionModal: boolean;
  DslWorkSidebarService = DslWorkSidebarService;
  getDataSourceNameList = getDataSourceNameList;
  Object = Object;
  DataSourceModeEnum = DataSourceModeEnum;
  FieldSourceModeEnum = FieldSourceModeEnum;
  @Input() isShowQueryPlan: boolean;
  @Input() basicDataSourceMode: DataSourceMode;
  @Input() queryPlanDataSourceMode: DataSourceMode;
  @Input() fieldSourceMode: FieldSourceMode;
  @Input() interceptDataSourceUpdateOperations: boolean;
  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() resetPageUIElement: EventEmitter<any> = new EventEmitter();
  @Output() changePageUIElementCode: EventEmitter<any> = new EventEmitter();
  destroy$ = new Subject();
  formSchemaList = [];
  language;

  sourceElement: Element; // 拖拽元素
  updateVisible: boolean = false;
  updateActionId: string = null;

  get isQueryPlan(): boolean {
    const dataSourceList = this.dslWorkSidebarService?.dataSourceList ?? [];
    return (
      this.isShowQueryPlan && dataSourceList.length > 0 && dataSourceList[0].type_alias === DataSourceTypeEnum.QUERYPLAN
    );
  }

  get dataSourceMode() {
    if (this.isQueryPlan) {
      return this.queryPlanDataSourceMode;
    }
    return this.basicDataSourceMode;
  }

  get transferData() {
    return { ...(this.dslWorkSidebarService.action || {}), useApp: 'true' };
  }

  constructor(
    public dslWorkSidebarService: DslWorkSidebarService,
    public dslFieldSetService: DslFieldSetService,
    private modal: AdModalService,
    private translateService: TranslateService,
    public mergeService: SourceMergeFieldsService,
    public dslFormRenderShareService: DslFormRenderShareService,
    private languageService: LocaleService,
    private el: ElementRef,
    public dslWorkDesignService: DslWorkDesignService,
  ) {
    this.language = this.languageService.currentLanguage;
    this.dslFormRenderShareService.formSchemaList$.pipe(takeUntil(this.destroy$)).subscribe((formSchemaList) => {
      this.formSchemaList = formSchemaList ?? [];
    });
  }

  ngOnInit(): void {
    this.handleInit();
  }

  ngAfterViewInit(): void {
    // element: div.drag-copy.gu-transit
    this.sourceElement = this.el.nativeElement.querySelector('div.drag-copy');
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  handleInit(): void {
    this.showPanel = true;
    this.handleTogglePanel('block');
  }

  // 刷新
  handleRefresh(): void {
    this.dslFieldSetService.handleInitField();
  }

  // 隐藏面板
  handleClosePanel(): void {
    this.showPanel = false;
    this.close.emit();
  }

  // 收缩面板
  handleTogglePanel(status: PanelStatus): void {
    this.dslWorkSidebarService.panelStatus = status;
  }

  // 切换DataSource
  handleSwitchSource({ dataSourceName, extendEvent }: { dataSourceName: string; extendEvent?: ExtendEvent }): void {
    // 更新时是否需要提示重制或者保留与之影响的数据
    const fromDataViewQueryCode =
      this.dslWorkSidebarService.dataSources[this.dslWorkSidebarService.dataSourceName]?.dataViewQuery?.code;
    const toDataViewQueryCode = this.dslWorkSidebarService.dataSources[dataSourceName]?.dataViewQuery?.code;
    switch (extendEvent) {
      case 'RESET':
        this.resetPageUIElement.emit({ from: fromDataViewQueryCode, to: toDataViewQueryCode, type: 'SWITCH' });
        break;
      case 'RESERVE':
        this.changePageUIElementCode.emit({ from: fromDataViewQueryCode, to: toDataViewQueryCode, type: 'SWITCH' });
      default:
        break;
    }

    this.dslWorkSidebarService.dataSourceName = dataSourceName;
    this.switchSourceModal = false;
    this.handleRefresh();
  }

  // 查询方案切换数据源
  handleSwitchQueryPlanSource(): void {
    this.handleRefresh();
  }

  // 字段搜索
  handleSearch = (node: any) => {
    return !!(node.title?.includes(this.searchValue) || node?.description?.[this.language]?.includes(this.searchValue));
  };

  // 切换Action
  handleSwitchAction(action: Action): void {
    if (action.actionId !== this.dslWorkSidebarService.actionId) {
      if (this.interceptDataSourceUpdateOperations) {
        this.updateVisible = true;
        this.updateActionId = action.actionId;
      } else {
        this.modal.confirm({
          nzTitle: this.translateService.instant('dj-更换API将导致所有设计结果重置，请确认是否更换？'),
          nzWrapClassName: 'vertical-center-modal',
          nzOkText: this.translateService.instant('dj-确定'),
          nzOnOk: () => {
            this.dslWorkSidebarService.actionId = action.actionId;
            this.switchActionModal = false;
            this.handleRefresh();
          },
          nzOnCancel: () => {},
        });
      }
    } else {
      this.switchActionModal = false;
    }
  }

  handleUpdateCancal(): void {
    this.updateVisible = false;
  }

  handleUpdateReset(): void {
    const fromActionId = this.dslWorkSidebarService.actionId;
    const toActionId = this.updateActionId;
    this.dslWorkSidebarService.actionId = this.updateActionId;
    this.resetPageUIElement.emit({ from: fromActionId, to: toActionId, type: 'SWITCH' });
    this.switchActionModal = false;
    this.handleRefresh();
    this.updateVisible = false;
    this.updateActionId = null;
  }

  handleUpdateReserve(): void {
    const fromActionId = this.dslWorkSidebarService.actionId;
    const toActionId = this.updateActionId;
    this.dslWorkSidebarService.actionId = this.updateActionId;
    this.changePageUIElementCode.emit({ from: fromActionId, to: toActionId, type: 'SWITCH' });
    this.switchActionModal = false;
    this.handleRefresh();
    this.updateVisible = false;
    this.updateActionId = null;
  }

  // descriptionToString(description) {
  //   return JSON.stringify(description);
  // }

  nodeDescriptionToString(node) {
    const description = node.origin?.description ?? {};
    return JSON.stringify(description);
  }
}
