import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SourceMergeFieldsService } from 'common/service/source-merge-fields.service';
import { flatMetaData, rebuildMetadata, rebuildPath } from 'common/utils/core.utils';
import { cloneDeep, isEmpty } from 'lodash';
import { Observable, Subject } from 'rxjs';
import { OriginDataSourceTypeEnum } from '../../../data-source/data-source.config';
import { formatField, formatCardField } from '../../shared/utils/dsl-field-set.component.util';
import { FieldDataModeEnum, FieldSourceModeEnum } from '../../shared/config/dsl-work-sidebar.config';
import { taskProcessFields } from 'common/config/submitActionsData';
import { FieldData, FieldParams, Mark } from '../../shared/interface/dsl-work-sidebar.interface';
import { DslWorkSidebarService } from './dsl-work-sidebar.service';
import { DslWorkDesignService } from '../../service/dsl-work-design.service';
import { LocaleService } from 'common/service/locale.service';

@Injectable()
export class DslFieldSetService {
  loading: boolean;
  loading$: Subject<boolean> = new Subject();

  // 服务自身使用的数据
  public _data: any; // 字段数据
  public _mark: Mark; // 字段子节点标识

  public fieldTree: any; // 字段树数据
  public cardFieldTree: any; // 字段树数据
  set fieldData(fieldData: FieldData) {
    const { data = null, mark = null } = fieldData ?? {};
    // 异常处理 过滤异常数据
    if (data?.hasOwnProperty('data_name') && data?.hasOwnProperty('data_type')) {
      this._data = data;
      this._mark = mark;
    } else {
      this._data = null;
      this._mark = null;
    }
    const { fieldSourceMode, dataSources, dataSourceName, actionId } = this.dslWorkSidebarService;
    this.fieldTree = formatField(data, mark, fieldSourceMode, dataSources, dataSourceName, actionId);
    this.cardFieldTree = formatCardField(
      data,
      mark,
      fieldSourceMode,
      dataSources,
      dataSourceName,
      actionId,
      this.dslWorkSidebarService?.openWindowFlag,
    );
    this.sendFieldData$.next({
      fieldData: this.fieldData,
      fieldTree: this.fieldTree,
      cardFieldTree: this.cardFieldTree,
    });
  }
  get fieldData() {
    return { data: this._data, mark: this._mark };
  }

  // 监听
  sendFieldData$ = new Subject<any>();

  constructor(
    public dslWorkSidebarService: DslWorkSidebarService,
    private translateService: TranslateService,
    public mergeService: SourceMergeFieldsService,
    private languageService: LocaleService,
    private dslWorkDesignService: DslWorkDesignService,
  ) {}

  // 初始化字段数据
  handleInitField(): void {
    this.fieldData = null;
    this.handleLoadField().subscribe((res) => {
      this.fieldData = res;
    });
  }

  // 获取字段
  handleLoadField(): Observable<FieldData> {
    return new Observable((subscriber) => {
      const { fieldSourceMode, fieldDataMode, masterFromDataSourceName, dataSourceName } = this.dslWorkSidebarService;
      if (fieldSourceMode === FieldSourceModeEnum.DataSource) {
        switch (fieldDataMode) {
          case FieldDataModeEnum.ActionResponse:
            this.handleLoadSourceField({ masterFromDataSourceName, dataSourceName }).subscribe(
              (res) => {
                subscriber.next(res);
              },
              () => {
                subscriber.next(null);
              },
            );
            break;
          default:
            break;
        }
      } else if (fieldSourceMode === FieldSourceModeEnum.Action) {
        switch (this.dslWorkSidebarService.fieldDataMode) {
          case FieldDataModeEnum.ActionRequest:
            this.handleLoadActionField().subscribe(
              (res) => {
                subscriber.next(res);
              },
              () => {
                subscriber.next(null);
              },
            );
            break;
          default:
            break;
        }
      }
    });
  }

  // Action模式 根据ActionId获取FieldData数据
  private handleLoadActionField(): Observable<FieldData> {
    return new Observable((subscriber) => {
      const actionId = this.dslWorkSidebarService.actionId;
      if (actionId === null || actionId === undefined) {
        return;
      }
      this.loading = true;
      this.loading$.next(true);
      this.dslWorkSidebarService.loadActionInfo({ label: 'Action', actionId }).subscribe(
        (res) => {
          this.loading = false;
          this.loading$.next(false);
          if (res.code === 0) {
            // Action模式时规则中定制使用
            this.dslWorkSidebarService.serviceName = res.data?.serviceName;
            const request_parameters = (res.data?.request_parameters || []).filter(
              (param) => param.data_type === 'object',
            );
            /** UIBOT定制业务 START */
            (request_parameters ?? []).forEach(({ data_name, field }) => {
              (this.dslWorkSidebarService.UIBOT__appendEocFields?.[data_name] ?? []).forEach((appendField) => {
                field.push({
                  ...appendField,
                  data_name: appendField?.name,
                  data_type: appendField?.dataType,
                });
              });
            });
            /** UIBOT定制业务 END */
            subscriber.next({
              data: {
                data_name: actionId,
                data_type: 'object',
                is_array: true,
                field: cloneDeep(request_parameters || []),
                noDrag: true,
              },
              mark: 'field',
            });
          } else subscriber.error();
        },
        () => {
          this.loading = false;
          this.loading$.next(false);
          subscriber.error();
        },
      );
    });
  }

  // DataSource模式 获取FieldData数据
  private handleLoadSourceField(fieldParams: FieldParams): Observable<FieldData> {
    return new Observable((subscriber) => {
      const { dataSources, dataSourceName } = this.dslWorkSidebarService;
      const originDataSource: any = dataSources[dataSourceName];
      const { type, actionId, metadataFields: metadata = [] } = originDataSource;
      const metadataFields = rebuildMetadata(metadata); // 转变字段key名
      // 处理元数据
      let handleMeta = () => {
        if (metadataFields.length > 0) {
          const { field = [] } = metadataFields[0];
          const data = {
            ...metadataFields[0],
            field: this.combineProcessFields.call(this, field, originDataSource, metadataFields[0]),
          };
          subscriber.next({ data, mark: 'field' });
        } else {
          subscriber.error();
        }
      };
      switch (type) {
        case OriginDataSourceTypeEnum.SD:
        case OriginDataSourceTypeEnum.ESP:
        case OriginDataSourceTypeEnum.QUERYPLAN:
        case OriginDataSourceTypeEnum.RAWDATA:
          if (actionId) {
            this.handleLoadSourceFieldWithActionId(fieldParams, actionId, originDataSource).subscribe(
              (res) => {
                const buildMeta = rebuildPath(metadataFields, res.data_name);
                const { data, mark } = flatMetaData(res, buildMeta);
                subscriber.next({ data, mark });
              },
              () => {
                subscriber.error();
              },
            );
          } else {
            handleMeta();
          }
          break;
        case OriginDataSourceTypeEnum.MIX_MERGE:
        case OriginDataSourceTypeEnum.MIX_LEFT_JOIN:
        case OriginDataSourceTypeEnum.MIX_LEFT_MERGE:
          const { left = {}, rightList = [] } = originDataSource;
          const { actionId: mixMergeActionId } = left;
          if (mixMergeActionId) {
            this.handleLoadSourceFieldWithActionId(fieldParams, mixMergeActionId, originDataSource).subscribe(
              (mixMergeRes) => {
                const buildMeta = rebuildPath(metadataFields, mixMergeRes.data_name);
                this.mergeService.mergeData(mixMergeRes ?? {}, rightList, buildMeta).then(
                  (res: any) => {
                    const { data, mark } = res;
                    subscriber.next({ data: data[0], mark });
                  },
                  () => {
                    subscriber.error();
                  },
                );
              },
              () => {
                subscriber.error();
              },
            );
          } else {
            handleMeta();
          }
          break;
        default:
          break;
      }
      handleMeta = null;
    });
  }

  // DataSource模式 根据ActionId获取接口字段数据
  private handleLoadSourceFieldWithActionId(
    fieldParams: FieldParams,
    actionId: string,
    originDataSource: any,
  ): Observable<any> {
    return new Observable((subscriber) => {
      this.loading = true;
      this.loading$.next(true);
      const { type } = originDataSource;
      let param = `?actionId=${actionId}&sourceType=${type}`;
      const upProcessNames = (originDataSource?.dataProcessors ?? [])
        .filter(
          (s) => s.serviceName === 'flatService' && (!s.applyTo || s.applyTo === this.dslWorkSidebarService.pageCode),
        )
        .map((s) => s.paras?.flatData)
        .join(',');
      if (!isEmpty(upProcessNames)) {
        param = `${param}&upProcessNames=${upProcessNames}`;
      }

      if (this.dslWorkDesignService?.config?.sidebarConfig?.isQueryEspActionFieldsInModel) {
        param = `${param}&code=${this.dslWorkDesignService.workData.code}`;
      }

      const { masterFromDataSourceName, dataSourceName } = fieldParams;
      if (masterFromDataSourceName === true) {
        param = `${param}&dataSourceName=${dataSourceName}`;
      }
      if (this.dslWorkSidebarService?.openWindowFlag) {
        param = `${param}&openWindowFlag=${true}`;
      }

      const requestObservable = this.dslWorkDesignService?.config?.sidebarConfig?.isQueryEspActionFieldsInModel
        ? this.dslWorkSidebarService.loadActionFieldModel(param)
        : this.dslWorkSidebarService.loadActionField(param);

      requestObservable.subscribe(
        (res) => {
          this.loading = false;
          this.loading$.next(false);
          if (res.code === 0) {
            const { field = [] } = res.data ?? {};
            subscriber.next({
              ...(res.data || {}),
              field: this.combineProcessFields(field, originDataSource, res.data),
            });
          } else subscriber.error();
        },
        () => {
          this.loading = false;
          this.loading$.next(false);
          subscriber.error();
        },
      );
    });
  }

  // 工具方法：组合词库处理器默认字段
  combineProcessFields(fields: any[], originDataSource: any, data?: any): any[] {
    if (this.dslWorkSidebarService.inquireWordLibrary) {
      const { dataProcessors = [] } = originDataSource;
      if (dataProcessors.some((d) => d.serviceName === 'atmcDataService' || d.serviceName === 'atmcBatchDataService')) {
        fields = [
          ...(fields || []),
          ...taskProcessFields.map((s) => {
            return {
              ...s,
              fullPath: `${data.data_name}.${s.data_name}`,
            };
          }),
        ];
      }
    }
    return fields;
  }
}
