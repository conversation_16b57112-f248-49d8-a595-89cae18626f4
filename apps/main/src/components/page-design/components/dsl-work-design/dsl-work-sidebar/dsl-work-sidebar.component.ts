import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { DataSourceComponent } from '../../data-source/data-source.component';
import { DslWorkDesignService } from '../service/dsl-work-design.service';
import { DslComponentSetComponent } from './component-set/dsl-component-set.component';
import { DslFieldSetService } from './service/dsl-field-set.service';
import { DslFieldSetComponent } from './field-set/dsl-field-set.component';
import { CommonRuleSetComponent } from 'components/page-design/components/common-rule-set/common-rule-set.component';
import { DataSourceModeEnum, FieldSourceModeEnum, SubModuleEnum } from '../shared/config/dsl-work-sidebar.config';
import { ModuleCode, ModuleConfig } from '../shared/interface/dsl-work-sidebar.interface';
import { DslWorkSidebarService } from './service/dsl-work-sidebar.service';
import { DslWorkSubmoduleComponent } from './work-submodule/dsl-work-submodule.component';
import { Subject, Subscription } from 'rxjs';
import { DslWorkFormService } from '../dsl-work-form/dsl-work-form.service';
import { AppService } from 'pages/apps/app.service';
import { AppTypes } from 'pages/app/typings';
import { isNone, isNotNone } from 'common/utils/core.utils';
import { cloneDeep } from 'lodash';
import { DataSourceTypeEnum } from '../../data-source/data-source.config';
import { getDataSourceNameList } from '../../data-source/data-source.component.util';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { DslFormRenderShareService } from '../../dsl-form-render/service/dsl-form-render-share.service';
@Component({
  selector: 'app-dsl-work-sidebar',
  templateUrl: './dsl-work-sidebar.component.html',
  styleUrls: ['./dsl-work-sidebar.component.less'],
  providers: [DslWorkSidebarService, DslFieldSetService],
})
export class DslWorkSidebarComponent implements OnInit, OnDestroy {
  isNotNone = isNotNone;
  selectedDataSubScription: Subscription;
  selectedDataState: any; // 当前数据状态

  _activeModule: ModuleCode;
  //dataSourceProps: any = {
  //  visible: false,
  //};
  set activeModule(code: ModuleCode) {
    this._activeModule = code;
    //const newVisible = this._activeModule === SubModuleEnum.DataSource;
    //if (newVisible !== this.dataSourceProps.visible) {
    //  this.dataSourceProps = {
    //    ...this.dataSourceProps,
    //    visible: newVisible,
    //  }
    //}
    this.dslWorkDesignService.activeModule = code;
  } // 显示数据源、字段、组件、规则面板
  get activeModule(): ModuleCode {
    return this._activeModule;
  }
  extendModalVisible: boolean = false; // 显示扩展弹窗
  SubModuleEnum = SubModuleEnum;
  Object = Object;
  private _dataSources?: Readonly<any>;
  destroy$ = new Subject();
  formSchemaList: any[] = [];

  private _config?: Partial<ModuleConfig>;
  // 静态配置
  @Input() set config(config: Partial<ModuleConfig>) {
    this._config = config;
    //this.dataSourceProps = {
    //  ...this.dataSourceProps,
    //  options: config?.dataSourceOptions,
    //  basicDataSourceMode: config?.basicDataSourceMode,
    //  queryPlanDataSourceMode: config?.queryPlanDataSourceMode,
    //}
    this.dslWorkSidebarService.config = config;
  }
  get config(): Partial<ModuleConfig> {
    return this._config;
  }
  @Input() isCustom: boolean; // 是否定制
  @Input() set actionId(id: string) {
    if (id !== this.dslWorkSidebarService.actionId) {
      this.dslWorkSidebarService.actionId = id;
      this?.handleSwitch('field');
    }
  }

  @Input() set dataSources(obj: any) {
    if (this.config?.fieldSourceMode === FieldSourceModeEnum.Action) {
      this._dataSources = {};
      this.dslWorkSidebarService.dataSources = this._dataSources;
      //this.dataSourceProps = {
      //  ...this.dataSourceProps,
      //  dataSources: this._dataSources,
      //}
      return;
    }

    this._dataSources = obj;
    //this.dataSourceProps = {
    //  ...this.dataSourceProps,
    //  dataSources: obj
    //}
    this.dslWorkSidebarService.dataSources = this._dataSources;
    const dataSourceNameList = getDataSourceNameList(this._dataSources ?? {});
    if (
      isNone(this.dslWorkSidebarService.dataSourceName) ||
      !dataSourceNameList.includes(this.dslWorkSidebarService.dataSourceName)
    ) {
      if (dataSourceNameList.length !== 0) {
        if (this.dataSourceMode === DataSourceModeEnum.Multiple) {
          const isDefaultDataSourceName = dataSourceNameList.find(
            (dataSourceName) => this._dataSources?.[dataSourceName]?.dataViewQuery?.isDefault,
          );
          if (isDefaultDataSourceName) {
            this.dslWorkSidebarService.dataSourceName = isDefaultDataSourceName;
          } else {
            // 不应该走该分支
            this.dslWorkSidebarService.dataSourceName = dataSourceNameList[0];
          }
        } else {
          this.dslWorkSidebarService.dataSourceName = dataSourceNameList[0];
        }
      }
    }
  }
  get dataSources(): any {
    return this._dataSources;
  }

  get dataSourceMode() {
    const dataSourceList = this.dslWorkSidebarService?.dataSourceList ?? [];
    if (
      this.config?.dataSourceOptions?.isShowQueryPlan &&
      dataSourceList.length > 0 &&
      dataSourceList[0].type_alias === DataSourceTypeEnum.QUERYPLAN
    ) {
      return this.config?.queryPlanDataSourceMode;
    }
    return this.config?.basicDataSourceMode;
  }

  get props() {
    return {
    }
  }

  @Input() set dataSourceName(name: string) {
    if (this.config?.fieldSourceMode === FieldSourceModeEnum.Action) {
      this.dslWorkSidebarService.dataSourceName = null;
    } else {
      const dataSourceNameList = getDataSourceNameList(this._dataSources ?? {});
      if (!isNone(name) && dataSourceNameList.includes(name)) {
        this.dslWorkSidebarService.dataSourceName = name;
      } else if (dataSourceNameList.length !== 0) {
        if (this.dataSourceMode === DataSourceModeEnum.Multiple) {
          const isDefaultDataSourceName = dataSourceNameList.find(
            (dataSourceName) => this._dataSources?.[dataSourceName]?.dataViewQuery?.isDefault,
          );
          if (isDefaultDataSourceName) {
            this.dslWorkSidebarService.dataSourceName = isDefaultDataSourceName;
          } else {
            // 不应该走该分支
            this.dslWorkSidebarService.dataSourceName = dataSourceNameList[0];
          }
        } else {
          this.dslWorkSidebarService.dataSourceName = dataSourceNameList[0];
        }
      }
    }
  }

  private _workData: Readonly<any>;
  // 必传字段 作业数据 调取接口的标识参数
  @Input() set workData(data: any) {
    this._workData = data;
    this.dslWorkSidebarService.taskCode = data.code;
  }
  get workData(): any {
    return this._workData;
  }

  private _pageCode: Readonly<any>;
  // options.inquireWordLibrary === true时 词库定制业务必传字段
  @Input() set pageCode(code: any) {
    this._pageCode = code;
    this.dslWorkSidebarService.pageCode = code;
  }
  get pageCode(): any {
    return this._pageCode;
  }
  @Input() pageData?: any;
  @Input() set UIBOT__appendEocFields(fields: any) {
    this.dslWorkSidebarService.UIBOT__appendEocFields = fields;
  }

  @Input() submitActionsBase?: any[];

  @Input() set masterFromDataSourceName(value: boolean) {
    this.dslWorkSidebarService.masterFromDataSourceName = value;
  }

  // 是否是场景化套件解决方案
  get isKITApp() {
    return this.appService.selectedApp?.appType === AppTypes.SCENARIO_KIT;
  }

  @Output() changeDataSource: EventEmitter<any> = new EventEmitter();
  @Output() changeFieldSource: EventEmitter<any> = new EventEmitter();
  @Output() changePage: EventEmitter<any> = new EventEmitter();
  @Output() sendFieldData: EventEmitter<any> = new EventEmitter();
  @Output() deleteRuleCallback: EventEmitter<any> = new EventEmitter();
  @Output() refreshPageView: EventEmitter<any> = new EventEmitter();
  @Output() syncTab: EventEmitter<any> = new EventEmitter();
  @Output() resetPageUIElement: EventEmitter<any> = new EventEmitter();
  @Output() changePageUIElementCode: EventEmitter<any> = new EventEmitter();
  @Output() fieldLoading: EventEmitter<any> = new EventEmitter();

  @ViewChild('workSubmodule') workSubmodule: DslWorkSubmoduleComponent;
  @ViewChild('dataSource') dataSource: DataSourceComponent;
  @ViewChild('field') field: DslFieldSetComponent;
  @ViewChild('widget') widget: DslComponentSetComponent;
  @ViewChild('rule') rule: CommonRuleSetComponent; // 规则组件
  constructor(
    public dslWorkSidebarService: DslWorkSidebarService,
    public dslWorkDesignService: DslWorkDesignService,
    private dslFieldSetService: DslFieldSetService,
    private dslFormRenderShareService: DslFormRenderShareService,
    public dslWorkFormService: DslWorkFormService,
    public appService: AppService,
  ) {
    // this.selectedDataSubScription = this.dslWorkDesignService.selectedDataState$.subscribe((selectedDataState) => {
    //   this.selectedDataState = selectedDataState;
    // });
    this.dslFormRenderShareService.formSchemaList$.pipe(takeUntil(this.destroy$)).subscribe((formSchemaList) => {
      this.formSchemaList = formSchemaList ?? [];
    });
    this.dslFieldSetService.loading$.subscribe((flag) => {
      this.fieldLoading.emit(flag);
    });
  }

  ngOnInit(): void {
    this.dslWorkSidebarService.changeFieldSourceSubject$.subscribe((fieldSource: string) => {
      if (
        this.config.ModuleCodeList.includes(SubModuleEnum.Field) ||
        this.config.ModuleCodeList.includes(SubModuleEnum.CardField)
      ) {
        this.dslFieldSetService.handleInitField();
        this.changeFieldSource.emit(fieldSource);
      }
    });
    this.dslFieldSetService.sendFieldData$.subscribe(({ fieldData, fieldTree }) => {
      this.sendFieldData.emit({ fieldData, fieldTree });
    });
    if (
      this.config.ModuleCodeList.includes(SubModuleEnum.Field) ||
      this.config.ModuleCodeList.includes(SubModuleEnum.CardField)
    ) {
      this.dslFieldSetService.handleInitField();
    }
    //this.dataSourceProps = {
    //  visible: this.activeModule === SubModuleEnum.DataSource,
    //  appCode: this.appService?.selectedApp?.code,
    //  options: this.config?.dataSourceOptions,
    //  basicDataSourceMode: this.config?.basicDataSourceMode,
    //  queryPlanDataSourceMode: this.config?.queryPlanDataSourceMode,
    //  dataSources: this.dataSources,
    //  applicationCodeProxy: this.dslWorkDesignService.applicationCodeProxy,
    //}
  }

  ngOnDestroy(): void {
    //   this.selectedDataSubScription.unsubscribe();
    this.dslFieldSetService.loading$.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  handleUpdateFieldSource(fieldSource: string): void {
    if (fieldSource === this.dslWorkSidebarService.dataSourceName) {
      if (
        this.config.ModuleCodeList.includes(SubModuleEnum.Field) ||
        this.config.ModuleCodeList.includes(SubModuleEnum.CardField)
      ) {
        this.dslFieldSetService.handleInitField();
        this.changeFieldSource.emit(fieldSource);
        this.rule && this.rule.handleLoadSource(); // 通知规则面板刷新数据源
      }
    }
  }

  handleChangeDataSource(e: any): void {
    this.dataSources = e;
    this.changeDataSource.emit(e);
    this.rule && this.rule.handleLoadSource(); // 通知规则面板刷新数据源
  }

  // 同步页签
  handleSyncTab(e: any): void {
    this.syncTab.emit(e);
  }

  // 数据源更新影响界面重置更新
  handleResetPageUIElement(e: any) {
    this.resetPageUIElement.emit(e);
  }

  // 数据源更新影响界面重置保留
  handleChangePageUIElementCode(e: any) {
    this.changePageUIElementCode.emit(e);
  }

  // 字段切换数据源 更新影响界面重置更新
  handleFieldResetPageUIElement(e: any) {
    this.resetPageUIElement.emit(e);
    this.changeDataSource.emit(this.dataSources);
  }

  // 字段切换数据源 更新影响界面重置保留
  handleFieldChangePageUIElementCode(e: any) {
    this.changePageUIElementCode.emit(e);
    this.changeDataSource.emit(this.dataSources);
  }

  public handleRefresh(): void {
    switch (this.activeModule) {
      case SubModuleEnum.DataSource:
        this.dataSource?.handleRefresh();
        break;
      case SubModuleEnum.Field:
        if (this.config.ModuleCodeList.includes(SubModuleEnum.Field)) {
          if (this.dslWorkSidebarService.dataSourceList.length === 0) {
            this.field?.handleClosePanel();
          }
        }
        break;
      case SubModuleEnum.CardField:
        if (this.config.ModuleCodeList.includes(SubModuleEnum.CardField)) {
          if (this.dslWorkSidebarService.dataSourceList.length === 0) {
            this.field?.handleClosePanel();
          }
        }
        break;
      case SubModuleEnum.Hook:
        break;
      default:
        break;
    }
  }

  handleTogglePanel(e: any): void {
    const { module } = e;
    if (module === SubModuleEnum.Config) {
      this.extendModalVisible = true;
      return;
    }
    this.activeModule = module;
    this.dslWorkSidebarService.panelStatus = 'block';
    // 场景化套件解决方案定制需求 初始化前端自动生成表单
    if (this.isKITApp) {
      if (module === SubModuleEnum.Field) {
        setTimeout(() => {
          this.dslWorkDesignService.layoutSubject.next(this.field.sourceElement);
        }, 0);
      }
    }
  }

  handleClosePanel(): void {
    this.dslWorkSidebarService.panelStatus = 'absolute';
    this.activeModule = 'null';
  }

  // 切换子功能菜单
  public handleSwitch(code: any): void {
    this.workSubmodule?.handleSwitch(code);
  }

  handleChangeRule(rule: any[]): void {
    this.changePage.emit({ ...(this.pageData ?? {}), rule });
  }

  handleChangePage(pageData: any): void {
    this.changePage.emit(pageData);
  }

  handleSendFieldData(e: any): void {
    this.sendFieldData.emit(e);
  }

  handleDeleteRuleCallback(e: any) {
    this.deleteRuleCallback.emit(e);
  }

  handleRefreshPageView() {
    this.refreshPageView.emit();
  }

  get hooks(): any[] {
    if (!isNone(this.pageData?.dataStates)) {
      const findIndex = (this.pageData.dataStates ?? []).findIndex(
        (state) => state.code === this.dslWorkDesignService._selectedDataState$.getValue(),
      );
      if (findIndex > -1) {
        return this.pageData.dataStates[findIndex]?.hooks;
      }
    }
    return this.pageData?.hooks || [];
  }

  get componentIdDataMap(): any[] {
    if (!isNone(this.pageData?.dataStates)) {
      const findIndex = (this.pageData.dataStates ?? []).findIndex(
        (state) => state.code === this.dslWorkDesignService._selectedDataState$.getValue(),
      );
      if (findIndex > -1) {
        return this.pageData.dataStates[findIndex]?.componentIdDataMap;
      }
    }
    return this.pageData?.componentIdDataMap || [];
  }

  get submitActions(): any[] {
    if (!isNone(this.pageData?.dataStates)) {
      const findIndex = (this.pageData.dataStates ?? []).findIndex(
        (state) => state.code === this.dslWorkDesignService._selectedDataState$.getValue(),
      );
      if (findIndex > -1) {
        return this.pageData.dataStates[findIndex]?.submitActions;
      }
    }
    return this.pageData?.submitActions || [];
  }

  get operations(): any[] {
    if (!isNone(this.pageData?.dataStates)) {
      const findIndex = (this.pageData.dataStates ?? []).findIndex(
        (state) => state.code === this.dslWorkDesignService._selectedDataState$.getValue(),
      );
      if (findIndex > -1) {
        return this.pageData.dataStates[findIndex]?.operations;
      }
    }
    return this.pageData?.operations || [];
  }

  handleHooksChange(e: any) {
    const findIndex = (this.pageData.dataStates ?? []).findIndex(
      (state) => state.code === this.dslWorkDesignService._selectedDataState$.getValue(),
    );
    if (findIndex > -1) {
      const pageData = cloneDeep(this.pageData);
      pageData.dataStates[findIndex]['hooks'] = e;
      this.dslWorkFormService.setPageData(pageData, false);
    }
    this.dslWorkDesignService.hooksSubject$.next(e);
  }

  changeWorkDetailData(workDetailData) {
    this.dslWorkFormService.changeWorkDetailData(workDetailData);
  }
}
