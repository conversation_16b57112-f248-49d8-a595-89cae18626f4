:host {
  height: 100%;
}
.field-set {
  width: 232px;
  height: 100%;
  padding: 10px 10px;
  background: white;
  box-shadow: 4px 0 4px 0 rgba(233, 233, 233, 0.5);
  z-index: 1;
  margin-left: 38px;
  .field-header {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    .field-header-title {
      font-size: 16px;
      color: #333333;
    }
    .field-icon {
      .iconfont {
        font-size: 14px;
        margin-right: 6px;
      }
      .closeIcon {
        font-size: 12px;
        color: #666666;
      }
    }
  }
  .field-search {
    position: relative;
    padding-bottom: 10px;
    .search-input {
      .no-data-option {
        position: absolute;
        display: flex;
        justify-content: center;
        text-align: center;
        flex-direction: column;
        width: 284px;
        top: 36px;
        padding-top: 10px;
        padding-bottom: 10px;
        background: white;
        box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
        z-index: 999;
      }
    }
  }
  .field-source {
    cursor: pointer;
    padding-bottom: 9px;
    font-size: 13px;
    line-height: 13px;
    color: #6a4cff;
    .source-title {
      display: inline-block;
      max-width: 170px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 4px;
    }
    .query-plan-source-title {
      width: 210px;
      height: 28px;
      ::ng-deep {
        .ant-select-selector {
          border: 0;
          color: #6a4cff;
        }
        .ant-select-selection-item {
          color: #6a4cff;
        }
      }
    }
  }
  .field-content {
    height: calc(100% - 102px);
    overflow-y: auto;
    ::ng-deep {
      .field-node {
        min-width: 100%;
        width: fit-content;
        white-space: nowrap;
        cursor: pointer;
        line-height: 24px;
        margin-left: 0;
        display: inline-block;
        .file-desc {
          padding-left: 4px;
          display: inline-block;
          color: #999;
          position: relative;
          left: 6px;
          font-size: 13px;
        }
      }
    }
    .isExist {
      opacity: 0.4;
    }
  }
  &.absolute-widget {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 4;
  }
}

.drag-custom-node {
  // color: red; // 暂时标记而已
}
