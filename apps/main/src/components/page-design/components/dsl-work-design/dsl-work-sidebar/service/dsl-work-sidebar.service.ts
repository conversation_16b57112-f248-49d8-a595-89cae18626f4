import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable, Subject } from 'rxjs';
import { transformDataSourceList } from '../../../data-source/data-source.component.util';
import { DataSource } from '../../../data-source/data-source.interface';
import {
  PanelStatus,
  Action,
  ModuleConfig,
  FieldSourceMode,
  FieldDataMode,
} from '../../shared/interface/dsl-work-sidebar.interface';
import { FieldSourceModeEnum } from '../../shared/config/dsl-work-sidebar.config';
import { DslWorkFormService } from '../../dsl-work-form/dsl-work-form.service';
import { debounceTime, takeUntil } from 'rxjs/operators';

@Injectable()
export class DslWorkSidebarService implements OnDestroy {
  private serviceUrl: any;
  private _dataSources: Readonly<any> = {};
  private _dataSourceList: Readonly<DataSource>[] = [];

  private _dataSourceName: string;
  private _actionId: string;
  public serviceName: string; // action中的serviceName
  action: Action = {
    actionId: '',
    actionName: '',
    actionType: '',
  };

  FieldSourceModeEnum = FieldSourceModeEnum;
  public fieldSourceMode: FieldSourceMode;
  public fieldDataMode: FieldDataMode;
  public inquireWordLibrary: boolean = false;
  public openWindowFlag: boolean = false;
  public UIBOT__appendEocFields: any;
  public taskCode: string;
  public pageCode: string;

  masterFromDataSourceName: boolean;

  public panelStatus: PanelStatus;

  changeFieldSourceSubject$ = new Subject<any>();

  showHooks: boolean = false;
  destroy$ = new Subject();

  constructor(
    protected http: HttpClient,
    protected configService: SystemConfigService,
    private dslWorkFormService: DslWorkFormService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
    this.dslWorkFormService.formIsReady$
      .pipe(takeUntil(this.destroy$))
      .pipe(debounceTime(500))
      .subscribe((val: any) => {
        if (val.isFinalReady) {
          this.showHooks = true;
        }
      });
  }

  set dataSources(obj: any) {
    this._dataSources = obj;
    this._dataSourceList = transformDataSourceList(obj);
  }
  get dataSources(): any {
    return this._dataSources;
  }

  get dataSourceList(): DataSource[] {
    return this._dataSourceList;
  }

  set dataSourceName(name: string) {
    this._dataSourceName = name;
    if (this.fieldSourceMode !== FieldSourceModeEnum.Action) {
      this.changeFieldSourceSubject$.next(name);
    }
  }

  get dataSourceName(): string {
    return this._dataSourceName;
  }

  set actionId(id: string) {
    this._actionId = id;
    this.action.actionId = id;
    if (this.fieldSourceMode === FieldSourceModeEnum.Action) {
      this.changeFieldSourceSubject$.next(id);
    }
  }

  get actionId(): string {
    return this._actionId;
  }

  set config(config: Partial<ModuleConfig>) {
    this.fieldSourceMode = config?.fieldSourceMode;
    this.fieldDataMode = config?.fieldDataMode;
    this.inquireWordLibrary = config?.inquireWordLibrary;
    this.openWindowFlag = config?.openWindowFlag;
  }

  ngOnDestroy(): void {
    this.changeFieldSourceSubject$.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 查询action元数据
  loadActionField(param: string): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/action/queryEspActionFields${param}`;
    return this.http.get(url);
  }

  // 查询action元数据(模型驱动)
  loadActionFieldModel(param: string): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/action/queryEspActionFieldsInModel${param}`;
    return this.http.get(url);
  }

  // 查询action详情
  loadActionInfo(param: { label: string; actionId: string }): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/action/findActionByActionId?label=${param.label}&actionId=${param.actionId}`;
    return this.http.get(url);
  }
}
