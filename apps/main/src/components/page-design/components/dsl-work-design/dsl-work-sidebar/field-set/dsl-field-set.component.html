<div
  class="field-set"
  *ngIf="showPanel"
  [ngClass]="{ 'absolute-widget': dslWorkSidebarService.panelStatus === 'absolute' }"
>
  <div class="field-header">
    <span class="field-header-title">
      {{ 'dj-字段' | translate }}
    </span>
    <span class="field-icon">
      <i
        *ngIf="dslWorkSidebarService.panelStatus === 'absolute'"
        adIcon
        iconfont="iconnouse"
        class="iconfont"
        aria-hidden="true"
        (click)="handleTogglePanel('block')"
      ></i>
      <i
        *ngIf="dslWorkSidebarService.panelStatus === 'block'"
        adIcon
        iconfont="icontagSelectedNormal"
        class="iconfont"
        aria-hidden="true"
        (click)="handleTogglePanel('absolute')"
      ></i>
      <i adIcon iconfont="icondanchuxiaoxiguanbi" class="closeIcon" aria-hidden="true" (click)="handleClosePanel()"></i>
    </span>
  </div>
  <div class="field-search">
    <div class="search-input">
      <nz-input-group [nzSuffix]="suffixIcon">
        <input type="text" nz-input [(ngModel)]="searchValue" [placeholder]="'dj-搜索' | translate" />
      </nz-input-group>
      <ng-template #suffixIcon>
        <span class="input-clear" *ngIf="!!searchValue">
          <i adIcon type="close-circle" theme="fill" (click)="searchValue = ''"></i>
        </span>
        <i adIcon iconfont="iconinputserach" class="searchIcon" aria-hidden="true"> </i>
      </ng-template>
    </div>
  </div>
  <div class="field-source">
    <ng-container *ngIf="FieldSourceModeEnum.DataSource === fieldSourceMode">
      <ng-container [ngSwitch]="dataSourceMode">
        <ng-container *ngSwitchCase="DataSourceModeEnum.SingleMultiple">
          <span
            class="source-title"
            nz-tooltip
            [nzTooltipTitle]="dslWorkSidebarService.dataSourceName"
            [nzTooltipTrigger]="'hover'"
            [nzTooltipPlacement]="'topLeft'"
            >{{
              !isQueryPlan
                ? dslWorkSidebarService.dataSourceName
                : dslWorkSidebarService.dataSources[dslWorkSidebarService.dataSourceName]?.title
            }}
          </span>
          <i adIcon iconfont="icongenghuanshujuyuan" class="iconfont" aria-hidden="true"></i>
          <span (click)="switchSourceModal = true">
            {{ 'dj-切换数据源' | translate }}
          </span>
        </ng-container>
        <ng-container *ngSwitchCase="DataSourceModeEnum.Single">
          <span
            class="source-title"
            nz-tooltip
            [nzTooltipTitle]="dslWorkSidebarService.dataSourceName"
            [nzTooltipTrigger]="'hover'"
            [nzTooltipPlacement]="'topLeft'"
            >{{
              !isQueryPlan
                ? dslWorkSidebarService.dataSourceName
                : dslWorkSidebarService.dataSources[dslWorkSidebarService.dataSourceName]?.title
            }}
          </span>
        </ng-container>
        <ng-container *ngSwitchCase="DataSourceModeEnum.Multiple">
          <ad-select
            class="query-plan-source-title"
            [(ngModel)]="dslWorkSidebarService.dataSourceName"
            (ngModelChange)="handleSwitchQueryPlanSource()"
            [nzAllowClear]="false"
            nz-tooltip
            [nzTooltipTitle]="dslWorkSidebarService.dataSources[dslWorkSidebarService.dataSourceName]?.title ?? ''"
            [nzTooltipTrigger]="'hover'"
            [nzTooltipPlacement]="'topLeft'"
          >
            <ad-option
              *ngFor="let dataSourceName of getDataSourceNameList(dslWorkSidebarService.dataSources)"
              [nzValue]="dataSourceName"
              [nzLabel]="dslWorkSidebarService.dataSources[dataSourceName]?.title ?? ''"
            ></ad-option>
          </ad-select>
        </ng-container>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="FieldSourceModeEnum.Action === fieldSourceMode">
      <span
        class="source-title"
        nz-tooltip
        [nzTooltipTitle]="dslWorkSidebarService.actionId"
        [nzTooltipTrigger]="'hover'"
        [nzTooltipPlacement]="'topLeft'"
        >{{ dslWorkSidebarService.actionId }}
      </span>
      <i adIcon iconfont="icongenghuanshujuyuan" class="iconfont" aria-hidden="true"></i>
      <span (click)="switchActionModal = true">
        {{ 'dj-切换查询API' | translate }}
      </span>
    </ng-container>
  </div>
  <div class="field-content">
    <nz-spin [nzSpinning]="loading">
      <nz-tree
        [nzData]="dslFieldSetService.fieldTree"
        [nzSearchValue]="searchValue"
        [nzTreeTemplate]="nzPageTemplate"
        [nzSearchFunc]="handleSearch"
        nzHideUnMatched
        nzExpandAll
        nzBlockNode
      >
        <ng-template #nzPageTemplate let-node let-origin="origin">
          <div class="field-node no-drop">
            <div
              nz-tooltip
              [nzTooltipPlacement]="'topLeft'"
              [nzTooltipTitle]="
                node.title + ' ' + (node.origin?.description ? node.origin?.description[language] || '' : '')
              "
              class="drag-copy"
              appDslFieldSetTreeNode
              [ngClass]="{ isExist: node.origin?.isExist, 'no-drag': node.origin?.isExist || node.origin?.noDrag }"
              data-title="{{ node.title }}"
              [attr.data-title]="
                (node.title ?? '') +
                '&&' +
                nodeDescriptionToString(node) +
                '&&' +
                (node.origin?.data_type ?? '') +
                '&&' +
                (node.origin?.is_array ?? '') +
                '&&' +
                (node.origin?.target ?? '') +
                '&&' +
                (node.origin?.isSystem ?? '') +
                '&&' +
                (node.origin?.defaultFormioDataType ?? '') +
                '&&' +
                (node.origin?.parentId ?? '') +
                '&&' +
                (node.origin?.category ?? '') +
                '&&' +
                (node.origin?.parentCategory ?? '') +
                '&&' +
                (node.origin?.path ?? '') +
                '&&' +
                (node.origin?.dbFieldType ?? '')
              "
            >
              <span class="file-name" [innerHTML]="node.title | highlight: searchValue"> </span>
              <span
                class="file-desc"
                [innerHTML]="
                  (node.origin?.description ? node.origin?.description[language] || '' : '') | highlight: searchValue
                "
              >
              </span>
            </div>
          </div>
        </ng-template>
      </nz-tree>
    </nz-spin>
  </div>
</div>

<app-dsl-switch-source-modal
  *ngIf="switchSourceModal"
  [visible]="switchSourceModal"
  [isQueryPlan]="isQueryPlan"
  [dataSources]="dslWorkSidebarService.dataSources"
  [dataSourceNameList]="getDataSourceNameList(dslWorkSidebarService.dataSources)"
  [dataSourceName]="dslWorkSidebarService.dataSourceName"
  [interceptDataSourceUpdateOperations]="interceptDataSourceUpdateOperations"
  [fieldSourceMode]="fieldSourceMode"
  (confirm)="handleSwitchSource($event)"
  (cancel)="switchSourceModal = false"
></app-dsl-switch-source-modal>

<app-action-modal
  *ngIf="switchActionModal"
  [transferModal]="switchActionModal"
  [transferData]="transferData"
  [favouriteCode]="dslWorkDesignService.favouriteCode"
  [applicationCodeProxy]="dslWorkDesignService.applicationCodeProxy"
  labelType="EspAction"
  (callBack)="handleSwitchAction($event)"
  (closeModal)="switchActionModal = false"
>
</app-action-modal>

<data-source-update-modal
  *ngIf="updateVisible"
  [visible]="updateVisible"
  [type]="'SWITCH'"
  [fieldSourceMode]="fieldSourceMode"
  (cancal)="handleUpdateCancal()"
  (reset)="handleUpdateReset()"
  (reserve)="handleUpdateReserve()"
></data-source-update-modal>
