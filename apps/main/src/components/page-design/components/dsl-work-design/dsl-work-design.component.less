.ant-spin-nested-loading,
.ant-spin-container {
  width: 100%;
  height: 100%;
}

.tag-work-design {
  display: flex;
  height: 100%;
  position: relative;

  .tag-work-design-container {
    flex-grow: 1;
    height: 100%;
    margin: 0 !important;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .custom-no-source {
      ::ng-deep {
        .custom-widget {
          width: calc(100% - 264px);
          justify-content: center;
          align-items: center;
          display: flex;
        }
        .custom-print {
          padding-top: 10px;
        }
      }
    }

    .no-dataSource {
      flex: 1;
      width: 100%;
    }

    .tag-work-design-center {
      flex: 1;
      height: 100%;
      overflow: auto hidden;
      position: relative;

      .multi-terminal {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }

      .drawing-center {
        flex: 1;
        overflow: auto;
        overflow-y: hidden;
        height: 100%;
        background: #fff;
        // border: 16px solid #edeff3;
        border-left: 16px solid #edeff3;
        border-bottom: 0;
        // padding: 16px 16px 0;
      }
    }

    .toggle-attr {
      position: absolute;
      right: 0;
      width: 32px;
      line-height: 44px;
      background: white;
      text-align: center;

      .iconfont {
        font-size: 12px;

        &:hover {
          color: #6a4cff;
        }
      }

      &.hide-attr-panel {
        background: #ffffff;
        border-radius: 4px 0 0 4px;
        box-shadow: -5px 5px 10px 0 rgba(0, 0, 0, 0.06);
      }
    }

    .attr-panel {
      width: 272px;
      height: 100%;
    }
  }

  .grey {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 30px;
    z-index: 99;
  }

  .geryOnModal {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background: white;
    height: 80%;
    border-radius: 30px 30px 42px 42px;
    z-index: 3001;
    padding-top: 28px;
  }

  .systermChangeBtn {
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: 1px solid;
    margin-bottom: 20px;
    cursor: pointer;
  }

  .systermIcon {
    margin-bottom: 2px;
    padding: 3px 10px;
    margin-right: 10px;

    i {
      font-size: 20px;
      color: rgb(148, 148, 148);
    }
  }
}
