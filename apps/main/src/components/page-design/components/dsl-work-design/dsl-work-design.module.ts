import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';

// import { DslFormRenderModule } from '@webdpt/src/lib/lowcode/design/dsl-form-render/dsl-form-render.module';
import { DslFormRenderModule } from '../dsl-form-render/dsl-form-render.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';

import { AdInputNumberModule } from 'components/ad-ui-components/ad-input-number/ad-input-number.module';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { AdDatePickerModule } from 'components/ad-ui-components/ad-date-picker/ad-date-picker.module';
import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';

import { NzMessageModule } from 'ng-zorro-antd/message';
import { AdTreeSelectModule } from 'components/ad-ui-components/ad-tree-select/ad-tree-select.module';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';

import { DataSourceModule } from '../data-source/data-source.module';
import { WorkDesignModule } from '../../entries/work-design/work-design.module';
import { OpenwindowConfigModalModule } from 'components/bussiness-components/component-config/openwindow-config-modal/openwindow-config-modal.module';
import { SelectConfigModalModule } from 'components/bussiness-components/component-config/select-config-modal/select-config-modal.module';

import { DslWorkDesignComponent } from './dsl-work-design.component';
import { DslWorkDesignService } from './service/dsl-work-design.service';
import { DslWorkSidebarComponents } from './dsl-work-sidebar/dsl-work-sidebar';
import { DslWorkSidebarComponent } from './dsl-work-sidebar/dsl-work-sidebar.component';
import { DslWorkSetService } from './dsl-work-form/components/dsl-work-set/dsl-work-set.service';
import { DslDrawCenterComponent } from './dsl-work-form/components/dsl-draw-center/dsl-draw-center.component';
import { DslPlanStatusComponent } from './dsl-work-form/components/dsl-plan-status/dsl-plan-status.component';
import { DslDataStatusModalComponent } from './dsl-work-form/components/dsl-plan-status/dsl-data-status-modal/dsl-data-status-modal.component';
// import { FormContentComponent } from './tag-work-form/form-content/form-content.component';
import { DslWorkFormComponent } from './dsl-work-form/dsl-work-form.component';
import { DslDrawCenterTitleComponent } from './dsl-work-form/components/dsl-draw-center-title/dsl-draw-center-title.component';
import { DslWorkSetSubmitModule } from './dsl-work-form/components/dsl-work-set/dsl-work-set.module';
import { ActionModalModule } from '../../../bussiness-components/action-modal/action-modal.module';

import { DwFormEditorModule } from '@webdpt/form-editor';
import { FormioModule } from '@formio/angular';

import { DslFieldSetPipe } from './dsl-work-sidebar/field-set/dsl-field-set.pipe';
import { DslWorkFormService } from './dsl-work-form/dsl-work-form.service';
import { DslDataPropertiesSetComponent } from './dsl-work-form/components/dsl-plan-status/dsl-data-properties-set/dsl-data-properties-set.component';
import { InputModule } from 'components/form-components/input/input.module';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { DslFieldSetTreeNodeDirective } from './dsl-work-sidebar/field-set/dsl-field-set-tree-node.directive';
import { TangentHooksModule } from 'components/bussiness-components/tangent-hooks/tangent-hooks.module';
import { ExtendEditorModalModule } from '../../../bussiness-components/extend-editor-modal/extend-editor-modal.module';
import { ModifyHistoryModule } from 'components/bussiness-components/modify-history/modify-history.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DirectiveModule } from 'common/directive/directive.module';
import { CustomWidgetModule } from '../../../bussiness-components/custom-widget/custom-widget.module';
import { CustomPrintModule } from 'components/bussiness-components/custom-print/custom-print.module';
import { NoSourceModule } from '../../../bussiness-components/no-source/no-source.module';
import { SourceMergeFieldsService } from 'common/service/source-merge-fields.service';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { PipeModule } from '../../../../common/pipe/pipe.module';

import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { DataSourceMFComponent } from 'common/micro-frontend/container/data-source/data-source-mf.component';
import { CommonRuleSetModule } from '../common-rule-set/common-rule-set.module';
import { RuleConfigModule } from 'components/page-design/entries/work-design/rule-config/rule-config.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { DslOperationHooksComponent } from './dsl-work-form/dsl-operation-hooks/dsl-operation-hooks.component';
import { DslFieldSetService } from './dsl-work-sidebar/service/dsl-field-set.service';

@NgModule({
  declarations: [
    DslWorkDesignComponent,
    ...DslWorkSidebarComponents,
    DslDrawCenterComponent,
    DslPlanStatusComponent,
    DslDataStatusModalComponent,
    DslWorkFormComponent,
    DslDrawCenterTitleComponent,
    DslFieldSetPipe,
    DslDataPropertiesSetComponent,
    DslFieldSetTreeNodeDirective,
    DataSourceMFComponent,
    DslOperationHooksComponent,
  ],
  exports: [DslWorkDesignComponent, DslWorkSidebarComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    WorkDesignModule,
    TranslateModule,
    AdIconModule,
    AdTabsModule,
    AdButtonModule,

    AdInputNumberModule,
    AdSelectModule,
    NzCheckboxModule,
    AdDatePickerModule,
    AdEmptyModule,

    AdModalModule,
    NzMessageModule,
    AdTreeSelectModule,
    NzSpaceModule,
    NzSelectModule,
    NzDatePickerModule,
    NzFormModule,
    NzInputModule,
    NzModalModule,
    NzSpinModule,
    NzTableModule,
    NzPopconfirmModule,
    NzToolTipModule,
    NzDrawerModule,
    NzTreeModule,
    NzGridModule,
    NzStepsModule,
    NzRadioModule,
    NzCheckboxModule,
    NzTreeSelectModule,
    NzSwitchModule,
    NzDropDownModule,
    NzInputNumberModule,
    NzDividerModule,
    NzLayoutModule,
    NzAnchorModule,
    NzTimePickerModule,
    DragDropModule,
    // LowCodeModule,
    DslFormRenderModule,
    DataSourceModule,
    DslWorkSetSubmitModule,
    OpenwindowConfigModalModule,
    SelectConfigModalModule,
    ActionModalModule,
    DwFormEditorModule,
    FormioModule,
    InputModule,
    NzCollapseModule,
    TangentHooksModule,
    ExtendEditorModalModule,
    ModifyHistoryModule,
    DirectiveModule,
    CustomWidgetModule,
    CustomPrintModule,
    NoSourceModule,
    NzIconModule,
    PipeModule,
    RuleConfigModule,
    CommonRuleSetModule,
    NzTabsModule,
    // DataSourceMFModule,
    AdModalModule,
  ],
  providers: [
    DslWorkDesignService,
    DslWorkSetService,
    DslWorkFormService,
    SourceMergeFieldsService,
    DslFieldSetService,
  ],
})
export class DslWorkDesignModule {}
