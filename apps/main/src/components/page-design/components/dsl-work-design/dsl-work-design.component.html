<div class="tag-work-design">
  <app-dsl-work-sidebar
    #dslWorkSidebar
    [config]="service.config.sidebarConfig"
    [isCustom]="isCustom"
    [dataSources]="dataSources"
    [dataSourceName]="dataSourceName"
    [actionId]="actionId"
    [workData]="service.workData"
    [pageCode]="service.config.pageCode"
    [pageData]="dslWorkFormService.pageData"
    [UIBOT__appendEocFields]="UIBOT__appendEocFields"
    [submitActionsBase]="submitActionsBase"
    [masterFromDataSourceName]="masterFromDataSourceName"
    (changeDataSource)="handleChangeDataSource($event)"
    (changeFieldSource)="handleChangeFieldSource($event)"
    (changePage)="handleChangePage($event)"
    (sendFieldData)="handleSendFieldData($event)"
    (deleteRuleCallback)="handleDeleteRuleCallback($event)"
    (refreshPageView)="handleRefreshPageView()"
    (syncTab)="handleSyncTab($event)"
    (resetPageUIElement)="handleResetPageUIElement($event)"
    (changePageUIElementCode)="handleChangePageUIElementCode($event)"
  ></app-dsl-work-sidebar>

  <div class="tag-work-design-container">
    <!--暂无数据源-->
    <div class="no-dataSource" *ngIf="isNoSource">
      <app-no-source
        [tip]="service.config.textContent.noSourceTip | translate"
        [isCustom]="isCustom && showOperateCustomTemplate"
        [ngClass]="{
          'custom-no-source': isCustom
        }"
        [canCustom]="
          service.config.canCustom &&
          (!service.config?.customWithDataSource || (service.config?.customWithDataSource && !isNone(dataSourceName)))
        "
        [customTip]="customTip"
        (setSource)="handleSetSource()"
        (setCustom)="handleSwitchCustom(true)"
      >
        <app-custom-print
          *ngIf="!!isCustom && showOperateCustomTemplate"
          [fields]="[]"
          [appCode]="appCode"
          [operations]="service.operateCustomTemplateRelates"
          [pageCode]="service.pageCode"
          (changeOperateCustomTemplate)="handleChangeOperateCustomTemplate($event)"
        >
        </app-custom-print>
      </app-no-source>
    </div>

    <!--存在数据源-->
    <ng-container *ngIf="!isNoSource">
      <!-- 中心区域 -->
      <div class="tag-work-design-center">
        <!--画板-->
        <div class="drawing-center">
          <app-dsl-work-form
            #dslWorkForm
            [workServiceData]="workServiceData"
            [showOperateCustomTemplate]="showOperateCustomTemplate"
            [renderStateManagementData]="renderStateManagementData"
            [workData]="service.workData"
            [isCustom]="isCustom"
            [customTip]="customTip"
            [submitActionsBase]="submitActionsBase"
            [allReturnFields_A]="allReturnFields_A"
            (changePage)="handleChangePage($event)"
            (handleSwitchCustomBack)="handleSwitchCustom($event)"
            (handleChangeOperateCustomTemplateBack)="handleChangeOperateCustomTemplate($event)"
            (handleModuleData)="handleModuleData($event)"
          >
          </app-dsl-work-form>
        </div>
      </div>
    </ng-container>
  </div>
</div>

<app-action-modal
  *ngIf="switchSourceModal"
  [transferModal]="switchSourceModal"
  [favouriteCode]="service.favouriteCode"
  [applicationCodeProxy]="service.applicationCodeProxy"
  [transferData]="{ useApp: 'true' }"
  labelType="EspAction"
  (callBack)="handleSelectAction($event)"
  (closeModal)="switchSourceModal = false"
>
</app-action-modal>
