import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AppService } from 'pages/apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { DslWorkSetService } from '../../dsl-work-set.service';
import { WorkDesignConfig } from '../../../../../shared/interface/dsl-work-design.interface';

@Component({
  selector: 'app-dsl-work-action-params',
  templateUrl: './dsl-action-params.component.html',
  styleUrls: ['./dsl-action-params.component.less'],
})
export class DslWorkActionParamsComponent implements OnInit {
  loading: boolean;
  activeIndex: any;
  selectItem: any;
  sourceType: any[]; // 来源类型
  converterList: any[]; // 转换器
  nameList: any[]; // 目标字段下拉
  valueList: any[]; // 值字段下拉
  nameVisible: boolean = false;
  valueVisible: boolean = false;
  @Input() data: any; // 原始数据
  @Input() transferModal: boolean;
  @Input() actionParams: any;
  @Input() dataSourceFields: any;
  @Input() config: WorkDesignConfig;

  @Output() callBack = new EventEmitter();
  @Output() closeModal = new EventEmitter();

  constructor(
    public appService: AppService,
    private trans: TranslateService,
    private dslWorkSetService: DslWorkSetService,
  ) {}

  ngOnInit(): void {
    this.sourceType = [
      { value: 'ACTIVE_ROW', label: this.trans.instant('dj-ACTIVE_ROW:当前行动态值') },
      { value: 'ACTIVE_ROW_CONSTANT', label: this.trans.instant('dj-ACTIVE_ROW_CONSTANT:静态值') },
      {
        value: 'GET_ACTION_RESPONSE',
        label: this.trans.instant('dj-GET_ACTION_RESPONSE:提交的值，或上一个action返回的值'),
      },
      { value: 'CONSTANT', label: this.trans.instant('dj-CONSTANT:静态值，空数组可以用[]') },
      { value: 'SYSTEM', label: this.trans.instant('dj-SYSTEM:取系统参数，value可以为当前登录id') },
      { value: 'PROCESS_VARIABLE', label: this.trans.instant('dj-PROCESS_VARIABLE:任务引擎推送来的变量') },
      { value: 'TM_VARIABLE', label: this.trans.instant('dj-TM_VARIABLE:交付设计器变量') },
    ];
    this.converterList = [
      { value: '', label: this.trans.instant('dj-空') },
      { value: 'stringToBooleanConverter', label: 'stringToBooleanConverter' },
      { value: 'stringToNumberConverter', label: 'stringToNumberConverter' },
    ];
    this.handleLoadName();

    if (this.config.formSetConfig.isNeedValueList) {
      this.handleFormat();
    }

    if (this.actionParams?.length > 0) {
      this.handleSelectParam(0);
    }
  }

  // 查询name
  handleLoadName(): void {
    if (this.data.type === 'ESP') {
      const param = {
        label: 'EspAction',
        actionId: this.data.actionId,
      };
      this.loading = true;
      this.dslWorkSetService.getActionInfo(param).subscribe(
        (res) => {
          if (res.code === 0) {
            const appData = res.data.request_parameters || [];
            const format = (origin) => {
              return (origin || []).map((d) => {
                const returnVal = {
                  ...d,
                  key: d.fullPath,
                  title: d.data_name,
                };
                if (d['field']?.length > 0) {
                  returnVal['children'] = format(d['field']);
                  returnVal['expanded'] = true;
                } else {
                  returnVal['isLeaf'] = true;
                }
                return returnVal;
              });
            };
            appData.forEach((d) => {
              d.key = d.fullPath;
              d.title = d.data_name;
              d.expanded = true;
              d.children = format(d['field']);
            });
            this.nameList = appData;
          }
          this.loading = false;
        },
        () => {
          this.loading = false;
        },
      );
    }
  }

  // 格式化
  handleFormat(): void {
    this.valueList = this.dslWorkSetService.flatValueTree(this.dataSourceFields);
  }

  // 设置name
  handleClickNameList(event: NzFormatEmitEvent): void {
    this.selectItem['name'] = event.node.key;
    if (this.activeIndex > -1) {
      this.actionParams[this.activeIndex]['name'] = event.node.key;
    }
    this.nameVisible = false;
  }

  // 设置value
  handleClickValueList(event: NzFormatEmitEvent): void {
    this.selectItem['value'] = event.node.key;
    if (this.activeIndex > -1) {
      this.actionParams[this.activeIndex]['value'] = event.node.key;
    }
    this.valueVisible = false;
  }

  // 回调修改的数据
  handleModifyData(key: any, data: any): void {
    if (this.activeIndex > -1) {
      this.actionParams[this.activeIndex][key] = data;
    }
  }

  // 新增
  handleAdd(): void {
    this.actionParams.push({
      name: '',
      type: '',
      value: '',
      typeConverter: '',
    });
    this.handleSelectParam(this.actionParams.length - 1);
  }

  // 选中
  handleSelectParam(index: any): void {
    this.activeIndex = index;
    this.selectItem = this.actionParams[index];
  }

  // 删除
  handleDeleteParam(index: any): void {
    this.actionParams.splice(index, 1);
  }

  // 关闭action
  handleCloseSet(): void {
    this.closeModal.emit();
  }

  // 确认
  handleConfirm(): void {
    this.callBack.emit(this.actionParams);
  }
}
