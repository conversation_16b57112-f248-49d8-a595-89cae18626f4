<div class="draw-submit" *ngIf="!!moduleData">
  <div class="submit-all" [style]="submitAllStyle" *ngIf="moduleData?.type === 'submitActions'">
    <div class="submit-title">{{ 'dj-提交设定' | translate }}</div>
    <div class="submit-tree-content">
      <div class="submit-tree-head">
        <span>{{ 'dj-操作列表' | translate }}</span>
        <span *ngIf="config.formSetConfig.canAddOperate" class="add-operate" (click)="handleAdd()">{{
          'dj-新增操作' | translate
        }}</span>
      </div>
      <nz-tree
        #tree
        [nzData]="treeData"
        (nzClick)="handleClickSubmit($event)"
        [nzTreeTemplate]="nzPageTemplate"
        nzExpandAll
        nzBlockNode
      >
        <ng-template #nzPageTemplate let-node let-origin="origin">
          <span class="custom-node">
            <span class="tree-node">
              <div class="tree-node-name">
                {{ node.origin.title }}
              </div>
              <div class="tree-icons">
                <i
                  class="treeIcon"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-新增' | translate"
                  adIcon
                  type="plus"
                  theme="outline"
                  (click)="handleAddSubmit($event, node)"
                ></i>
                <i
                  *ngIf="
                    (config.formSetConfig.deleteTreeType === 'report' && node.level > 0) ||
                    config.formSetConfig.deleteTreeType === 'all'
                  "
                  adIcon
                  iconfont="icondelete3"
                  aria-hidden="true"
                  class="treeIcon iconfont"
                  nz-tooltip
                  [nzTooltipTitle]="'dj-删除' | translate"
                  (click)="handleDeleteSubmit($event, node)"
                >
                </i>
              </div>
            </span>
          </span>
        </ng-template>
      </nz-tree>
    </div>
  </div>
  <div class="submit-node">
    <div *ngIf="moduleData?.type === 'submitAction'">
      <span>
        {{ 'dj-布局' | translate }}
      </span>
      <span class="title-detail">
        <span>
          >
          {{ moduleData?.data?.lang?.title?.[('dj-LANG' | translate)] || moduleData?.data?.title








          }}{{ 'dj-逻辑' | translate }}</span
        >
      </span>
    </div>
  </div>
  <!-- submitActions -->
  <div
    class="prop-tabs"
    *ngIf="config.formSetConfig?.isNeedActionHooks && !!selectedSubmit && !selectedSubmit?.isChild"
  >
    <span [class.panel-active]="tabIndex === 0" (click)="tabIndex = 0">{{ 'dj-属性' | translate }}</span>
    <span [class.panel-active]="tabIndex === 1" (click)="tabIndex = 1">{{ 'dj-高级' | translate }}</span>
  </div>
  <!-- submitActions 属性 -->
  <div class="submit-form" *ngIf="!!selectedSubmit && tabIndex === 0">
    <div class="form-item">
      <span class="item-title">
        <span class="item-required">*</span>
        {{ 'dj-标题' | translate }}
      </span>
      <app-component-input
        [ngClass]="{ hasError: !errors['title'].validate }"
        [attr]="{
          name: '标题',
          required: true,
          needLang: true,
          nodeKey: selectedTreeNode?.key,
          lang: {
            value: selectedSubmit?.lang?.title ?? {}
          }
        }"
        [value]="selectedSubmit?.lang?.title?.[('dj-LANG' | translate)]"
        (callBack)="handlePatch('title', $event)"
        (blur)="handleValidate('title')"
        style="width: 100%"
      >
      </app-component-input>
      <div class="error" *ngIf="!errors['title'].validate">
        {{ errors['title']['error'] }}
      </div>
    </div>
    <div class="form-item">
      <span class="item-title">
        <span class="item-required">*</span>
        {{ 'dj-类型' | translate }}
      </span>
      <input
        nz-dropdown
        [nzDropdownMenu]="menu"
        nz-input
        [ngClass]="{ hasErrorBorder: !errors['type'].validate }"
        type="text"
        [readonly]="config.formSetConfig.isReadonly"
        [(ngModel)]="dslWorkSetService.typeReflect[selectedSubmit['type']] || selectedSubmit['type']"
        (ngModelChange)="handleModifyData('type', $event)"
        (blur)="handleValidate('type')"
        [nzTrigger]="'click'"
      />
      <nz-dropdown-menu #menu="nzDropdownMenu">
        <ul nz-menu>
          <li
            nz-menu-item
            *ngFor="let item of typeList"
            (click)="handlePatch('type', { value: item.value })"
            style="padding: 4px 8px; font-size: 13px"
          >
            {{ item.label }}
          </li>
        </ul>
      </nz-dropdown-menu>
      <div class="error" *ngIf="!errors['type'].validate">
        {{ errors['type']['error'] }}
      </div>
    </div>
    <div class="form-item" *ngIf="config.canCustom && !selectedSubmit.isChild">
      <label nz-checkbox [(ngModel)]="selectedSubmit['isCustomize']" (ngModelChange)="handleCustom($event)"
        >{{ 'dj-定制' | translate }}
      </label>
    </div>
    <div
      class="form-item"
      *ngIf="config.formSetConfig.hasBusinessMenu && !['RECYCLE'].includes(selectedSubmit['type'])"
    >
      <span class="item-title">
        {{ 'dj-业务类型' | translate }}
      </span>
      <input
        nz-dropdown
        [nzDropdownMenu]="businessMenu"
        nz-input
        type="text"
        [(ngModel)]="selectedSubmit['actionType']"
        (ngModelChange)="handleModifyData('actionType', $event)"
        [nzTrigger]="'click'"
      />
      <nz-dropdown-menu #businessMenu="nzDropdownMenu">
        <ul nz-menu>
          <li
            nz-menu-item
            *ngFor="let item of actionTypeList"
            (click)="handlePatch('actionType', { value: item })"
            style="padding: 4px 8px; font-size: 13px"
          >
            {{ item }}
          </li>
        </ul>
      </nz-dropdown-menu>
    </div>
    <div class="form-item" *ngIf="isWorkFlowType()">
      <span class="item-title">
        {{ 'dj-绑定业务流' | translate }}
      </span>
      <div>
        <ad-select
          style="width: 246px"
          nzShowSearch
          nzAllowClear
          [ngModel]="selectedSubmit?.['paras']?.['processId']"
          (ngModelChange)="handleModelChange($event)"
        >
          <ad-option *ngFor="let item of processList" [nzValue]="item.processId" [nzLabel]="item.processName">
          </ad-option>
        </ad-select>
      </div>
    </div>
    <div
      class="form-item"
      *ngIf="
        config.formSetConfig.isUiBot
          ? !!selectedSubmit?.type &&
            !['COMBINE', 'TaskEngine', 'workflow', 'PTM', 'MANUALPROJECT_NEW', 'LCDP', 'RECYCLE'].includes(
              selectedSubmit['type']
            )
          : config.canCustom
          ? !!selectedSubmit['isCustomize'] ||
            (!!selectedSubmit?.type &&
              !['TaskEngine', 'workflow', 'PTM', 'MANUALPROJECT_NEW', 'LCDP'].includes(selectedSubmit['type']))
          : !!selectedSubmit?.type &&
            !['TaskEngine', 'workflow', 'PTM', 'MANUALPROJECT_NEW', 'LCDP'].includes(selectedSubmit['type'])
      "
    >
      <span class="item-title">
        <span
          class="item-required"
          *ngIf="
            (!config.canCustom || !selectedSubmit['isCustomize']) &&
            ['ESP', 'SD', 'UIBOT', 'TM'].includes(selectedSubmit['type'])
          "
          >*</span
        >
        {{ 'dj-服务' | translate }}
      </span>
      <nz-input-group
        *ngIf="selectedSubmit['type'] !== 'UIBOT'"
        [nzSuffix]="suffixIcon"
        [ngClass]="{ hasError: !errors['actionId'].validate }"
      >
        <input
          readonly
          nz-input
          [(ngModel)]="selectedSubmit['actionId']"
          [placeholder]="'dj-请选择' | translate"
          (blur)="handleValidate('actionId')"
        />
      </nz-input-group>
      <ng-template #suffixIcon>
        <i
          adIcon
          iconfont="iconkaichuang"
          aria-hidden="true"
          class="window-icon iconfont"
          (click)="handleOpenAction()"
        ></i>
      </ng-template>
      <input
        *ngIf="selectedSubmit['type'] === 'UIBOT'"
        nz-dropdown
        [nzDropdownMenu]="uibotMenu"
        nz-input
        type="text"
        [(ngModel)]="dslWorkSetService.actionReflect[selectedSubmit['actionId']] || selectedSubmit['actionId']"
        (ngModelChange)="handleModifyData('actionId', $event)"
        [nzTrigger]="'click'"
      />
      <nz-dropdown-menu #uibotMenu="nzDropdownMenu">
        <ul nz-menu>
          <li
            nz-menu-item
            *ngFor="let item of dslWorkSetService.actionList"
            (click)="handlePatch('actionId', { value: item.value })"
            style="padding: 4px 8px; font-size: 13px"
          >
            {{ item.label }}
          </li>
        </ul>
      </nz-dropdown-menu>
      <div class="error" *ngIf="!errors['actionId'].validate">
        {{ errors['actionId']['error'] }}
      </div>
    </div>
    <div
      class="form-item"
      *ngIf="
        config.canCustom
          ? !!selectedSubmit['isCustomize'] ||
            !['SD', 'UIBOT', 'TM', 'MANUALPROJECT_NEW', 'LCDP'].includes(selectedSubmit['type'])
          : !config.formSetConfig.isUiBot
          ? !['SD', 'UIBOT', 'TM', 'MANUALPROJECT_NEW', 'LCDP'].includes(selectedSubmit['type'])
          : !['COMBINE', 'SD', 'UIBOT', 'TM', 'MANUALPROJECT_NEW', 'LCDP'].includes(selectedSubmit['type'])
      "
    >
      <span class="item-title">
        {{ 'dj-服务名' | translate }}
      </span>
      <app-component-input
        *ngIf="!['TaskEngine', 'workflow', 'PTM'].includes(selectedSubmit['type'])"
        [attr]="{
          name: '服务名',
          required: true,
          needLang: false
        }"
        [value]="selectedSubmit?.serviceName"
        (callBack)="handlePatch('serviceName', $event)"
        style="width: 100%"
      >
      </app-component-input>
      <input
        *ngIf="selectedSubmit['type'] === 'PTM'"
        nz-dropdown
        [nzDropdownMenu]="ptmServiceList"
        nz-input
        type="text"
        [(ngModel)]="
          dslWorkSetService.workflowServiceReflect[selectedSubmit['serviceName']] || selectedSubmit['serviceName']
        "
        (ngModelChange)="handleModifyData('serviceName', $event)"
        [nzTrigger]="'click'"
      />
      <nz-dropdown-menu #ptmServiceList="nzDropdownMenu">
        <ul nz-menu>
          <li
            nz-menu-item
            *ngFor="let item of dslWorkSetService.workflowServiceList"
            (click)="handlePatch('serviceName', { value: item.value })"
            style="padding: 4px 8px; font-size: 13px"
          >
            {{ item.label }}
          </li>
        </ul>
      </nz-dropdown-menu>
      <input
        *ngIf="selectedSubmit['type'] === 'TaskEngine'"
        nz-dropdown
        [nzDropdownMenu]="serviceNameMenu"
        nz-input
        type="text"
        [(ngModel)]="dslWorkSetService.serviceReflect[selectedSubmit['serviceName']] || selectedSubmit['serviceName']"
        (ngModelChange)="handleModifyData('serviceName', $event)"
        [nzTrigger]="'click'"
      />
      <nz-dropdown-menu #serviceNameMenu="nzDropdownMenu">
        <ul nz-menu>
          <li
            nz-menu-item
            *ngFor="let item of dslWorkSetService.serviceList"
            (click)="handlePatch('serviceName', { value: item.value })"
            style="padding: 4px 8px; font-size: 13px"
          >
            {{ item.label }}
          </li>
        </ul>
      </nz-dropdown-menu>
      <input
        *ngIf="selectedSubmit['type'] === 'workflow'"
        nz-dropdown
        [nzDropdownMenu]="workflowServiceList"
        nz-input
        type="text"
        [(ngModel)]="
          dslWorkSetService.workflowServiceReflect[selectedSubmit['serviceName']] || selectedSubmit['serviceName']
        "
        (ngModelChange)="handleModifyData('serviceName', $event)"
        [nzTrigger]="'click'"
      />
      <nz-dropdown-menu #workflowServiceList="nzDropdownMenu">
        <ul nz-menu>
          <li
            nz-menu-item
            *ngFor="let item of dslWorkSetService.workflowServiceList"
            (click)="handlePatch('serviceName', { value: item.value })"
            style="padding: 4px 8px; font-size: 13px"
          >
            {{ item.label }}
          </li>
        </ul>
      </nz-dropdown-menu>
    </div>
    <div
      class="form-item"
      *ngIf="
        config.canCustom
          ? !!selectedSubmit['isCustomize'] ||
            selectedSubmit['type'] === 'LCDP' ||
            !['ESP', 'SD', 'UIBOT', 'TM', 'TaskEngine', 'PTM', 'MANUALPROJECT_NEW'].includes(selectedSubmit['type'])
          : !config.formSetConfig.isUiBot
          ? selectedSubmit['type'] === 'LCDP' ||
            !['ESP', 'SD', 'UIBOT', 'TM', 'TaskEngine', 'PTM', 'MANUALPROJECT_NEW'].includes(selectedSubmit['type'])
          : selectedSubmit['type'] === 'LCDP' ||
            !['COMBINE', 'ESP', 'SD', 'UIBOT', 'TM', 'TaskEngine', 'PTM', 'MANUALPROJECT_NEW'].includes(
              selectedSubmit['type']
            )
      "
    >
      <span class="item-title">
        <span
          class="item-required"
          *ngIf="
            config.canCustom
              ? !selectedSubmit['isCustomize'] && selectedSubmit['type'] === 'LCDP'
              : selectedSubmit['type'] === 'LCDP'
          "
          >*</span
        >
        {{ 'dj-API路径' | translate }}
      </span>
      <app-component-input
        [ngClass]="{ hasError: selectedSubmit['type'] === 'LCDP' && !errors['url'].validate }"
        [attr]="{
          name: 'API路径',
          required: true,
          needLang: false
        }"
        [value]="selectedSubmit?.url"
        (blur)="handleValidate('url')"
        (callBack)="handlePatch('url', $event)"
        style="width: 100%"
      >
      </app-component-input>
      <div class="error" *ngIf="!errors['url'].validate">
        {{ errors['url']['error'] }}
      </div>
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <label nz-checkbox [(ngModel)]="selectedSubmit['terminateProcess']" (ngModelChange)="handleModifyData()">{{
        'dj-是否终止流程' | translate
      }}</label>
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <label nz-checkbox [(ngModel)]="selectedSubmit['dispatch']" (ngModelChange)="handleModifyData()">{{
        'dj-数据处理完毕，是否结束任务' | translate
      }}</label>
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <label
        nz-checkbox
        [(ngModel)]="selectedSubmit['executeAfterCheckCompleted']"
        (ngModelChange)="handleModifyData()"
        >{{ 'dj-是否在数据提交之后执行' | translate }}</label
      >
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <label nz-checkbox [(ngModel)]="selectedSubmit['defaultAction']" (ngModelChange)="handleModifyData()">{{
        'dj-设置为默认按钮' | translate
      }}</label>
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <label nz-checkbox [(ngModel)]="selectedSubmit['debounce']" (ngModelChange)="handleModifyData()">
        {{ 'dj-防抖' | translate }}
      </label>
    </div>

    <div class="form-item" *ngIf="!config.formSetConfig.isSubmitTypeEmbedded || !selectedSubmit.isChild">
      <span class="item-title">
        {{ 'dj-提交数据' | translate }}
      </span>
      <app-component-input
        [attr]="{
          name: '提交数据',
          required: true,
          needLang: false
        }"
        [value]="!config.formSetConfig.isSubmitTypeEmbedded ? submitType?.schema : selectedSubmit['submitType']?.schema"
        (callBack)="handleSubmitType('schema', $event)"
        style="width: 100%"
      >
      </app-component-input>
    </div>
    <div
      class="form-item"
      *ngIf="!selectedSubmit.isChild && (!config.formSetConfig.isSubmitTypeEmbedded || selectedSubmit['submitType'])"
    >
      <label
        *ngIf="!config.formSetConfig.isSubmitTypeEmbedded"
        nz-checkbox
        [(ngModel)]="submitType['isBatch']"
        (ngModelChange)="handleModifyData()"
        >{{ 'dj-是否分批' | translate }}</label
      >

      <label
        *ngIf="config.formSetConfig.isSubmitTypeEmbedded"
        nz-checkbox
        [(ngModel)]="selectedSubmit['submitType']['isBatch']"
        (ngModelChange)="handleModifyData()"
        >{{ 'dj-是否分批' | translate }}</label
      >
    </div>
    <div
      class="form-item"
      *ngIf="!selectedSubmit.isChild && (!config.formSetConfig.isSubmitTypeEmbedded || selectedSubmit['submitType'])"
    >
      <label
        *ngIf="!config.formSetConfig.isSubmitTypeEmbedded"
        nz-checkbox
        [(ngModel)]="selectedSubmit['submitAll']"
        (ngModelChange)="handleModifyData()"
        >{{ 'dj-是否提交所有数据' | translate }}</label
      >
      <label
        *ngIf="config.formSetConfig.isSubmitTypeEmbedded"
        nz-checkbox
        [(ngModel)]="selectedSubmit['submitType']['submitAll']"
        (ngModelChange)="handleModifyData()"
        >{{ 'dj-是否提交所有数据' | translate }}</label
      >
    </div>
    <div class="form-item" *ngIf="config.isMultiSelect && !selectedSubmit.isChild && selectedSubmit['submitType']">
      <label
        nz-checkbox
        [ngModel]="selectedSubmit['submitType']['multipleSelect'] === false"
        (ngModelChange)="handleChangeMultipleSelect($event)"
        >{{ 'dj-数据撤回是否单选' | translate }}</label
      >
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <span class="item-title">Condition</span>
      <app-json-editor-modal
        [attr]="{ inputWidth: '100%', inputHeight: '32px' }"
        [value]="selectedSubmit?.condition"
        (callBack)="handleChangeValue('condition', $event)"
      ></app-json-editor-modal>
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <span class="item-title">Hidden</span>
      <app-json-editor-modal
        [attr]="{ inputWidth: '100%', inputHeight: '32px' }"
        [value]="selectedSubmit?.hidden"
        (callBack)="handleChangeValue('hidden', $event)"
      ></app-json-editor-modal>
    </div>
    <div class="form-item" *ngIf="!selectedSubmit.isChild">
      <span class="item-title">
        {{ 'dj-提示' | translate }}
      </span>
      <app-component-input
        [attr]="{
          name: '提示',
          required: true,
          needLang: true,
          lang: {
            value: selectedSubmit?.confirm?.lang?.content ?? {}
          }
        }"
        [value]="selectedSubmit?.confirm?.lang?.content?.[('dj-LANG' | translate)]"
        (callBack)="handlePatchConfirm($event)"
        style="width: 100%"
      >
      </app-component-input>
    </div>
    <div class="form-item">
      <span class="item-title">
        {{ 'dj-成功之后的签章文本' | translate }}
      </span>
      <app-component-input
        [attr]="{
          name: '成功之后的签章文本',
          required: true,
          needLang: true,
          lang: {
            value: selectedSubmit?.lang?.returnText ?? {}
          }
        }"
        [value]="selectedSubmit?.lang?.returnText?.[('dj-LANG' | translate)]"
        (callBack)="handlePatch('returnText', $event)"
        style="width: 100%"
      >
      </app-component-input>
    </div>
    <div class="form-end">
      <span>{{ 'dj-参数' | translate }}</span>
      <span class="field-set" (click)="handleActionParams()">{{ 'dj-设置' | translate }}</span>
    </div>
    <div class="form-end">
      <span>{{ 'dj-扩展' | translate }}</span>
      <i adIcon iconfont="iconkuozhan" class="iconfont" aria-hidden="true" (click)="showSubmitView = true"> </i>
    </div>
  </div>
  <!-- submitActions 高级 -->
  <div class="submit-form" *ngIf="config.formSetConfig?.isNeedActionHooks && !!selectedSubmit && tabIndex === 1">
    <dsl-action-hooks
      [hooks]="hooks"
      [eventSource]="selectedSubmit?.id"
      [eventSourceType]="'submitAction'"
      [componentTitle]="selectedSubmit?.lang?.title?.[('dj-LANG' | translate)]"
      (change)="handleHooksChange($event)"
    ></dsl-action-hooks>
  </div>
  <i adIcon iconfont="icondanchuxiaoxiguanbi" class="closeIcon" aria-hidden="true" (click)="handleClose()"></i>
</div>

<!--action开窗组件-->
<app-action-modal
  *ngIf="actionModal"
  [transferModal]="actionModal"
  [transferData]="actionData"
  [confirmTip]="true"
  [favouriteCode]="dslWorkDesignService.favouriteCode"
  [applicationCodeProxy]="dslWorkDesignService.applicationCodeProxy"
  [labelType]="labelType"
  (callBack)="handleSelectAction($event)"
  (closeModal)="actionModal = false"
>
</app-action-modal>

<!-- 扩展 -->
<app-extend-editor-modal
  *ngIf="showSubmitView"
  [data]="selectedSubmit"
  [width]="'640px'"
  (ok)="handleShowSubmit($event)"
  (close)="showSubmitView = false"
></app-extend-editor-modal>

<app-dsl-work-action-params
  *ngIf="actionParamsModal"
  [data]="selectedSubmit"
  [dataSourceFields]="dataSourceFields"
  [transferModal]="actionParamsModal"
  [actionParams]="selectedSubmit?.actionParams || []"
  [config]="config"
  (callBack)="handleSetAction($event)"
  (closeModal)="actionParamsModal = false"
>
</app-dsl-work-action-params>
