import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzFormatEmitEvent, NzTreeNode } from 'ng-zorro-antd/tree';
import { cloneDeep, isNone } from '../../../../../../../../common/utils/core.utils';
import { UUID } from 'angular2-uuid';
import { DslWorkSetService } from '../dsl-work-set.service';
import { WorkDesignConfig } from '../../../../shared/interface/dsl-work-design.interface';
import { getServiceName } from 'components/page-design/components/data-source/data-source.component.util';
import { isEmpty, isEqual } from 'lodash';
import { DslWorkDesignService } from 'components/page-design/components/dsl-work-design/service/dsl-work-design.service';
import { GlobalService } from 'common/service/global.service';
import { AppTypes } from 'pages/app/typings';
import { AppService } from 'pages/apps/app.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dsl-work-set-submit',
  templateUrl: './dsl-work-set-submit.component.html',
  styleUrls: ['./dsl-work-set-submit.component.less'],
})
export class DslWorkSetSubmitComponent implements OnInit, OnChanges {
  loading: boolean; // 页面loading
  treeData: any; // 操作树
  selectedTreeNode: any; // 选中的tree 节点
  _selectedSubmit: any; // 选中提交
  set selectedSubmit(data: any) {
    this._selectedSubmit = data;
    this.hooks = this.hookItems?.find((item) => item?.eventSource === data?.id) || {};
  }
  get selectedSubmit(): any {
    return this._selectedSubmit;
  }
  submitType: any;
  actionTypeList: any[];
  typeList: any[]; // 类型
  actionModal: boolean = false; // action开窗
  actionData: any; // action回参
  labelType: any; // 类型
  showSubmitView: boolean; // 显隐view
  actionParamsModal: boolean;
  errors: any = this.dslWorkSetService.errors; // 错误日志
  processList: any = []; // 绑定流程列表
  type: string;
  tabIndex: number = 0;
  @ViewChild('tree') tree;
  @Input() moduleData: any; // 数据
  @Output() confirmModule: EventEmitter<any> = new EventEmitter();
  @Output() changeAction: EventEmitter<any> = new EventEmitter();
  @Output() closePanel: EventEmitter<any> = new EventEmitter();
  @Output() hooksChange: EventEmitter<any> = new EventEmitter();
  // 全量的数据源字段数据
  @Input() dataSourceFields: any;
  @Input() config: WorkDesignConfig;
  @Input() submitAllStyle: string;
  @Input() dataSourceName: string;
  @Input() isFromPageData: boolean = false; // 从作业过来 dslWorkDesignService服务中没有dataSourceName， 现在dataSourceName input 进来，等泽宇版本上线后，优化
  @Input() hookItems;
  hooks: any;

  constructor(
    private translate: TranslateService,
    private cd: ChangeDetectorRef,
    public dslWorkSetService: DslWorkSetService,
    public dslWorkDesignService: DslWorkDesignService,
    private appService: AppService,
    private router: Router,
  ) {
    this.type = this.router.url.match(/[a-z]+\?/)?.[0]?.slice(0, -1);
  }

  ngOnInit(): void {
    this.typeList = this.dslWorkSetService.typeListDict[this.config.formSetConfig.typeListType];

    if (
      GlobalService.appType === AppTypes.MODEL_DRIVEN &&
      (this.type === 'edit' || this.type === 'design' || !isEmpty(this.dslWorkDesignService.extendHeader))
    ) {
      this.typeList = [...this.typeList, ...this.dslWorkSetService.typeModelDrivenListDict];
      this.handleLoadProcessList();
    }
    this.actionTypeList = this.dslWorkSetService.actionTypeList;

    if (!this.config.formSetConfig.isSubmitTypeEmbedded) {
      this.submitType = this.moduleData?.submitType || {};
      if (!this.submitType['schema'] && this.submitType['schema'] !== this.getSchema()) {
        this.submitType['schema'] = this.getSchema();
        this.handleModifyData();
      }
      if (isNone(this.submitType['isBatch'])) {
        this.submitType['isBatch'] = false;
        this.handleModifyData();
      }
    }
    if (isNone(this.selectedSubmit['debounce'])) {
      this.selectedSubmit['debounce'] = true;
      this.handleModifyData();
    }
    this.cd.detectChanges();
  }

  handleLoadProcessList(): void {
    const application = this.appService.selectedApp?.code;
    this.dslWorkSetService.loadProcessList(application).subscribe((res) => {
      if (res.code === 0) {
        this.processList = (res?.data ?? []).map((process) => {
          return {
            processId: process?.processId,
            processName: process?.processName,
          };
        });
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('moduleData')) {
      this.tabIndex = 0;
      if (changes.moduleData.currentValue?.type === 'submitActions' && this.tree) {
        const nodeOriginList = this.tree.getTreeNodes().map((node) => {
          return node.origin;
        });

        const notChange = this.checkDataOriginChange(changes.moduleData.currentValue?.data, nodeOriginList);

        if (notChange) return;
      }
      let mark = false;
      if (this.config.formSetConfig.isSubmitTypeEmbedded) {
        mark = this.handleSchema();
      }

      if (this.moduleData?.type === 'submitActions') {
        this.selectedSubmit = null;
        this.handleFormat();
      } else {
        this.selectedSubmit = this.moduleData?.data;
        if (!!this.selectedSubmit) {
          this.handleLang();
        }
      }
      if (mark) {
        this.handleModifyData();
      }

      if (!this.config.formSetConfig.isSubmitTypeEmbedded) {
        this.submitType = this.moduleData?.submitType || null;
        if (!this.submitType || Object.keys(this.submitType).length === 0) {
          this.submitType = {
            schema: this.getSchema(),
            isBatch: false,
          };
        }
      }

      Object.keys(this.errors || {}).forEach((key) => {
        this.errors[key].validate = true;
      });
      if (this.moduleData?.type === 'submitAction') {
        this.initHandleValidate();
        this.selectedTreeNode = null; // 点击齿轮，点击按钮tree,再点击画布下面的按钮（不是抽屉里的按钮tree）, 要清空selectedTreeNode，否则selectedSubmit无法赋值
      }
    }
    if (!isEqual(changes.hookItems?.previousValue, changes.hookItems?.currentValue)) {
      this.hooks = this.hookItems?.find((item) => item?.eventSource === this.selectedSubmit?.id) || {};
    }
  }
  initHandleValidate() {
    const keys = ['title', 'type', 'actionId', 'url'];
    keys.forEach((key) => {
      this.handleValidate(key);
    });
  }

  handleClose() {
    this.closePanel.emit();
  }
  /**
   * 暂时获取schema
   * @returns
   */
  getSchema() {
    return this.dataSourceFields?.[0]?.data_name ?? '';
  }

  // 根据moduleData中的字段判断target中对应的属性是否发生了变化，以此判断是否需要刷新树节点
  // moduleData是传入的moduleData，target是当前的渲染树中的节点数据
  checkDataOriginChange(moduleData, target) {
    return moduleData.every((moduleDataItem, index) => {
      return Object.keys(moduleDataItem).every((moduleDataKey) => {
        // 判断子层级
        if (moduleDataKey === 'attachActions') {
          const moduleDataAttachActions = moduleDataItem[moduleDataKey];
          const targetAttachActions = target?.[index]?.['children'];
          return this.checkDataOriginChange(moduleDataAttachActions, targetAttachActions);
        }
        return isEqual(moduleDataItem[moduleDataKey], target?.[index]?.[moduleDataKey]);
      });
    });
  }

  // 处理schema
  handleSchema(): boolean {
    let mark = false;
    if (this.moduleData?.type === 'submitActions') {
      (this.moduleData.data || []).forEach((s) => {
        const { schema } = s.submitType || {};
        if (!schema && schema !== this.getSchema()) {
          mark = true;
          s.submitType = {
            ...(s?.submitType || {}),
            schema: this.getSchema(),
            isBatch: false,
          };
        }
      });
    } else {
      if (!isNone(this.moduleData.data)) {
        const { schema } = this.moduleData?.data?.submitType || {};
        if (!schema && schema !== this.getSchema()) {
          mark = true;
          this.moduleData.data = {
            ...(this.moduleData.data || {}),
            submitType: {
              ...(this.moduleData.data?.submitType || {}),
              schema: this.getSchema(),
              isBatch: false,
            },
          };
        }
      }
    }
    return mark;
  }

  // 处理lang
  handleLang(): void {
    this.selectedSubmit['lang'] = {
      ...(this.selectedSubmit['lang'] || {}),
      title: {
        ...(this.selectedSubmit.lang?.title || {}),
        [this.translate.instant('dj-LANG')]:
          this.selectedSubmit.lang?.title?.[this.translate.instant('dj-LANG')] || this.selectedSubmit['title'],
      },
      returnText: this.selectedSubmit.lang?.returnText || {},
    };
    if (!!this.selectedSubmit?.confirm?.content) {
      this.selectedSubmit['confirm'] = {
        ...(this.selectedSubmit['confirm'] || {}),
        lang: {
          ...(this.selectedSubmit?.confirm?.lang || {}),
          content: this.selectedSubmit?.confirm?.lang?.content || {},
        },
      };
    }
  }

  // 格式化
  handleFormat(): void {
    const { data = [] } = this.moduleData;
    const appData = cloneDeep(data || []);
    const format = (origin, parent) => {
      return (origin || []).map((d) => {
        const returnVal = {
          ...d,
          key: 'tmp_' + UUID.UUID().replace(/-/g, '').toLocaleUpperCase(),
          title: d.title,
          parentId: parent,
        };

        const childMark = d.type === 'COMBINE' ? 'combineActions' : 'attachActions';

        if (d[childMark]?.length > 0) {
          returnVal['children'] = format(d[childMark], returnVal['key']);
          returnVal['expanded'] = true;
        } else {
          returnVal['isLeaf'] = true;
        }
        return returnVal;
      });
    };
    appData.forEach((d) => {
      const childMark = d.type === 'COMBINE' ? 'combineActions' : 'attachActions';

      d.key = 'tmp_' + UUID.UUID().replace(/-/g, '').toLocaleUpperCase();
      d.expanded = true;
      d.children = format(d[childMark] || [], d['key']);
    });
    this.treeData = appData;
  }

  // Submit点击事件
  handleClickSubmit(event: NzFormatEmitEvent): void {
    this.tabIndex = 0;
    // 临时解法，后续会调整逻辑
    // 原因：input组件的onChange有300ms的防抖，修改某节点的title后快速切换时会先触发这里的selectedSubmit赋值
    // 之后触发之前节点的onChange后的handlePatch，此时当前节点的title会被覆盖
    // 除了300ms的防抖，input里面还有请求翻译的逻辑，翻译返回时机不可控，所以在请求翻译时锁定对应的attr，之后根据attr中的key确定更新的节点
    const nodeList = this.tree.getSelectedNodeList();
    nodeList.forEach((node) => {
      node.isSelected = false;
    });
    event.node.isSelected = true;

    setTimeout(() => {
      this.selectedTreeNode = event.node;
      this.selectedSubmit = event.node.origin;
      this.handleLang();
      Object.keys(this.errors).forEach((key) => {
        this.errors[key].validate = true;
      });
      if (event.node.level !== 0) {
        this.selectedSubmit.isChild = true;
      }
      this.initHandleValidate();
      this.cd.detectChanges();
    }, 300);
  }

  // 新增
  handleAdd(): void {
    const key = 'tmp_' + UUID.UUID().replace(/-/g, '').toLocaleUpperCase();
    const tmp = new NzTreeNode({
      key,
      title: this.translate.instant('dj-新增'),
      isLeaf: false,
    });
    tmp.origin.title = this.translate.instant('dj-新增');
    tmp.origin['lang'] = {
      title: {
        zh_TW: '新增',
        zh_CN: '新增',
        en_US: 'add',
      },
    };
    const id = UUID.UUID();
    tmp.origin['id'] = id;

    const n = this.tree.getTreeNodes();
    let currentSelectedIndex = -1;
    n.forEach((node, index) => {
      if (node.isSelected) {
        currentSelectedIndex = index;
      }
      node.isSelected = false;
    });

    n.push(tmp);
    this.treeData = n;
    this.handleModifyData();
    if (currentSelectedIndex !== -1) {
      this.treeData[currentSelectedIndex].isSelected = true;
    }
  }

  // 左侧: 新增attach事件
  handleAddSubmit(e: any, node: any): void {
    node.isExpanded = true;
    node.isLeaf = false;
    e.stopPropagation();
    node.isLeaf = false;
    const key = 'tmp_' + UUID.UUID().replace(/-/g, '').toLocaleUpperCase();
    const tmp = new NzTreeNode({
      key,
      title: this.translate.instant('dj-新增'),
      isLeaf: false,
      lang: {
        title: {
          zh_TW: '新增',
          zh_CN: '新增',
          en_US: 'add',
        },
      },
    });
    node.addChildren([tmp]);
    this.handleModifyData();
  }

  // 左侧: 删除action事件
  handleDeleteSubmit(e: any, node: any): void {
    e.stopPropagation();
    if (node.level == 0) {
      const index = this.treeData.findIndex((s) => s.key === node.key);
      this.treeData = this.treeData.filter((s) => s.key !== node.key);
      this.tree.getTreeNodes().splice(index, 1);
    } else {
      node.remove();
    }
    if (node.key === this.selectedSubmit?.key) {
      this.selectedSubmit = null;
    }
    this.handleModifyData();
  }

  // 特殊处理提示字段
  handlePatchConfirm(data: any): void {
    if (!!data.value) {
      this.selectedSubmit['confirm'] = {
        enable: true,
        title: this.translate.instant('dj-提示'),
        lang: {
          title: {
            zh_TW: '提示',
            zh_CN: '提示',
          },
          content: data.lang?.value,
        },
        content: data?.value,
      };
    } else {
      this.selectedSubmit['confirm'] = {};
    }
    this.handleModifyData();
  }

  // 更改数据
  handlePatch(key: any, data: any): void {
    let node = this.selectedTreeNode;

    // 匹配node的key而不是origin的key是因为origin与selectedSubmit为同一对象，而selectedSubmit可以被手动编辑
    // 为保证异步的多语言标题能够命中对应的节点所以额外携带nodeKey
    if (data?.nodeKey) {
      node = this.tree?.getTreeNodeByKey(data.nodeKey);
    }

    // 同步父节点的origin中的children对象
    if (node?.parentNode) {
      const index = node.parentNode.children.findIndex((childrenNode) => childrenNode === node);
      node.parentNode.origin.children[index] = node.origin;
    }

    if (this.moduleData?.type === 'submitActions' && !node) return;

    const handleTarget = node?.origin ?? this.selectedSubmit;

    handleTarget[key] = data?.value;
    if (Object.keys(this.errors).includes(key)) {
      this.errors[key].validate = !!data?.value;
    }
    if (data?.needLang) {
      handleTarget.lang = {
        ...(handleTarget?.lang || {}),
        [key]: data.lang?.value,
      };
    }
    if (key === 'type') {
      // 解决配置好查询条件以后配置查询按钮的类型条件设计会被清空
      if (data?.value === handleTarget?.type) {
        return;
      }
      if (data?.value === 'MANUALPROJECT_NEW') {
        handleTarget['actionId'] = 'sd_manual.project.create';
        handleTarget['serviceName'] = 'manual.project.create';
      }
      if (data?.value === 'RECYCLE') {
        handleTarget['actionId'] = 'recycle.delete';
        handleTarget['actionType'] = 'basic-data-delete';
      }
      if (!['TaskEngine', 'PTM', 'LCDP', 'RECYCLE'].includes(data?.value)) {
        if (Reflect.has(handleTarget, 'actionId')) {
          Reflect.deleteProperty(handleTarget, 'actionId');
        }
      }
      if (!['SD', 'UIBOT', 'TM', 'LCDP'].includes(data?.value)) {
        if (Reflect.has(handleTarget, 'serviceName')) {
          Reflect.deleteProperty(handleTarget, 'serviceName');
        }
      }
    }
    if (['TaskEngine', 'PTM'].includes(handleTarget['type']) && key === 'serviceName') {
      handleTarget['actionId'] = data?.value;
    }
    if (handleTarget['type'] === 'workflow' && key === 'serviceName') {
      handleTarget['actionId'] = this.dslWorkSetService.actionIdByWorkFlow[data?.value] || data?.value;
    }
    if (key === 'processId') {
      handleTarget['paras'] = {
        ...(handleTarget['paras'] ?? {}),
        processId: data,
      };
    }
    this.handleModifyData();
    this.cd.detectChanges();
  }

  // 校验
  handleValidate(key: string): void {
    switch (key) {
      case 'title': {
        const value =
          this.selectedSubmit?.lang?.[key]?.[this.translate.instant('dj-LANG')] || this.selectedSubmit['title'];
        this.errors[key].validate = !!value;
        break;
      }
      case 'actionId':
      case 'url': {
        // 按钮
        if (this.selectedSubmit['type'] === 'LCDP') {
          this.errors[key].validate = !!this.selectedSubmit[key];

          if (this.config.canCustom && !!this.selectedSubmit['isCustomize']) {
            this.errors[key].validate = true;
          }
        } else {
          this.errors[key].validate = true;
        }

        break;
      }
      case 'type': {
        const value = this.dslWorkSetService.typeReflect[this.selectedSubmit?.type] || this.selectedSubmit?.type;
        this.errors[key].validate = !!value;
        break;
      }
      default:
        break;
    }
  }

  // 更改submitType
  handleSubmitType(key: any, data: any): void {
    if (this.config.formSetConfig.isSubmitTypeEmbedded) {
      // this.selectedSubmit = {
      //   ...(this.selectedSubmit || {}),
      //   submitType: {
      //     ...(this.selectedSubmit?.submitType || {}),
      //     [key]: data?.value,
      //   },
      // };
      this.selectedSubmit.submitType = {
        ...(this.selectedSubmit?.submitType || {}),
        [key]: data?.value,
      };
    } else {
      this.submitType = {
        ...(this.submitType || {}),
        [key]: data?.value,
      };
    }

    this.handleModifyData();
  }

  handleChangeValue(key: any, data: any): void {
    this.selectedSubmit = {
      ...(this.selectedSubmit || {}),
      [key]: data?.value,
    };

    this.handleModifyData();
  }

  // 选择服务action
  handleOpenAction(): void {
    this.actionModal = true;
    this.actionData = {
      actionId: this.selectedSubmit['actionId'] || '',
      actionName: '',
    };
    this.labelType = this.selectedSubmit['type'] === 'ESP' ? 'EspAction' : '';
  }

  // 确认选择
  handleSelectAction(data: any): void {
    const { actionId, actionName } = data;
    this.selectedSubmit['actionId'] = actionId;
    this.errors['actionId'].validate = !!actionId;

    this.selectedSubmit['serviceName'] = getServiceName(actionId);

    this.actionModal = false;
    this.handleModifyData();

    this.changeAction.emit({
      actionId,
      actionName,
      type: this.selectedSubmit?.type,
    });
  }

  // 打开设置actionParams
  handleActionParams(): void {
    this.actionParamsModal = true;
  }

  // 保存actionParams
  handleSetAction(data: any[]): void {
    this.selectedSubmit.actionParams = data || [];
    this.actionParamsModal = false;
    this.handleModifyData();
  }

  // 调整activityConfig
  handleShowSubmit(data: any): void {
    this.selectedSubmit = data;
    this.showSubmitView = false;
    const selectedNodeList = this.tree?.getSelectedNodeList();
    if (selectedNodeList && selectedNodeList.length > 0) {
      const node = selectedNodeList[0];
      node.origin = this.selectedSubmit;

      node.children?.forEach((childrenNode, index) => {
        const childMark = this.selectedSubmit['type'] === 'COMBINE' ? 'combineActions' : 'attachActions';
        childrenNode.origin = {
          ...childrenNode.origin,
          ...(this.selectedSubmit?.[childMark]?.[index] ?? {}),
        };
        if (node.origin?.children?.[index]) node.origin.children[index] = childrenNode.origin;
      });

      if (node.parentNode) {
        const index = node.parentNode.children.findIndex((childrenNode) => childrenNode === node);
        node.parentNode.origin.children[index] = node.origin;
      }
    }
    this.handleModifyData();
  }

  // 调整自定义
  handleCustom(isCustom: boolean): void {
    if (!!isCustom) {
      this.errors['actionId'].validate = true;
      this.errors['url'].validate = true;
    }
    this.handleModifyData();
  }

  handleHooksChange({ hooks, type }): void {
    const hookItems = cloneDeep(this.hookItems);
    const id = hooks?.eventSource;
    const index = hookItems.findIndex((item) => item.eventSource === id);
    if (type === 'del' && !hooks?.name) {
      hookItems.splice(index, 1);
    } else if (index < 0) {
      // 新增
      hookItems.push(hooks);
    } else {
      // 更新
      hookItems[index] = hooks;
    }
    this.hooksChange.emit(hookItems);
  }

  // 回调修改的数据
  handleModifyData(key?: any, data?: any): void {
    if (!!key) {
      this.selectedSubmit[key] = data;
      if (key === 'type') {
        if (data === 'MANUALPROJECT_NEW') {
          this.selectedSubmit['actionId'] = 'sd_manual.project.create';
          this.selectedSubmit['serviceName'] = 'manual.project.create';
        }
        if (data === 'RECYCLE') {
          this.selectedSubmit['actionId'] = 'recycle.delete';
          this.selectedSubmit['actionType'] = 'basic-data-delete';
        }
        if (Object.keys(this.errors).includes(key)) {
          this.errors[key].validate = !!(
            this.dslWorkSetService.typeReflect[this.selectedSubmit?.type] || this.selectedSubmit?.type
          );
          this.errors['actionId'].validate = true;
          this.errors['url'].validate = true;
        }
      }
    }
    if (['TaskEngine', 'PTM'].includes(this.selectedSubmit?.type) && key === 'serviceName') {
      this.selectedSubmit['actionId'] = data;
    }
    let callback;
    if (this.moduleData?.type === 'submitActions') {
      if (!!this.tree && !!this.tree.getTreeNodes()) {
        const tree = this.tree?.getTreeNodes();
        const data = [];
        tree.forEach((s) => {
          const {
            key,
            selected,
            children = [],
            expanded,
            isLeaf,
            icon,
            level,
            isChild,
            combineActions,
            attachActions,
            ...temp
          } = s.origin || {};
          const childMark = temp['type'] === 'COMBINE' ? 'combineActions' : 'attachActions';
          const format = (origin: any): any => {
            return origin.map((d) => {
              const {
                key: childKey,
                selected: childSelect,
                children: childChildren = [],
                expanded: childExpend,
                isLeaf: childLeaf,
                icon: childIcon,
                level: childLevel,
                combineActions,
                attachActions,
                isChild,
                ...tempChild
              } = d || {};
              const mark = tempChild['type'] === 'COMBINE' ? 'combineActions' : 'attachActions';
              if (childChildren.length > 0) {
                tempChild[mark] = format(childChildren);
              }
              return tempChild;
            });
          };
          const actualChildren = format(children);
          s.origin[childMark] = actualChildren;
          data.push({
            ...temp,
            [childMark]: actualChildren,
          });
        });

        // const data = this.treeDataToModuleData(tree);

        callback = {
          ...this.moduleData,
          data,
        };

        if (!this.config.formSetConfig.isSubmitTypeEmbedded) {
          callback['submitType'] = this.submitType;
        }
      } else {
        callback = {
          ...this.moduleData,
        };

        if (!this.config.formSetConfig.isSubmitTypeEmbedded) {
          callback['submitType'] = this.submitType;
        }
      }
    } else {
      callback = {
        ...this.moduleData,
        data: this.selectedSubmit,
      };

      if (!this.config.formSetConfig.isSubmitTypeEmbedded) {
        callback['submitType'] = this.submitType;
      }
    }
    this.confirmModule.emit(callback);
    this.cd.detectChanges();
  }

  // 校验页面
  handleValidateSubmit(): boolean {
    if (!this.moduleData.data || !this.selectedSubmit) {
      return true;
    }
    const keys = ['title', 'type'];
    let validate = true;
    const toValidate = (key: string): void => {
      this.handleValidate(key);
      if (validate && !this.errors[key].validate) {
        validate = false;
      }
    };
    for (const key of keys) {
      toValidate(key);
    }
    if (['ESP', 'SD', 'UIBOT', 'TM'].includes(this.selectedSubmit?.type)) {
      toValidate('actionId');
    }
    if (this.selectedSubmit['type'] === 'LCDP') {
      toValidate('url');
    }
    return validate;
  }

  isWorkFlowType(): boolean {
    return this.selectedSubmit['type'] === 'workflow' && this.selectedSubmit['serviceName'] === 'workflow-invoke';
  }

  handleModelChange(e: any) {
    this.selectedSubmit['paras'] = {
      ...(this.selectedSubmit['paras'] ?? {}),
      processId: e,
    };
    this.handlePatch('processId', e);
  }

  handleChangeMultipleSelect(check: boolean) {
    this.selectedSubmit['submitType']['multipleSelect'] = !check;
    this.handleModifyData();
  }
}
