<!--action列表开窗-->
<ad-modal
  nzClassName="view-action-params"
  [nzWidth]="'666px'"
  [nzTitle]="'dj-参数设置' | translate"
  [(nzVisible)]="transferModal"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="handleCloseSet()"
>
  <ng-container *adModalContent>
    <nz-spin [nzSpinning]="loading">
      <div class="action-params">
        <div class="field-list">
          <div class="title" (click)="handleAdd()">+ {{ 'dj-新增字段' | translate }}</div>
          <div *ngIf="actionParams?.length > 0" class="param-content">
            <div
              *ngFor="let param of actionParams; let i = index"
              class="field-data"
              [ngClass]="{ 'active-param': activeIndex === i }"
              (click)="handleSelectParam(i)"
            >
              <div class="field-title" nz-tooltip [nzTooltipTitle]="param.name || ('dj-字段' | translate) + (i + 1)">
                {{ param.name || ('dj-字段' | translate) + (i + 1) }}
              </div>
              <div class="field-icon">
                <i
                  adIcon
                  nz-tooltip
                  iconfont="iconxiaoliebiaoshanchu"
                  aria-hidden="true"
                  (click)="handleDeleteParam(i)"
                  [nzTooltipTitle]="'dj-删除' | translate"
                >
                </i>
              </div>
            </div>
          </div>
        </div>
        <div class="field-detail" *ngIf="!!selectItem">
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-提交目标字段' | translate }}
            </div>
            <input
              nz-dropdown
              [nzDropdownMenu]="nameMenu"
              nz-input
              type="text"
              [(ngModel)]="selectItem['name']"
              (ngModelChange)="handleModifyData('name', $event)"
              [nzTrigger]="data['type'] === 'ESP' ? 'click' : null"
              [(nzVisible)]="nameVisible"
            />
            <nz-dropdown-menu #nameMenu="nzDropdownMenu">
              <div class="select-value-drop">
                <nz-tree [nzData]="nameList" (nzClick)="handleClickNameList($event)" nzExpandAll nzBlockNode> </nz-tree>
              </div>
            </nz-dropdown-menu>
          </div>
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-类型' | translate }}
            </div>
            <ad-select [(ngModel)]="selectItem['type']" (ngModelChange)="handleModifyData('type', $event)">
              <ad-option *ngFor="let option of sourceType" [nzValue]="option.value" [nzLabel]="option.label">
              </ad-option>
            </ad-select>
          </div>
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-值' | translate }}
            </div>
            <input
              nz-dropdown
              [nzDropdownMenu]="valueMenu"
              nz-input
              type="text"
              [(ngModel)]="selectItem['value']"
              (ngModelChange)="handleModifyData('value', $event)"
              [nzTrigger]="data['type'] === 'ESP' ? 'click' : null"
              [(nzVisible)]="valueVisible"
            />
            <nz-dropdown-menu #valueMenu="nzDropdownMenu">
              <div class="select-value-drop">
                <nz-tree
                  [nzSelectedKeys]="[selectItem['value']]"
                  [nzData]="config.formSetConfig.isNeedValueList ? valueList : nameList"
                  (nzClick)="handleClickValueList($event)"
                  nzExpandAll
                  nzBlockNode
                >
                </nz-tree>
              </div>
            </nz-dropdown-menu>
          </div>
          <div class="field-item">
            <div class="field-title">
              {{ 'dj-转换器' | translate }}
            </div>
            <ad-select
              [(ngModel)]="selectItem['typeConverter']"
              (ngModelChange)="handleModifyData('typeConverter', $event)"
            >
              <ad-option *ngFor="let option of converterList" [nzValue]="option.value" [nzLabel]="option.label">
              </ad-option>
            </ad-select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button ad-button adType="default" (click)="handleCloseSet()">
          {{ 'dj-取消' | translate }}
        </button>
        <button ad-button adType="primary" (click)="handleConfirm()">
          {{ 'dj-确定' | translate }}
        </button>
      </div>
    </nz-spin>
  </ng-container>
</ad-modal>
