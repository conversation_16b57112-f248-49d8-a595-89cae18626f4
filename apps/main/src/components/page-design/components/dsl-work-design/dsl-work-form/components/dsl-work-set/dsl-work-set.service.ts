/*
 * @Description: 描述
 * @Author: 庄泽宇
 * @Date: 2023-05-09 10:55:29
 * @LastEditors: 庄泽宇
 * @LastEditTime: 2023-09-14 09:25:27
 */
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class DslWorkSetService {
  adesignerUrl: string;

  typeReflect: any;
  serviceReflect: any;
  serviceList: any;
  workflowServiceList: any;
  workflowServiceReflect: any;
  actionReflect: any;
  actionList: any;

  typeModelDrivenListDict: any;
  typeListDict: any;
  actionTypeList: any;
  errors: any;
  actionIdByWorkFlow: any;

  constructor(
    protected configService: SystemConfigService,
    protected http: HttpClient,
    private translate: TranslateService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this.initConfig();
  }

  initConfig() {
    this.typeReflect = {
      COMBINE: this.translate.instant('dj-COMBINE:API的组合调用'),
      ESP: this.translate.instant('dj-ESP:企业服务池的API'),
      SD: this.translate.instant('dj-SD:数据驱动引擎的API'),
      UIBOT: this.translate.instant('dj-UIBOT:封装好的API'),
      TM: this.translate.instant('dj-TM:知识图谱的API'),
      TaskEngine: this.translate.instant('dj-TaskEngine:任务引擎的API'),
      PTM: this.translate.instant('dj-PTM:任务引擎PTM的API'),
      MANUALPROJECT_NEW: this.translate.instant('dj-MANUALPROJECT_NEW:手动发起项目'),
      LCDP: this.translate.instant('dj-LCDP:低代码设计器数据中心的API'),
      RECYCLE: this.translate.instant('dj-RECYCLE:彻底删除'),
      workflow: this.translate.instant('dj-WORKFLOW:工作流引擎的API'),
    };

    this.serviceReflect = {
      'add-task': this.translate.instant('dj-加签:add-task'),
      agree: this.translate.instant('dj-同意:agree'),
      disagree: this.translate.instant('dj-不同意:disagree'),
      reapprove: this.translate.instant('dj-退回重签:reapprove'),
      reassign: this.translate.instant('dj-转派:reassign'),
      reexecute: this.translate.instant('dj-退回重办:reexecute'),
      'start-new-project': this.translate.instant('dj-发起项目:start-new-project'),
      'terminate-task': this.translate.instant('dj-终止流程:terminate-task'),
      'submit-data': this.translate.instant('dj-提交数据到流程:submit-data'),
    };

    this.serviceList = [
      { value: 'add-task', label: this.translate.instant('dj-加签:add-task') },
      { value: 'agree', label: this.translate.instant('dj-同意:agree') },
      { value: 'disagree', label: this.translate.instant('dj-不同意:disagree') },
      { value: 'reapprove', label: this.translate.instant('dj-退回重签:reapprove') },
      { value: 'reassign', label: this.translate.instant('dj-转派:reassign') },
      { value: 'reexecute', label: this.translate.instant('dj-退回重办:reexecute') },
      { value: 'start-new-project', label: this.translate.instant('dj-发起项目:start-new-project') },
      { value: 'terminate-task', label: this.translate.instant('dj-终止流程:terminate-task') },
      { value: 'submit-data', label: this.translate.instant('dj-提交数据到流程:submit-data') },
    ];

    this.workflowServiceReflect = {
      'add-task': this.translate.instant('dj-加签:add-task'),
      'workflow-act-agree': this.translate.instant('dj-同意:workflow-act-agree'),
      disagree: this.translate.instant('dj-不同意:disagree'),
      'workflow-act-return': this.translate.instant('dj-退回重签:workflow-act-return'),
      'workflow-act-submit': this.translate.instant('dj-提交: workflow-act-submit'),
      'workflow-invoke': `${this.translate.instant('dj-送审')}:workflow-invoke`,
      'workflow-abort': `${this.translate.instant('dj-撤审')}:workflow-abort`,
    };

    // 2024-05-22 闻武老师要求我在workflowServiceList中加了四个
    this.workflowServiceList = [
      { value: 'add-task', label: this.translate.instant('dj-加签:add-task') },
      { value: 'workflow-act-agree', label: this.translate.instant('dj-同意:workflow-act-agree') },
      { value: 'disagree', label: this.translate.instant('dj-不同意:disagree') },
      { value: 'workflow-act-return', label: this.translate.instant('dj-退回重签:workflow-act-return') },
      { value: 'workflow-act-submit', label: this.translate.instant('dj-提交: workflow-act-submit') },
      { value: 'workflow-invoke', label: `${this.translate.instant('dj-送审')}:workflow-invoke` },
      { value: 'workflow-abort', label: `${this.translate.instant('dj-撤审')}:workflow-abort` },
    ];

    this.actionReflect = {
      'commit-data-to-fi': this.translate.instant('dj-commit-data-to-fi:提交数据到ForecastingIntelligence'),
      'data-reassignment-action': this.translate.instant('dj-data-reassignment-action:数据转派'),
      'delete-activity-bpm-variable-value': this.translate.instant(
        'dj-delete-activity-bpm-variable-value:从流程变量中的集合中删除提交的数据',
      ),
      'filter-selected-data-action': this.translate.instant('dj-filter-selected-data-action:过滤选择数据'),
      'start-new-process': this.translate.instant('dj-start-new-process:发起流程'),
      'terminate-data': this.translate.instant('dj-terminate-data:终止任务的数据'),
      uibot_task_withdraw: this.translate.instant('dj-uibot_task_withdraw:撤回'),
      'update-activity-query-variable-value': this.translate.instant(
        'dj-update-activity-query-variable-value:从流程变量中的集合中删除提交的数据',
      ),
      'update-solve-task-approval-state': this.translate.instant(
        'dj-update-solve-task-approval-state:更新任务签核状态',
      ),
      'update-table-field': this.translate.instant('dj-update-table-field:更新数据状态'),
      'update-task-trace-state': this.translate.instant(
        'dj-update-task-trace-state:添加所有需要追踪的数据id到任务中心',
      ),
      uibot_retrive: this.translate.instant('dj-uibot_retrive:任务撤回'),
    };

    this.actionList = [
      {
        value: 'commit-data-to-fi',
        label: this.translate.instant('dj-commit-data-to-fi:提交数据到ForecastingIntelligence'),
      },
      { value: 'data-reassignment-action', label: this.translate.instant('dj-data-reassignment-action:数据转派') },
      {
        value: 'delete-activity-bpm-variable-value',
        label: this.translate.instant('dj-delete-activity-bpm-variable-value:从流程变量中的集合中删除提交的数据'),
      },
      {
        value: 'filter-selected-data-action',
        label: this.translate.instant('dj-filter-selected-data-action:过滤选择数据'),
      },
      { value: 'start-new-process', label: this.translate.instant('dj-start-new-process:发起流程') },
      { value: 'terminate-data', label: this.translate.instant('dj-terminate-data:终止任务的数据') },
      { value: 'uibot_task_withdraw', label: this.translate.instant('dj-uibot_task_withdraw:撤回') },
      {
        value: 'update-activity-query-variable-value',
        label: this.translate.instant('dj-update-activity-query-variable-value:从流程变量中的集合中删除提交的数据'),
      },
      {
        value: 'update-solve-task-approval-state',
        label: this.translate.instant('dj-update-solve-task-approval-state:更新任务签核状态'),
      },
      { value: 'update-table-field', label: this.translate.instant('dj-update-table-field:更新数据状态') },
      {
        value: 'update-task-trace-state',
        label: this.translate.instant('dj-update-task-trace-state:添加所有需要追踪的数据id到任务中心'),
      },
      { value: 'uibot_retrive', label: this.translate.instant('dj-uibot_retrive:任务撤回') },
    ];

    const typeListBase = [
      { value: 'ESP', label: this.translate.instant('dj-ESP:企业服务池的API') },
      { value: 'SD', label: this.translate.instant('dj-SD:数据驱动引擎的API') },
      { value: 'UIBOT', label: this.translate.instant('dj-UIBOT:封装好的API') },
      { value: 'TM', label: this.translate.instant('dj-TM:知识图谱的API') },
      { value: 'TaskEngine', label: this.translate.instant('dj-TaskEngine:任务引擎的API') },
      { value: 'PTM', label: this.translate.instant('dj-PTM:任务引擎PTM的API') },
      { value: 'MANUALPROJECT_NEW', label: this.translate.instant('dj-MANUALPROJECT_NEW:手动发起项目') },
      // { value: 'LCDP', label: this.translate.instant('dj-LCDP:低代码设计器数据中心的API') },
    ];

    const typeListCombine = [
      { value: 'COMBINE', label: this.translate.instant('dj-COMBINE:API的组合调用') },
      { value: 'RECYCLE', label: this.translate.instant('dj-RECYCLE:彻底删除') },
      ...typeListBase,
    ];

    const typeListReport = [
      { value: 'ESP', label: this.translate.instant('dj-ESP:企业服务池的API') },
      { value: 'LCDP', label: this.translate.instant('dj-LCDP:低代码设计器数据中心的API') },
    ];

    this.typeModelDrivenListDict = [
      { value: 'workflow', label: this.translate.instant('dj-WORKFLOW:工作流引擎的API') },
    ];

    this.typeListDict = {
      typeListBase,
      typeListCombine,
      typeListReport,
    };

    this.actionTypeList = [
      'basic-data-combine-save',
      'basic-data-update',
      'basic-data-save',
      'basic-data-delete',
      'basic-data-recover',
    ];

    this.errors = {
      title: {
        validate: true,
        error: this.translate.instant('dj-请填写必填项！'),
      },
      type: {
        validate: true,
        error: this.translate.instant('dj-请填写必填项！'),
      },
      actionId: {
        validate: true,
        error: this.translate.instant('dj-请填写必填项！'),
      },
      url: {
        validate: true,
        error: this.translate.instant('dj-请填写必填项！'),
      },
    };

    this.actionIdByWorkFlow = {
      'add-task': 'athena_bpm_add_task',
      'workflow-act-agree': 'athena.workflow.process.activity.work.agree',
      disagree: 'disagree',
      'workflow-act-return': 'athena.workflow.process.activity.return',
      'workflow-act-submit': 'athena.workflow.process.activity.work.submit',
    };
  }

  // 查询标签
  loadTaskTags(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/tag/queryPage`;
    return this.http.post(url, param);
  }

  delRule(ruleId: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/rule/deleteRule/${ruleId}`;
    return this.http.get(url);
  }

  saveRuleF(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/rule/saveRule`;
    return this.http.post(url, param);
  }

  loadProcessList(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/process/findProcessList?adpTyp=single&application=${param}`;
    return this.http.get(url);
  }

  // 获取数据源类型定义别名
  getTypeAlias(dataSource: any): string {
    switch (dataSource?.type) {
      case 'SD':
      case 'ESP':
        return 'esp';
      case 'RAWDATA':
        return 'fixedValue';
      default:
        return 'other';
    }
  }

  // 查询action详情
  getActionInfo(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/action/findActionByActionId?label=${param.label}&actionId=${param.actionId}`;
    return this.http.get(url);
  }

  /**
   * 拉平值数据
   */
  flatValueTree(data: any[]): any[] {
    const format = (origin, parent) => {
      return (origin || []).map((d) => {
        const key = `${parent}.${d.data_name}`;
        const returnVal = {
          ...d,
          key,
        };
        if (d['children']?.length > 0) {
          returnVal['children'] = format(d['children'], key);
        }
        return returnVal;
      });
    };
    (data || []).forEach((d) => {
      // d.key = d.userTarget || d.fullPath || d.data_name;
      d.key = d.fullPath || d.data_name; // 仅dsl模式不需要取userTarget
      d.title = d.key;
      d.children = format(d['children'], d.key);
    });
    return data;
  }
}
