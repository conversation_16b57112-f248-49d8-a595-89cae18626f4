const isDevelopment = process.env.NODE_ENV === 'development';

const data = {
  name: 'athena_designer_core',
  remotes: {
    athena_designer_core: `athena_designer_core@${
      isDevelopment ? 'http://localhost:3000' : '/athena-designer-core'
    }/remoteEntry.js`,
  },
  remoteModules: [
    {
      remoteEntry: `${isDevelopment ? 'http://localhost:3000' : `/athena-designer-core`}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './ModelGraph',
    },
    {
      remoteEntry: `${isDevelopment ? 'http://localhost:3000' : `/athena-designer-core`}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './ComponentSetting',
    },
    {
      remoteEntry: `${isDevelopment ? 'http://localhost:3000' : `/athena-designer-core`}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './OperatePermission',
    },
    {
      remoteEntry: `${isDevelopment ? 'http://localhost:3000' : `/athena-designer-core`}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './DropDownVocabularyModal',
    },
    {
      remoteEntry: `${isDevelopment ? 'http://localhost:3000' : `/athena-designer-core`}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './DataElementModal',
    },
    {
      remoteEntry: `${isDevelopment ? 'http://localhost:3000' : `/athena-designer-core`}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './RepresentClassModal',
    },
    // {
    //   remoteEntry: `${!environment.production ? 'http://localhost:3000' : '/athena-designer-core'}/remoteEntry.js`,
    //   remoteName: 'athena_designer_core',
    //   exposedModule: './react',
    // },
    // {
    //   remoteEntry: `${!environment.production ? 'http://localhost:3000' : '/athena-designer-core'}/remoteEntry.js`,
    //   remoteName: 'athena_designer_core',
    //   exposedModule: './react-dom',
    // },
  ],
  shared: {
    react: {
      singleton: true,
      requiredVersion: '18.2.0',
      // eager: true,
    },
    'react-dom/client': {
      singleton: true,
      requiredVersion: '18.2.0',
      // eager: true,
    },
  },
};
export default data;
