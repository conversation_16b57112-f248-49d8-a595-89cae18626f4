{
  "compileOnSave": false,
  "angularCompilerOptions": {
    "fullTemplateTypeCheck": false,
    "strictTemplates": false /* 对模板进行严格检查，存在较多错误提示导致无法启动，先注释掉 */
  },
  "compilerOptions": {
    "skipLibCheck": true,
    "importHelpers": true,
    "module": "es2020",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "moduleResolution": "node",
    "downlevelIteration": true,
    "emitDecoratorMetadata": false,
    "experimentalDecorators": true,
    "resolveJsonModule": true,
    "target": "es2015",
    "types": ["node"],
    "typeRoots": ["node_modules/@types"],
    "baseUrl": "src",
    "paths": {
      "@webdpt/form-editor": ["../projects/form-editor/src/public-api"],
      "@webdpt/form-editor-components": ["../projects/form-editor-components/src/public-api"],
      "mobile-ui": ["../projects/mobile-ui/src/public-api"],
      "tslib": ["node_modules/tslib/tslib.d.ts"]
      // "@athena-designer-core/*": ["../../athena-designer-core/*"],
    },
    "lib": ["es2019", "dom"],
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.html",
  ],
  "exclude": [
    "src/test.ts",
    "**/*.spec.ts",
    "**/node_modules",
    "**/dist",
    "**/tmp",
    "**/out",
    "**/.angular",
    "**/.cache",
    "**/.vscode",
    "**/coverage"
  ]
}
