// const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
// const path = require('path');
//
// module.exports = {
//   entry: {
//     'editor.worker': 'monaco-editor/esm/vs/editor/editor.worker.js',
//     'json.worker': 'monaco-editor/esm/vs/language/json/json.worker',
//     'css.worker': 'monaco-editor/esm/vs/language/css/css.worker',
//     'html.worker': 'monaco-editor/esm/vs/language/html/html.worker',
//     'ts.worker': 'monaco-editor/esm/vs/language/typescript/ts.worker'
//   },
//   output: {
//     globalObject: 'self',
//     filename: '[name].bundle.js',
//     path: path.resolve(__dirname, 'dist'),
//   },
//   plugins: [
//     new MonacoWebpackPlugin({
//       languages: ['javascript', 'xml', "typescript", "sql", "json"]
//     }),
//   ],
// };
import { container } from 'webpack';
import microFrontend from './src/common/micro-frontend/config/athena-designer-core';
// import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
// const dllPlugins = require('./dll.webpack.config').plugins;

module.exports = {
  devServer: {
    port: 4201,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': '*',
      'Access-Control-Allow-Headers': '*',
    },
    historyApiFallback: true,
    hot: false,
    proxy: [
      {
        context: ['/athena-designer'],
        // target: 'http://**************:8000',
        target: 'https://adp-paas.apps.digiwincloud.com.cn',
        // target: 'https://adp-uc-test.apps.digiwincloud.com.cn',
        changeOrigin: true,
      },
    ],
  },
  plugins: [
    // new BundleAnalyzerPlugin(),
    new container.ModuleFederationPlugin({
      remotes: microFrontend.remotes,
      shared: microFrontend.shared,
    }),
    // ...dllPlugins,
  ],
};
